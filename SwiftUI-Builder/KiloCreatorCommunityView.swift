//
//  KiloCreatorCommunityView.swift
//  HandNote
//
//  Created by 杨冰冰 on 2025/5/16.
//  Copyright © 2025 top-stack. All rights reserved.
//

import SwiftUI

struct KiloCreatorCommunityView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.horizontalSizeClass) var sizeClass
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部关闭按钮
            if sizeClass == .compact {
                closeButton
            }
            
            VStack(spacing: 24) {
                // 顶部插图
                ZStack(alignment: .top) {
                    Image(.creatorCommunityBanner)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                    
                    if sizeClass == .regular {
                        closeButton
                    }
                }
                
                
                // 标题和描述
                VStack(alignment: .leading, spacing: 22) {
                    Text("Be Part of Our Creator Club")
                        .font(.pingFang(24, weight: .bold))
                        .foregroundColor(.colorLabel)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Free Kilonotes subscriptions.")
                            .font(.pingFang(15))
                            .foregroundColor(.colorLabel)
                        
                        Text("Cash rewards.")
                            .font(.pingFang(15))
                            .foregroundColor(.colorLabel)
                        
                        Text("Create with Kilonotes Official.")
                            .font(.pingFang(15))
                            .foregroundColor(.colorLabel)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 底部说明和按钮
                VStack(spacing: 12) {
                    Text("Minimum of 200 followers on social media required")
                        .font(.system(size: 12))
                        .foregroundColor(.color3B3D4F)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    Button {
                        // 申请加入操作
                    } label: {
                        Text("申请加入")
                            .font(.pingFang(16, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 48)
                            .padding(.vertical, 8)
                            .background(Color.color2E82FF)
                            .cornerRadius(27)
                    }
                }
                .padding(.bottom, 24)
            }
        }
    }
    
    var closeButton: some View {
        HStack {
            Spacer()
            Button {
                dismiss()
            } label: {
                Image(systemName: "xmark")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.gray)
            }
        }
        .padding(sizeClass == .compact ? 13 : 24)
    }
}



#Preview {
    KiloCreatorCommunityView()
}
