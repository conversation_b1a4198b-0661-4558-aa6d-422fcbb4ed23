//
//  SwiftUI+Extension.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/4/2.
//

import SwiftUI

extension Font {
    
    public static func pingFang(_ size: CGFloat, weight: Font.Weight = .regular) -> Font {
        if weight == .medium {
            return .custom("PingFangSC-Medium", size: size)
        } else if weight == .bold {
            return .custom("PingFangSC-Bold", size: size)
        } else {
            return .custom("PingFangSC-Regular", size: size)
        }
    }
    
}


extension Color {
    public static let switchBackground: Color = Color(uiColor: .secondarySystemFill)
    
    public static let colorBackground: Color = Color(uiColor: .systemBackground)
    
    public static let colorSecondary: Color = Color(uiColor: .secondarySystemBackground)
    
    public static let colorSecondaryForAi: Color = Color(light: Color.colorF2F2F2, dark: Color.colorSecondary)
    
    public static let colorLabel: Color = Color(uiColor: .label)
    
    public static let colorF2F2F2: Color = .init(red: 242/255, green: 242/255, blue: 242/255)
    
    public static let color909090: Color = .init(red: 144/255, green: 144/255, blue: 144/255)
    
    public static let color626262: Color = .init(red: 98/255, green: 98/255, blue: 98/255)
    
    public static let color2E82FF: Color = .init(red: 46/255, green: 130/255, blue: 255/255)
    
    public static let colorE8E8E8: Color = .init(red: 232/255, green: 232/255, blue: 232/255)
    
    public static let colorFFAEAA: Color = .init(red: 255/255, green: 174/255, blue: 170/255)
    
    public static let colorF05460: Color = .init(red: 240/255, green: 84/255, blue: 96/255)
    
    public static let colorD9E2F0: Color = .init(red: 217/255, green: 226/255, blue: 240/255)
    
    public static let colorAFAFAF: Color = .init(red: 175/255, green: 175/255, blue: 175/255)
    
    public static let color3B3D4F: Color = .init(red: 59/255, green: 61/255, blue: 79/255)
}

// 扩展Color以支持十六进制颜色代码
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
