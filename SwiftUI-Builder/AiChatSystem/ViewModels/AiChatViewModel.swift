//
//  AiChatViewModel.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/13.
//

import Foundation
import SwiftUI
import Combine
import AIChatKit

typealias AiChatMessage = KiloAIChatManager.AIChatMessage
typealias AIModel = KiloAIChatManager.AIModel
typealias ConversationType = KiloAIChatManager.ConversationType
typealias AiChatRole = KiloAIChatManager.Role

class AiChatViewModel: ObservableObject {
    /// 输入框中的内容
    @Published var contentInput: String = ""
    /// 选择的聊天类型
    @AppStorage("AiChatInputViewModel.selectedType")
    var selectedType: ConversationType = .general
    /// 用户选择的图像
    @Published var seletedImage: UIImage?
    /// 是否正在loading
    @Published var isLoading: Bool = false
    /// 模型名称
    @Published var modelName:String = ""
    /// 所有消息
    @Published private(set) var messages: [AiChatMessage] = []
    // 是否可以使用图片
    @Published var canUseExtraTool: Bool = false
    /// 是否应该展示工具
    @Published  var showExtraTools = false
    /// 滚动到底部发布者
    @Published var shouldScrollToBottom: Bool = false
    /// 聊天管理器
    private let chatManager: KiloAIChatManager = .shared
    /// 选中的模型
    private var selectedModel: AIModel? = nil
    /// 当前可能未完成的对话
    private var currentMessage: AiChatMessage?
    /// 构建的图片附件
    private var currentImageAttachment: MessageAttachmentSchema?

    // uikit协调器
    public weak var coordinator: Coordinator?

    private var cancellables = Set<AnyCancellable>()

    init() {
        // 添加demo消息历史记录
        messages = demoMessages
        /// 监听用户登录状态
//        ZDUserLoginManager.manager.$loginUserInfo.receive(on: RunLoop.main).sink(receiveValue: { info in
//            Task { @MainActor in
//                self.chatManager.configure(with: .init(userOpenID: info?.userId))
//            }
//        }).store(in: &cancellables)

        /// 监听其他事件(从服务器拉取模型配置信息，从中台拉取模型信息）
        Publishers.Zip(chatManager.$modelConfig, chatManager.$models)
            .receive(on: RunLoop.main)
            .sink {[weak self] (rs1, rs2) in
                guard let `self` = self else { return }
                Task {
                    await self.switchModel(to: self.selectedType)
                }
            }
            .store(in: &cancellables)

    }
}

//MARK: -输入框行为-
extension AiChatViewModel {
    /// 是否可以发送信息
    func canSendMessage() -> Bool {
        if currentMessage != nil {
            return false
        } else {
            let isEmpty = contentInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            let hasImage = hasImage()
            return !isEmpty || hasImage
        }
    }

    private func hasImage() -> Bool {
        if let currentMessage {
            return currentMessage.attachments.contains(where: { $0.type == .image })
        }
        return false
    }

    /// 构建信息
    private func buildMessage() -> AiChatMessage {
        var message = AiChatMessage(id: UUID().uuidString, role: .user)
        // 添加输入
        message.content = contentInput
        // 添加附件
        if let currentImageAttachment {
            message.attachments = [currentImageAttachment]
        }
        return message
    }

    private func buildAlertMessage() -> AiChatMessage {
        var alertMessage = AiChatMessage(id: UUID().uuidString, role: .assistant)
        alertMessage.content = "当前没有可用的模型,请稍后重试!".localized()
        return alertMessage
    }

    /// 发送信息
    func sendMessage() async {
        guard canSendMessage() else { return }
        // 存储用户message信息
        let userMessage = buildMessage()
        await MainActor.run {
            messages.append(userMessage)
        }
        // 清理输入内容
        await cleanInputState()
        // 向AI中台发送信息
        guard let model = selectedModel,
              chatManager.models.isEmpty == false
        else {
            // 如果没有可用的模型,添加一条提示信息
            let alertMessage = buildAlertMessage()
            await MainActor.run {
                messages.append(alertMessage)
            }
            return
        }

        // 开启一个对话
        await chatManager.createNewConversationIfNeeded(model: model, conversationType: selectedType)
        // 发送消息
        _ = try? await chatManager.sendMessage2CurrentConversation(message: userMessage)
    }

    /// 选中图片并上传
    func seletedImageAndUplolad(_ image: UIImage) async {
        // 检查是否已经有图片
        if seletedImage != nil {
            let _ = await coordinator?.sendEvent(AiChatShowToastEvent(message: "只能添加一张图片".localized()))
            return
        }

        await MainActor.run {
            self.seletedImage = image
        }

        if let seletedImage, let data = seletedImage.jpegData(compressionQuality: 0.8) {
            let fileName = UUID().uuidString + ".jpg"
            let anAttachment = MessageAttachmentSchema(type: .image, data: data, fileName: fileName, mimeType: "image/jpeg")
            self.currentImageAttachment = anAttachment

            _ = try? await chatManager.uploadAttachments([anAttachment], progressHandler: {_,_ in })
        }
    }

    /// 切换对话模型
    @MainActor func switchModel(to type: ConversationType) {
        selectedType = type
        canUseExtraTool = type == .problemSolver
        selectedModel = chatManager.getModel(by: type)
        modelName = selectedModel?.name ?? ""
        if canUseExtraTool == false {
            clearSelectedImage()
            showExtraTools = false
        }
    }

    /// 清理图片选中态
    @MainActor func clearSelectedImage() {
        self.seletedImage = nil
        self.currentImageAttachment = nil
    }

    /// 清理输入状态
    @MainActor func cleanInputState() {
        contentInput = ""
        seletedImage = nil
        currentImageAttachment = nil
    }

    /// 发送内容到输入框
    func sendToInputView(image: UIImage?, content: String?) async {
        if let message = content, message.count > 0 {
            await MainActor.run {
                contentInput = message
            }
        }

        if let image {
            await seletedImageAndUplolad(image)
        }
    }
}

//MARK: -消息接收-
extension AiChatViewModel: ViewControllerActionHandler {
    func handleAction<A>(_ action: A) async throws -> A.Response where A : ViewControllerAction {
        /// 类型校验
        guard let aiAction = action as? (any AiChatAction) else {
            throw ActionHandlingError.invalidActionType
        }

        switch aiAction.actionType {
        case .sendMessage:
            if let sendMessageAction = aiAction as? AiChatSendMessageAction {
                await sendToInputView(image: sendMessageAction.image, content: sendMessageAction.message)
                if sendMessageAction.sendImmediately {
                    await sendMessage()
                }
            }
            return () as! A.Response
        }
    }

}

//MARK: -Demo数据-
extension AiChatViewModel {
    var demoMessages: [AiChatMessage] {

        // 第一组对话：傅里叶变换
        let message1 = AiChatMessage(id: UUID().uuidString, role: .user)
        message1.content = "请解释一下傅里叶变换的基本原理"

        let message2 = AiChatMessage(id: UUID().uuidString, role: .assistant)
        message2.content = """
傅里叶变换是将一个函数从时域转换到频域的重要数学工具。其基本思想是：任何周期函数都可以表示为不同频率的正弦和余弦函数的线性组合。

**连续傅里叶变换公式**:
$$\\[ F(\\omega) = \\int_{-\\infty}^{\\infty} f(t) e^{-i\\omega t} dt \\]$$

**离散傅里叶变换(DFT)公式**:
$$\\[ X_k = \\sum_{n=0}^{N-1} x_n \\cdot e^{-i 2\\pi k n / N} \\]$$

其中：
- $\\(f(t)\\)$ 是时域信号
- $\\(F(\\omega)\\)$ 是对应的频域表示
- $\\(i\\)$ 是虚数单位

傅里叶变换在信号处理、图像处理等领域有广泛应用。
"""

        // 第二组对话：拉普拉斯变换
        let message3 = AiChatMessage(id: UUID().uuidString, role: .user)
        message3.content = "能举例说明拉普拉斯变换的应用吗？"

        let message4 = AiChatMessage(id: UUID().uuidString, role: .assistant)
        message4.content = """
拉普拉斯变换在工程和物理学中有广泛应用，特别是在控制系统分析和微分方程求解中。

**拉普拉斯变换定义**:
$$\\[ F(s) = \\mathcal{L}\\{f(t)\\} = \\int_0^\\infty f(t)e^{-st}dt \\]$$

**典型应用举例**:
1. 电路分析：
   对于RLC电路，我们可以建立微分方程：
   $$\\[ L\\frac{di}{dt} + Ri + \\frac{1}{C}\\int i dt = v(t) \\]$$
   使用拉普拉斯变换可将其转换为代数方程求解。

2. 控制系统：
   传递函数是拉普拉斯变换的重要应用，例如：
   $$\\[ G(s) = \\frac{Y(s)}{U(s)} = \\frac{1}{s^2 + 2\\zeta\\omega_n s + \\omega_n^2} \\]$$
   其中 $\\(\\zeta\\)$ 是阻尼比，$\\(\\omega_n\\)$ 是自然频率。
"""

        // 第三组对话：矩阵特征值和特征向量
        let message5 = AiChatMessage(id: UUID().uuidString, role: .user)
        message5.content = "请解释矩阵的特征值和特征向量"

        let message6 = AiChatMessage(id: UUID().uuidString, role: .assistant)
        message6.content = """
**特征值和特征向量**是线性代数中的重要概念。对于一个方阵 $\\(A\\)$，如果存在非零向量 $\\(\\mathbf{v}\\)$ 和标量 $\\(\\lambda\\)$ 使得：
$$\\[ A\\mathbf{v} = \\lambda\\mathbf{v} \\]$$
则称 $\\(\\lambda\\)$ 为矩阵 $\\(A\\)$ 的特征值，$\\(\\mathbf{v}\\)$ 为对应的特征向量。

**计算方法**:
1. 求解特征方程：
   $$\\[ \\det(A - \\lambda I) = 0 \\]$$
2. 对每个特征值 $\\(\\lambda_i\\)$，解方程：
   $$\\[ (A - \\lambda_i I)\\mathbf{v} = 0 \\]$$
   得到对应的特征向量。

**几何意义**:
特征向量表示矩阵变换中方向不变的向量，特征值则表示该方向上的缩放因子。

**应用举例**:
- 主成分分析(PCA)
- 振动分析
- 量子力学中的本征态
"""

        // 第四组对话：微积分基本定理
        let message7 = AiChatMessage(id: UUID().uuidString, role: .user)
        message7.content = "请解释微积分基本定理"

        let message8 = AiChatMessage(id: UUID().uuidString, role: .assistant)
        message8.content = """
**微积分基本定理**连接了微分和积分这两个核心概念，是微积分学的基石。

**第一基本定理**（牛顿-莱布尼茨公式）:
如果函数 $\\(f(x)\\)$ 在区间 $\\([a,b]\\)$ 上连续，$\\(F(x)\\)$ 是 $\\(f(x)\\)$ 的一个原函数，则：
$$\\[ \\int_a^b f(x)dx = F(b) - F(a) \\]$$

**第二基本定理**:
如果函数 $\\(f(x)\\)$ 在区间 $\\([a,b]\\)$ 上连续，则函数：
$$\\[ F(x) = \\int_a^x f(t)dt \\]$$
在 $\\([a,b]\\)$ 上可导，且 $\\(F'(x) = f(x)\\)$。

**几何意义**:
- 定积分表示曲线下的面积
- 导数表示曲线的斜率
- 基本定理说明了面积函数的变化率等于被积函数的值

这个定理使我们能够通过反导数来计算定积分，极大地简化了积分计算。
"""

        // 第五组对话：泰勒级数
        let message9 = AiChatMessage(id: UUID().uuidString, role: .user)
        message9.content = "什么是泰勒级数？能给出一些常见函数的泰勒展开吗？"

        let message10 = AiChatMessage(id: UUID().uuidString, role: .assistant)
        message10.content = """
**泰勒级数**是用无穷级数来表示函数的方法，在数学分析和工程计算中极其重要。

**泰勒级数一般形式**:
$$\\[ f(x) = \\sum_{n=0}^{\\infty} \\frac{f^{(n)}(a)}{n!}(x-a)^n \\]$$

当 $\\(a=0\\)$ 时，称为**麦克劳林级数**：
$$\\[ f(x) = \\sum_{n=0}^{\\infty} \\frac{f^{(n)}(0)}{n!}x^n \\]$$

**常见函数的泰勒展开**:

1. 指数函数：
   $$\\[ e^x = 1 + x + \\frac{x^2}{2!} + \\frac{x^3}{3!} + \\cdots = \\sum_{n=0}^{\\infty} \\frac{x^n}{n!} \\]$$

2. 正弦函数：
   $$\\[ \\sin x = x - \\frac{x^3}{3!} + \\frac{x^5}{5!} - \\frac{x^7}{7!} + \\cdots = \\sum_{n=0}^{\\infty} \\frac{(-1)^n x^{2n+1}}{(2n+1)!} \\]$$

3. 余弦函数：
   $$\\[ \\cos x = 1 - \\frac{x^2}{2!} + \\frac{x^4}{4!} - \\frac{x^6}{6!} + \\cdots = \\sum_{n=0}^{\\infty} \\frac{(-1)^n x^{2n}}{(2n)!} \\]$$

4. 几何级数：
   $$\\[ \\frac{1}{1-x} = 1 + x + x^2 + x^3 + \\cdots = \\sum_{n=0}^{\\infty} x^n \\quad (|x| < 1) \\]$$

泰勒级数在数值计算、物理建模和工程近似中有广泛应用。
"""

        // 第六组对话：概率论中的贝叶斯定理
        let message11 = AiChatMessage(id: UUID().uuidString, role: .user)
        message11.content = "请解释贝叶斯定理及其在机器学习中的应用"

        let message12 = AiChatMessage(id: UUID().uuidString, role: .assistant)
        message12.content = """
**贝叶斯定理**是概率论中的重要定理，描述了在已知某些条件下，事件概率的计算方法。

**贝叶斯定理公式**:
$$\\[ P(A|B) = \\frac{P(B|A) \\cdot P(A)}{P(B)} \\]$$

其中：
- $\\(P(A|B)\\)$ 是后验概率：在事件B发生的条件下事件A发生的概率
- $\\(P(B|A)\\)$ 是似然度：在事件A发生的条件下事件B发生的概率
- $\\(P(A)\\)$ 是先验概率：事件A发生的概率
- $\\(P(B)\\)$ 是边际概率：事件B发生的概率

**连续形式**（贝叶斯推断）:
$$\\[ p(\\theta|D) = \\frac{p(D|\\theta) \\cdot p(\\theta)}{p(D)} \\]$$

**在机器学习中的应用**:

1. **朴素贝叶斯分类器**:
   $$\\[ P(类别|特征) = \\frac{P(特征|类别) \\cdot P(类别)}{P(特征)} \\]$$

2. **贝叶斯网络**: 用于建模变量间的概率依赖关系

3. **贝叶斯优化**: 用于超参数调优和黑盒函数优化

4. **变分推断**: 在深度学习中用于近似复杂后验分布
```python
def greet(name):
    print("hello, {name}!")
greet("Bob")
```
贝叶斯方法提供了一种原则性的方式来处理不确定性，在现代AI系统中发挥着重要作用。
```swift
func sum(x: Int, y: Int) -> Bool {
    return x + y
}
```
"""

        return [message9]
    }
}
