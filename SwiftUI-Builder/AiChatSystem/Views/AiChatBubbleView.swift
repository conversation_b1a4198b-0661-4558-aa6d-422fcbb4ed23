//
//  AiChatBubbleView.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/13.
//

import SwiftUI
import UIKit
import Foundation
import MarkdownUI

struct AiChatBubbleView: View {
    /// Message对象
    var message: AiChatMessage
    /// 上层服务，仅用于Api调用
    let viewModel: AiChatViewModel
    /// 布局方向
    @Environment(\.layoutDirection) var layoutDirection
    /// 由于图片解析较慢，此处用于单独解析
    @State var image: UIImage?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 3) {
            bubbleView
            
            // 复制按钮
            if message.role == .assistant && hasContent {
                Button {
                    let copyContent = message.content ?? ""
                    Task {
                        await viewModel.coordinator?.sendEvent(AiChatCopyMarkdownEvent(markdown: copyContent))
                    }
                } label: {
                    Image("ai_chat_copy")
                        .renderingMode(.template)
                        .foregroundStyle(Color.colorLabel)
                }
                .padding(.horizontal, 12)
            }
        }
    }
    
    private var hasContent: Bool {
        (message.content ?? "").count > 0
    }
    
    // 气泡视图
    var bubbleView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 深度思考
            if let thinking = message.reasoning,
               message.role == .assistant {
                VStack(alignment: .leading) {
                    HStack {
                        Image(.deepThinkingSelected)
                            .resizable()
                            .frame(width: 16, height: 16)
                        
                        Text("深度思考")
                            .font(.pingFang(14, weight: .medium))
                            .foregroundColor(Color.colorLabel)
                    }
                    
                    // 深度思考内容
                    Markdown(thinking)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(8)
                .background(Color.colorBackground)
                .cornerRadius(10)
                .padding(.horizontal, 8)
                .padding(.top, 8)
            }
            
            if let image {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: 200)
                    .onTapGesture {
                        Task {
                            await viewModel.coordinator?.sendEvent(AiChatShowImageEvent(image: image))
                        }
                    }
                    .padding(12)
            }
            
            // 回复内容
            if let content = message.content {
                Markdown(content)
                    .padding(12)
            }
        }
        .onAppear {
            Task {
                if let imageData = message.attachments.first(where: { $0.type == .image })?.data,
                   let image = UIImage(data: imageData) {
                    await MainActor.run {
                        self.image = image
                    }
                }
            }
        }
        .background(
            ChatBubbleShape(role: message.role, isRTL: layoutDirection == .rightToLeft)
                .fill(bubbleColor)
        )
        .overlay(
            ChatBubbleShape(role: message.role, isRTL: layoutDirection == .rightToLeft)
                .stroke(bubbleBorderColor, lineWidth: 0.3)
        )
        .frame(maxWidth: .infinity, alignment: message.role == .assistant ? .leading : .trailing)
    }
    
    private var bubbleColor: Color {
        switch message.role {
        case .user:
            return Color(light: Color(hex: "#E8F1FF"), dark: Color(hex: "#2A3549"))
        case .assistant:
            return Color.colorSecondaryForAi
        case .system:
            return Color.colorSecondaryForAi
        }
    }
    
    private var bubbleBorderColor: Color {
        switch message.role {
        case .user:
            return Color(light: Color(hex: "#B2C7E6"), dark: Color(hex: "#506485"))
        case .assistant:
            return Color.gray
        case .system:
            return Color.gray
        }
    }
}

// 自定义聊天气泡形状
struct ChatBubbleShape: Shape {
    let role: AiChatRole
    let isRTL: Bool
    
    func path(in rect: CGRect) -> Path {
        let radius: CGFloat = 16
        let path = UIBezierPath()
        
        // 确定哪个角应该是尖角
        let shouldPointAtLeft: Bool
        if role == .user {
            // 用户气泡：在LTR环境中右上角是尖角，在RTL环境中左上角是尖角
            shouldPointAtLeft = isRTL
        } else {
            // AI助手气泡：在LTR环境中左上角是尖角，在RTL环境中右上角是尖角
            shouldPointAtLeft = !isRTL
        }
        
        if shouldPointAtLeft {
            // 左上角是尖角，其他三个是圆角
            drawBubbleWithLeftPointingCorner(path: path, rect: rect, radius: radius)
        } else {
            // 右上角是尖角，其他三个是圆角
            drawBubbleWithRightPointingCorner(path: path, rect: rect, radius: radius)
        }
        
        return Path(path.cgPath)
    }
    
    // 绘制左上角为尖角的气泡
    private func drawBubbleWithLeftPointingCorner(path: UIBezierPath, rect: CGRect, radius: CGFloat) {
        path.move(to: CGPoint(x: rect.minX, y: rect.minY)) // 左上角是尖角
        path.addLine(to: CGPoint(x: rect.maxX - radius, y: rect.minY))
        path.addArc(withCenter: CGPoint(x: rect.maxX - radius, y: rect.minY + radius),
                    radius: radius, startAngle: .pi * 3/2, endAngle: 0, clockwise: true)
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - radius))
        path.addArc(withCenter: CGPoint(x: rect.maxX - radius, y: rect.maxY - radius),
                    radius: radius, startAngle: 0, endAngle: .pi/2, clockwise: true)
        path.addLine(to: CGPoint(x: rect.minX + radius, y: rect.maxY))
        path.addArc(withCenter: CGPoint(x: rect.minX + radius, y: rect.maxY - radius),
                    radius: radius, startAngle: .pi/2, endAngle: .pi, clockwise: true)
        path.addLine(to: CGPoint(x: rect.minX, y: rect.minY))
        path.close()
    }
    
    // 绘制右上角为尖角的气泡
    private func drawBubbleWithRightPointingCorner(path: UIBezierPath, rect: CGRect, radius: CGFloat) {
        path.move(to: CGPoint(x: rect.minX, y: rect.minY + radius))
        path.addArc(withCenter: CGPoint(x: rect.minX + radius, y: rect.minY + radius),
                    radius: radius, startAngle: .pi, endAngle: .pi * 3/2, clockwise: true)
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.minY)) // 右上角是尖角
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - radius))
        path.addArc(withCenter: CGPoint(x: rect.maxX - radius, y: rect.maxY - radius),
                    radius: radius, startAngle: 0, endAngle: .pi/2, clockwise: true)
        path.addLine(to: CGPoint(x: rect.minX + radius, y: rect.maxY))
        path.addArc(withCenter: CGPoint(x: rect.minX + radius, y: rect.maxY - radius),
                    radius: radius, startAngle: .pi/2, endAngle: .pi, clockwise: true)
        path.close()
    }
}
