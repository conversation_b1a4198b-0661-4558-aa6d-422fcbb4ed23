//
//  AiChatMainView.swift
//  HandNote
//
//  Created by 杨冰冰 on 2025/5/13.
//  Copyright © 2025 top-stack. All rights reserved.
//

import SwiftUI
import UIKit
import Foundation

struct AiChatMainView: View {
    
    @StateObject private var viewModel: AiChatViewModel
    
    init(viewModel: AiChatViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 导航栏
            navBar

            // 聊天内容区域
            chatContent

            // 底部输入区域
            AiChatInputView(viewModel: viewModel)
        }
    }

    // 导航栏
    private var navBar: some View {
        VStack(spacing: 0) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("阅读助手")
                        .font(.system(size: 18, weight: .medium))

                    Text("\(viewModel.modelName)")
                        .font(.system(size: 12))
                        .foregroundStyle(Color.colorLabel)
                }

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)

            Divider()
        }
    }

    // 聊天内容区域
    private var chatContent: some View {
        ScrollViewReader { scrollProxy in
            ScrollView {
                LazyVStack(spacing: 20) {
                    ForEach(viewModel.messages) { message in
                        AiChatBubbleView(message: message, viewModel: viewModel)
                            .padding(.horizontal, 16)
                            .id(message.id) // 为每个消息设置ID，用于滚动定位
                    }

                    if viewModel.isLoading {
                        HStack {
                            Spacer()
                            ProgressView()
                                .padding()
                            Spacer()
                        }
                        .id("loadingIndicator") // 为加载指示器设置ID
                    }

                    // 底部锚点视图，用于滚动到底部
                    Color.clear
                        .frame(height: 1)
                        .id("bottomAnchor")
                }
                .padding(.vertical, 8)
            }
            .background(Color(UIColor.systemBackground))
            .onChange(of: viewModel.messages.count) { _ in
                // 当消息数量变化时，滚动到底部
                withAnimation(.easeOut(duration: 0.3)) {
                    scrollProxy.scrollTo("bottomAnchor", anchor: .bottom)
                }
            }
            .onChange(of: viewModel.shouldScrollToBottom) { shouldScroll in
                // 当需要滚动到底部时
                if shouldScroll {
                    withAnimation(.easeOut(duration: 0.3)) {
                        scrollProxy.scrollTo("bottomAnchor", anchor: .bottom)
                    }
                    // 重置标志
                    DispatchQueue.main.async {
                        viewModel.shouldScrollToBottom = false
                    }
                }
            }
            .onAppear {
                // 视图出现时滚动到底部
                DispatchQueue.main.async {
                    scrollProxy.scrollTo("bottomAnchor", anchor: .bottom)
                }
            }
        }
    }

}

#Preview {
    AiChatMainView(viewModel: AiChatViewModel())
}
