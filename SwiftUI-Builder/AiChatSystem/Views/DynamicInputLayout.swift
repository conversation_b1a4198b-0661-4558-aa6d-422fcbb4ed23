//
//  DynamicInputLayout.swift
//  SwiftUI-Builder
//
//  Created by yangbing<PERSON> on 2025/5/20.
//

import SwiftUI

@available(iOS 16.0, *)
struct DynamicInputLayout: Layout {
    private let horizontalSpacing: CGFloat = 0
    private let verticalSpacing: CGFloat = 0
    // 这个比例用于判断 TextField 的高度是否“类似单行”
    // 相对于 ToolBox 的高度。如果需要，请进行调整。
    private let singleLineHeightThresholdRatio: CGFloat = 1.5
    
    struct CacheData {
        var shouldLayoutVertically: Bool = false
        var textFieldSize: CGSize = .zero
        var toolBoxSize: CGSize = .zero
    }
    
    func makeCache(subviews: Subviews) -> CacheData {
        return CacheData()
    }
    
    func updateCache(_ cache: inout CacheData, subviews: Subviews) {
        // 如果子视图的标识发生变化，则会调用此方法，适合在此处更新尺寸
        // 但 sizeThatFits 会处理它。
    }
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout CacheData) -> CGSize {
        guard subviews.count == 2 else {
            cache.shouldLayoutVertically = false
            return .zero
        }
        let textField = subviews[0]
        let toolBox = subviews[1]

        let toolBoxIdealSize = toolBox.sizeThatFits(.unspecified)
        cache.toolBoxSize = toolBoxIdealSize

        // 模拟如果 textField 和 toolBox 水平并排（非重叠）时 textField 的情况
        let textFieldAvailableWidthIfSideBySide = proposal.width.map { max(0, $0 - toolBoxIdealSize.width - horizontalSpacing) } ?? .infinity
        let textFieldSizeIfSideBySide = textField.sizeThatFits(.init(width: textFieldAvailableWidthIfSideBySide, height: nil))

        // 决策：如果 textField 在假设的并排布局中会变得太高（相对于 toolBox），则切换到垂直布局
        // "太高" 的定义是 textField 的高度超过 toolBox 高度的某个比例
        let textFieldWouldWrapSignificantly = textFieldSizeIfSideBySide.height > (toolBoxIdealSize.height * singleLineHeightThresholdRatio)
        
        if textFieldWouldWrapSignificantly && proposal.width != nil { // 仅当有宽度约束时，换行才有意义触发垂直布局
            cache.shouldLayoutVertically = true
            // 垂直布局
            let textFieldSizeForVertical = textField.sizeThatFits(.init(width: proposal.width, height: nil))
            cache.textFieldSize = textFieldSizeForVertical
            
            let width = proposal.width ?? max(textFieldSizeForVertical.width, toolBoxIdealSize.width)
            let height = textFieldSizeForVertical.height + verticalSpacing + toolBoxIdealSize.height
            return CGSize(width: width, height: height)
        } else {
            cache.shouldLayoutVertically = false
            // 水平重叠布局: TextField 占据全部建议宽度
            let textFieldSizeForHorizontalOverlap = textField.sizeThatFits(.init(width: proposal.width, height: nil))
            cache.textFieldSize = textFieldSizeForHorizontalOverlap
            
            let width = proposal.width ?? textFieldSizeForHorizontalOverlap.width
            let height = max(textFieldSizeForHorizontalOverlap.height, toolBoxIdealSize.height)
            return CGSize(width: width, height: height)
        }
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout CacheData) {
        guard subviews.count == 2 else { return }
        let textField = subviews[0]
        let toolBox = subviews[1]
        
        let textFieldSize = cache.textFieldSize
        let toolBoxSize = cache.toolBoxSize
        
        if cache.shouldLayoutVertically {
            // 垂直布局
            textField.place(at: CGPoint(x: bounds.minX, y: bounds.minY),
                            anchor: .topLeading,
                            proposal: ProposedViewSize(width: bounds.width, height: textFieldSize.height))
            
            toolBox.place(at: CGPoint(x: bounds.maxX - toolBoxSize.width, y: bounds.minY + textFieldSize.height + verticalSpacing),
                          anchor: .topLeading,
                          proposal: ProposedViewSize(toolBoxSize))
        } else {
            // 水平重叠布局
            // textField 占据全部宽度，垂直居中
            // toolBox 在 textField 右侧重叠，垂直居中
            
            let yPosTextField = bounds.minY + (bounds.height - textFieldSize.height) / 2
            textField.place(at: CGPoint(x: bounds.minX, y: yPosTextField),
                            anchor: .topLeading,
                            proposal: ProposedViewSize(width: bounds.width, height: textFieldSize.height))
            
            let yPosToolBox = bounds.minY + (bounds.height - toolBoxSize.height) / 2
            toolBox.place(at: CGPoint(x: bounds.maxX - toolBoxSize.width, y: yPosToolBox),
                          anchor: .topLeading, // Anchor to topLeading of its own frame
                          proposal: ProposedViewSize(toolBoxSize))
        }
    }
}
