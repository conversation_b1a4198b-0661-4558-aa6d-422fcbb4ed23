//
//  AiChatInputView.swift
//  HandNote
//
//  Created by 杨冰冰 on 2025/5/28.
//  Copyright © 2025 top-stack. All rights reserved.
//

import SwiftUI
import UniformTypeIdentifiers

struct AiChatInputView: View {
    
    @ObservedObject var viewModel: AiChatViewModel
    
    // 支持的拖拽类型
    private var supportedDropTypes: [UTType] {
        [.text, .plainText, .image, .png, .jpeg]
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 模式选择按钮
            HStack(spacing: 8) {
                Button(action: {
                    if viewModel.selectedType == .deepThink {
                        viewModel.switchModel(to: .general)
                    } else {
                        viewModel.switchModel(to: .deepThink)
                    }
                }) {
                    HStack {
                        Image(viewModel.selectedType == .deepThink ? "deep_thinking_selected" : "deep_thinking_normal")
                            .resizable()
                            .frame(width: 16, height: 16)
                        Text("深度思考")
                            .font(.pingFang(12))
                            .foregroundColor(viewModel.selectedType == .deepThink ? Color(hex: "#2E82FF") : Color.gray)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .stroke(viewModel.selectedType == .deepThink ? Color.color2E82FF : Color(hex: "#B8B7B8"))
                    )
                }
                
                Button(action: {
                    if viewModel.selectedType == .problemSolver {
                        viewModel.switchModel(to: .general)
                    } else {
                        viewModel.switchModel(to: .problemSolver)
                    }
                }) {
                    HStack {
                        Image(viewModel.selectedType == .problemSolver ? "ai_expert_selected" : "ai_expert_normal")
                            .resizable()
                            .frame(width: 16, height: 16)
                        Text("解题专家")
                            .font(.pingFang(12))
                            .foregroundColor(viewModel.selectedType == .problemSolver ? Color(hex: "#2E82FF") : Color.gray)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .stroke(viewModel.selectedType == .problemSolver ? Color.color2E82FF : Color(hex: "#B8B7B8"))
                    )
                }
                
                Spacer()
            }
            
            // 输入框和按钮
            inputView
                .onDrop(of: supportedDropTypes, isTargeted: nil) { providers in
                    handleDropProviders(providers)
                }
            
            // 工具页面
            if viewModel.showExtraTools && viewModel.canUseExtraTool {
                extralToolsView
                    .padding(.horizontal, 12)
                    .padding(.top, 4)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 12)
    }
    
    
    // 输入框
    private var inputView: some View {
        VStack {
            if let image = viewModel.seletedImage {
                HStack {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 36, height: 36)
                        .cornerRadius(6)
                        .overlay(
                            RoundedRectangle(cornerRadius: 6, style: .continuous)
                                .stroke(Color(hex: "#B8B7B8"), lineWidth: 0.5)
                        )
                        .overlay(
                            Button(action: {
                                viewModel.clearSelectedImage()
                            }) {
                                Image("ai_image_browser_close")
                                    .renderingMode(.template)
                                    .resizable()
                                    .frame(width: 16, height: 16)
                                    .foregroundStyle(Color.colorLabel)
                            }
                                .offset(x: 8, y: -8),
                            alignment: .topTrailing
                        )
                        .onTapGesture {
                            Task {
                                await viewModel.coordinator?.sendEvent(AiChatShowImageEvent(image: image))
                            }
                        }
                        .padding(8)
                    
                    Spacer()
                }
                .padding(.horizontal, 8)
                
                Divider()
            }
            
            Group {
                if #available(iOS 16.0, *) {
                    DynamicInputLayout {
                        textField
                        toolBox
                    }
                } else {
                    VStack(alignment: .trailing, spacing: 0) {
                        textField
                        toolBox
                    }
                }
            }
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 14, style: .continuous)
                .fill(Color.colorSecondaryForAi)
        )
        .overlay {
            RoundedRectangle(cornerRadius: 14, style: .continuous)
                .stroke(Color(hex: "#C2C2C2"), lineWidth: 0.5)
        }
    }
    
    private var textField: some View {
        Group {
            if #available(iOS 16.0, *) {
                TextField("请输入...", text: $viewModel.contentInput, axis: .vertical)
                    .lineLimit(3)
                
            } else {
                TextField("请输入...", text: $viewModel.contentInput)
            }
        }
        .font(.pingFang(12))
        .foregroundStyle(Color.colorLabel)
        .onSubmit {
            Task {
                await viewModel.sendMessage()
            }
        }
        .textInputAutocapitalization(.never)
        .disableAutocorrection(true)
        .padding(8)
    }
    
    
    private var toolBox: some View {
        HStack {
            if viewModel.canUseExtraTool {
                // 添加图片按钮
                Button(action: {
                    viewModel.showExtraTools.toggle()
                }) {
                    Image(viewModel.showExtraTools ? "ai_add_image_selected" :"ai_add_image_normal")
                        .renderingMode(.template)
                        .resizable()
                        .frame(width: 28, height: 28)
                        .foregroundStyle(Color.colorLabel)
                }
            }
            
            // 发送按钮
            Button(action: {
                Task {
                    await viewModel.sendMessage()
                }
            }) {
                Image(!viewModel.canSendMessage() ? "ai_send_disable" : "ai_send_enable")
                    .resizable()
                    .frame(width: 28, height: 28)
            }
            .disabled(!viewModel.canSendMessage())
        }
    }
    
    private var extralToolsView: some View {
        HStack {
            Button {
                Task {
                    await viewModel.coordinator?.sendEvent(AiChatOpenImagePickerEvent())
                }
            } label: {
                VStack(spacing: 6) {
                    Image(.aiImageIcon)
                        .renderingMode(.template)
                        .resizable()
                        .frame(width: 24, height: 24)
                        .foregroundStyle(Color.colorLabel)
                    Text("图片")
                        .font(.pingFang(11))
                        .foregroundStyle(Color.colorLabel)
                }
                .padding(EdgeInsets(top: 10, leading: 18, bottom: 8, trailing: 18))
                .background(Color.colorSecondaryForAi)
                .cornerRadius(6)
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(Color(hex: "#D3D3D3"), lineWidth: 0.5)
                )
            }
            
            Button {
                Task {
                    await viewModel.coordinator?.sendEvent(AiChatOpenCameraEvent())
                }
            } label: {
                VStack(spacing: 6) {
                    Image(.aiCameraIcon)
                        .renderingMode(.template)
                        .resizable()
                        .frame(width: 24, height: 24)
                        .foregroundStyle(Color.colorLabel)
                    Text("相机")
                        .font(.pingFang(11))
                        .foregroundStyle(Color.colorLabel)
                }
                .padding(EdgeInsets(top: 10, leading: 18, bottom: 8, trailing: 18))
                .background(Color.colorSecondaryForAi)
                .cornerRadius(6)
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(Color(hex: "#D3D3D3"), lineWidth: 0.5)
                )
            }
        }
    }
}

extension AiChatInputView {
    // 处理拖拽内容
    private func handleDropProviders(_ providers: [NSItemProvider]) -> Bool {
        // 处理图片拖拽
        if let imageProvider = providers.first(where: { $0.canLoadObject(ofClass: UIImage.self) }) {
            imageProvider.loadObject(ofClass: UIImage.self) { image, error in
                if let droppedImage = image as? UIImage {
                    Task {
                        await viewModel.seletedImageAndUplolad(droppedImage)
                    }
                }
            }
            return true
        }
        
        return false
    }
}
