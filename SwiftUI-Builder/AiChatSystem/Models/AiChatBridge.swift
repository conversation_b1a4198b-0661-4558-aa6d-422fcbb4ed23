//
//  AiChatBridge.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//

import Foundation
import UIKit


//MARK: -Actions(AI聊天业务中UIKit向SwiftUI聊天页面发送的事件)-
public enum AiChatActionType: String {
    // 发送信息
    case sendMessage
}

public protocol AiChatAction: ViewControllerAction {
    var actionType: AiChatActionType { get }
}

public extension AiChatAction {
    var id: String {
        actionType.rawValue
    }
}

/// 发送图片和文字，让输入框产生消息
public struct AiChatSendMessageAction: AiChatAction {
    ///不需要返回值
    public typealias Response = Void
    /// 类型
    public var actionType: AiChatActionType = .sendMessage
    /// 消息
    public let message: String?
    /// 图片
    public let image: UIImage?
    /// 是否立即发送
    public let sendImmediately: Bool
}

//MARK: -Events(AI聊天业务中Swift向UIUIKit聊天页面发送的事件)-
public enum AiChatEventType: String {
    // 展示图片
    case showImage
    // markdown 拷贝事件
    case copyMarkdown
    // 打开相机
    case openCamera
    // 打开图片选择器
    case openImagePicker
    // 显示toast提示
    case showToast
}

public protocol AiChatEvent: ViewModelEvent {
    var eventType: AiChatEventType { get }
}

public extension AiChatEvent {
    var id: String {
        eventType.rawValue
    }
}

/// 聊天页面要求UIKit打开图片放大
public struct AiChatShowImageEvent: AiChatEvent {
    public typealias Response = Void
    public var eventType: AiChatEventType = .showImage
    public let image: UIImage
}

/// 聊天页面要求UIKit打开相机
public struct AiChatOpenCameraEvent: AiChatEvent {
    public typealias Response = Void
    public var eventType: AiChatEventType = .openCamera
}

/// 聊天页面要求UIKit打开相机
public struct AiChatOpenImagePickerEvent: AiChatEvent {
    public typealias Response = Void
    public var eventType: AiChatEventType = .openImagePicker
}

/// 聊天页面拷贝了Markdown
public struct AiChatCopyMarkdownEvent: AiChatEvent {
    public typealias Response = Void
    public var eventType: AiChatEventType = .copyMarkdown
    public let markdown: String
}

/// 聊天页面要求UIKit显示toast
public struct AiChatShowToastEvent: AiChatEvent {
    public typealias Response = Void
    public var eventType: AiChatEventType = .showToast
    public let message: String
}
