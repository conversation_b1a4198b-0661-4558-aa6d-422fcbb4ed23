# UIKit-SwiftUI 通信协议

这个模块实现了 UIKit 中的 UIViewController 和 SwiftUI 视图之间的双向通信协议。通过协调器模式，实现了类型安全、异步支持的通信机制。

## 主要特性

- 类型安全的 Action 和 Event 传递
- 基于枚举的 Action 和 Event 类型定义，避免硬编码字符串
- 支持异步操作（使用 async/await）
- 松耦合设计，便于测试和维护
- 完善的错误处理机制
- 超时处理支持

## 目录结构

```
SwiftUIKitBridge/
├── Sources/
│   └── BridgeProtocols.swift       # 基础协议定义
│   └── BridgeCoordinator.swift     # 协调器实现
├── Example/
│   ├── BridgeActions.swift         # Action 定义
│   └── BridgeEvents.swift          # Event 定义
│   └── BridgeExampleViewModel.swift # 示例 ViewModel
│   └── BridgeExampleView.swift     # 示例 SwiftUI 视图
    └── BridgeExampleViewController.swift # 示例 UIViewController
```

## 使用方法

### 1. 创建自定义 Action

```swift
// 首先在 ActionType 枚举中添加新的类型
extension ActionType {
    static let myCustomAction = ActionType(rawValue: "myCustomAction")
}

// 然后创建基于枚举的 Action
struct MyCustomAction: EnumBasedAction {
    typealias Response = [String: Any]

    let actionType: ActionType = .myCustomAction
    let parameters: [String: Any]

    init(parameters: [String: Any]) {
        self.parameters = parameters
    }
}
```

### 2. 创建自定义 Event

```swift
// 首先在 EventType 枚举中添加新的类型
extension EventType {
    static let myCustomEvent = EventType(rawValue: "myCustomEvent")
}

// 然后创建基于枚举的 Event
struct MyCustomEvent: EnumBasedEvent {
    typealias Response = Bool

    let eventType: EventType = .myCustomEvent
    let data: [String: Any]

    init(data: [String: Any]) {
        self.data = data
    }
}
```

### 3. 在 UIViewController 中实现

```swift
class MyViewController: UIViewController, ViewModelEventHandler {
    private var coordinator: Coordinator?
    private var hostingController: UIHostingController<AnyView>?

    override func viewDidLoad() {
        super.viewDidLoad()

        // 创建 ViewModel
        let viewModel = MyViewModel()

        // 创建协调器
        coordinator = SwiftUICoordinator(viewController: self, viewModel: viewModel)

        // 将协调器传递给 ViewModel
        viewModel.coordinator = coordinator

        // 创建 SwiftUI 视图
        let contentView = MySwiftUIView(viewModel: viewModel)

        // 创建 UIHostingController
        hostingController = UIHostingController(rootView: AnyView(contentView))

        // 添加 hostingController 作为子控制器
        if let hostingController = hostingController {
            addChild(hostingController)
            view.addSubview(hostingController.view)
            hostingController.view.frame = view.bounds
            hostingController.view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
            hostingController.didMove(toParent: self)
        }
    }

    // 发送 Action 到 ViewModel
    func sendActionToViewModel() {
        Task {
            let action = MyCustomAction(parameters: ["key": "value"])
            let result = await coordinator?.sendAction(action)

            switch result {
            case .success(let response):
                print("Action 处理成功: \(response)")
            case .failure(let error):
                print("Action 处理失败: \(error)")
            case .none:
                print("协调器不可用")
            }
        }
    }

    // 实现 ViewModelEventHandler 协议
    func handleEvent<E: ViewModelEvent>(_ event: E) async throws -> E.Response {
        // 如果是基于枚举的 Event，使用 eventType 进行判断
        if let enumEvent = event as? EnumBasedEvent {
            switch enumEvent.eventType {
            case .myCustomEvent:
                if let event = event as? MyCustomEvent {
                    // 处理自定义事件
                    print("处理自定义事件: \(event.data)")
                    return true as! E.Response
                }
            default:
                throw EventHandlingError.unknownEventType
            }
        } else {
            // 兼容旧版 Event，使用 id 进行判断
            switch event.id {
            case "myCustomEvent":
                if let event = event as? MyCustomEvent {
                    // 处理自定义事件
                    print("处理自定义事件: \(event.data)")
                    return true as! E.Response
                }
            default:
                throw EventHandlingError.unknownEventType
            }
        }

        throw EventHandlingError.invalidEventType
    }
}
```

### 4. 在 ViewModel 中实现

```swift
class MyViewModel: ObservableObject, ViewControllerActionHandler {
    @Published var data: [String: Any] = [:]

    // 持有协调器的弱引用
    weak var coordinator: Coordinator?

    // 发送 Event 到 UIViewController
    func sendEventToViewController() {
        Task {
            let event = MyCustomEvent(data: ["key": "value"])
            let result = await coordinator?.sendEvent(event)

            switch result {
            case .success(let success):
                print("Event 处理成功: \(success)")
            case .failure(let error):
                print("Event 处理失败: \(error)")
            case .none:
                print("协调器不可用")
            }
        }
    }

    // 实现 ViewControllerActionHandler 协议
    func handleAction<A: ViewControllerAction>(_ action: A) async throws -> A.Response {
        // 如果是基于枚举的 Action，使用 actionType 进行判断
        if let enumAction = action as? EnumBasedAction {
            switch enumAction.actionType {
            case .myCustomAction:
                if let action = action as? MyCustomAction {
                    // 处理自定义 Action
                    print("处理基于枚举的自定义 Action: \(action.parameters)")
                    return ["result": "success"] as! A.Response
                }
            default:
                throw ActionHandlingError.unknownActionType
            }
        } else {
            // 兼容旧版 Action，使用 id 进行判断
            switch action.id {
            case "myCustomAction":
                if let action = action as? MyCustomAction {
                    // 处理自定义 Action
                    print("处理自定义 Action: \(action.parameters)")
                    return ["result": "success"] as! A.Response
                }
            default:
                throw ActionHandlingError.unknownActionType
            }
        }

        throw ActionHandlingError.invalidActionType
    }
}
```

### 5. 在 SwiftUI 视图中使用

```swift
struct MySwiftUIView: View {
    @ObservedObject var viewModel: MyViewModel

    var body: some View {
        VStack {
            Text("SwiftUI 视图")
                .font(.title)

            Button("发送事件到 UIViewController") {
                viewModel.sendEventToViewController()
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .padding()
    }
}
```

## 注意事项

1. 确保在 UIViewController 和 ViewModel 之间正确传递协调器
2. 使用弱引用避免循环引用：
   - 协调器对 ViewModel 使用弱引用，因为 ViewModel 通常被 SwiftUI 视图持有
   - ViewModel 对协调器使用弱引用，避免循环引用
   - 协调器对 UIViewController 使用弱引用，因为 UIViewController 通常持有协调器
3. 处理异步操作时考虑超时情况
4. 为每个 Action 和 Event 定义明确的响应类型
5. 在处理 Action 和 Event 时添加适当的错误处理
6. 由于使用了弱引用，在使用对象前始终检查它们是否为 nil
