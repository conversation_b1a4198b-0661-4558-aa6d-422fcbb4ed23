//
//  BridgeCoordinator.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//  Copyright © 2025 top-stack. All rights reserved.

import Foundation

// MARK: - 协调器协议

/// UIKit 和 SwiftUI 之间的通信协调器协议
public protocol Coordinator: AnyObject {
    /// 发送 Action 到 ViewModel
    /// - Parameter action: 要发送的 Action
    /// - Returns: Action 的处理结果
    func sendAction<A: ViewControllerAction>(_ action: A) async -> Result<A.Response, Error>

    /// 发送 Event 到 UIViewController
    /// - Parameter event: 要发送的 Event
    /// - Returns: Event 的处理结果
    func sendEvent<E: ViewModelEvent>(_ event: E) async -> Result<E.Response, Error>

    /// 带超时的 Action 发送
    /// - Parameters:
    ///   - action: 要发送的 Action
    ///   - timeout: 超时时间（秒）
    /// - Returns: Action 的处理结果
    func sendActionWithTimeout<A: ViewControllerAction>(
        _ action: A,
        timeout: TimeInterval
    ) async -> Result<A.Response, Error>

    /// 带超时的 Event 发送
    /// - Parameters:
    ///   - event: 要发送的 Event
    ///   - timeout: 超时时间（秒）
    /// - Returns: Event 的处理结果
    func sendEventWithTimeout<E: ViewModelEvent>(
        _ event: E,
        timeout: TimeInterval
    ) async -> Result<E.Response, Error>
}

// MARK: - 协调器默认实现

public extension Coordinator {
    /// 带超时的 Action 发送默认实现
    func sendActionWithTimeout<A: ViewControllerAction>(
        _ action: A,
        timeout: TimeInterval = 5.0
    ) async -> Result<A.Response, Error> {
        return await withTaskGroup(of: Result<A.Response, Error>.self) { group in
            // 添加实际操作任务
            group.addTask {
                return await self.sendAction(action)
            }

            // 添加超时任务
            group.addTask {
                try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                return .failure(CoordinatorError.timeout)
            }

            // 返回先完成的任务结果
            if let result = await group.next() {
                group.cancelAll()
                return result
            }

            return .failure(CoordinatorError.unknown)
        }
    }

    /// 带超时的 Event 发送默认实现
    func sendEventWithTimeout<E: ViewModelEvent>(
        _ event: E,
        timeout: TimeInterval = 5.0
    ) async -> Result<E.Response, Error> {
        return await withTaskGroup(of: Result<E.Response, Error>.self) { group in
            // 添加实际操作任务
            group.addTask {
                return await self.sendEvent(event)
            }

            // 添加超时任务
            group.addTask {
                try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                return .failure(CoordinatorError.timeout)
            }

            // 返回先完成的任务结果
            if let result = await group.next() {
                group.cancelAll()
                return result
            }

            return .failure(CoordinatorError.unknown)
        }
    }
}

// MARK: - 具体协调器实现

/// 应用协调器实现
public class SwiftUICoordinator: Coordinator {
    /// 持有 UIViewController 的弱引用
    private weak var viewController: (any ViewModelEventHandler)?

    /// 持有 ViewModel 的弱引用，避免循环引用
    /// 因为 ViewModel 通常被 SwiftUI 视图持有，而 SwiftUI 视图被 UIHostingController 持有，
    /// UIHostingController 又被 UIViewController 持有，UIViewController 持有协调器
    private weak var viewModel: (any ViewControllerActionHandler)?

    /// 初始化协调器
    /// - Parameters:
    ///   - viewController: 实现了 ViewModelEventHandler 的 UIViewController
    ///   - viewModel: 实现了 ViewControllerActionHandler 的 ViewModel
    public init(viewController: (any ViewModelEventHandler)?, viewModel: (any ViewControllerActionHandler)?) {
        self.viewController = viewController
        self.viewModel = viewModel
    }

    /// 发送 Action 到 ViewModel
    public func sendAction<A: ViewControllerAction>(_ action: A) async -> Result<A.Response, Error> {
        // 由于 viewModel 是弱引用，需要先检查它是否仍然存在
        guard let viewModel = viewModel else {
            return .failure(CoordinatorError.viewModelNotAvailable)
        }

        do {
            let response = try await viewModel.handleAction(action)
            return .success(response)
        } catch {
            return .failure(error)
        }
    }

    /// 发送 Event 到 UIViewController
    public func sendEvent<E: ViewModelEvent>(_ event: E) async -> Result<E.Response, Error> {
        guard let viewController = viewController else {
            return .failure(CoordinatorError.viewControllerNotAvailable)
        }

        do {
            let response = try await viewController.handleEvent(event)
            return .success(response)
        } catch {
            return .failure(error)
        }
    }
}
