//
//  BridgeProtocols.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//  Copyright © 2025 top-stack. All rights reserved.

import Foundation

// MARK: - 基础 Action 和 Event 协议

/// UIViewController 发送给 SwiftUI ViewModel 的 Action 协议
public protocol ViewControllerAction {
    /// Action 的响应类型
    associatedtype Response

    /// Action 的唯一标识符
    var id: String { get }
}

/// SwiftUI ViewModel 发送给 UIViewController 的 Event 协议
public protocol ViewModelEvent {
    /// Event 的响应类型
    associatedtype Response

    /// Event 的唯一标识符
    var id: String { get }
}

// MARK: - 类型擦除包装器

/// 类型擦除的 ViewControllerAction 包装器
public struct AnyViewControllerAction {
    private let action: Any
    public let id: String

    public init<A: ViewControllerAction>(_ action: A) {
        self.action = action
        self.id = action.id
    }

    public func unwrap<A: ViewControllerAction>() -> A? {
        return action as? A
    }
}

/// 类型擦除的 ViewModelEvent 包装器
public struct AnyViewModelEvent {
    private let event: Any
    public let id: String

    public init<E: ViewModelEvent>(_ event: E) {
        self.event = event
        self.id = event.id
    }

    public func unwrap<E: ViewModelEvent>() -> E? {
        return event as? E
    }
}

// MARK: - 处理器协议

/// ViewModel 处理 Action 的协议
/// 使用 AnyObject 约束确保该协议只能被类实现，以支持弱引用
public protocol ViewControllerActionHandler: AnyObject {
    /// 处理来自 UIViewController 的 Action
    /// - Parameter action: 要处理的 Action
    /// - Returns: Action 的响应结果
    func handleAction<A: ViewControllerAction>(_ action: A) async throws -> A.Response
}

/// UIViewController 处理 Event 的协议
public protocol ViewModelEventHandler: AnyObject {
    /// 处理来自 ViewModel 的 Event
    /// - Parameter event: 要处理的 Event
    /// - Returns: Event 的响应结果
    func handleEvent<E: ViewModelEvent>(_ event: E) async throws -> E.Response
}

// MARK: - 错误类型

/// 协调器错误类型
public enum CoordinatorError: Error {
    /// 超时错误
    case timeout
    /// 未知错误
    case unknown
    /// ViewModel 不可用
    case viewModelNotAvailable
    /// UIViewController 不可用
    case viewControllerNotAvailable
}

/// Action 处理错误
public enum ActionHandlingError: Error {
    /// 未知的 Action 类型
    case unknownActionType
    /// 无效的 Action 类型
    case invalidActionType
}

/// Event 处理错误
public enum EventHandlingError: Error {
    /// 未知的 Event 类型
    case unknownEventType
    /// 无效的 Event 类型
    case invalidEventType
}
