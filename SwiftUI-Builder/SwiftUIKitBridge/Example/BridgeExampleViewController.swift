//
//  BridgeExampleViewController.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//  Copyright © 2025 top-stack. All rights reserved.

import UIKit
import SwiftUI

/// 示例 UIViewController 实现
public class BridgeExampleViewController: UIViewController, ViewModelEventHandler {
    // MARK: - 属性

    /// 持有协调器的引用
    private var coordinator: Coordinator?

    /// SwiftUI 视图的容器
    private var hostingController: UIHostingController<AnyView>?

    /// 状态标签
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = .darkGray
        label.numberOfLines = 0
        label.text = "等待事件..."
        return label
    }()

    /// 操作按钮
    private lazy var actionButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("发送 Action 到 SwiftUI", for: .normal)
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 10
        button.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - 生命周期

    public override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupSwiftUIView()
    }

    // MARK: - UI 设置

    private func setupUI() {
        view.backgroundColor = .white
        title = "UIKit-SwiftUI 桥接示例"

        // 添加状态标签
        view.addSubview(statusLabel)

        // 添加操作按钮
        view.addSubview(actionButton)

        // 设置约束
        NSLayoutConstraint.activate([
            statusLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            statusLabel.bottomAnchor.constraint(equalTo: actionButton.topAnchor, constant: -20),

            actionButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            actionButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            actionButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
            actionButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }

    private func setupSwiftUIView() {
        // 创建 ViewModel
        let viewModel = BridgeExampleViewModel()

        // 创建协调器并连接 ViewController 和 ViewModel
        coordinator = SwiftUICoordinator(viewController: self, viewModel: viewModel)

        // 将协调器传递给 ViewModel
        viewModel.coordinator = coordinator

        // 创建 SwiftUI 视图并传入 ViewModel
        let contentView = BridgeExampleView(viewModel: viewModel)

        // 创建 UIHostingController 来托管 SwiftUI 视图
        hostingController = UIHostingController(rootView: AnyView(contentView))

        // 添加 hostingController 作为子控制器
        if let hostingController = hostingController {
            addChild(hostingController)
            view.addSubview(hostingController.view)

            // 设置 SwiftUI 视图的约束
            hostingController.view.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                hostingController.view.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
                hostingController.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                hostingController.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                hostingController.view.bottomAnchor.constraint(equalTo: statusLabel.topAnchor, constant: -20)
            ])

            hostingController.didMove(toParent: self)
        }
    }

    // MARK: - 操作

    @objc private func actionButtonTapped() {
        Task {
            await fetchDataFromViewModel()
        }
    }

    /// 从 ViewModel 获取数据
    private func fetchDataFromViewModel() async {
        updateStatus("正在获取数据...")

        let action = FetchDataAction(parameters: ["source": "UIKit", "timestamp": Date().timeIntervalSince1970])
        let result = await coordinator?.sendAction(action)

        switch result {
        case .success(let data):
            // 处理成功获取的数据
            if let items = data["items"] as? [String], let count = data["count"] as? Int {
                let itemsString = items.joined(separator: ", ")
                updateStatus("获取到 \(count) 项数据: \(itemsString)")
            } else {
                updateStatus("获取到数据，但格式不符合预期")
            }
        case .failure(let error):
            // 处理错误
            updateStatus("获取数据失败: \(error.localizedDescription)")
        case .none:
            updateStatus("协调器不可用")
        }
    }

    /// 更新状态标签
    private func updateStatus(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            self?.statusLabel.text = message
        }
    }

    // MARK: - ViewModelEventHandler 实现

    /// 处理来自 ViewModel 的事件
    public func handleEvent<E: ViewModelEvent>(_ event: E) async throws -> E.Response {
        // 如果是基于枚举的 Event，使用 eventType 进行判断
        if let enumEvent = event as? (any EnumBasedEvent) {
            switch enumEvent.eventType {
            case .userInteraction:
                if let event = event as? UserInteractionEvent {
                    return try await handleUserInteraction(event) as! E.Response
                }
            case .navigation:
                if let event = event as? NavigationEvent {
                    return try await handleNavigation(event) as! E.Response
                }
            case .permissionRequest:
                if let event = event as? PermissionRequestEvent {
                    return try await handlePermissionRequest(event) as! E.Response
                }
            case .shareContent:
                if let event = event as? ShareContentEvent {
                    return try await handleShareContent(event) as! E.Response
                }
            case .showSystemAlert:
                if let event = event as? ShowSystemAlertEvent {
                    return try await handleShowSystemAlert(event) as! E.Response
                }
            case .showActionSheet:
                if let event = event as? ShowActionSheetEvent {
                    return try await handleShowActionSheet(event) as! E.Response
                }
            default:
                throw EventHandlingError.unknownEventType
            }
        } else {
            // 兼容旧版 Event，使用 id 进行判断
            switch event.id {
            case "userInteraction":
                if let event = event as? UserInteractionEvent {
                    return try await handleUserInteraction(event) as! E.Response
                }
            case "navigation":
                if let event = event as? NavigationEvent {
                    return try await handleNavigation(event) as! E.Response
                }
            case "permissionRequest":
                if let event = event as? PermissionRequestEvent {
                    return try await handlePermissionRequest(event) as! E.Response
                }
            case "shareContent":
                if let event = event as? ShareContentEvent {
                    return try await handleShareContent(event) as! E.Response
                }
            case "showSystemAlert":
                if let event = event as? ShowSystemAlertEvent {
                    return try await handleShowSystemAlert(event) as! E.Response
                }
            case "showActionSheet":
                if let event = event as? ShowActionSheetEvent {
                    return try await handleShowActionSheet(event) as! E.Response
                }
            default:
                throw EventHandlingError.unknownEventType
            }
        }

        throw EventHandlingError.invalidEventType
    }

    // MARK: - 事件处理方法

    /// 处理用户交互事件
    private func handleUserInteraction(_ event: UserInteractionEvent) async throws -> Void {
        // 在主线程更新 UI
        await MainActor.run {
            let typeString = event.interactionType
            let timestamp = event.metadata["timestamp"] as? TimeInterval ?? 0
            let dateString = Date(timeIntervalSince1970: timestamp).formatted(date: .abbreviated, time: .standard)

            updateStatus("收到用户交互事件: \(typeString)\n时间: \(dateString)")
        }

        // 模拟处理延迟
        try await Task.sleep(nanoseconds: 500_000_000)

        return ()
    }

    /// 处理导航事件
    private func handleNavigation(_ event: NavigationEvent) async throws -> Bool {
        await MainActor.run {
            updateStatus("收到导航请求: \(event.destination)\n参数: \(event.parameters?.description ?? "无")")
        }

        // 模拟导航逻辑
        // 在实际应用中，这里会执行真正的导航操作
        try await Task.sleep(nanoseconds: 1_000_000_000)

        // 返回导航是否成功
        return true
    }

    /// 处理权限请求事件
    private func handlePermissionRequest(_ event: PermissionRequestEvent) async throws -> Bool {
        await MainActor.run {
            updateStatus("收到权限请求: \(event.permissionType)\n原因: \(event.reason ?? "无")")
        }

        // 模拟权限请求逻辑
        try await Task.sleep(nanoseconds: 1_500_000_000)

        // 返回权限是否授予
        return true
    }

    /// 处理分享内容事件
    private func handleShareContent(_ event: ShareContentEvent) async throws -> Bool {
        await MainActor.run {
            updateStatus("收到分享请求: \(event.shareType)\n内容: \(event.content.description)")
        }

        // 模拟分享逻辑
        try await Task.sleep(nanoseconds: 800_000_000)

        // 返回分享是否成功
        return true
    }

    /// 处理显示系统弹窗事件
    private func handleShowSystemAlert(_ event: ShowSystemAlertEvent) async throws -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: false)
                    return
                }

                let alert = UIAlertController(title: event.title, message: event.message, preferredStyle: .alert)

                for buttonTitle in event.buttons {
                    let action = UIAlertAction(title: buttonTitle, style: .default) { _ in
                        // 按钮点击处理
                    }
                    alert.addAction(action)
                }

                self.present(alert, animated: true) {
                    self.updateStatus("显示系统弹窗: \(event.title)")
                    continuation.resume(returning: true)
                }
            }
        }
    }

    /// 处理显示操作表事件
    private func handleShowActionSheet(_ event: ShowActionSheetEvent) async throws -> Int? {
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }

                let actionSheet = UIAlertController(title: event.title, message: event.message, preferredStyle: .actionSheet)

                // 添加选项
                for (index, option) in event.options.enumerated() {
                    let action = UIAlertAction(title: option, style: .default) { _ in
                        continuation.resume(returning: index)
                    }
                    actionSheet.addAction(action)
                }

                // 添加取消选项
                let cancelAction = UIAlertAction(title: event.cancelOption, style: .cancel) { _ in
                    continuation.resume(returning: nil)
                }
                actionSheet.addAction(cancelAction)

                self.present(actionSheet, animated: true) {
                    self.updateStatus("显示操作表")
                }
            }
        }
    }
}
