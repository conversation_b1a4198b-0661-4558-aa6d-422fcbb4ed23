//
//  BridgeActions.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//  Copyright © 2025 top-stack. All rights reserved.

import Foundation

// MARK: - 通用 Action 示例

/// 获取数据的 Action
public struct FetchDataAction: EnumBasedAction {
    public typealias Response = [String: Any]

    public let actionType: ActionType = .fetchData
    public let parameters: [String: Any]

    public init(parameters: [String: Any]) {
        self.parameters = parameters
    }
}

/// 保存数据的 Action
public struct SaveDataAction: EnumBasedAction {
    public typealias Response = Bool

    public let actionType: ActionType = .saveData
    public let data: [String: Any]

    public init(data: [String: Any]) {
        self.data = data
    }
}

/// 更新 UI 的 Action
public struct UpdateUIAction: EnumBasedAction {
    public typealias Response = Void

    public let actionType: ActionType = .updateUI
    public let updateType: String
    public let parameters: [String: Any]?

    public init(updateType: String, parameters: [String: Any]? = nil) {
        self.updateType = updateType
        self.parameters = parameters
    }
}

/// 执行业务逻辑的 Action
public struct PerformBusinessLogicAction: EnumBasedAction {
    public typealias Response = [String: Any]

    public let actionType: ActionType = .performBusinessLogic
    public let operation: String
    public let parameters: [String: Any]

    public init(operation: String, parameters: [String: Any]) {
        self.operation = operation
        self.parameters = parameters
    }
}

/// 获取用户偏好的 Action
public struct GetUserPreferencesAction: EnumBasedAction {
    public typealias Response = [String: Any]

    public let actionType: ActionType = .getUserPreferences
    public let keys: [String]?

    public init(keys: [String]? = nil) {
        self.keys = keys
    }
}

/// 保存用户偏好的 Action
public struct SaveUserPreferencesAction: EnumBasedAction {
    public typealias Response = Bool

    public let actionType: ActionType = .saveUserPreferences
    public let preferences: [String: Any]

    public init(preferences: [String: Any]) {
        self.preferences = preferences
    }
}
