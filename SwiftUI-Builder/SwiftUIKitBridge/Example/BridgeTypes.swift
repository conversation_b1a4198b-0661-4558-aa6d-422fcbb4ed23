//
//  BridgeTypes.swift
//  SwiftUI-Builder
//
//  Created for SwiftUI-Builder
//

import Foundation

// MARK: - Action 类型枚举

/// Action 类型枚举
/// 使用 String 协议使其可以直接用作 id
public enum ActionType: String {
    // 数据操作
    case fetchData
    case saveData
    case deleteData
    case updateData
    
    // UI 操作
    case updateUI
    case refreshUI
    case showLoading
    case hideLoading
    
    // 业务逻辑
    case performBusinessLogic
    case processInput
    case validateData
    
    // 用户偏好
    case getUserPreferences
    case saveUserPreferences
    
    // AI 相关
    case sendAIMessage
    case cancelAIRequest
    case generateContent
    
    // 其他
    case custom
}

// MARK: - Event 类型枚举

/// Event 类型枚举
/// 使用 String 协议使其可以直接用作 id
public enum EventType: String {
    // 用户交互
    case userInteraction
    case buttonTap
    case swipeGesture
    case longPress
    
    // 导航
    case navigation
    case openScreen
    case closeScreen
    case goBack
    
    // 系统交互
    case permissionRequest
    case shareContent
    case showSystemAlert
    case showActionSheet
    
    // 数据更新
    case dataUpdated
    case dataDeleted
    case dataCreated
    
    // 状态变化
    case stateChanged
    case loginStateChanged
    case networkStateChanged
    
    // 其他
    case custom
}

// MARK: - 扩展 ViewControllerAction 协议

/// 为 ViewControllerAction 协议添加默认实现
public extension ViewControllerAction {
    /// 默认使用类型名作为 id
    var id: String {
        return String(describing: Self.self)
    }
}

// MARK: - 扩展 ViewModelEvent 协议

/// 为 ViewModelEvent 协议添加默认实现
public extension ViewModelEvent {
    /// 默认使用类型名作为 id
    var id: String {
        return String(describing: Self.self)
    }
}

// MARK: - 基于枚举的 Action 基类

/// 基于枚举的 Action 基类
public protocol EnumBasedAction: ViewControllerAction {
    /// Action 类型
    var actionType: ActionType { get }
}

/// 为 EnumBasedAction 提供默认实现
public extension EnumBasedAction {
    /// 使用枚举的原始值作为 id
    var id: String {
        return actionType.rawValue
    }
}

// MARK: - 基于枚举的 Event 基类

/// 基于枚举的 Event 基类
public protocol EnumBasedEvent: ViewModelEvent {
    /// Event 类型
    var eventType: EventType { get }
}

/// 为 EnumBasedEvent 提供默认实现
public extension EnumBasedEvent {
    /// 使用枚举的原始值作为 id
    var id: String {
        return eventType.rawValue
    }
}
