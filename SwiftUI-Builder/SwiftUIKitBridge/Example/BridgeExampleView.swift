//
//  BridgeExampleView.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//  Copyright © 2025 top-stack. All rights reserved.

import SwiftUI

/// 示例 SwiftUI 视图
public struct BridgeExampleView: View {
    /// ViewModel
    @ObservedObject var viewModel: BridgeExampleViewModel
    
    /// 初始化
    public init(viewModel: BridgeExampleViewModel) {
        self.viewModel = viewModel
    }
    
    public var body: some View {
        VStack(spacing: 20) {
            headerView
            
            if viewModel.isLoading {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(message: errorMessage)
            } else {
                contentView
            }
            
            Spacer()
            
            actionButtonsView
        }
        .padding()
    }
    
    // MARK: - 子视图
    
    /// 头部视图
    private var headerView: some View {
        VStack(spacing: 8) {
            Text("SwiftUI-UIKit 桥接示例")
                .font(.title)
                .fontWeight(.bold)
            
            Text("通过协调器模式实现双向通信")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    /// 加载视图
    private var loadingView: some View {
        VStack {
            ProgressView()
                .scaleEffect(1.5)
                .padding()
            
            Text("加载中...")
                .font(.headline)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 错误视图
    private func errorView(message: String) -> some View {
        VStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.red)
            
            Text("发生错误")
                .font(.headline)
            
            Text(message)
                .font(.subheadline)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            Button("重试") {
                viewModel.errorMessage = nil
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 8)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    /// 内容视图
    private var contentView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("数据内容")
                .font(.headline)
            
            if viewModel.data.isEmpty {
                Text("暂无数据，请点击下方按钮获取数据")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            } else {
                dataListView
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    /// 数据列表视图
    private var dataListView: some View {
        VStack(alignment: .leading, spacing: 8) {
            if let items = viewModel.data["items"] as? [String] {
                ForEach(items, id: \.self) { item in
                    HStack {
                        Image(systemName: "circle.fill")
                            .font(.system(size: 8))
                            .foregroundColor(.blue)
                        
                        Text(item)
                            .font(.body)
                    }
                    .padding(.vertical, 4)
                }
            }
            
            if let count = viewModel.data["count"] as? Int {
                Divider()
                
                Text("总计: \(count) 项")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    /// 操作按钮视图
    private var actionButtonsView: some View {
        VStack(spacing: 12) {
            Button("获取数据") {
                Task {
                    let action = FetchDataAction(parameters: ["source": "SwiftUI", "timestamp": Date().timeIntervalSince1970])
                    _ = await viewModel.coordinator?.sendAction(action)
                }
            }
            .buttonStyle(PrimaryButtonStyle())
            
            Button("发送用户交互事件") {
                Task {
                    await viewModel.sendUserInteraction(
                        type: "buttonTap",
                        metadata: ["timestamp": Date().timeIntervalSince1970, "source": "SwiftUI"]
                    )
                }
            }
            .buttonStyle(SecondaryButtonStyle())
            
            Button("请求导航") {
                Task {
                    let success = await viewModel.requestNavigation(
                        to: "DetailScreen",
                        with: ["id": "123", "source": "SwiftUI"]
                    )
                    
                    if success {
                        print("导航请求被接受")
                    } else {
                        print("导航请求被拒绝")
                    }
                }
            }
            .buttonStyle(SecondaryButtonStyle())
            
            Button("显示系统弹窗") {
                Task {
                    _ = await viewModel.showAlert(
                        title: "来自 SwiftUI 的弹窗",
                        message: "这是通过 UIKit 显示的系统弹窗",
                        buttons: ["确定", "取消"]
                    )
                }
            }
            .buttonStyle(SecondaryButtonStyle())
        }
    }
}

// MARK: - 按钮样式

/// 主要按钮样式
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .frame(maxWidth: .infinity)
            .background(configuration.isPressed ? Color.blue.opacity(0.8) : Color.blue)
            .foregroundColor(.white)
            .font(.headline)
            .cornerRadius(10)
            .scaleEffect(configuration.isPressed ? 0.98 : 1)
            .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
    }
}

/// 次要按钮样式
struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .frame(maxWidth: .infinity)
            .background(configuration.isPressed ? Color.gray.opacity(0.15) : Color.gray.opacity(0.1))
            .foregroundColor(.primary)
            .font(.headline)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1)
            .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
    }
}

// MARK: - 预览

struct BridgeExampleView_Previews: PreviewProvider {
    static var previews: some View {
        let viewModel = BridgeExampleViewModel()
        
        // 添加一些示例数据
        viewModel.data = [
            "items": ["示例项目 1", "示例项目 2", "示例项目 3"],
            "count": 3
        ]
        
        return Group {
            BridgeExampleView(viewModel: viewModel)
                .previewDisplayName("正常状态")
            
            BridgeExampleView(viewModel: {
                let vm = BridgeExampleViewModel()
                vm.isLoading = true
                return vm
            }())
            .previewDisplayName("加载状态")
            
            BridgeExampleView(viewModel: {
                let vm = BridgeExampleViewModel()
                vm.errorMessage = "无法连接到服务器，请检查网络连接后重试"
                return vm
            }())
            .previewDisplayName("错误状态")
        }
    }
}
