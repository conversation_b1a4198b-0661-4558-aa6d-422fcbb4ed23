//
//  BridgeEvents.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//  Copyright © 2025 top-stack. All rights reserved.

import Foundation

// MARK: - 通用 Event 示例

/// 用户交互事件
public struct UserInteractionEvent: EnumBasedEvent {
    public typealias Response = Void

    public let eventType: EventType = .userInteraction
    public let interactionType: String
    public let metadata: [String: Any]

    public init(interactionType: String, metadata: [String: Any]) {
        self.interactionType = interactionType
        self.metadata = metadata
    }
}

/// 导航事件
public struct NavigationEvent: EnumBasedEvent {
    public typealias Response = Bool

    public let eventType: EventType = .navigation
    public let destination: String
    public let parameters: [String: Any]?

    public init(destination: String, parameters: [String: Any]? = nil) {
        self.destination = destination
        self.parameters = parameters
    }
}

/// 权限请求事件
public struct PermissionRequestEvent: EnumBasedEvent {
    public typealias Response = Bool

    public let eventType: EventType = .permissionRequest
    public let permissionType: String
    public let reason: String?

    public init(permissionType: String, reason: String? = nil) {
        self.permissionType = permissionType
        self.reason = reason
    }
}

/// 分享内容事件
public struct ShareContentEvent: EnumBasedEvent {
    public typealias Response = Bool

    public let eventType: EventType = .shareContent
    public let content: [String: Any]
    public let shareType: String

    public init(content: [String: Any], shareType: String) {
        self.content = content
        self.shareType = shareType
    }
}

/// 显示系统弹窗事件
public struct ShowSystemAlertEvent: EnumBasedEvent {
    public typealias Response = Bool

    public let eventType: EventType = .showSystemAlert
    public let title: String
    public let message: String
    public let buttons: [String]

    public init(title: String, message: String, buttons: [String] = ["确定"]) {
        self.title = title
        self.message = message
        self.buttons = buttons
    }
}

/// 显示操作表事件
public struct ShowActionSheetEvent: EnumBasedEvent {
    public typealias Response = Int?

    public let eventType: EventType = .showActionSheet
    public let title: String?
    public let message: String?
    public let options: [String]
    public let cancelOption: String

    public init(title: String? = nil, message: String? = nil, options: [String], cancelOption: String = "取消") {
        self.title = title
        self.message = message
        self.options = options
        self.cancelOption = cancelOption
    }
}
