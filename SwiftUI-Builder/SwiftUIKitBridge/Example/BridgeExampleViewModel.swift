//
//  BridgeExampleViewModel.swift
//  SwiftUI-Builder
//
//  Created by 杨冰冰 on 2025/5/23.
//  Copyright © 2025 top-stack. All rights reserved.

import Foundation
import SwiftUI
import Combine

/// 示例 ViewModel 实现
public class BridgeExampleViewModel: ObservableObject, ViewControllerActionHandler {
    // MARK: - 可观察状态

    /// 加载状态
    @Published public var isLoading = false

    /// 数据
    @Published public var data: [String: Any] = [:]

    /// 错误信息
    @Published public var errorMessage: String?

    // MARK: - 协调器

    /// 持有协调器的弱引用，避免循环引用
    /// ViewModel 被 SwiftUI 视图持有，SwiftUI 视图被 UIHostingController 持有，
    /// UIHostingController 被 UIViewController 持有，UIViewController 持有协调器，
    /// 协调器弱引用 ViewModel，形成完整的引用链，但不会造成循环引用
    public weak var coordinator: Coordinator?

    // MARK: - 初始化

    public init(coordinator: Coordinator? = nil) {
        self.coordinator = coordinator
    }

    // MARK: - 公共方法

    /// 发送用户交互事件到 UIViewController
    /// - Parameters:
    ///   - type: 交互类型
    ///   - metadata: 元数据
    public func sendUserInteraction(type: String, metadata: [String: Any]) async {
        let event = UserInteractionEvent(interactionType: type, metadata: metadata)
        let result = await coordinator?.sendEvent(event)

        switch result {
        case .success:
            // 事件处理成功
            print("User interaction event handled successfully")
        case .failure(let error):
            // 事件处理失败
            print("Error handling user interaction: \(error)")
            await MainActor.run {
                self.errorMessage = "交互事件处理失败: \(error.localizedDescription)"
            }
        case .none:
            print("Coordinator not available")
            await MainActor.run {
                self.errorMessage = "协调器不可用"
            }
        }
    }

    /// 请求导航到指定目的地
    /// - Parameters:
    ///   - destination: 导航目的地
    ///   - parameters: 导航参数
    /// - Returns: 导航是否成功
    public func requestNavigation(to destination: String, with parameters: [String: Any]? = nil) async -> Bool {
        let event = NavigationEvent(destination: destination, parameters: parameters)
        let result = await coordinator?.sendEvent(event)

        switch result {
        case .success(let success):
            return success
        case .failure, .none:
            return false
        }
    }

    /// 请求系统权限
    /// - Parameters:
    ///   - permissionType: 权限类型
    ///   - reason: 请求原因
    /// - Returns: 权限是否授予
    public func requestPermission(type permissionType: String, reason: String? = nil) async -> Bool {
        let event = PermissionRequestEvent(permissionType: permissionType, reason: reason)
        let result = await coordinator?.sendEvent(event)

        switch result {
        case .success(let granted):
            return granted
        case .failure, .none:
            return false
        }
    }

    /// 显示系统弹窗
    /// - Parameters:
    ///   - title: 标题
    ///   - message: 消息
    ///   - buttons: 按钮文本数组
    /// - Returns: 是否显示成功
    public func showAlert(title: String, message: String, buttons: [String] = ["确定"]) async -> Bool {
        let event = ShowSystemAlertEvent(title: title, message: message, buttons: buttons)
        let result = await coordinator?.sendEvent(event)

        switch result {
        case .success(let shown):
            return shown
        case .failure, .none:
            return false
        }
    }

    // MARK: - ViewControllerActionHandler 实现

    /// 处理来自 UIViewController 的 Action
    public func handleAction<A: ViewControllerAction>(_ action: A) async throws -> A.Response {
        
        guard let enumAction = action as? (any EnumBasedAction) else {
            throw ActionHandlingError.invalidActionType
        }
        
        switch enumAction.actionType {
        case .fetchData:
            if let action = action as? FetchDataAction {
                return try await fetchData(with: action.parameters) as! A.Response
            }
        case .saveData:
            if let action = action as? SaveDataAction {
                return try await saveData(action.data) as! A.Response
            }
        case .updateUI:
            if let action = action as? UpdateUIAction {
                await updateUI(type: action.updateType, parameters: action.parameters)
                return () as! A.Response
            }
        case .performBusinessLogic:
            if let action = action as? PerformBusinessLogicAction {
                return try await performBusinessLogic(operation: action.operation, parameters: action.parameters) as! A.Response
            }
        case .getUserPreferences:
            if let action = action as? GetUserPreferencesAction {
                return try await getUserPreferences(keys: action.keys) as! A.Response
            }
        case .saveUserPreferences:
            if let action = action as? SaveUserPreferencesAction {
                return try await saveUserPreferences(action.preferences) as! A.Response
            }
        default:
            throw ActionHandlingError.unknownActionType
        }

        throw ActionHandlingError.invalidActionType
    }

    // MARK: - 私有方法

    /// 获取数据
    private func fetchData(with parameters: [String: Any]) async throws -> [String: Any] {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // 模拟网络延迟
        try await Task.sleep(nanoseconds: 1_000_000_000)

        // 模拟获取的数据
        let fetchedData: [String: Any] = [
            "items": ["Item 1", "Item 2", "Item 3"],
            "count": 3,
            "parameters": parameters
        ]

        await MainActor.run {
            isLoading = false
            data = fetchedData
        }

        return fetchedData
    }

    /// 保存数据
    private func saveData(_ data: [String: Any]) async throws -> Bool {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // 模拟保存延迟
        try await Task.sleep(nanoseconds: 500_000_000)

        // 模拟保存成功
        await MainActor.run {
            isLoading = false
        }

        return true
    }

    /// 更新 UI
    private func updateUI(type: String, parameters: [String: Any]?) async {
        await MainActor.run {
            // 根据不同的更新类型执行不同的 UI 更新
            switch type {
            case "refreshData":
                // 刷新数据
                print("Refreshing data with parameters: \(parameters ?? [:])")
            case "showLoading":
                isLoading = true
            case "hideLoading":
                isLoading = false
            case "showError":
                errorMessage = parameters?["message"] as? String
            case "clearError":
                errorMessage = nil
            default:
                print("Unknown UI update type: \(type)")
            }
        }
    }

    /// 执行业务逻辑
    private func performBusinessLogic(operation: String, parameters: [String: Any]) async throws -> [String: Any] {
        // 模拟业务逻辑处理
        try await Task.sleep(nanoseconds: 800_000_000)

        // 返回处理结果
        return [
            "operation": operation,
            "result": "success",
            "processedParameters": parameters
        ]
    }

    /// 获取用户偏好
    private func getUserPreferences(keys: [String]?) async throws -> [String: Any] {
        // 模拟从存储中获取用户偏好
        let allPreferences: [String: Any] = [
            "theme": "dark",
            "fontSize": 14,
            "notifications": true,
            "language": "zh-CN"
        ]

        if let keys = keys {
            // 只返回请求的键
            var result: [String: Any] = [:]
            for key in keys {
                if let value = allPreferences[key] {
                    result[key] = value
                }
            }
            return result
        } else {
            // 返回所有偏好
            return allPreferences
        }
    }

    /// 保存用户偏好
    private func saveUserPreferences(_ preferences: [String: Any]) async throws -> Bool {
        // 模拟保存用户偏好
        try await Task.sleep(nanoseconds: 300_000_000)

        // 模拟保存成功
        return true
    }
}
