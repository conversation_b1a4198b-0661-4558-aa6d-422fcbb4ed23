Taking commonmark tests from the spec for benchmarking here:

<a><bab><c2c>

<a/><b2/>

<a  /><b2
data="foo" >

<a foo="bar" bam = 'baz <em>"</em>'
_boolean zoop:33=zoop:33 />

<33> <__>

<a h*#ref="hi">

<a href="hi'> <a href=hi'>

< a><
foo><bar/ >

<a href='bar'title=title>

</a>
</foo >

</a href="foo">

foo <!-- this is a
comment - with hyphen -->

foo <!-- not a comment -- two hyphens -->

foo <?php echo $a; ?>

foo <!ELEMENT br EMPTY>

foo <![CDATA[>&<]]>

<a href="&ouml;">

<a href="\*">

<a href="\"">
