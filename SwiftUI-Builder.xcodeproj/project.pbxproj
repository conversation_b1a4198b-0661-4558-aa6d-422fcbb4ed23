// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0122A038CFD21AEAA22FDB50 /* Pods_SwiftUI_Builder.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2AC292256F51A1A88F43C451 /* Pods_SwiftUI_Builder.framework */; };
		6B1DC0CF2DDB257900DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0CE2DDB257900DAC4A9 /* MarkdownUI */; };
		6B1DC0D22DDB25C300DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0D12DDB25C300DAC4A9 /* MarkdownUI */; };
		6B1DC0D52DDB260D00DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0D42DDB260D00DAC4A9 /* MarkdownUI */; };
		6B1DC0F12DDB3C0500DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0F02DDB3C0500DAC4A9 /* MarkdownUI */; };
		6B1DC0F42DDB456000DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0F32DDB456000DAC4A9 /* MarkdownUI */; };
		6B1DC0F72DDB48D300DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0F62DDB48D300DAC4A9 /* MarkdownUI */; };
		6B1DC0FA2DDB4A1900DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0F92DDB4A1900DAC4A9 /* MarkdownUI */; };
		6B1DC0FD2DDB4A5500DAC4A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B1DC0FC2DDB4A5500DAC4A9 /* MarkdownUI */; };
		6B67A1822DDD65920068BA37 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B67A1812DDD65920068BA37 /* MarkdownUI */; };
		6B67A1852DDD65AF0068BA37 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B67A1842DDD65AF0068BA37 /* MarkdownUI */; };
		6B67A1882DDD69070068BA37 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B67A1872DDD69070068BA37 /* MarkdownUI */; };
		6B890DDD2DDC5895006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DDC2DDC5895006479A9 /* MarkdownUI */; };
		6B890DE02DDC591F006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DDF2DDC591F006479A9 /* MarkdownUI */; };
		6B890DE32DDC5F58006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DE22DDC5F58006479A9 /* MarkdownUI */; };
		6B890DE62DDC68F3006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DE52DDC68F3006479A9 /* MarkdownUI */; };
		6B890DEB2DDC6DB8006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DEA2DDC6DB8006479A9 /* MarkdownUI */; };
		6B890DEE2DDC72CA006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DED2DDC72CA006479A9 /* MarkdownUI */; };
		6B890DF12DDC72FA006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DF02DDC72FA006479A9 /* MarkdownUI */; };
		6B890DF42DDC752B006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DF32DDC752B006479A9 /* MarkdownUI */; };
		6B890DF72DDC7958006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DF62DDC7958006479A9 /* MarkdownUI */; };
		6B890DFA2DDC7B68006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DF92DDC7B68006479A9 /* MarkdownUI */; };
		6B890DFD2DDC8F78006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DFC2DDC8F78006479A9 /* MarkdownUI */; };
		6B890E002DDC9CEC006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890DFF2DDC9CEC006479A9 /* MarkdownUI */; };
		6B890E032DDC9D12006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890E022DDC9D12006479A9 /* MarkdownUI */; };
		6B890E062DDCA3B1006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890E052DDCA3B1006479A9 /* MarkdownUI */; };
		6B890E092DDCA426006479A9 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B890E082DDCA426006479A9 /* MarkdownUI */; };
		6B8D8B172DE9596700022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B162DE9596700022012 /* MarkdownUI */; };
		6B8D8B1A2DE95F0200022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B192DE95F0200022012 /* MarkdownUI */; };
		6B8D8B1D2DE9603200022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B1C2DE9603200022012 /* MarkdownUI */; };
		6B8D8B202DE9608000022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B1F2DE9608000022012 /* MarkdownUI */; };
		6B8D8B232DE990F100022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B222DE990F100022012 /* MarkdownUI */; };
		6B8D8B262DE9944400022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B252DE9944400022012 /* MarkdownUI */; };
		6B8D8B292DE997A200022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B282DE997A200022012 /* MarkdownUI */; };
		6B8D8B2C2DE99A2600022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B2B2DE99A2600022012 /* MarkdownUI */; };
		6B8D8B2F2DE99A5700022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B2E2DE99A5700022012 /* MarkdownUI */; };
		6B8D8B322DE99B9C00022012 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6B8D8B312DE99B9C00022012 /* MarkdownUI */; };
		6BDCDEF62DD7134E00314B4B /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 6BDCDEF52DD7134E00314B4B /* MarkdownUI */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2AC292256F51A1A88F43C451 /* Pods_SwiftUI_Builder.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_SwiftUI_Builder.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6B3C14522D9A9FBE00809EBE /* SwiftUI-Builder.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "SwiftUI-Builder.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		87B34B201205D783873A4B9B /* Pods-SwiftUI-Builder.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SwiftUI-Builder.debug.xcconfig"; path = "Target Support Files/Pods-SwiftUI-Builder/Pods-SwiftUI-Builder.debug.xcconfig"; sourceTree = "<group>"; };
		8E5F6A9C010BEDA91527C12B /* Pods-SwiftUI-Builder.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SwiftUI-Builder.release.xcconfig"; path = "Target Support Files/Pods-SwiftUI-Builder/Pods-SwiftUI-Builder.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		6B3C14542D9A9FBE00809EBE /* SwiftUI-Builder */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "SwiftUI-Builder";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		6B3C144F2D9A9FBE00809EBE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B1DC0D22DDB25C300DAC4A9 /* MarkdownUI in Frameworks */,
				6B890DEB2DDC6DB8006479A9 /* MarkdownUI in Frameworks */,
				6B1DC0F72DDB48D300DAC4A9 /* MarkdownUI in Frameworks */,
				6B8D8B2C2DE99A2600022012 /* MarkdownUI in Frameworks */,
				6B890E092DDCA426006479A9 /* MarkdownUI in Frameworks */,
				6B67A1822DDD65920068BA37 /* MarkdownUI in Frameworks */,
				6B890DF72DDC7958006479A9 /* MarkdownUI in Frameworks */,
				6B8D8B322DE99B9C00022012 /* MarkdownUI in Frameworks */,
				6B8D8B2F2DE99A5700022012 /* MarkdownUI in Frameworks */,
				6BDCDEF62DD7134E00314B4B /* MarkdownUI in Frameworks */,
				6B890E062DDCA3B1006479A9 /* MarkdownUI in Frameworks */,
				6B8D8B1D2DE9603200022012 /* MarkdownUI in Frameworks */,
				6B890DE32DDC5F58006479A9 /* MarkdownUI in Frameworks */,
				6B8D8B1A2DE95F0200022012 /* MarkdownUI in Frameworks */,
				6B8D8B202DE9608000022012 /* MarkdownUI in Frameworks */,
				6B890DE62DDC68F3006479A9 /* MarkdownUI in Frameworks */,
				6B890DFA2DDC7B68006479A9 /* MarkdownUI in Frameworks */,
				6B1DC0FD2DDB4A5500DAC4A9 /* MarkdownUI in Frameworks */,
				6B1DC0FA2DDB4A1900DAC4A9 /* MarkdownUI in Frameworks */,
				6B1DC0CF2DDB257900DAC4A9 /* MarkdownUI in Frameworks */,
				6B890DEE2DDC72CA006479A9 /* MarkdownUI in Frameworks */,
				6B8D8B172DE9596700022012 /* MarkdownUI in Frameworks */,
				6B890DF42DDC752B006479A9 /* MarkdownUI in Frameworks */,
				6B1DC0F42DDB456000DAC4A9 /* MarkdownUI in Frameworks */,
				6B67A1852DDD65AF0068BA37 /* MarkdownUI in Frameworks */,
				6B890DF12DDC72FA006479A9 /* MarkdownUI in Frameworks */,
				6B890E002DDC9CEC006479A9 /* MarkdownUI in Frameworks */,
				6B1DC0D52DDB260D00DAC4A9 /* MarkdownUI in Frameworks */,
				6B67A1882DDD69070068BA37 /* MarkdownUI in Frameworks */,
				6B890DE02DDC591F006479A9 /* MarkdownUI in Frameworks */,
				6B8D8B262DE9944400022012 /* MarkdownUI in Frameworks */,
				6B1DC0F12DDB3C0500DAC4A9 /* MarkdownUI in Frameworks */,
				6B8D8B292DE997A200022012 /* MarkdownUI in Frameworks */,
				6B890E032DDC9D12006479A9 /* MarkdownUI in Frameworks */,
				6B890DDD2DDC5895006479A9 /* MarkdownUI in Frameworks */,
				6B8D8B232DE990F100022012 /* MarkdownUI in Frameworks */,
				6B890DFD2DDC8F78006479A9 /* MarkdownUI in Frameworks */,
				0122A038CFD21AEAA22FDB50 /* Pods_SwiftUI_Builder.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6B3C14492D9A9FBE00809EBE = {
			isa = PBXGroup;
			children = (
				6B3C14542D9A9FBE00809EBE /* SwiftUI-Builder */,
				6B3C14532D9A9FBE00809EBE /* Products */,
				C19060AD6BBCE4D6EBDE3A06 /* Pods */,
				E35BF8F9A3F84E3152C17398 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		6B3C14532D9A9FBE00809EBE /* Products */ = {
			isa = PBXGroup;
			children = (
				6B3C14522D9A9FBE00809EBE /* SwiftUI-Builder.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C19060AD6BBCE4D6EBDE3A06 /* Pods */ = {
			isa = PBXGroup;
			children = (
				87B34B201205D783873A4B9B /* Pods-SwiftUI-Builder.debug.xcconfig */,
				8E5F6A9C010BEDA91527C12B /* Pods-SwiftUI-Builder.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E35BF8F9A3F84E3152C17398 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2AC292256F51A1A88F43C451 /* Pods_SwiftUI_Builder.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6B3C14512D9A9FBE00809EBE /* SwiftUI-Builder */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6B3C14602D9A9FBF00809EBE /* Build configuration list for PBXNativeTarget "SwiftUI-Builder" */;
			buildPhases = (
				AF9C44F1E354826913017690 /* [CP] Check Pods Manifest.lock */,
				6B3C144E2D9A9FBE00809EBE /* Sources */,
				6B3C144F2D9A9FBE00809EBE /* Frameworks */,
				6B3C14502D9A9FBE00809EBE /* Resources */,
				8BE5D127AC17224BB964C7D3 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				6B3C14542D9A9FBE00809EBE /* SwiftUI-Builder */,
			);
			name = "SwiftUI-Builder";
			packageProductDependencies = (
				6BDCDEF52DD7134E00314B4B /* MarkdownUI */,
				6B1DC0CE2DDB257900DAC4A9 /* MarkdownUI */,
				6B1DC0D12DDB25C300DAC4A9 /* MarkdownUI */,
				6B1DC0D42DDB260D00DAC4A9 /* MarkdownUI */,
				6B1DC0F02DDB3C0500DAC4A9 /* MarkdownUI */,
				6B1DC0F32DDB456000DAC4A9 /* MarkdownUI */,
				6B1DC0F62DDB48D300DAC4A9 /* MarkdownUI */,
				6B1DC0F92DDB4A1900DAC4A9 /* MarkdownUI */,
				6B1DC0FC2DDB4A5500DAC4A9 /* MarkdownUI */,
				6B890DDC2DDC5895006479A9 /* MarkdownUI */,
				6B890DDF2DDC591F006479A9 /* MarkdownUI */,
				6B890DE22DDC5F58006479A9 /* MarkdownUI */,
				6B890DE52DDC68F3006479A9 /* MarkdownUI */,
				6B890DEA2DDC6DB8006479A9 /* MarkdownUI */,
				6B890DED2DDC72CA006479A9 /* MarkdownUI */,
				6B890DF02DDC72FA006479A9 /* MarkdownUI */,
				6B890DF32DDC752B006479A9 /* MarkdownUI */,
				6B890DF62DDC7958006479A9 /* MarkdownUI */,
				6B890DF92DDC7B68006479A9 /* MarkdownUI */,
				6B890DFC2DDC8F78006479A9 /* MarkdownUI */,
				6B890DFF2DDC9CEC006479A9 /* MarkdownUI */,
				6B890E022DDC9D12006479A9 /* MarkdownUI */,
				6B890E052DDCA3B1006479A9 /* MarkdownUI */,
				6B890E082DDCA426006479A9 /* MarkdownUI */,
				6B67A1812DDD65920068BA37 /* MarkdownUI */,
				6B67A1842DDD65AF0068BA37 /* MarkdownUI */,
				6B67A1872DDD69070068BA37 /* MarkdownUI */,
				6B8D8B162DE9596700022012 /* MarkdownUI */,
				6B8D8B192DE95F0200022012 /* MarkdownUI */,
				6B8D8B1C2DE9603200022012 /* MarkdownUI */,
				6B8D8B1F2DE9608000022012 /* MarkdownUI */,
				6B8D8B222DE990F100022012 /* MarkdownUI */,
				6B8D8B252DE9944400022012 /* MarkdownUI */,
				6B8D8B282DE997A200022012 /* MarkdownUI */,
				6B8D8B2B2DE99A2600022012 /* MarkdownUI */,
				6B8D8B2E2DE99A5700022012 /* MarkdownUI */,
				6B8D8B312DE99B9C00022012 /* MarkdownUI */,
			);
			productName = "SwiftUI-Builder";
			productReference = 6B3C14522D9A9FBE00809EBE /* SwiftUI-Builder.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6B3C144A2D9A9FBE00809EBE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					6B3C14512D9A9FBE00809EBE = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 6B3C144D2D9A9FBE00809EBE /* Build configuration list for PBXProject "SwiftUI-Builder" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6B3C14492D9A9FBE00809EBE;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				6B8D8B302DE99B9C00022012 /* XCLocalSwiftPackageReference "../NotePods/KiloMarkdownKit" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 6B3C14532D9A9FBE00809EBE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6B3C14512D9A9FBE00809EBE /* SwiftUI-Builder */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6B3C14502D9A9FBE00809EBE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		8BE5D127AC17224BB964C7D3 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SwiftUI-Builder/Pods-SwiftUI-Builder-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SwiftUI-Builder/Pods-SwiftUI-Builder-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SwiftUI-Builder/Pods-SwiftUI-Builder-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AF9C44F1E354826913017690 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SwiftUI-Builder-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6B3C144E2D9A9FBE00809EBE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		6B3C145E2D9A9FBF00809EBE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		6B3C145F2D9A9FBF00809EBE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6B3C14612D9A9FBF00809EBE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 87B34B201205D783873A4B9B /* Pods-SwiftUI-Builder.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SwiftUI-Builder/Preview Content\"";
				DEVELOPMENT_TEAM = 4WB3QMP893;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.top-stack.SwiftUI-Builder";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6B3C14622D9A9FBF00809EBE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8E5F6A9C010BEDA91527C12B /* Pods-SwiftUI-Builder.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SwiftUI-Builder/Preview Content\"";
				DEVELOPMENT_TEAM = 4WB3QMP893;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.top-stack.SwiftUI-Builder";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6B3C144D2D9A9FBE00809EBE /* Build configuration list for PBXProject "SwiftUI-Builder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6B3C145E2D9A9FBF00809EBE /* Debug */,
				6B3C145F2D9A9FBF00809EBE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6B3C14602D9A9FBF00809EBE /* Build configuration list for PBXNativeTarget "SwiftUI-Builder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6B3C14612D9A9FBF00809EBE /* Debug */,
				6B3C14622D9A9FBF00809EBE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		6B8D8B302DE99B9C00022012 /* XCLocalSwiftPackageReference "../NotePods/KiloMarkdownKit" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../NotePods/KiloMarkdownKit;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		6B1DC0CE2DDB257900DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B1DC0D12DDB25C300DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B1DC0D42DDB260D00DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B1DC0F02DDB3C0500DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B1DC0F32DDB456000DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B1DC0F62DDB48D300DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B1DC0F92DDB4A1900DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B1DC0FC2DDB4A5500DAC4A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B67A1812DDD65920068BA37 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B67A1842DDD65AF0068BA37 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B67A1872DDD69070068BA37 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DDC2DDC5895006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DDF2DDC591F006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DE22DDC5F58006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DE52DDC68F3006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DEA2DDC6DB8006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DED2DDC72CA006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DF02DDC72FA006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DF32DDC752B006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DF62DDC7958006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DF92DDC7B68006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DFC2DDC8F78006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890DFF2DDC9CEC006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890E022DDC9D12006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890E052DDCA3B1006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B890E082DDCA426006479A9 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B162DE9596700022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B192DE95F0200022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B1C2DE9603200022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B1F2DE9608000022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B222DE990F100022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B252DE9944400022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B282DE997A200022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B2B2DE99A2600022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B2E2DE99A5700022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6B8D8B312DE99B9C00022012 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
		6BDCDEF52DD7134E00314B4B /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MarkdownUI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 6B3C144A2D9A9FBE00809EBE /* Project object */;
}
