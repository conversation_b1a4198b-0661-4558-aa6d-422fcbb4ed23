# Uncomment the next line to define a global platform for your project
# platform :ios, '9.0'
source '****************:top-stack/iOS/Specs.git'
source 'https://gitee.com/mirrors/CocoaPods-Specs.git'
platform :ios, "15.0"

def _check_local_pod_branch_valid(name, path, valid_branch)
  Dir::chdir(path) do
    branch_in_project = `git symbolic-ref -q --short HEAD`
    branch_in_project = branch_in_project.strip

    if valid_branch.length > 1 then
      
      if valid_branch == branch_in_project then;else
        puts "参数中的分支信息与目录中的分支不匹配 \n 【#{name}】 组件位于分支 【#{branch_in_project}】 但应该是 【#{valid_branch}】"
        # 你需要调整Podfile 中的分支参数或者调整组件目录中的分支
        raise
      end

    end
  end
end

def local_pod(*args, **kwargs)
    name = args[0]
    dir = name
    splited = name.split("/")
  
    if splited.length > 1 then; dir = splited[0]; end;
    
    path = "../NotePods/#{dir}"

    if kwargs.has_key?(:branch) then
      branch = kwargs[:branch]
      _check_local_pod_branch_valid name, path, branch
    end
    
    pod "#{name}",:path => "#{path}"
end

target 'SwiftUI-Builder' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!
  local_pod 'AIChatKit', :git => '****************:top-stack/iOS/AIChatKit.git', :branch => '3.24.0'
  # Pods for SwiftUI-Builder
  pod 'KiloNetwork', :git => '****************:top-stack/iOS/KiloNetwork.git', :branch => 'master'
  pod 'ZDBaseKit', :git => '****************:top-stack/iOS/ZDBaseKit.git'

end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['SWIFT_COMPILATION_MODE'] = 'singlefile'
      #统一设置所有的子pod，debug下也导出dsym（如果你遇到某些堆栈未能正常解析）
#      config.build_settings['DEBUG_INFORMATION_FORMAT'] = 'dwarf-with-dsym'
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
      config.build_settings['CODE_SIGN_IDENTITY'] = ''
    end
  end
end
