.TH "cmark-gfm" "1" "March 24, 2016" "LOCAL" "General Commands Manual"
.SH "NAME"
\fBcmark\fR
\- convert CommonMark formatted text with GitHub Flavored Markdown extensions to HTML
.SH "SYNOPSIS"
.HP 6n
\fBcmark-gfm\fR
[options]
file*
.SH "DESCRIPTION"
\fBcmark-gfm\fR
converts Markdown formatted plain text to either HTML, groff man,
CommonMark XML, LaTeX, or CommonMark, using the conventions
described in the CommonMark spec.  It reads input from \fIstdin\fR
or the specified files (concatenating their contents) and writes
output to \fIstdout\fR.
.SH "OPTIONS"
.TP 12n
.B \-\-to, \-t \f[I]FORMAT\f[]
Specify output format (\f[C]html\f[], \f[C]man\f[], \f[C]xml\f[],
\f[C]latex\f[], \f[C]commonmark\f[]).
.TP 12n
.B \-\-width \f[I]WIDTH\f[]
Specify a column width to which to wrap the output. For no wrapping, use
the value 0 (the default).  This option currently only affects the
commonmark, latex, and man renderers.
.TP 12n
.B \-\-hardbreaks
Render soft breaks (newlines inside paragraphs in the CommonMark source)
as hard line breaks in the target format.  If this option is specified,
hard wrapping is disabled for CommonMark output, regardless of the value
given with \-\-width.
.TP 12n
.B \-\-nobreaks
Render soft breaks as spaces.  If this option is specified,
hard wrapping is disabled for all output formats, regardless of the value
given with \-\-width.
.TP 12n
.B \-\-sourcepos
Include source position attribute.
.TP 12n
.B \-\-normalize
Consolidate adjacent text nodes.
.TP 12n
.B \-\-extension, \-e \f[I]EXTENSION_NAME\f[]
Specify an extension name to use.
.TP 12n
.B \-\-list\-extensions
List available extensions and quit.
.TP 12n
.B \-\-validate-utf8
Validate UTF-8, replacing illegal sequences with U+FFFD.
.TP 12n
.B \-\-smart
Use smart punctuation.  Straight double and single quotes will
be rendered as curly quotes, depending on their position.
\f[C]\-\-\f[] will be rendered as an en-dash.
\f[C]\-\-\-\f[] will be rendered as an em-dash.
\f[C]...\f[] will be rendered as ellipses.
.TP 12n
.B \-\-unsafe
Render raw HTML and potentially dangerous URLs.
(Raw HTML is not replaced by a placeholder comment; potentially
dangerous URLs are not replaced by empty strings.)  Dangerous
URLs are those that begin with `javascript:`, `vbscript:`,
`file:`, or `data:` (except for `image/png`, `image/gif`,
`image/jpeg`, or `image/webp` mime types).
.TP 12n
.B \-\-help
Print usage information.
.TP 12n
.B \-\-version
Print version.
.SH "AUTHORS"
John MacFarlane, Vicent Marti, Kārlis Gaņģis, Nick Wellnhofer.
.SH "SEE ALSO"
.PP
CommonMark spec:  \f[C]http://spec.commonmark.org\f[].
