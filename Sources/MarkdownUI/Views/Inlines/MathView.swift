import SwiftUI
import SwiftMath

/// A view that displays LaTeX math equations.
struct MathView: View {
    let latex: String
    let isInline: Bool

    @Environment(\.colorScheme) private var colorScheme

    init(latex: String, isInline: Bool = false) {
        self.latex = latex
        self.isInline = isInline
    }

    var body: some View {
        MathViewRepresentable(
            equation: latex,
            font: .latinModernFont,
            textAlignment: .left,
            fontSize: isInline ? 16 : 20,
            labelMode: isInline ? .text : .display
        )
    }
}

#if os(iOS)
struct MathViewRepresentable: UIViewRepresentable {
    var equation: String
    var font: MathFont = .latinModernFont
    var textAlignment: MTTextAlignment = .center
    var fontSize: CGFloat = 18
    var labelMode: MTMathUILabelMode = .display
    var insets: MTEdgeInsets = MTEdgeInsets()

    func makeUIView(context: Context) -> MTMathUILabel {
        let view = MTMathUILabel()
        view.contentInsets = insets
        return view
    }

    func updateUIView(_ view: MTMathUILabel, context: Context) {
        // 处理转义反斜杠
        let processedEquation = processLatexEquation(equation)

        view.latex = processedEquation
        view.font = MTFontManager().font(withName: font.rawValue, size: fontSize)
        view.textAlignment = textAlignment
        view.labelMode = labelMode
        view.textColor = MTColor(Color.primary)

        // 确保视图大小适应内容
        view.sizeToFit()
    }

    // 处理LaTeX公式中的转义字符
    private func processLatexEquation(_ equation: String) -> String {
        // 将双反斜杠替换为单反斜杠
        return equation.replacingOccurrences(of: "\\\\", with: "\\")
    }

    #if swift(>=5.7) && canImport(UIKit)
    @available(iOS 16.0, *)
    func sizeThatFits(_ proposal: ProposedViewSize, uiView: MTMathUILabel, context: Context) -> CGSize {
        let size = uiView.sizeThatFits(CGSize(width: proposal.width ?? .infinity, height: proposal.height ?? .infinity))
        return size
    }
    #endif
}
#else
struct MathViewRepresentable: NSViewRepresentable {
    var equation: String
    var font: MathFont = .latinModernFont
    var textAlignment: MTTextAlignment = .center
    var fontSize: CGFloat = 18
    var labelMode: MTMathUILabelMode = .display
    var insets: MTEdgeInsets = MTEdgeInsets()

    func makeNSView(context: Context) -> MTMathUILabel {
        let view = MTMathUILabel()
        view.contentInsets = insets
        return view
    }

    func updateNSView(_ view: MTMathUILabel, context: Context) {
        // 处理转义反斜杠
        let processedEquation = processLatexEquation(equation)

        view.latex = processedEquation
        view.font = MTFontManager().font(withName: font.rawValue, size: fontSize)
        view.textAlignment = textAlignment
        view.labelMode = labelMode
        view.textColor = MTColor(Color.primary)

        // 确保视图大小适应内容
        view.sizeToFit()
    }

    // 处理LaTeX公式中的转义字符
    private func processLatexEquation(_ equation: String) -> String {
        // 将双反斜杠替换为单反斜杠
        return equation.replacingOccurrences(of: "\\\\", with: "\\")
    }
}
#endif
