import SwiftUI

struct InlineText: View {
  @Environment(\.inlineImageProvider) private var inlineImageProvider
  @Environment(\.baseURL) private var baseURL
  @Environment(\.imageBaseURL) private var imageBaseURL
  @Environment(\.softBreakMode) private var softBreakMode
  @Environment(\.theme) private var theme

  @State private var inlineImages: [String: Image] = [:]

  private let inlines: [InlineNode]

  init(_ inlines: [InlineNode]) {
    self.inlines = inlines
  }

  var body: some View {
      // 检查是否包含数学公式
      let containsMath = inlines.contains { inline in
          if case .mathInline = inline { return true }
          return false
      }

      Group {
          if containsMath {
              // 将连续的非公式内联节点分组
              let groups = groupConsecutiveNonMathInlines(inlines)

              // 检查是否只有一个数学公式
              if groups.count == 1, case .mathGroup(let content) = groups[0] {
                  // 如果只有一个数学公式，直接使用MathView
                  MathView(latex: content, isInline: true)
                      .fixedSize(horizontal: true, vertical: false)
                      .padding(.horizontal, 2)
              } else {
                  // 如果包含多个元素，为了防止整体过长，使用ScrollView
                  ScrollView(.horizontal, showsIndicators: true) {
                      HStack(alignment: .center, spacing: 0) {
                          ForEach(0..<groups.count, id: \.self) { groupIndex in
                              let group = groups[groupIndex]

                              if case .mathGroup(let content) = group {
                                  // 渲染数学公式
                                  MathView(latex: content, isInline: true)
                                      .fixedSize(horizontal: true, vertical: false)
                                      .padding(.horizontal, 2)
                              } else if case .textGroup(let inlineNodes) = group {
                                  // 渲染连续的非公式内联节点
                                  TextStyleAttributesReader { attributes in
                                      inlineNodes.renderText(
                                          baseURL: self.baseURL,
                                          textStyles: .init(
                                              code: self.theme.code,
                                              emphasis: self.theme.emphasis,
                                              strong: self.theme.strong,
                                              strikethrough: self.theme.strikethrough,
                                              link: self.theme.link
                                          ),
                                          images: self.inlineImages,
                                          softBreakMode: self.softBreakMode,
                                          attributes: attributes
                                      )
                                  }
                              }
                          }
                      }
                  }
              }
          } else {
              // 如果不包含数学公式，直接使用TextStyleAttributesReader
              TextStyleAttributesReader { attributes in
                  inlines.renderText(
                      baseURL: self.baseURL,
                      textStyles: .init(
                          code: self.theme.code,
                          emphasis: self.theme.emphasis,
                          strong: self.theme.strong,
                          strikethrough: self.theme.strikethrough,
                          link: self.theme.link
                      ),
                      images: self.inlineImages,
                      softBreakMode: self.softBreakMode,
                      attributes: attributes
                  )
              }
          }
      }
      .textSelection(.enabled)
      .fixedSize(horizontal: false, vertical: true)
      .task(id: self.inlines) {
          self.inlineImages = (try? await self.loadInlineImages()) ?? [:]
      }
  }

  // 将连续的非公式内联节点分组
  private func groupConsecutiveNonMathInlines(_ inlines: [InlineNode]) -> [InlineGroup] {
      var groups: [InlineGroup] = []
      var currentTextNodes: [InlineNode] = []

      for inline in inlines {
          if case .mathInline(let content) = inline {
              // 如果当前有累积的文本节点，先添加到组中
              if !currentTextNodes.isEmpty {
                  groups.append(.textGroup(currentTextNodes))
                  currentTextNodes = []
              }
              // 添加公式节点
              groups.append(.mathGroup(content))
          } else {
              // 累积非公式节点
              currentTextNodes.append(inline)
          }
      }

      // 添加最后一组文本节点（如果有）
      if !currentTextNodes.isEmpty {
          groups.append(.textGroup(currentTextNodes))
      }

      return groups
  }

  // 内联节点组类型
  private enum InlineGroup {
      case textGroup([InlineNode])  // 连续的非公式内联节点
      case mathGroup(String)        // 数学公式
  }

  private func loadInlineImages() async throws -> [String: Image] {
    let images = Set(self.inlines.compactMap(\.imageData))
    guard !images.isEmpty else { return [:] }

    return try await withThrowingTaskGroup(of: (String, Image).self) { taskGroup in
      for image in images {
        guard let url = URL(string: image.source, relativeTo: self.imageBaseURL) else {
          continue
        }

        taskGroup.addTask {
          (image.source, try await self.inlineImageProvider.image(with: url, label: image.alt))
        }
      }

      var inlineImages: [String: Image] = [:]

      for try await result in taskGroup {
        inlineImages[result.0] = result.1
      }

      return inlineImages
    }
  }
}
