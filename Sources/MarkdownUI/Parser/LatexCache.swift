import Foundation

/// 用于缓存已处理过的LaTeX公式
struct LatexCache {
    /// 缓存内联公式
    static var inlineCache: [String: String] = [:]

    /// 缓存块级公式
    static var blockCache: [String: String] = [:]

    /// 缓存文本到节点的映射
    static var textToNodesCache: [String: [InlineNode]] = [:]

    /// 获取缓存的内联公式
    static func getCachedInline(_ formula: String) -> String? {
        return inlineCache[formula]
    }

    /// 缓存内联公式
    static func cacheInline(_ formula: String, processed: String) {
        inlineCache[formula] = processed
    }

    /// 获取缓存的块级公式
    static func getCachedBlock(_ formula: String) -> String? {
        return blockCache[formula]
    }

    /// 缓存块级公式
    static func cacheBlock(_ formula: String, processed: String) {
        blockCache[formula] = processed
    }

    /// 获取缓存的处理结果
    static func getCachedNodes(for text: String) -> [InlineNode]? {
        return textToNodesCache[text]
    }

    /// 缓存处理结果
    static func cacheNodes(for text: String, nodes: [InlineNode]) {
        // 只缓存包含公式的文本，避免缓存过多
        if nodes.contains(where: {
            if case .mathInline = $0 { return true }
            return false
        }) {
            textToNodesCache[text] = nodes
        }
    }

    /// 限制缓存大小
    static func limitCacheSize(maxSize: Int = 1000) {
        if textToNodesCache.count > maxSize {
            // 移除一些缓存项
            let keysToRemove = Array(textToNodesCache.keys.prefix(textToNodesCache.count - maxSize))
            for key in keysToRemove {
                textToNodesCache.removeValue(forKey: key)
            }
        }

        if inlineCache.count > maxSize {
            // 移除一些缓存项
            let keysToRemove = Array(inlineCache.keys.prefix(inlineCache.count - maxSize))
            for key in keysToRemove {
                inlineCache.removeValue(forKey: key)
            }
        }

        if blockCache.count > maxSize {
            // 移除一些缓存项
            let keysToRemove = Array(blockCache.keys.prefix(blockCache.count - maxSize))
            for key in keysToRemove {
                blockCache.removeValue(forKey: key)
            }
        }
    }

    /// 清除所有缓存
    static func clearCache() {
        inlineCache.removeAll()
        blockCache.removeAll()
        textToNodesCache.removeAll()
    }
}
