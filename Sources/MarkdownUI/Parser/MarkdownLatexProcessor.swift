import Foundation

/// 处理Markdown中的LaTeX公式
struct MarkdownLatexProcessor {
    // 预编译的正则表达式
    private static let inlineRegex = try! NSRegularExpression(pattern: #"(?<!\$)(?<!\\)\$(?!\$)((?:\\.|[^\$\\])+?)\$(?!\$)"#, options: [])
    private static let blockRegex = try! NSRegularExpression(pattern: #"(\$\$)([\s\S]*?)(\$\$)"#, options: [])

    /// 检查文本是否包含LaTeX公式
    static func containsLatex(_ text: String) -> Bool {
        return text.contains("$")
    }

    /// 处理块级节点中的LaTeX公式
    static func processLatexInBlocks(blocks: [BlockNode]) -> [BlockNode] {
        // 快速检查是否有可能包含公式
        let mightContainLatex = blocks.contains { block in
            switch block {
            case .paragraph(let content), .heading(_, let content):
                return content.contains { inline in
                    if case .text(let text) = inline, text.contains("$") {
                        return true
                    }
                    return false
                }
            default:
                // 其他需要进一步解析
                return true
            }
        }

        if !mightContainLatex {
            return blocks
        }

        return blocks.flatMap { block -> [BlockNode] in
            switch block {
            case .paragraph(let content):
                // 首先检查段落是否只包含一个块级公式
                // 方法1：检查原始文本
                let originalText = content.compactMap { node -> String? in
                    if case .text(let text) = node { return text }
                    return nil
                }.joined().trimmingCharacters(in: .whitespacesAndNewlines)

                // 检查原始文本是否只包含一个块级公式
                if originalText.hasPrefix("$$") && originalText.hasSuffix("$$") && originalText.count > 4 {
                    // 去掉$$符号
                    let blockContent = String(originalText.dropFirst(2).dropLast(2)).trimmingCharacters(in: .whitespacesAndNewlines)
                    return [.mathBlock(content: blockContent)]
                }

                // 方法2：处理段落中的LaTeX公式
                let newContent = processLatexInInlines(inlines: content)

                // 检查处理后的内容是否只有一个内联公式，且这个公式可能是从块级公式转换而来的
                if newContent.count == 1, case .mathInline(let formula) = newContent[0] {
                    // 如果公式内容看起来像是块级公式（例如，包含多行或特定的块级公式特征）
                    if formula.contains("\n") || formula.contains("\\begin{align") || formula.contains("\\begin{equation") {
                        return [.mathBlock(content: formula)]
                    }
                }

                return [.paragraph(content: newContent)]

            case .heading(let level, let content):
                // 处理标题中的LaTeX公式
                let newContent = processLatexInInlines(inlines: content)
                return [.heading(level: level, content: newContent)]

            case .table(let columnAlignments, let rows):
                // 处理表格中的LaTeX公式
                let newRows = rows.map { row -> RawTableRow in
                    let newCells = row.cells.map { cell -> RawTableCell in
                        let newContent = processLatexInInlines(inlines: cell.content)
                        return RawTableCell(content: newContent)
                    }
                    return RawTableRow(cells: newCells)
                }
                return [.table(columnAlignments: columnAlignments, rows: newRows)]

            case .blockquote(let children):
                // 处理引用块中的LaTeX公式
                let newChildren = children.flatMap { child -> [BlockNode] in
                    processLatexInBlocks(blocks: [child])
                }
                return [.blockquote(children: newChildren)]

            case .bulletedList(let isTight, let items):
                // 处理无序列表中的LaTeX公式
                let newItems = items.map { item -> RawListItem in
                    let newChildren = item.children.flatMap { child -> [BlockNode] in
                        processLatexInBlocks(blocks: [child])
                    }
                    return RawListItem(children: newChildren)
                }
                return [.bulletedList(isTight: isTight, items: newItems)]

            case .numberedList(let isTight, let start, let items):
                // 处理有序列表中的LaTeX公式
                let newItems = items.map { item -> RawListItem in
                    let newChildren = item.children.flatMap { child -> [BlockNode] in
                        processLatexInBlocks(blocks: [child])
                    }
                    return RawListItem(children: newChildren)
                }
                return [.numberedList(isTight: isTight, start: start, items: newItems)]

            case .taskList(let isTight, let items):
                // 处理任务列表中的LaTeX公式
                let newItems = items.map { item -> RawTaskListItem in
                    let newChildren = item.children.flatMap { child -> [BlockNode] in
                        processLatexInBlocks(blocks: [child])
                    }
                    return RawTaskListItem(isCompleted: item.isCompleted, children: newChildren)
                }
                return [.taskList(isTight: isTight, items: newItems)]

            case .codeBlock, .htmlBlock, .thematicBreak, .mathBlock:
                // 这些块不需要处理
                return [block]
            }
        }
    }

    /// 处理内联节点中的LaTeX公式
    static func processLatexInInlines(inlines: [InlineNode]) -> [InlineNode] {
        // 快速检查是否有可能包含公式
        let mightContainLatex = inlines.contains { inline in
            if case .text(let text) = inline, text.contains("$") {
                return true
            }
            return false
        }

        if !mightContainLatex {
            return inlines
        }

        return inlines.flatMap { inline -> [InlineNode] in
            switch inline {
            case .text(let text):
                // 处理文本中的LaTeX公式
                return processLatexInText(text)

            case .emphasis(let children):
                // 处理强调文本中的LaTeX公式
                let newChildren = processLatexInInlines(inlines: children)
                return [.emphasis(children: newChildren)]

            case .strong(let children):
                // 处理加粗文本中的LaTeX公式
                let newChildren = processLatexInInlines(inlines: children)
                return [.strong(children: newChildren)]

            case .strikethrough(let children):
                // 处理删除线文本中的LaTeX公式
                let newChildren = processLatexInInlines(inlines: children)
                return [.strikethrough(children: newChildren)]

            case .link(let destination, let children):
                // 处理链接文本中的LaTeX公式
                let newChildren = processLatexInInlines(inlines: children)
                return [.link(destination: destination, children: newChildren)]

            case .image(let source, let children):
                // 处理图片替代文本中的LaTeX公式
                let newChildren = processLatexInInlines(inlines: children)
                return [.image(source: source, children: newChildren)]

            case .code, .html, .softBreak, .lineBreak, .mathInline:
                // 这些内联节点不需要处理
                return [inline]
            }
        }
    }

    /// 处理文本中的LaTeX公式
    static func processLatexInText(_ text: String) -> [InlineNode] {
        // 快速检查是否包含公式
        if !text.contains("$") {
            return [.text(text)]
        }

        // 检查缓存
        if let cachedNodes = LatexCache.getCachedNodes(for: text) {
            return cachedNodes
        }

        var result: [InlineNode] = []

        // 使用NSString进行高效的子字符串提取
        let nsString = NSString(string: text)

        // 先查找块级公式
        let blockMatches = blockRegex.matches(in: text, options: [], range: NSRange(location: 0, length: nsString.length))

        if !blockMatches.isEmpty {
            // 处理块级公式
            var lastEndIndex = text.startIndex

            for match in blockMatches {
                let matchRange = match.range
                let formulaRange = match.range(at: 2)
                let formula = nsString.substring(with: formulaRange)

                // 添加公式前的文本
                let startIndex = text.index(text.startIndex, offsetBy: matchRange.location)
                if startIndex > lastEndIndex {
                    let prefix = String(text[lastEndIndex..<startIndex])
                    if !prefix.isEmpty {
                        // 递归处理前缀文本中的内联公式
                        result.append(contentsOf: processInlineLatexInText(prefix))
                    }
                }

                // 使用缓存处理公式
                if let cachedFormula = LatexCache.getCachedBlock(formula) {
                    // 在内联上下文中，将块级公式作为内联公式处理
                    // 但我们需要保留原始的格式信息，以便后续处理
                    result.append(.mathInline(content: cachedFormula))
                } else {
                    // 处理公式并缓存结果
                    LatexCache.cacheBlock(formula, processed: formula)
                    // 在内联上下文中，将块级公式作为内联公式处理
                    result.append(.mathInline(content: formula))
                }

                // 更新lastEndIndex
                lastEndIndex = text.index(text.startIndex, offsetBy: matchRange.location + matchRange.length)
            }

            // 添加最后一个公式后的文本
            if lastEndIndex < text.endIndex {
                let suffix = String(text[lastEndIndex..<text.endIndex])
                if !suffix.isEmpty {
                    // 递归处理后缀文本中的内联公式
                    result.append(contentsOf: processInlineLatexInText(suffix))
                }
            }

            // 缓存处理结果
            LatexCache.cacheNodes(for: text, nodes: result)

            return result
        }

        // 如果没有块级公式，处理内联公式
        let inlineResult = processInlineLatexInText(text)

        // 缓存处理结果
        LatexCache.cacheNodes(for: text, nodes: inlineResult)

        return inlineResult
    }

    /// 处理文本中的内联LaTeX公式
    static func processInlineLatexInText(_ text: String) -> [InlineNode] {
        // 如果文本为空，直接返回
        if text.isEmpty {
            return [.text(text)]
        }

        // 快速检查是否包含公式
        if !text.contains("$") {
            return [.text(text)]
        }

        var result: [InlineNode] = []

        // 使用NSString进行高效的子字符串提取
        let nsString = NSString(string: text)

        // 查找内联公式
        let inlineMatches = inlineRegex.matches(in: text, options: [], range: NSRange(location: 0, length: nsString.length))

        if !inlineMatches.isEmpty {
            // 处理内联公式
            var lastEndIndex = text.startIndex

            for match in inlineMatches {
                let matchRange = match.range
                let formulaRange = match.range(at: 1)
                let formula = nsString.substring(with: formulaRange)

                // 添加公式前的文本
                let startIndex = text.index(text.startIndex, offsetBy: matchRange.location)
                if startIndex > lastEndIndex {
                    let prefix = String(text[lastEndIndex..<startIndex])
                    if !prefix.isEmpty {
                        result.append(.text(prefix))
                    }
                }

                // 使用缓存处理公式
                if let cachedFormula = LatexCache.getCachedInline(formula) {
                    result.append(.mathInline(content: cachedFormula))
                } else {
                    // 处理公式并缓存结果
                    LatexCache.cacheInline(formula, processed: formula)
                    result.append(.mathInline(content: formula))
                }

                // 更新lastEndIndex
                lastEndIndex = text.index(text.startIndex, offsetBy: matchRange.location + matchRange.length)
            }

            // 添加最后一个公式后的文本
            if lastEndIndex < text.endIndex {
                let suffix = String(text[lastEndIndex..<text.endIndex])
                if !suffix.isEmpty {
                    result.append(.text(suffix))
                }
            }

            return result
        }

        // 如果没有内联公式，直接返回原始文本
        return [.text(text)]
    }
}
