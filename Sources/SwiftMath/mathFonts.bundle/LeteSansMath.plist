<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>Im</key>
		<integer>291</integer>
		<key>Planckconst</key>
		<integer>244</integer>
		<key>Planckconst.st</key>
		<integer>291</integer>
		<key>acutecomb</key>
		<integer>-200</integer>
		<key>annuity</key>
		<integer>-350</integer>
		<key>asteraccent</key>
		<integer>-130</integer>
		<key>bar</key>
		<integer>-200</integer>
		<key>candra</key>
		<integer>-200</integer>
		<key>cdot</key>
		<integer>143</integer>
		<key>cdotp</key>
		<integer>126</integer>
		<key>coprod</key>
		<integer>440</integer>
		<key>coprod.display</key>
		<integer>693</integer>
		<key>ddddot</key>
		<integer>-252</integer>
		<key>ddddot.st</key>
		<integer>-252</integer>
		<key>dddot</key>
		<integer>-214</integer>
		<key>dddot.st</key>
		<integer>-214</integer>
		<key>ddot</key>
		<integer>-200</integer>
		<key>dot</key>
		<integer>-200</integer>
		<key>dotlessi</key>
		<integer>120</integer>
		<key>dotlessj</key>
		<integer>120</integer>
		<key>droang</key>
		<integer>-200</integer>
		<key>ell</key>
		<integer>352</integer>
		<key>gravecomb</key>
		<integer>-200</integer>
		<key>hslash</key>
		<integer>247</integer>
		<key>imath</key>
		<integer>167</integer>
		<key>imath.st</key>
		<integer>166</integer>
		<key>jmath</key>
		<integer>245</integer>
		<key>jmath.st</key>
		<integer>186</integer>
		<key>mathunderbar</key>
		<integer>-250</integer>
		<key>mbfA</key>
		<integer>347</integer>
		<key>mbfAlpha</key>
		<integer>347</integer>
		<key>mbfB</key>
		<integer>300</integer>
		<key>mbfBeta</key>
		<integer>300</integer>
		<key>mbfC</key>
		<integer>406</integer>
		<key>mbfChi</key>
		<integer>335</integer>
		<key>mbfD</key>
		<integer>340</integer>
		<key>mbfDelta</key>
		<integer>350</integer>
		<key>mbfE</key>
		<integer>316</integer>
		<key>mbfEpsilon</key>
		<integer>316</integer>
		<key>mbfEta</key>
		<integer>386</integer>
		<key>mbfF</key>
		<integer>314</integer>
		<key>mbfG</key>
		<integer>412</integer>
		<key>mbfGamma</key>
		<integer>255</integer>
		<key>mbfH</key>
		<integer>386</integer>
		<key>mbfI</key>
		<integer>170</integer>
		<key>mbfIota</key>
		<integer>170</integer>
		<key>mbfJ</key>
		<integer>288</integer>
		<key>mbfK</key>
		<integer>338</integer>
		<key>mbfKappa</key>
		<integer>338</integer>
		<key>mbfL</key>
		<integer>157</integer>
		<key>mbfLambda</key>
		<integer>346</integer>
		<key>mbfM</key>
		<integer>473</integer>
		<key>mbfMu</key>
		<integer>473</integer>
		<key>mbfN</key>
		<integer>390</integer>
		<key>mbfNu</key>
		<integer>390</integer>
		<key>mbfO</key>
		<integer>405</integer>
		<key>mbfOmega</key>
		<integer>408</integer>
		<key>mbfOmicron</key>
		<integer>405</integer>
		<key>mbfP</key>
		<integer>304</integer>
		<key>mbfPhi</key>
		<integer>426</integer>
		<key>mbfPi</key>
		<integer>386</integer>
		<key>mbfPsi</key>
		<integer>416</integer>
		<key>mbfQ</key>
		<integer>404</integer>
		<key>mbfR</key>
		<integer>308</integer>
		<key>mbfRho</key>
		<integer>308</integer>
		<key>mbfS</key>
		<integer>296</integer>
		<key>mbfSigma</key>
		<integer>278</integer>
		<key>mbfT</key>
		<integer>302</integer>
		<key>mbfTau</key>
		<integer>302</integer>
		<key>mbfTheta</key>
		<integer>404</integer>
		<key>mbfU</key>
		<integer>372</integer>
		<key>mbfUpsilon</key>
		<integer>325</integer>
		<key>mbfV</key>
		<integer>348</integer>
		<key>mbfW</key>
		<integer>525</integer>
		<key>mbfX</key>
		<integer>335</integer>
		<key>mbfXi</key>
		<integer>312</integer>
		<key>mbfY</key>
		<integer>325</integer>
		<key>mbfZ</key>
		<integer>313</integer>
		<key>mbfZeta</key>
		<integer>313</integer>
		<key>mbfa</key>
		<integer>256</integer>
		<key>mbfalpha</key>
		<integer>263</integer>
		<key>mbfb</key>
		<integer>227</integer>
		<key>mbfbeta</key>
		<integer>282</integer>
		<key>mbfc</key>
		<integer>261</integer>
		<key>mbfchi</key>
		<integer>238</integer>
		<key>mbfd</key>
		<integer>346</integer>
		<key>mbfdelta</key>
		<integer>274</integer>
		<key>mbfe</key>
		<integer>283</integer>
		<key>mbfepsilon</key>
		<integer>270</integer>
		<key>mbfeta</key>
		<integer>263</integer>
		<key>mbff</key>
		<integer>246</integer>
		<key>mbfg</key>
		<integer>252</integer>
		<key>mbfgamma</key>
		<integer>286</integer>
		<key>mbfh</key>
		<integer>228</integer>
		<key>mbfi</key>
		<integer>126</integer>
		<key>mbfi.dtls</key>
		<integer>128</integer>
		<key>mbfiota</key>
		<integer>120</integer>
		<key>mbfitA</key>
		<integer>456</integer>
		<key>mbfitAlpha</key>
		<integer>454</integer>
		<key>mbfitB</key>
		<integer>346</integer>
		<key>mbfitBeta</key>
		<integer>346</integer>
		<key>mbfitC</key>
		<integer>443</integer>
		<key>mbfitChi</key>
		<integer>429</integer>
		<key>mbfitD</key>
		<integer>382</integer>
		<key>mbfitDelta</key>
		<integer>472</integer>
		<key>mbfitE</key>
		<integer>350</integer>
		<key>mbfitEpsilon</key>
		<integer>334</integer>
		<key>mbfitEta</key>
		<integer>404</integer>
		<key>mbfitF</key>
		<integer>348</integer>
		<key>mbfitG</key>
		<integer>408</integer>
		<key>mbfitGamma</key>
		<integer>308</integer>
		<key>mbfitH</key>
		<integer>425</integer>
		<key>mbfitI</key>
		<integer>271</integer>
		<key>mbfitIota</key>
		<integer>262</integer>
		<key>mbfitJ</key>
		<integer>338</integer>
		<key>mbfitK</key>
		<integer>364</integer>
		<key>mbfitKappa</key>
		<integer>358</integer>
		<key>mbfitL</key>
		<integer>214</integer>
		<key>mbfitLambda</key>
		<integer>464</integer>
		<key>mbfitM</key>
		<integer>485</integer>
		<key>mbfitMu</key>
		<integer>504</integer>
		<key>mbfitN</key>
		<integer>414</integer>
		<key>mbfitNu</key>
		<integer>403</integer>
		<key>mbfitO</key>
		<integer>402</integer>
		<key>mbfitOmega</key>
		<integer>416</integer>
		<key>mbfitOmicron</key>
		<integer>445</integer>
		<key>mbfitP</key>
		<integer>325</integer>
		<key>mbfitPhi</key>
		<integer>464</integer>
		<key>mbfitPi</key>
		<integer>410</integer>
		<key>mbfitPsi</key>
		<integer>428</integer>
		<key>mbfitQ</key>
		<integer>414</integer>
		<key>mbfitR</key>
		<integer>349</integer>
		<key>mbfitRho</key>
		<integer>344</integer>
		<key>mbfitS</key>
		<integer>334</integer>
		<key>mbfitSigma</key>
		<integer>366</integer>
		<key>mbfitT</key>
		<integer>295</integer>
		<key>mbfitTau</key>
		<integer>294</integer>
		<key>mbfitTheta</key>
		<integer>417</integer>
		<key>mbfitU</key>
		<integer>386</integer>
		<key>mbfitUpsilon</key>
		<integer>324</integer>
		<key>mbfitV</key>
		<integer>333</integer>
		<key>mbfitW</key>
		<integer>504</integer>
		<key>mbfitX</key>
		<integer>424</integer>
		<key>mbfitXi</key>
		<integer>397</integer>
		<key>mbfitY</key>
		<integer>327</integer>
		<key>mbfitZ</key>
		<integer>392</integer>
		<key>mbfitZeta</key>
		<integer>384</integer>
		<key>mbfita</key>
		<integer>311</integer>
		<key>mbfitalpha</key>
		<integer>309</integer>
		<key>mbfitb</key>
		<integer>248</integer>
		<key>mbfitbeta</key>
		<integer>328</integer>
		<key>mbfitc</key>
		<integer>275</integer>
		<key>mbfitchi</key>
		<integer>330</integer>
		<key>mbfitd</key>
		<integer>378</integer>
		<key>mbfitdelta</key>
		<integer>317</integer>
		<key>mbfite</key>
		<integer>294</integer>
		<key>mbfitepsilon</key>
		<integer>292</integer>
		<key>mbfiteta</key>
		<integer>298</integer>
		<key>mbfitf</key>
		<integer>262</integer>
		<key>mbfitg</key>
		<integer>298</integer>
		<key>mbfitg.var</key>
		<integer>316</integer>
		<key>mbfitgamma</key>
		<integer>304</integer>
		<key>mbfith</key>
		<integer>278</integer>
		<key>mbfithbar</key>
		<integer>266</integer>
		<key>mbfithslash</key>
		<integer>276</integer>
		<key>mbfiti</key>
		<integer>189</integer>
		<key>mbfiti.dtls</key>
		<integer>192</integer>
		<key>mbfitiota</key>
		<integer>157</integer>
		<key>mbfitj</key>
		<integer>237</integer>
		<key>mbfitj.dtls</key>
		<integer>255</integer>
		<key>mbfitk</key>
		<integer>243</integer>
		<key>mbfitkappa</key>
		<integer>292</integer>
		<key>mbfitl</key>
		<integer>202</integer>
		<key>mbfitlambda</key>
		<integer>320</integer>
		<key>mbfitm</key>
		<integer>465</integer>
		<key>mbfitmu</key>
		<integer>319</integer>
		<key>mbfitn</key>
		<integer>312</integer>
		<key>mbfitnabla</key>
		<integer>388</integer>
		<key>mbfitnu</key>
		<integer>284</integer>
		<key>mbfito</key>
		<integer>306</integer>
		<key>mbfitomega</key>
		<integer>402</integer>
		<key>mbfitomicron</key>
		<integer>309</integer>
		<key>mbfitp</key>
		<integer>328</integer>
		<key>mbfitpartial</key>
		<integer>336</integer>
		<key>mbfitphi</key>
		<integer>430</integer>
		<key>mbfitpi</key>
		<integer>373</integer>
		<key>mbfitpsi</key>
		<integer>423</integer>
		<key>mbfitq</key>
		<integer>319</integer>
		<key>mbfitr</key>
		<integer>235</integer>
		<key>mbfitrho</key>
		<integer>326</integer>
		<key>mbfits</key>
		<integer>281</integer>
		<key>mbfitsigma</key>
		<integer>340</integer>
		<key>mbfitt</key>
		<integer>192</integer>
		<key>mbfittau</key>
		<integer>274</integer>
		<key>mbfittheta</key>
		<integer>313</integer>
		<key>mbfitu</key>
		<integer>308</integer>
		<key>mbfitupsilon</key>
		<integer>282</integer>
		<key>mbfitv</key>
		<integer>267</integer>
		<key>mbfitvarTheta</key>
		<integer>411</integer>
		<key>mbfitvarepsilon</key>
		<integer>266</integer>
		<key>mbfitvarkappa</key>
		<integer>348</integer>
		<key>mbfitvarphi</key>
		<integer>403</integer>
		<key>mbfitvarpi</key>
		<integer>453</integer>
		<key>mbfitvarrho</key>
		<integer>326</integer>
		<key>mbfitvarsigma</key>
		<integer>280</integer>
		<key>mbfitvartheta</key>
		<integer>362</integer>
		<key>mbfitw</key>
		<integer>383</integer>
		<key>mbfitx</key>
		<integer>333</integer>
		<key>mbfitxi</key>
		<integer>283</integer>
		<key>mbfity</key>
		<integer>269</integer>
		<key>mbfitz</key>
		<integer>274</integer>
		<key>mbfitzeta</key>
		<integer>285</integer>
		<key>mbfj</key>
		<integer>123</integer>
		<key>mbfj.dtls</key>
		<integer>127</integer>
		<key>mbfk</key>
		<integer>232</integer>
		<key>mbfkappa</key>
		<integer>258</integer>
		<key>mbfl</key>
		<integer>136</integer>
		<key>mbflambda</key>
		<integer>236</integer>
		<key>mbfm</key>
		<integer>440</integer>
		<key>mbfmu</key>
		<integer>285</integer>
		<key>mbfn</key>
		<integer>298</integer>
		<key>mbfnabla</key>
		<integer>354</integer>
		<key>mbfnu</key>
		<integer>264</integer>
		<key>mbfo</key>
		<integer>290</integer>
		<key>mbfomega</key>
		<integer>395</integer>
		<key>mbfomicron</key>
		<integer>288</integer>
		<key>mbfp</key>
		<integer>272</integer>
		<key>mbfpartial</key>
		<integer>285</integer>
		<key>mbfphi</key>
		<integer>385</integer>
		<key>mbfpi</key>
		<integer>318</integer>
		<key>mbfpsi</key>
		<integer>392</integer>
		<key>mbfq</key>
		<integer>285</integer>
		<key>mbfr</key>
		<integer>231</integer>
		<key>mbfrho</key>
		<integer>288</integer>
		<key>mbfs</key>
		<integer>233</integer>
		<key>mbfscrA</key>
		<integer>573</integer>
		<key>mbfscrB</key>
		<integer>420</integer>
		<key>mbfscrC</key>
		<integer>290</integer>
		<key>mbfscrD</key>
		<integer>494</integer>
		<key>mbfscrE</key>
		<integer>365</integer>
		<key>mbfscrF</key>
		<integer>527</integer>
		<key>mbfscrG</key>
		<integer>379</integer>
		<key>mbfscrH</key>
		<integer>505</integer>
		<key>mbfscrI</key>
		<integer>377</integer>
		<key>mbfscrJ</key>
		<integer>596</integer>
		<key>mbfscrK</key>
		<integer>434</integer>
		<key>mbfscrL</key>
		<integer>463</integer>
		<key>mbfscrM</key>
		<integer>686</integer>
		<key>mbfscrN</key>
		<integer>545</integer>
		<key>mbfscrO</key>
		<integer>471</integer>
		<key>mbfscrP</key>
		<integer>434</integer>
		<key>mbfscrQ</key>
		<integer>478</integer>
		<key>mbfscrR</key>
		<integer>424</integer>
		<key>mbfscrS</key>
		<integer>424</integer>
		<key>mbfscrT</key>
		<integer>444</integer>
		<key>mbfscrU</key>
		<integer>415</integer>
		<key>mbfscrV</key>
		<integer>348</integer>
		<key>mbfscrW</key>
		<integer>579</integer>
		<key>mbfscrX</key>
		<integer>513</integer>
		<key>mbfscrY</key>
		<integer>426</integer>
		<key>mbfscrZ</key>
		<integer>471</integer>
		<key>mbfsigma</key>
		<integer>309</integer>
		<key>mbft</key>
		<integer>176</integer>
		<key>mbftau</key>
		<integer>234</integer>
		<key>mbftheta</key>
		<integer>302</integer>
		<key>mbfu</key>
		<integer>279</integer>
		<key>mbfupsilon</key>
		<integer>274</integer>
		<key>mbfv</key>
		<integer>268</integer>
		<key>mbfvarTheta</key>
		<integer>404</integer>
		<key>mbfvarepsilon</key>
		<integer>230</integer>
		<key>mbfvarkappa</key>
		<integer>294</integer>
		<key>mbfvarphi</key>
		<integer>390</integer>
		<key>mbfvarpi</key>
		<integer>450</integer>
		<key>mbfvarrho</key>
		<integer>288</integer>
		<key>mbfvarsigma</key>
		<integer>245</integer>
		<key>mbfvartheta</key>
		<integer>324</integer>
		<key>mbfw</key>
		<integer>407</integer>
		<key>mbfx</key>
		<integer>262</integer>
		<key>mbfxi</key>
		<integer>229</integer>
		<key>mbfy</key>
		<integer>277</integer>
		<key>mbfz</key>
		<integer>234</integer>
		<key>mbfzeta</key>
		<integer>232</integer>
		<key>mitA</key>
		<integer>441</integer>
		<key>mitA.sst</key>
		<integer>441</integer>
		<key>mitA.st</key>
		<integer>441</integer>
		<key>mitAlpha</key>
		<integer>456</integer>
		<key>mitAlpha.sst</key>
		<integer>456</integer>
		<key>mitAlpha.st</key>
		<integer>456</integer>
		<key>mitB</key>
		<integer>332</integer>
		<key>mitB.sst</key>
		<integer>332</integer>
		<key>mitB.st</key>
		<integer>332</integer>
		<key>mitBeta</key>
		<integer>332</integer>
		<key>mitBeta.sst</key>
		<integer>332</integer>
		<key>mitBeta.st</key>
		<integer>332</integer>
		<key>mitC</key>
		<integer>389</integer>
		<key>mitC.sst</key>
		<integer>389</integer>
		<key>mitC.st</key>
		<integer>389</integer>
		<key>mitChi</key>
		<integer>396</integer>
		<key>mitChi.sst</key>
		<integer>396</integer>
		<key>mitChi.st</key>
		<integer>396</integer>
		<key>mitD</key>
		<integer>373</integer>
		<key>mitD.sst</key>
		<integer>373</integer>
		<key>mitD.st</key>
		<integer>373</integer>
		<key>mitDelta</key>
		<integer>451</integer>
		<key>mitDelta.sst</key>
		<integer>451</integer>
		<key>mitDelta.st</key>
		<integer>451</integer>
		<key>mitE</key>
		<integer>333</integer>
		<key>mitE.sst</key>
		<integer>333</integer>
		<key>mitE.st</key>
		<integer>333</integer>
		<key>mitEpsilon</key>
		<integer>332</integer>
		<key>mitEpsilon.sst</key>
		<integer>332</integer>
		<key>mitEpsilon.st</key>
		<integer>332</integer>
		<key>mitEta</key>
		<integer>412</integer>
		<key>mitEta.sst</key>
		<integer>412</integer>
		<key>mitEta.st</key>
		<integer>412</integer>
		<key>mitF</key>
		<integer>349</integer>
		<key>mitF.sst</key>
		<integer>349</integer>
		<key>mitF.st</key>
		<integer>349</integer>
		<key>mitG</key>
		<integer>409</integer>
		<key>mitG.sst</key>
		<integer>409</integer>
		<key>mitG.st</key>
		<integer>409</integer>
		<key>mitGamma</key>
		<integer>288</integer>
		<key>mitGamma.sst</key>
		<integer>288</integer>
		<key>mitGamma.st</key>
		<integer>288</integer>
		<key>mitH</key>
		<integer>396</integer>
		<key>mitH.sst</key>
		<integer>396</integer>
		<key>mitH.st</key>
		<integer>396</integer>
		<key>mitI</key>
		<integer>282</integer>
		<key>mitI.sst</key>
		<integer>282</integer>
		<key>mitI.st</key>
		<integer>282</integer>
		<key>mitIota</key>
		<integer>259</integer>
		<key>mitIota.sst</key>
		<integer>259</integer>
		<key>mitIota.st</key>
		<integer>259</integer>
		<key>mitJ</key>
		<integer>285</integer>
		<key>mitJ.sst</key>
		<integer>285</integer>
		<key>mitJ.st</key>
		<integer>285</integer>
		<key>mitK</key>
		<integer>345</integer>
		<key>mitK.sst</key>
		<integer>345</integer>
		<key>mitK.st</key>
		<integer>345</integer>
		<key>mitKappa</key>
		<integer>338</integer>
		<key>mitKappa.sst</key>
		<integer>338</integer>
		<key>mitKappa.st</key>
		<integer>338</integer>
		<key>mitL</key>
		<integer>213</integer>
		<key>mitL.sst</key>
		<integer>213</integer>
		<key>mitL.st</key>
		<integer>213</integer>
		<key>mitLambda</key>
		<integer>446</integer>
		<key>mitLambda.sst</key>
		<integer>446</integer>
		<key>mitLambda.st</key>
		<integer>446</integer>
		<key>mitM</key>
		<integer>487</integer>
		<key>mitM.sst</key>
		<integer>487</integer>
		<key>mitM.st</key>
		<integer>487</integer>
		<key>mitMu</key>
		<integer>474</integer>
		<key>mitMu.sst</key>
		<integer>474</integer>
		<key>mitMu.st</key>
		<integer>474</integer>
		<key>mitN</key>
		<integer>417</integer>
		<key>mitN.sst</key>
		<integer>417</integer>
		<key>mitN.st</key>
		<integer>417</integer>
		<key>mitNu</key>
		<integer>406</integer>
		<key>mitNu.sst</key>
		<integer>406</integer>
		<key>mitNu.st</key>
		<integer>406</integer>
		<key>mitO</key>
		<integer>408</integer>
		<key>mitO.sst</key>
		<integer>408</integer>
		<key>mitO.st</key>
		<integer>408</integer>
		<key>mitOmega</key>
		<integer>462</integer>
		<key>mitOmega.sst</key>
		<integer>462</integer>
		<key>mitOmega.st</key>
		<integer>462</integer>
		<key>mitOmicron</key>
		<integer>429</integer>
		<key>mitOmicron.sst</key>
		<integer>429</integer>
		<key>mitOmicron.st</key>
		<integer>429</integer>
		<key>mitP</key>
		<integer>291</integer>
		<key>mitP.sst</key>
		<integer>291</integer>
		<key>mitP.st</key>
		<integer>291</integer>
		<key>mitPhi</key>
		<integer>458</integer>
		<key>mitPhi.sst</key>
		<integer>458</integer>
		<key>mitPhi.st</key>
		<integer>458</integer>
		<key>mitPi</key>
		<integer>398</integer>
		<key>mitPi.sst</key>
		<integer>398</integer>
		<key>mitPi.st</key>
		<integer>398</integer>
		<key>mitPsi</key>
		<integer>414</integer>
		<key>mitPsi.sst</key>
		<integer>414</integer>
		<key>mitPsi.st</key>
		<integer>414</integer>
		<key>mitQ</key>
		<integer>409</integer>
		<key>mitQ.sst</key>
		<integer>409</integer>
		<key>mitQ.st</key>
		<integer>409</integer>
		<key>mitR</key>
		<integer>325</integer>
		<key>mitR.sst</key>
		<integer>325</integer>
		<key>mitR.st</key>
		<integer>325</integer>
		<key>mitRho</key>
		<integer>321</integer>
		<key>mitRho.sst</key>
		<integer>321</integer>
		<key>mitRho.st</key>
		<integer>321</integer>
		<key>mitS</key>
		<integer>308</integer>
		<key>mitS.sst</key>
		<integer>308</integer>
		<key>mitS.st</key>
		<integer>308</integer>
		<key>mitSigma</key>
		<integer>357</integer>
		<key>mitSigma.sst</key>
		<integer>357</integer>
		<key>mitSigma.st</key>
		<integer>357</integer>
		<key>mitT</key>
		<integer>296</integer>
		<key>mitT.sst</key>
		<integer>296</integer>
		<key>mitT.st</key>
		<integer>296</integer>
		<key>mitTau</key>
		<integer>296</integer>
		<key>mitTau.sst</key>
		<integer>296</integer>
		<key>mitTau.st</key>
		<integer>296</integer>
		<key>mitTheta</key>
		<integer>409</integer>
		<key>mitTheta.sst</key>
		<integer>409</integer>
		<key>mitTheta.st</key>
		<integer>409</integer>
		<key>mitU</key>
		<integer>331</integer>
		<key>mitU.sst</key>
		<integer>331</integer>
		<key>mitU.st</key>
		<integer>331</integer>
		<key>mitUpsilon</key>
		<integer>326</integer>
		<key>mitUpsilon.sst</key>
		<integer>326</integer>
		<key>mitUpsilon.st</key>
		<integer>326</integer>
		<key>mitV</key>
		<integer>342</integer>
		<key>mitV.sst</key>
		<integer>342</integer>
		<key>mitV.st</key>
		<integer>342</integer>
		<key>mitW</key>
		<integer>493</integer>
		<key>mitW.sst</key>
		<integer>493</integer>
		<key>mitW.st</key>
		<integer>493</integer>
		<key>mitX</key>
		<integer>397</integer>
		<key>mitX.sst</key>
		<integer>397</integer>
		<key>mitX.st</key>
		<integer>397</integer>
		<key>mitXi</key>
		<integer>354</integer>
		<key>mitXi.sst</key>
		<integer>354</integer>
		<key>mitXi.st</key>
		<integer>354</integer>
		<key>mitY</key>
		<integer>308</integer>
		<key>mitY.sst</key>
		<integer>308</integer>
		<key>mitY.st</key>
		<integer>308</integer>
		<key>mitZ</key>
		<integer>381</integer>
		<key>mitZ.sst</key>
		<integer>381</integer>
		<key>mitZ.st</key>
		<integer>381</integer>
		<key>mitZeta</key>
		<integer>377</integer>
		<key>mitZeta.sst</key>
		<integer>377</integer>
		<key>mitZeta.st</key>
		<integer>377</integer>
		<key>mita</key>
		<integer>285</integer>
		<key>mita.sst</key>
		<integer>285</integer>
		<key>mita.st</key>
		<integer>285</integer>
		<key>mitalpha</key>
		<integer>302</integer>
		<key>mitalpha.sst</key>
		<integer>302</integer>
		<key>mitalpha.st</key>
		<integer>302</integer>
		<key>mitb</key>
		<integer>274</integer>
		<key>mitb.sst</key>
		<integer>274</integer>
		<key>mitb.st</key>
		<integer>274</integer>
		<key>mitbeta</key>
		<integer>326</integer>
		<key>mitbeta.sst</key>
		<integer>326</integer>
		<key>mitbeta.st</key>
		<integer>326</integer>
		<key>mitc</key>
		<integer>261</integer>
		<key>mitc.sst</key>
		<integer>261</integer>
		<key>mitc.st</key>
		<integer>261</integer>
		<key>mitchi</key>
		<integer>302</integer>
		<key>mitchi.sst</key>
		<integer>302</integer>
		<key>mitchi.st</key>
		<integer>302</integer>
		<key>mitd</key>
		<integer>325</integer>
		<key>mitd.sst</key>
		<integer>325</integer>
		<key>mitd.st</key>
		<integer>325</integer>
		<key>mitdelta</key>
		<integer>324</integer>
		<key>mitdelta.sst</key>
		<integer>324</integer>
		<key>mitdelta.st</key>
		<integer>324</integer>
		<key>mite</key>
		<integer>277</integer>
		<key>mite.sst</key>
		<integer>277</integer>
		<key>mite.st</key>
		<integer>277</integer>
		<key>mitepsilon</key>
		<integer>250</integer>
		<key>mitepsilon.sst</key>
		<integer>250</integer>
		<key>mitepsilon.st</key>
		<integer>250</integer>
		<key>miteta</key>
		<integer>286</integer>
		<key>miteta.sst</key>
		<integer>286</integer>
		<key>miteta.st</key>
		<integer>286</integer>
		<key>mitf</key>
		<integer>266</integer>
		<key>mitf.sst</key>
		<integer>266</integer>
		<key>mitf.st</key>
		<integer>266</integer>
		<key>mitg</key>
		<integer>294</integer>
		<key>mitg.sst</key>
		<integer>294</integer>
		<key>mitg.st</key>
		<integer>294</integer>
		<key>mitg.var</key>
		<integer>316</integer>
		<key>mitg.var.sst</key>
		<integer>314</integer>
		<key>mitg.var.st</key>
		<integer>308</integer>
		<key>mitgamma</key>
		<integer>300</integer>
		<key>mitgamma.sst</key>
		<integer>300</integer>
		<key>mitgamma.st</key>
		<integer>300</integer>
		<key>mithbar</key>
		<integer>211</integer>
		<key>mithbar.sst</key>
		<integer>211</integer>
		<key>mithbar.st</key>
		<integer>239</integer>
		<key>mithslash.st</key>
		<integer>268</integer>
		<key>miti</key>
		<integer>167</integer>
		<key>miti.sst</key>
		<integer>167</integer>
		<key>miti.st</key>
		<integer>167</integer>
		<key>mitiota</key>
		<integer>137</integer>
		<key>mitiota.sst</key>
		<integer>137</integer>
		<key>mitiota.st</key>
		<integer>137</integer>
		<key>mitj</key>
		<integer>248</integer>
		<key>mitj.sst</key>
		<integer>248</integer>
		<key>mitj.st</key>
		<integer>248</integer>
		<key>mitk</key>
		<integer>256</integer>
		<key>mitk.sst</key>
		<integer>256</integer>
		<key>mitk.st</key>
		<integer>256</integer>
		<key>mitkappa</key>
		<integer>269</integer>
		<key>mitkappa.sst</key>
		<integer>269</integer>
		<key>mitkappa.st</key>
		<integer>269</integer>
		<key>mitl</key>
		<integer>185</integer>
		<key>mitl.sst</key>
		<integer>185</integer>
		<key>mitl.st</key>
		<integer>185</integer>
		<key>mitlambda</key>
		<integer>276</integer>
		<key>mitlambda.sst</key>
		<integer>276</integer>
		<key>mitlambda.st</key>
		<integer>276</integer>
		<key>mitm</key>
		<integer>444</integer>
		<key>mitm.sst</key>
		<integer>444</integer>
		<key>mitm.st</key>
		<integer>444</integer>
		<key>mitmu</key>
		<integer>310</integer>
		<key>mitmu.sst</key>
		<integer>310</integer>
		<key>mitmu.st</key>
		<integer>310</integer>
		<key>mitn</key>
		<integer>314</integer>
		<key>mitn.sst</key>
		<integer>314</integer>
		<key>mitn.st</key>
		<integer>314</integer>
		<key>mitnabla</key>
		<integer>367</integer>
		<key>mitnabla.sst</key>
		<integer>367</integer>
		<key>mitnabla.st</key>
		<integer>367</integer>
		<key>mitnu</key>
		<integer>262</integer>
		<key>mitnu.sst</key>
		<integer>262</integer>
		<key>mitnu.st</key>
		<integer>262</integer>
		<key>mito</key>
		<integer>318</integer>
		<key>mito.sst</key>
		<integer>318</integer>
		<key>mito.st</key>
		<integer>318</integer>
		<key>mitomega</key>
		<integer>370</integer>
		<key>mitomega.sst</key>
		<integer>370</integer>
		<key>mitomega.st</key>
		<integer>370</integer>
		<key>mitomicron</key>
		<integer>299</integer>
		<key>mitomicron.sst</key>
		<integer>299</integer>
		<key>mitomicron.st</key>
		<integer>299</integer>
		<key>mitp</key>
		<integer>292</integer>
		<key>mitp.sst</key>
		<integer>292</integer>
		<key>mitp.st</key>
		<integer>292</integer>
		<key>mitpartial</key>
		<integer>328</integer>
		<key>mitpartial.sst</key>
		<integer>328</integer>
		<key>mitpartial.st</key>
		<integer>328</integer>
		<key>mitphi</key>
		<integer>371</integer>
		<key>mitphi.sst</key>
		<integer>371</integer>
		<key>mitphi.st</key>
		<integer>371</integer>
		<key>mitpi</key>
		<integer>341</integer>
		<key>mitpi.sst</key>
		<integer>341</integer>
		<key>mitpi.st</key>
		<integer>341</integer>
		<key>mitpsi</key>
		<integer>407</integer>
		<key>mitpsi.sst</key>
		<integer>407</integer>
		<key>mitpsi.st</key>
		<integer>407</integer>
		<key>mitq</key>
		<integer>354</integer>
		<key>mitq.sst</key>
		<integer>354</integer>
		<key>mitq.st</key>
		<integer>354</integer>
		<key>mitr</key>
		<integer>245</integer>
		<key>mitr.sst</key>
		<integer>245</integer>
		<key>mitr.st</key>
		<integer>245</integer>
		<key>mitrho</key>
		<integer>302</integer>
		<key>mitrho.sst</key>
		<integer>302</integer>
		<key>mitrho.st</key>
		<integer>302</integer>
		<key>mits</key>
		<integer>270</integer>
		<key>mits.sst</key>
		<integer>270</integer>
		<key>mits.st</key>
		<integer>270</integer>
		<key>mitsigma</key>
		<integer>386</integer>
		<key>mitsigma.sst</key>
		<integer>386</integer>
		<key>mitsigma.st</key>
		<integer>386</integer>
		<key>mitt</key>
		<integer>201</integer>
		<key>mitt.sst</key>
		<integer>201</integer>
		<key>mitt.st</key>
		<integer>201</integer>
		<key>mittau</key>
		<integer>261</integer>
		<key>mittau.sst</key>
		<integer>261</integer>
		<key>mittau.st</key>
		<integer>261</integer>
		<key>mittheta</key>
		<integer>313</integer>
		<key>mittheta.sst</key>
		<integer>313</integer>
		<key>mittheta.st</key>
		<integer>313</integer>
		<key>mitu</key>
		<integer>297</integer>
		<key>mitu.sst</key>
		<integer>297</integer>
		<key>mitu.st</key>
		<integer>297</integer>
		<key>mitupsilon</key>
		<integer>271</integer>
		<key>mitupsilon.sst</key>
		<integer>271</integer>
		<key>mitupsilon.st</key>
		<integer>271</integer>
		<key>mitv</key>
		<integer>259</integer>
		<key>mitv.sst</key>
		<integer>259</integer>
		<key>mitv.st</key>
		<integer>259</integer>
		<key>mitvarTheta</key>
		<integer>407</integer>
		<key>mitvarTheta.sst</key>
		<integer>407</integer>
		<key>mitvarTheta.st</key>
		<integer>407</integer>
		<key>mitvarepsilon</key>
		<integer>274</integer>
		<key>mitvarepsilon.sst</key>
		<integer>274</integer>
		<key>mitvarepsilon.st</key>
		<integer>274</integer>
		<key>mitvarkappa</key>
		<integer>318</integer>
		<key>mitvarkappa.sst</key>
		<integer>318</integer>
		<key>mitvarkappa.st</key>
		<integer>318</integer>
		<key>mitvarphi</key>
		<integer>379</integer>
		<key>mitvarphi.sst</key>
		<integer>379</integer>
		<key>mitvarphi.st</key>
		<integer>379</integer>
		<key>mitvarpi</key>
		<integer>427</integer>
		<key>mitvarpi.sst</key>
		<integer>427</integer>
		<key>mitvarpi.st</key>
		<integer>427</integer>
		<key>mitvarrho</key>
		<integer>306</integer>
		<key>mitvarrho.sst</key>
		<integer>306</integer>
		<key>mitvarrho.st</key>
		<integer>306</integer>
		<key>mitvarsigma</key>
		<integer>268</integer>
		<key>mitvarsigma.sst</key>
		<integer>268</integer>
		<key>mitvarsigma.st</key>
		<integer>268</integer>
		<key>mitvartheta</key>
		<integer>325</integer>
		<key>mitvartheta.sst</key>
		<integer>325</integer>
		<key>mitvartheta.st</key>
		<integer>325</integer>
		<key>mitw</key>
		<integer>379</integer>
		<key>mitw.sst</key>
		<integer>379</integer>
		<key>mitw.st</key>
		<integer>379</integer>
		<key>mitx</key>
		<integer>296</integer>
		<key>mitx.sst</key>
		<integer>314</integer>
		<key>mitx.st</key>
		<integer>314</integer>
		<key>mitxi</key>
		<integer>274</integer>
		<key>mitxi.sst</key>
		<integer>274</integer>
		<key>mitxi.st</key>
		<integer>274</integer>
		<key>mity</key>
		<integer>259</integer>
		<key>mitz</key>
		<integer>289</integer>
		<key>mitz.sst</key>
		<integer>289</integer>
		<key>mitz.st</key>
		<integer>289</integer>
		<key>mitzeta</key>
		<integer>278</integer>
		<key>mitzeta.sst</key>
		<integer>278</integer>
		<key>mitzeta.st</key>
		<integer>278</integer>
		<key>mscrA</key>
		<integer>581</integer>
		<key>mscrA.sst</key>
		<integer>581</integer>
		<key>mscrA.st</key>
		<integer>581</integer>
		<key>mscrB</key>
		<integer>421</integer>
		<key>mscrB.sst</key>
		<integer>421</integer>
		<key>mscrB.st</key>
		<integer>421</integer>
		<key>mscrC</key>
		<integer>348</integer>
		<key>mscrC.sst</key>
		<integer>348</integer>
		<key>mscrC.st</key>
		<integer>348</integer>
		<key>mscrD</key>
		<integer>482</integer>
		<key>mscrD.sst</key>
		<integer>482</integer>
		<key>mscrD.st</key>
		<integer>482</integer>
		<key>mscrE</key>
		<integer>380</integer>
		<key>mscrE.sst</key>
		<integer>380</integer>
		<key>mscrE.st</key>
		<integer>380</integer>
		<key>mscrF</key>
		<integer>540</integer>
		<key>mscrF.sst</key>
		<integer>540</integer>
		<key>mscrF.st</key>
		<integer>540</integer>
		<key>mscrG</key>
		<integer>375</integer>
		<key>mscrG.sst</key>
		<integer>375</integer>
		<key>mscrG.st</key>
		<integer>375</integer>
		<key>mscrH</key>
		<integer>499</integer>
		<key>mscrH.sst</key>
		<integer>499</integer>
		<key>mscrH.st</key>
		<integer>499</integer>
		<key>mscrI</key>
		<integer>426</integer>
		<key>mscrI.sst</key>
		<integer>426</integer>
		<key>mscrI.st</key>
		<integer>426</integer>
		<key>mscrJ</key>
		<integer>561</integer>
		<key>mscrJ.sst</key>
		<integer>561</integer>
		<key>mscrJ.st</key>
		<integer>561</integer>
		<key>mscrK</key>
		<integer>448</integer>
		<key>mscrK.sst</key>
		<integer>448</integer>
		<key>mscrK.st</key>
		<integer>448</integer>
		<key>mscrL</key>
		<integer>448</integer>
		<key>mscrL.sst</key>
		<integer>448</integer>
		<key>mscrL.st</key>
		<integer>448</integer>
		<key>mscrM</key>
		<integer>699</integer>
		<key>mscrM.sst</key>
		<integer>699</integer>
		<key>mscrM.st</key>
		<integer>699</integer>
		<key>mscrN</key>
		<integer>565</integer>
		<key>mscrN.sst</key>
		<integer>565</integer>
		<key>mscrN.st</key>
		<integer>565</integer>
		<key>mscrO</key>
		<integer>477</integer>
		<key>mscrO.sst</key>
		<integer>477</integer>
		<key>mscrO.st</key>
		<integer>477</integer>
		<key>mscrP</key>
		<integer>398</integer>
		<key>mscrP.sst</key>
		<integer>398</integer>
		<key>mscrP.st</key>
		<integer>398</integer>
		<key>mscrQ</key>
		<integer>489</integer>
		<key>mscrQ.sst</key>
		<integer>489</integer>
		<key>mscrQ.st</key>
		<integer>489</integer>
		<key>mscrR</key>
		<integer>429</integer>
		<key>mscrR.sst</key>
		<integer>429</integer>
		<key>mscrR.st</key>
		<integer>429</integer>
		<key>mscrS</key>
		<integer>415</integer>
		<key>mscrS.sst</key>
		<integer>415</integer>
		<key>mscrS.st</key>
		<integer>415</integer>
		<key>mscrT</key>
		<integer>443</integer>
		<key>mscrT.sst</key>
		<integer>443</integer>
		<key>mscrT.st</key>
		<integer>443</integer>
		<key>mscrU</key>
		<integer>421</integer>
		<key>mscrU.sst</key>
		<integer>421</integer>
		<key>mscrU.st</key>
		<integer>421</integer>
		<key>mscrV</key>
		<integer>360</integer>
		<key>mscrV.sst</key>
		<integer>360</integer>
		<key>mscrV.st</key>
		<integer>360</integer>
		<key>mscrW</key>
		<integer>571</integer>
		<key>mscrW.sst</key>
		<integer>571</integer>
		<key>mscrW.st</key>
		<integer>571</integer>
		<key>mscrX</key>
		<integer>507</integer>
		<key>mscrX.sst</key>
		<integer>507</integer>
		<key>mscrX.st</key>
		<integer>507</integer>
		<key>mscrY</key>
		<integer>413</integer>
		<key>mscrY.sst</key>
		<integer>413</integer>
		<key>mscrY.st</key>
		<integer>413</integer>
		<key>mscrZ</key>
		<integer>483</integer>
		<key>mscrZ.sst</key>
		<integer>483</integer>
		<key>mscrZ.st</key>
		<integer>483</integer>
		<key>mupA</key>
		<integer>338</integer>
		<key>mupAlpha</key>
		<integer>338</integer>
		<key>mupB</key>
		<integer>297</integer>
		<key>mupBeta</key>
		<integer>297</integer>
		<key>mupC</key>
		<integer>406</integer>
		<key>mupChi</key>
		<integer>324</integer>
		<key>mupD</key>
		<integer>342</integer>
		<key>mupDelta</key>
		<integer>340</integer>
		<key>mupDigamma</key>
		<integer>255</integer>
		<key>mupE</key>
		<integer>318</integer>
		<key>mupEpsilon</key>
		<integer>318</integer>
		<key>mupEta</key>
		<integer>384</integer>
		<key>mupF</key>
		<integer>316</integer>
		<key>mupG</key>
		<integer>414</integer>
		<key>mupGamma</key>
		<integer>255</integer>
		<key>mupH</key>
		<integer>384</integer>
		<key>mupI</key>
		<integer>160</integer>
		<key>mupIota</key>
		<integer>162</integer>
		<key>mupJ</key>
		<integer>290</integer>
		<key>mupK</key>
		<integer>350</integer>
		<key>mupKappa</key>
		<integer>350</integer>
		<key>mupL</key>
		<integer>146</integer>
		<key>mupLambda</key>
		<integer>338</integer>
		<key>mupM</key>
		<integer>466</integer>
		<key>mupMu</key>
		<integer>466</integer>
		<key>mupN</key>
		<integer>389</integer>
		<key>mupNu</key>
		<integer>389</integer>
		<key>mupO</key>
		<integer>401</integer>
		<key>mupOmega</key>
		<integer>404</integer>
		<key>mupOmicron</key>
		<integer>401</integer>
		<key>mupP</key>
		<integer>294</integer>
		<key>mupPhi</key>
		<integer>416</integer>
		<key>mupPi</key>
		<integer>384</integer>
		<key>mupPsi</key>
		<integer>408</integer>
		<key>mupQ</key>
		<integer>401</integer>
		<key>mupR</key>
		<integer>298</integer>
		<key>mupRho</key>
		<integer>294</integer>
		<key>mupS</key>
		<integer>294</integer>
		<key>mupSigma</key>
		<integer>272</integer>
		<key>mupT</key>
		<integer>297</integer>
		<key>mupTau</key>
		<integer>297</integer>
		<key>mupTheta</key>
		<integer>400</integer>
		<key>mupU</key>
		<integer>368</integer>
		<key>mupUpsilon</key>
		<integer>318</integer>
		<key>mupV</key>
		<integer>340</integer>
		<key>mupW</key>
		<integer>518</integer>
		<key>mupX</key>
		<integer>324</integer>
		<key>mupXi</key>
		<integer>310</integer>
		<key>mupY</key>
		<integer>313</integer>
		<key>mupZ</key>
		<integer>310</integer>
		<key>mupZeta</key>
		<integer>310</integer>
		<key>mupa</key>
		<integer>244</integer>
		<key>mupalpha</key>
		<integer>266</integer>
		<key>mupb</key>
		<integer>210</integer>
		<key>mupbackepsilon</key>
		<integer>207</integer>
		<key>mupbeta</key>
		<integer>277</integer>
		<key>mupc</key>
		<integer>278</integer>
		<key>mupchi</key>
		<integer>228</integer>
		<key>mupd</key>
		<integer>339</integer>
		<key>mupdelta</key>
		<integer>272</integer>
		<key>mupdigamma</key>
		<integer>291</integer>
		<key>mupe</key>
		<integer>280</integer>
		<key>mupepsilon</key>
		<integer>269</integer>
		<key>mupeta</key>
		<integer>250</integer>
		<key>mupf</key>
		<integer>213</integer>
		<key>mupg</key>
		<integer>258</integer>
		<key>mupgamma</key>
		<integer>279</integer>
		<key>muph</key>
		<integer>200</integer>
		<key>mupi</key>
		<integer>120</integer>
		<key>mupiota</key>
		<integer>112</integer>
		<key>mupj</key>
		<integer>120</integer>
		<key>mupk</key>
		<integer>200</integer>
		<key>mupkappa</key>
		<integer>254</integer>
		<key>mupl</key>
		<integer>119</integer>
		<key>muplambda</key>
		<integer>232</integer>
		<key>mupm</key>
		<integer>425</integer>
		<key>mupmu</key>
		<integer>282</integer>
		<key>mupn</key>
		<integer>282</integer>
		<key>mupnu</key>
		<integer>276</integer>
		<key>mupo</key>
		<integer>284</integer>
		<key>mupomega</key>
		<integer>382</integer>
		<key>mupomicron</key>
		<integer>284</integer>
		<key>mupp</key>
		<integer>263</integer>
		<key>mupphi</key>
		<integer>368</integer>
		<key>muppi</key>
		<integer>310</integer>
		<key>muppsi</key>
		<integer>382</integer>
		<key>mupq</key>
		<integer>304</integer>
		<key>mupr</key>
		<integer>210</integer>
		<key>muprho</key>
		<integer>286</integer>
		<key>mups</key>
		<integer>224</integer>
		<key>mupsigma</key>
		<integer>361</integer>
		<key>mupt</key>
		<integer>172</integer>
		<key>muptau</key>
		<integer>228</integer>
		<key>muptheta</key>
		<integer>299</integer>
		<key>mupu</key>
		<integer>279</integer>
		<key>mupupsilon</key>
		<integer>270</integer>
		<key>mupv</key>
		<integer>258</integer>
		<key>mupvarTheta</key>
		<integer>400</integer>
		<key>mupvarepsilon</key>
		<integer>246</integer>
		<key>mupvarkappa</key>
		<integer>290</integer>
		<key>mupvarphi</key>
		<integer>370</integer>
		<key>mupvarpi</key>
		<integer>428</integer>
		<key>mupvarrho</key>
		<integer>286</integer>
		<key>mupvarsigma</key>
		<integer>242</integer>
		<key>mupvartheta</key>
		<integer>318</integer>
		<key>mupw</key>
		<integer>394</integer>
		<key>mupx</key>
		<integer>248</integer>
		<key>mupxi</key>
		<integer>228</integer>
		<key>mupy</key>
		<integer>271</integer>
		<key>mupz</key>
		<integer>237</integer>
		<key>mupzeta</key>
		<integer>234</integer>
		<key>notaccent</key>
		<integer>0</integer>
		<key>ocirc</key>
		<integer>-200</integer>
		<key>ocommatopright</key>
		<integer>-200</integer>
		<key>oturnedcomma</key>
		<integer>-200</integer>
		<key>overleftarrow</key>
		<integer>-250</integer>
		<key>overleftarrow.size1</key>
		<integer>400</integer>
		<key>overleftarrow.size2</key>
		<integer>650</integer>
		<key>overleftarrow.size3</key>
		<integer>900</integer>
		<key>overleftarrow.size4</key>
		<integer>1200</integer>
		<key>overleftarrow.st</key>
		<integer>-250</integer>
		<key>overleftharpoon</key>
		<integer>-250</integer>
		<key>overleftharpoon.size1</key>
		<integer>400</integer>
		<key>overleftharpoon.size2</key>
		<integer>650</integer>
		<key>overleftharpoon.size3</key>
		<integer>900</integer>
		<key>overleftharpoon.size4</key>
		<integer>1200</integer>
		<key>overleftharpoon.st</key>
		<integer>-250</integer>
		<key>overleftrightarrow</key>
		<integer>-250</integer>
		<key>overleftrightarrow.size1</key>
		<integer>400</integer>
		<key>overleftrightarrow.size2</key>
		<integer>650</integer>
		<key>overleftrightarrow.size3</key>
		<integer>900</integer>
		<key>overleftrightarrow.size4</key>
		<integer>1200</integer>
		<key>overleftrightarrow.st</key>
		<integer>-256</integer>
		<key>overrightarrow</key>
		<integer>-250</integer>
		<key>overrightarrow.size1</key>
		<integer>400</integer>
		<key>overrightarrow.size2</key>
		<integer>650</integer>
		<key>overrightarrow.size3</key>
		<integer>900</integer>
		<key>overrightarrow.size4</key>
		<integer>1200</integer>
		<key>overrightarrow.st</key>
		<integer>-250</integer>
		<key>overrightharpoon</key>
		<integer>-250</integer>
		<key>overrightharpoon.size1</key>
		<integer>400</integer>
		<key>overrightharpoon.size2</key>
		<integer>650</integer>
		<key>overrightharpoon.size3</key>
		<integer>900</integer>
		<key>overrightharpoon.size4</key>
		<integer>1200</integer>
		<key>overrightharpoon.st</key>
		<integer>-250</integer>
		<key>ovhook</key>
		<integer>-200</integer>
		<key>partial</key>
		<integer>255</integer>
		<key>prod</key>
		<integer>440</integer>
		<key>prod.display</key>
		<integer>686</integer>
		<key>sum</key>
		<integer>447</integer>
		<key>sum.display</key>
		<integer>698</integer>
		<key>threeunderdots</key>
		<integer>-140</integer>
		<key>threeunderdots.st</key>
		<integer>-144</integer>
		<key>underleftarrow</key>
		<integer>-220</integer>
		<key>underleftarrow.st</key>
		<integer>-250</integer>
		<key>underleftharpoondown</key>
		<integer>-220</integer>
		<key>underleftrightarrow</key>
		<integer>-250</integer>
		<key>underrightarrow</key>
		<integer>-220</integer>
		<key>underrightarrow.st</key>
		<integer>-250</integer>
		<key>underrightharpoondown</key>
		<integer>-220</integer>
		<key>vertoverlay</key>
		<integer>-217</integer>
		<key>vertoverlay.size1</key>
		<integer>-217</integer>
		<key>vertoverlay.size2</key>
		<integer>-217</integer>
		<key>vertoverlay.size3</key>
		<integer>-217</integer>
		<key>vertoverlay.size4</key>
		<integer>-217</integer>
		<key>vertoverlay.size5</key>
		<integer>-217</integer>
		<key>vertoverlay.size6</key>
		<integer>-217</integer>
		<key>widebreve</key>
		<integer>-200</integer>
		<key>widebridgeabove</key>
		<integer>-214</integer>
		<key>widebridgeabove.st</key>
		<integer>-214</integer>
		<key>widecheck</key>
		<integer>-200</integer>
		<key>widehat</key>
		<integer>-200</integer>
		<key>wideoverbar</key>
		<integer>-294</integer>
		<key>widetilde</key>
		<integer>-242</integer>
		<key>wideutilde</key>
		<integer>-242</integer>
		<key>wp</key>
		<integer>426</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>527</integer>
		<key>AxisHeight</key>
		<integer>280</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1500</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1500</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>689</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>200</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>700</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>80</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>480</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>200</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>580</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>80</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>450</integer>
		<key>FractionRuleThickness</key>
		<integer>66</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>600</integer>
		<key>LowerLimitGapMin</key>
		<integer>150</integer>
		<key>MathLeading</key>
		<integer>150</integer>
		<key>MinConnectorOverlap</key>
		<integer>20</integer>
		<key>OverbarExtraAscender</key>
		<integer>50</integer>
		<key>OverbarRuleThickness</key>
		<integer>66</integer>
		<key>OverbarVerticalGap</key>
		<integer>150</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>64</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>142</integer>
		<key>RadicalExtraAscender</key>
		<integer>76</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-400</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>276</integer>
		<key>RadicalRuleThickness</key>
		<integer>76</integer>
		<key>RadicalVerticalGap</key>
		<integer>96</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>70</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>55</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>0</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>0</integer>
		<key>SpaceAfterScript</key>
		<integer>60</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>700</integer>
		<key>StackBottomShiftDown</key>
		<integer>480</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>500</integer>
		<key>StackGapMin</key>
		<integer>200</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>580</integer>
		<key>StackTopShiftUp</key>
		<integer>450</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>600</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>150</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>150</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>300</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>200</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>100</integer>
		<key>SubscriptShiftDown</key>
		<integer>250</integer>
		<key>SubscriptTopMax</key>
		<integer>400</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>400</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>400</integer>
		<key>SuperscriptBottomMin</key>
		<integer>130</integer>
		<key>SuperscriptShiftUp</key>
		<integer>420</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>270</integer>
		<key>UnderbarExtraDescender</key>
		<integer>50</integer>
		<key>UnderbarRuleThickness</key>
		<integer>66</integer>
		<key>UnderbarVerticalGap</key>
		<integer>150</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>150</integer>
		<key>UpperLimitGapMin</key>
		<integer>150</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>LLeftarrow</key>
		<array/>
		<key>Leftarrow</key>
		<array>
			<string>Leftarrow</string>
			<string>Longleftarrow</string>
		</array>
		<key>Leftrightarrow</key>
		<array>
			<string>Leftrightarrow</string>
			<string>Longleftrightarrow</string>
		</array>
		<key>Lleftarrow</key>
		<array/>
		<key>RRightarrow</key>
		<array/>
		<key>Rightarrow</key>
		<array>
			<string>Rightarrow</string>
			<string>Longrightarrow</string>
		</array>
		<key>Rrightarrow</key>
		<array/>
		<key>hookleftarrow</key>
		<array/>
		<key>hookrightarrow</key>
		<array/>
		<key>leftarrow</key>
		<array/>
		<key>leftrightarrow</key>
		<array>
			<string>leftrightarrow</string>
			<string>longleftrightarrow</string>
		</array>
		<key>mapsfrom</key>
		<array>
			<string>mapsfrom</string>
			<string>longmapsfrom</string>
		</array>
		<key>mapsto</key>
		<array>
			<string>mapsto</string>
			<string>longmapsto</string>
		</array>
		<key>mathunderbar</key>
		<array>
			<string>mathunderbar</string>
			<string>mathunderbar.size1</string>
			<string>mathunderbar.size2</string>
			<string>mathunderbar.size3</string>
			<string>mathunderbar.size4</string>
		</array>
		<key>overbrace</key>
		<array>
			<string>overbrace</string>
			<string>overbrace.size1</string>
			<string>overbrace.size2</string>
			<string>overbrace.size3</string>
			<string>overbrace.size4</string>
			<string>overbrace.size5</string>
			<string>overbrace.size6</string>
			<string>overbrace.size7</string>
			<string>overbrace.size8</string>
			<string>overbrace.size9</string>
			<string>overbrace.size10</string>
			<string>overbrace.size11</string>
			<string>overbrace.size12</string>
			<string>overbrace.size13</string>
			<string>overbrace.size14</string>
			<string>overbrace.size15</string>
		</array>
		<key>overbracket</key>
		<array>
			<string>overbracket</string>
			<string>overbracket.size1</string>
			<string>overbracket.size2</string>
			<string>overbracket.size3</string>
			<string>overbracket.size4</string>
			<string>overbracket.size5</string>
			<string>overbracket.size6</string>
			<string>overbracket.size7</string>
			<string>overbracket.size8</string>
			<string>overbracket.size9</string>
			<string>overbracket.size10</string>
			<string>overbracket.size11</string>
			<string>overbracket.size12</string>
			<string>overbracket.size13</string>
			<string>overbracket.size14</string>
			<string>overbracket.size15</string>
		</array>
		<key>overleftarrow</key>
		<array>
			<string>overleftarrow</string>
			<string>overleftarrow.size1</string>
			<string>overleftarrow.size2</string>
			<string>overleftarrow.size3</string>
			<string>overleftarrow.size4</string>
		</array>
		<key>overleftharpoon</key>
		<array>
			<string>overleftharpoon</string>
			<string>overleftharpoon.size1</string>
			<string>overleftharpoon.size2</string>
			<string>overleftharpoon.size3</string>
			<string>overleftharpoon.size4</string>
		</array>
		<key>overleftrightarrow</key>
		<array>
			<string>overleftrightarrow</string>
			<string>overleftrightarrow.size1</string>
			<string>overleftrightarrow.size2</string>
			<string>overleftrightarrow.size3</string>
			<string>overleftrightarrow.size4</string>
		</array>
		<key>overparen</key>
		<array>
			<string>overparen</string>
			<string>overparen.size1</string>
			<string>overparen.size2</string>
			<string>overparen.size3</string>
			<string>overparen.size4</string>
			<string>overparen.size5</string>
			<string>overparen.size6</string>
			<string>overparen.size7</string>
			<string>overparen.size8</string>
			<string>overparen.size9</string>
			<string>overparen.size10</string>
			<string>overparen.size11</string>
			<string>overparen.size12</string>
			<string>overparen.size13</string>
			<string>overparen.size14</string>
			<string>overparen.size15</string>
		</array>
		<key>overrightarrow</key>
		<array>
			<string>overrightarrow</string>
			<string>overrightarrow.size1</string>
			<string>overrightarrow.size2</string>
			<string>overrightarrow.size3</string>
			<string>overrightarrow.size4</string>
		</array>
		<key>overrightharpoon</key>
		<array>
			<string>overrightharpoon</string>
			<string>overrightharpoon.size1</string>
			<string>overrightharpoon.size2</string>
			<string>overrightharpoon.size3</string>
			<string>overrightharpoon.size4</string>
		</array>
		<key>rightarrow</key>
		<array/>
		<key>underbrace</key>
		<array>
			<string>underbrace</string>
			<string>underbrace.size1</string>
			<string>underbrace.size2</string>
			<string>underbrace.size3</string>
			<string>underbrace.size4</string>
			<string>underbrace.size5</string>
			<string>underbrace.size6</string>
			<string>underbrace.size7</string>
			<string>underbrace.size8</string>
			<string>underbrace.size9</string>
			<string>underbrace.size10</string>
			<string>underbrace.size11</string>
			<string>underbrace.size12</string>
			<string>underbrace.size13</string>
			<string>underbrace.size14</string>
			<string>underbrace.size15</string>
		</array>
		<key>underbracket</key>
		<array>
			<string>underbracket</string>
			<string>underbracket.size1</string>
			<string>underbracket.size2</string>
			<string>underbracket.size3</string>
			<string>underbracket.size4</string>
			<string>underbracket.size5</string>
			<string>underbracket.size6</string>
			<string>underbracket.size7</string>
			<string>underbracket.size8</string>
			<string>underbracket.size9</string>
			<string>underbracket.size10</string>
			<string>underbracket.size11</string>
			<string>underbracket.size12</string>
			<string>underbracket.size13</string>
			<string>underbracket.size14</string>
			<string>underbracket.size15</string>
		</array>
		<key>underleftarrow</key>
		<array>
			<string>underleftarrow</string>
			<string>underleftarrow.size1</string>
			<string>underleftarrow.size2</string>
			<string>underleftarrow.size3</string>
			<string>underleftarrow.size4</string>
		</array>
		<key>underleftharpoondown</key>
		<array>
			<string>underleftharpoondown</string>
			<string>underleftharpoondown.size1</string>
			<string>underleftharpoondown.size2</string>
			<string>underleftharpoondown.size3</string>
			<string>underleftharpoondown.size4</string>
		</array>
		<key>underleftrightarrow</key>
		<array>
			<string>underleftrightarrow</string>
			<string>underleftrightarrow.size1</string>
			<string>underleftrightarrow.size2</string>
			<string>underleftrightarrow.size3</string>
			<string>underleftrightarrow.size4</string>
		</array>
		<key>underparen</key>
		<array>
			<string>underparen</string>
			<string>underparen.size1</string>
			<string>underparen.size2</string>
			<string>underparen.size3</string>
			<string>underparen.size4</string>
			<string>underparen.size5</string>
			<string>underparen.size6</string>
			<string>underparen.size7</string>
			<string>underparen.size8</string>
			<string>underparen.size9</string>
			<string>underparen.size10</string>
			<string>underparen.size11</string>
			<string>underparen.size12</string>
			<string>underparen.size13</string>
			<string>underparen.size14</string>
			<string>underparen.size15</string>
		</array>
		<key>underrightarrow</key>
		<array>
			<string>underrightarrow</string>
			<string>underrightarrow.size1</string>
			<string>underrightarrow.size2</string>
			<string>underrightarrow.size3</string>
			<string>underrightarrow.size4</string>
		</array>
		<key>underrightharpoondown</key>
		<array>
			<string>underrightharpoondown</string>
			<string>underrightharpoondown.size1</string>
			<string>underrightharpoondown.size2</string>
			<string>underrightharpoondown.size3</string>
			<string>underrightharpoondown.size4</string>
		</array>
		<key>widebreve</key>
		<array>
			<string>widebreve</string>
			<string>widebreve.size1</string>
			<string>widebreve.size2</string>
			<string>widebreve.size3</string>
			<string>widebreve.size4</string>
			<string>widebreve.size5</string>
			<string>widebreve.size6</string>
			<string>widebreve.size7</string>
			<string>widebreve.size8</string>
			<string>widebreve.size9</string>
			<string>widebreve.size10</string>
			<string>widebreve.size11</string>
			<string>widebreve.size12</string>
			<string>widebreve.size13</string>
			<string>widebreve.size14</string>
			<string>widebreve.size15</string>
		</array>
		<key>widebridgeabove</key>
		<array/>
		<key>widecheck</key>
		<array>
			<string>widecheck</string>
			<string>widecheck.size1</string>
			<string>widecheck.size2</string>
			<string>widecheck.size3</string>
			<string>widecheck.size4</string>
			<string>widecheck.size5</string>
			<string>widecheck.size6</string>
			<string>widecheck.size7</string>
			<string>widecheck.size8</string>
			<string>widecheck.size9</string>
			<string>widecheck.size10</string>
			<string>widecheck.size11</string>
			<string>widecheck.size12</string>
			<string>widecheck.siZE13</string>
			<string>widecheck.size14</string>
			<string>widecheck.size15</string>
		</array>
		<key>widehat</key>
		<array>
			<string>widehat</string>
			<string>widehat.size1</string>
			<string>widehat.size2</string>
			<string>widehat.size3</string>
			<string>widehat.size4</string>
			<string>widehat.size5</string>
			<string>widehat.size6</string>
			<string>widehat.size7</string>
			<string>widehat.size8</string>
			<string>widehat.size9</string>
			<string>widehat.size10</string>
			<string>widehat.size11</string>
			<string>widehat.size12</string>
			<string>widehat.size13</string>
			<string>widehat.size14</string>
			<string>widehat.size15</string>
		</array>
		<key>wideoverbar</key>
		<array>
			<string>wideoverbar</string>
			<string>wideoverbar.size1</string>
			<string>wideoverbar.size2</string>
			<string>wideoverbar.size3</string>
			<string>wideoverbar.size4</string>
		</array>
		<key>widetilde</key>
		<array>
			<string>widetilde</string>
			<string>widetilde.size1</string>
			<string>widetilde.size2</string>
			<string>widetilde.size3</string>
			<string>widetilde.size4</string>
			<string>widetilde.size5</string>
			<string>widetilde.size6</string>
			<string>widetilde.size7</string>
			<string>widetilde.size8</string>
			<string>widetilde.size9</string>
			<string>widetilde.size10</string>
			<string>widetilde.size11</string>
			<string>widetilde.size12</string>
			<string>widetilde.size13</string>
			<string>widetilde.size14</string>
			<string>widetilde.size15</string>
		</array>
		<key>wideutilde</key>
		<array>
			<string>wideutilde</string>
			<string>wideutilde.size1</string>
			<string>wideutilde.size2</string>
			<string>wideutilde.size3</string>
			<string>wideutilde.size4</string>
			<string>wideutilde.size5</string>
			<string>wideutilde.size6</string>
			<string>wideutilde.size7</string>
			<string>wideutilde.size8</string>
			<string>wideutilde.size9</string>
			<string>wideutilde.size10</string>
			<string>wideutilde.size11</string>
			<string>wideutilde.size12</string>
			<string>wideutilde.size13</string>
			<string>wideutilde.size14</string>
			<string>wideutilde.size15</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>Im</key>
		<integer>40</integer>
		<key>Planckconst</key>
		<integer>48</integer>
		<key>Planckconst.st</key>
		<integer>52</integer>
		<key>awint</key>
		<integer>180</integer>
		<key>awint.display</key>
		<integer>550</integer>
		<key>awint.up</key>
		<integer>150</integer>
		<key>awint.up.display</key>
		<integer>250</integer>
		<key>ell</key>
		<integer>12</integer>
		<key>fint</key>
		<integer>180</integer>
		<key>fint.display</key>
		<integer>400</integer>
		<key>fint.up</key>
		<integer>142</integer>
		<key>fint.up.display</key>
		<integer>145</integer>
		<key>hslash</key>
		<integer>46</integer>
		<key>iiiint</key>
		<integer>200</integer>
		<key>iiiint.display</key>
		<integer>400</integer>
		<key>iiiint.small</key>
		<integer>160</integer>
		<key>iiiint.up</key>
		<integer>142</integer>
		<key>iiiint.up.display</key>
		<integer>250</integer>
		<key>iiint</key>
		<integer>200</integer>
		<key>iiint.display</key>
		<integer>400</integer>
		<key>iiint.small</key>
		<integer>160</integer>
		<key>iiint.up</key>
		<integer>142</integer>
		<key>iiint.up.display</key>
		<integer>250</integer>
		<key>iint</key>
		<integer>200</integer>
		<key>iint.display</key>
		<integer>400</integer>
		<key>iint.small</key>
		<integer>160</integer>
		<key>iint.up</key>
		<integer>142</integer>
		<key>iint.up.display</key>
		<integer>250</integer>
		<key>imath.st</key>
		<integer>32</integer>
		<key>int</key>
		<integer>200</integer>
		<key>int.display</key>
		<integer>400</integer>
		<key>int.small</key>
		<integer>160</integer>
		<key>int.sst</key>
		<integer>200</integer>
		<key>int.st</key>
		<integer>200</integer>
		<key>int.up</key>
		<integer>142</integer>
		<key>int.up.display</key>
		<integer>250</integer>
		<key>int.up.sst</key>
		<integer>142</integer>
		<key>int.up.st</key>
		<integer>142</integer>
		<key>intBar</key>
		<integer>180</integer>
		<key>intBar.display</key>
		<integer>400</integer>
		<key>intBar.up</key>
		<integer>142</integer>
		<key>intBar.up.display</key>
		<integer>145</integer>
		<key>intbar</key>
		<integer>180</integer>
		<key>intbar.display</key>
		<integer>400</integer>
		<key>intbar.up</key>
		<integer>142</integer>
		<key>intbar.up.display</key>
		<integer>145</integer>
		<key>intclockwise</key>
		<integer>180</integer>
		<key>intclockwise.display</key>
		<integer>400</integer>
		<key>intclockwise.up</key>
		<integer>150</integer>
		<key>intclockwise.up.display</key>
		<integer>250</integer>
		<key>jmath.st</key>
		<integer>42</integer>
		<key>mbff</key>
		<integer>33</integer>
		<key>mbfitA</key>
		<integer>58</integer>
		<key>mbfitAlpha</key>
		<integer>58</integer>
		<key>mbfitB</key>
		<integer>40</integer>
		<key>mbfitBeta</key>
		<integer>46</integer>
		<key>mbfitC</key>
		<integer>84</integer>
		<key>mbfitChi</key>
		<integer>92</integer>
		<key>mbfitD</key>
		<integer>46</integer>
		<key>mbfitDelta</key>
		<integer>54</integer>
		<key>mbfitE</key>
		<integer>62</integer>
		<key>mbfitEpsilon</key>
		<integer>76</integer>
		<key>mbfitEta</key>
		<integer>41</integer>
		<key>mbfitF</key>
		<integer>86</integer>
		<key>mbfitG</key>
		<integer>62</integer>
		<key>mbfitGamma</key>
		<integer>80</integer>
		<key>mbfitH</key>
		<integer>60</integer>
		<key>mbfitI</key>
		<integer>67</integer>
		<key>mbfitIota</key>
		<integer>59</integer>
		<key>mbfitJ</key>
		<integer>45</integer>
		<key>mbfitK</key>
		<integer>94</integer>
		<key>mbfitKappa</key>
		<integer>94</integer>
		<key>mbfitLambda</key>
		<integer>54</integer>
		<key>mbfitM</key>
		<integer>62</integer>
		<key>mbfitMu</key>
		<integer>64</integer>
		<key>mbfitN</key>
		<integer>60</integer>
		<key>mbfitNu</key>
		<integer>62</integer>
		<key>mbfitO</key>
		<integer>52</integer>
		<key>mbfitOmega</key>
		<integer>48</integer>
		<key>mbfitOmicron</key>
		<integer>52</integer>
		<key>mbfitP</key>
		<integer>54</integer>
		<key>mbfitPhi</key>
		<integer>50</integer>
		<key>mbfitPi</key>
		<integer>82</integer>
		<key>mbfitPsi</key>
		<integer>62</integer>
		<key>mbfitQ</key>
		<integer>52</integer>
		<key>mbfitR</key>
		<integer>48</integer>
		<key>mbfitRho</key>
		<integer>54</integer>
		<key>mbfitS</key>
		<integer>62</integer>
		<key>mbfitSigma</key>
		<integer>78</integer>
		<key>mbfitT</key>
		<integer>78</integer>
		<key>mbfitTau</key>
		<integer>82</integer>
		<key>mbfitTheta</key>
		<integer>46</integer>
		<key>mbfitU</key>
		<integer>62</integer>
		<key>mbfitUpsilon</key>
		<integer>82</integer>
		<key>mbfitV</key>
		<integer>98</integer>
		<key>mbfitW</key>
		<integer>98</integer>
		<key>mbfitX</key>
		<integer>82</integer>
		<key>mbfitXi</key>
		<integer>78</integer>
		<key>mbfitY</key>
		<integer>100</integer>
		<key>mbfitZ</key>
		<integer>62</integer>
		<key>mbfitZeta</key>
		<integer>72</integer>
		<key>mbfita</key>
		<integer>64</integer>
		<key>mbfitalpha</key>
		<integer>60</integer>
		<key>mbfitb</key>
		<integer>52</integer>
		<key>mbfitbeta</key>
		<integer>52</integer>
		<key>mbfitc</key>
		<integer>62</integer>
		<key>mbfitchi</key>
		<integer>84</integer>
		<key>mbfitd</key>
		<integer>64</integer>
		<key>mbfitdelta</key>
		<integer>48</integer>
		<key>mbfite</key>
		<integer>46</integer>
		<key>mbfitepsilon</key>
		<integer>72</integer>
		<key>mbfiteta</key>
		<integer>52</integer>
		<key>mbfitf</key>
		<integer>80</integer>
		<key>mbfitg</key>
		<integer>86</integer>
		<key>mbfitg.var</key>
		<integer>40</integer>
		<key>mbfitgamma</key>
		<integer>62</integer>
		<key>mbfith</key>
		<integer>50</integer>
		<key>mbfithbar</key>
		<integer>50</integer>
		<key>mbfithslash</key>
		<integer>50</integer>
		<key>mbfiti</key>
		<integer>40</integer>
		<key>mbfitiota</key>
		<integer>46</integer>
		<key>mbfitj</key>
		<integer>44</integer>
		<key>mbfitk</key>
		<integer>84</integer>
		<key>mbfitkappa</key>
		<integer>90</integer>
		<key>mbfitl</key>
		<integer>38</integer>
		<key>mbfitlambda</key>
		<integer>50</integer>
		<key>mbfitm</key>
		<integer>50</integer>
		<key>mbfitmu</key>
		<integer>48</integer>
		<key>mbfitn</key>
		<integer>46</integer>
		<key>mbfitnabla</key>
		<integer>90</integer>
		<key>mbfitnu</key>
		<integer>64</integer>
		<key>mbfito</key>
		<integer>52</integer>
		<key>mbfitomega</key>
		<integer>50</integer>
		<key>mbfitomicron</key>
		<integer>50</integer>
		<key>mbfitp</key>
		<integer>48</integer>
		<key>mbfitpartial</key>
		<integer>46</integer>
		<key>mbfitphi</key>
		<integer>52</integer>
		<key>mbfitpi</key>
		<integer>82</integer>
		<key>mbfitpsi</key>
		<integer>50</integer>
		<key>mbfitq</key>
		<integer>62</integer>
		<key>mbfitr</key>
		<integer>84</integer>
		<key>mbfitrho</key>
		<integer>48</integer>
		<key>mbfits</key>
		<integer>64</integer>
		<key>mbfitsigma</key>
		<integer>80</integer>
		<key>mbfitt</key>
		<integer>64</integer>
		<key>mbfittau</key>
		<integer>84</integer>
		<key>mbfittheta</key>
		<integer>52</integer>
		<key>mbfitu</key>
		<integer>62</integer>
		<key>mbfitupsilon</key>
		<integer>40</integer>
		<key>mbfitv</key>
		<integer>92</integer>
		<key>mbfitvarTheta</key>
		<integer>54</integer>
		<key>mbfitvarepsilon</key>
		<integer>64</integer>
		<key>mbfitvarkappa</key>
		<integer>68</integer>
		<key>mbfitvarphi</key>
		<integer>50</integer>
		<key>mbfitvarpi</key>
		<integer>84</integer>
		<key>mbfitvarrho</key>
		<integer>48</integer>
		<key>mbfitvarsigma</key>
		<integer>74</integer>
		<key>mbfitvartheta</key>
		<integer>92</integer>
		<key>mbfitw</key>
		<integer>92</integer>
		<key>mbfitx</key>
		<integer>66</integer>
		<key>mbfitxi</key>
		<integer>74</integer>
		<key>mbfity</key>
		<integer>94</integer>
		<key>mbfitz</key>
		<integer>64</integer>
		<key>mbfitzeta</key>
		<integer>70</integer>
		<key>mbfr</key>
		<integer>60</integer>
		<key>mbfscrA</key>
		<integer>60</integer>
		<key>mbfscrB</key>
		<integer>68</integer>
		<key>mbfscrC</key>
		<integer>76</integer>
		<key>mbfscrD</key>
		<integer>46</integer>
		<key>mbfscrE</key>
		<integer>92</integer>
		<key>mbfscrF</key>
		<integer>102</integer>
		<key>mbfscrG</key>
		<integer>74</integer>
		<key>mbfscrH</key>
		<integer>32</integer>
		<key>mbfscrI</key>
		<integer>118</integer>
		<key>mbfscrJ</key>
		<integer>152</integer>
		<key>mbfscrK</key>
		<integer>42</integer>
		<key>mbfscrL</key>
		<integer>42</integer>
		<key>mbfscrM</key>
		<integer>40</integer>
		<key>mbfscrN</key>
		<integer>110</integer>
		<key>mbfscrO</key>
		<integer>52</integer>
		<key>mbfscrP</key>
		<integer>68</integer>
		<key>mbfscrQ</key>
		<integer>44</integer>
		<key>mbfscrR</key>
		<integer>54</integer>
		<key>mbfscrS</key>
		<integer>68</integer>
		<key>mbfscrT</key>
		<integer>100</integer>
		<key>mbfscrU</key>
		<integer>62</integer>
		<key>mbfscrV</key>
		<integer>68</integer>
		<key>mbfscrW</key>
		<integer>62</integer>
		<key>mbfscrX</key>
		<integer>70</integer>
		<key>mbfscrY</key>
		<integer>62</integer>
		<key>mbfscrZ</key>
		<integer>66</integer>
		<key>mbft</key>
		<integer>60</integer>
		<key>mbfx</key>
		<integer>20</integer>
		<key>mbfy</key>
		<integer>20</integer>
		<key>mbfz</key>
		<integer>20</integer>
		<key>mitA.st</key>
		<integer>52</integer>
		<key>mitAlpha</key>
		<integer>62</integer>
		<key>mitAlpha.st</key>
		<integer>52</integer>
		<key>mitB</key>
		<integer>40</integer>
		<key>mitB.sst</key>
		<integer>40</integer>
		<key>mitB.st</key>
		<integer>52</integer>
		<key>mitBeta</key>
		<integer>52</integer>
		<key>mitBeta.sst</key>
		<integer>40</integer>
		<key>mitBeta.st</key>
		<integer>48</integer>
		<key>mitC</key>
		<integer>68</integer>
		<key>mitC.sst</key>
		<integer>80</integer>
		<key>mitC.st</key>
		<integer>84</integer>
		<key>mitChi</key>
		<integer>92</integer>
		<key>mitChi.sst</key>
		<integer>60</integer>
		<key>mitChi.st</key>
		<integer>92</integer>
		<key>mitD</key>
		<integer>32</integer>
		<key>mitD.sst</key>
		<integer>15</integer>
		<key>mitD.st</key>
		<integer>54</integer>
		<key>mitDelta</key>
		<integer>50</integer>
		<key>mitDelta.st</key>
		<integer>52</integer>
		<key>mitE</key>
		<integer>60</integer>
		<key>mitE.sst</key>
		<integer>60</integer>
		<key>mitE.st</key>
		<integer>72</integer>
		<key>mitEpsilon</key>
		<integer>62</integer>
		<key>mitEpsilon.sst</key>
		<integer>58</integer>
		<key>mitEpsilon.st</key>
		<integer>80</integer>
		<key>mitEta</key>
		<integer>64</integer>
		<key>mitEta.sst</key>
		<integer>44</integer>
		<key>mitEta.st</key>
		<integer>64</integer>
		<key>mitF</key>
		<integer>92</integer>
		<key>mitF.sst</key>
		<integer>92</integer>
		<key>mitF.st</key>
		<integer>94</integer>
		<key>mitG</key>
		<integer>62</integer>
		<key>mitG.sst</key>
		<integer>20</integer>
		<key>mitG.st</key>
		<integer>62</integer>
		<key>mitGamma</key>
		<integer>84</integer>
		<key>mitGamma.sst</key>
		<integer>71</integer>
		<key>mitGamma.st</key>
		<integer>82</integer>
		<key>mitH</key>
		<integer>48</integer>
		<key>mitH.sst</key>
		<integer>48</integer>
		<key>mitH.st</key>
		<integer>64</integer>
		<key>mitI</key>
		<integer>74</integer>
		<key>mitI.sst</key>
		<integer>74</integer>
		<key>mitI.st</key>
		<integer>74</integer>
		<key>mitIota</key>
		<integer>60</integer>
		<key>mitIota.sst</key>
		<integer>60</integer>
		<key>mitIota.st</key>
		<integer>60</integer>
		<key>mitJ</key>
		<integer>72</integer>
		<key>mitJ.sst</key>
		<integer>72</integer>
		<key>mitJ.st</key>
		<integer>72</integer>
		<key>mitK</key>
		<integer>80</integer>
		<key>mitK.sst</key>
		<integer>80</integer>
		<key>mitK.st</key>
		<integer>86</integer>
		<key>mitKappa</key>
		<integer>92</integer>
		<key>mitKappa.sst</key>
		<integer>70</integer>
		<key>mitKappa.st</key>
		<integer>86</integer>
		<key>mitLambda</key>
		<integer>58</integer>
		<key>mitLambda.st</key>
		<integer>56</integer>
		<key>mitM</key>
		<integer>59</integer>
		<key>mitM.sst</key>
		<integer>59</integer>
		<key>mitM.st</key>
		<integer>59</integer>
		<key>mitMu</key>
		<integer>74</integer>
		<key>mitMu.sst</key>
		<integer>46</integer>
		<key>mitMu.st</key>
		<integer>64</integer>
		<key>mitN</key>
		<integer>45</integer>
		<key>mitN.sst</key>
		<integer>45</integer>
		<key>mitN.st</key>
		<integer>45</integer>
		<key>mitNu</key>
		<integer>78</integer>
		<key>mitNu.sst</key>
		<integer>43</integer>
		<key>mitNu.st</key>
		<integer>62</integer>
		<key>mitO</key>
		<integer>55</integer>
		<key>mitO.sst</key>
		<integer>20</integer>
		<key>mitO.st</key>
		<integer>52</integer>
		<key>mitOmega</key>
		<integer>52</integer>
		<key>mitOmega.sst</key>
		<integer>20</integer>
		<key>mitOmega.st</key>
		<integer>48</integer>
		<key>mitOmicron</key>
		<integer>50</integer>
		<key>mitOmicron.sst</key>
		<integer>20</integer>
		<key>mitOmicron.st</key>
		<integer>52</integer>
		<key>mitP</key>
		<integer>66</integer>
		<key>mitP.sst</key>
		<integer>66</integer>
		<key>mitP.st</key>
		<integer>66</integer>
		<key>mitPhi</key>
		<integer>50</integer>
		<key>mitPhi.sst</key>
		<integer>15</integer>
		<key>mitPhi.st</key>
		<integer>48</integer>
		<key>mitPi</key>
		<integer>72</integer>
		<key>mitPi.sst</key>
		<integer>48</integer>
		<key>mitPi.st</key>
		<integer>62</integer>
		<key>mitPsi</key>
		<integer>62</integer>
		<key>mitPsi.sst</key>
		<integer>47</integer>
		<key>mitPsi.st</key>
		<integer>62</integer>
		<key>mitQ</key>
		<integer>32</integer>
		<key>mitQ.st</key>
		<integer>44</integer>
		<key>mitR.st</key>
		<integer>48</integer>
		<key>mitRho</key>
		<integer>62</integer>
		<key>mitRho.sst</key>
		<integer>46</integer>
		<key>mitRho.st</key>
		<integer>52</integer>
		<key>mitS</key>
		<integer>70</integer>
		<key>mitS.sst</key>
		<integer>70</integer>
		<key>mitS.st</key>
		<integer>64</integer>
		<key>mitSigma</key>
		<integer>74</integer>
		<key>mitSigma.sst</key>
		<integer>47</integer>
		<key>mitSigma.st</key>
		<integer>74</integer>
		<key>mitT</key>
		<integer>63</integer>
		<key>mitT.sst</key>
		<integer>63</integer>
		<key>mitT.st</key>
		<integer>82</integer>
		<key>mitTau</key>
		<integer>86</integer>
		<key>mitTau.sst</key>
		<integer>72</integer>
		<key>mitTau.st</key>
		<integer>78</integer>
		<key>mitTheta</key>
		<integer>54</integer>
		<key>mitTheta.sst</key>
		<integer>15</integer>
		<key>mitTheta.st</key>
		<integer>48</integer>
		<key>mitU</key>
		<integer>55</integer>
		<key>mitU.sst</key>
		<integer>55</integer>
		<key>mitU.st</key>
		<integer>64</integer>
		<key>mitUpsilon</key>
		<integer>84</integer>
		<key>mitUpsilon.sst</key>
		<integer>58</integer>
		<key>mitUpsilon.st</key>
		<integer>80</integer>
		<key>mitV</key>
		<integer>76</integer>
		<key>mitV.sst</key>
		<integer>76</integer>
		<key>mitV.st</key>
		<integer>96</integer>
		<key>mitW</key>
		<integer>106</integer>
		<key>mitW.sst</key>
		<integer>58</integer>
		<key>mitW.st</key>
		<integer>88</integer>
		<key>mitX</key>
		<integer>60</integer>
		<key>mitX.sst</key>
		<integer>60</integer>
		<key>mitX.st</key>
		<integer>82</integer>
		<key>mitXi</key>
		<integer>82</integer>
		<key>mitXi.sst</key>
		<integer>45</integer>
		<key>mitXi.st</key>
		<integer>62</integer>
		<key>mitY</key>
		<integer>122</integer>
		<key>mitY.sst</key>
		<integer>77</integer>
		<key>mitY.st</key>
		<integer>98</integer>
		<key>mitZ</key>
		<integer>86</integer>
		<key>mitZ.sst</key>
		<integer>30</integer>
		<key>mitZ.st</key>
		<integer>66</integer>
		<key>mitZeta</key>
		<integer>68</integer>
		<key>mitZeta.sst</key>
		<integer>68</integer>
		<key>mitZeta.st</key>
		<integer>68</integer>
		<key>mita</key>
		<integer>56</integer>
		<key>mita.st</key>
		<integer>64</integer>
		<key>mitalpha</key>
		<integer>58</integer>
		<key>mitalpha.sst</key>
		<integer>40</integer>
		<key>mitalpha.st</key>
		<integer>60</integer>
		<key>mitb</key>
		<integer>42</integer>
		<key>mitb.st</key>
		<integer>52</integer>
		<key>mitbeta</key>
		<integer>42</integer>
		<key>mitbeta.sst</key>
		<integer>15</integer>
		<key>mitbeta.st</key>
		<integer>46</integer>
		<key>mitc</key>
		<integer>50</integer>
		<key>mitc.sst</key>
		<integer>27</integer>
		<key>mitc.st</key>
		<integer>64</integer>
		<key>mitchi</key>
		<integer>64</integer>
		<key>mitchi.sst</key>
		<integer>42</integer>
		<key>mitchi.st</key>
		<integer>84</integer>
		<key>mitd</key>
		<integer>70</integer>
		<key>mitd.sst</key>
		<integer>45</integer>
		<key>mitd.st</key>
		<integer>62</integer>
		<key>mitdelta</key>
		<integer>52</integer>
		<key>mitdelta.sst</key>
		<integer>18</integer>
		<key>mitdelta.st</key>
		<integer>50</integer>
		<key>mite</key>
		<integer>56</integer>
		<key>mite.st</key>
		<integer>52</integer>
		<key>mitepsilon</key>
		<integer>72</integer>
		<key>mitepsilon.sst</key>
		<integer>40</integer>
		<key>mitepsilon.st</key>
		<integer>64</integer>
		<key>miteta</key>
		<integer>52</integer>
		<key>miteta.sst</key>
		<integer>40</integer>
		<key>miteta.st</key>
		<integer>52</integer>
		<key>mitf</key>
		<integer>96</integer>
		<key>mitf.sst</key>
		<integer>61</integer>
		<key>mitf.st</key>
		<integer>80</integer>
		<key>mitg</key>
		<integer>81</integer>
		<key>mitg.sst</key>
		<integer>81</integer>
		<key>mitg.st</key>
		<integer>100</integer>
		<key>mitg.var</key>
		<integer>40</integer>
		<key>mitg.var.sst</key>
		<integer>40</integer>
		<key>mitg.var.st</key>
		<integer>40</integer>
		<key>mitgamma</key>
		<integer>64</integer>
		<key>mitgamma.sst</key>
		<integer>64</integer>
		<key>mitgamma.st</key>
		<integer>62</integer>
		<key>mithbar.st</key>
		<integer>46</integer>
		<key>mithslash.st</key>
		<integer>52</integer>
		<key>miti</key>
		<integer>38</integer>
		<key>miti.sst</key>
		<integer>38</integer>
		<key>miti.st</key>
		<integer>38</integer>
		<key>mitiota.st</key>
		<integer>54</integer>
		<key>mitj</key>
		<integer>41</integer>
		<key>mitj.sst</key>
		<integer>41</integer>
		<key>mitj.st</key>
		<integer>41</integer>
		<key>mitk</key>
		<integer>84</integer>
		<key>mitk.sst</key>
		<integer>54</integer>
		<key>mitk.st</key>
		<integer>80</integer>
		<key>mitkappa</key>
		<integer>86</integer>
		<key>mitkappa.sst</key>
		<integer>21</integer>
		<key>mitkappa.st</key>
		<integer>86</integer>
		<key>mitl</key>
		<integer>39</integer>
		<key>mitl.sst</key>
		<integer>39</integer>
		<key>mitl.st</key>
		<integer>39</integer>
		<key>mitlambda</key>
		<integer>56</integer>
		<key>mitlambda.st</key>
		<integer>58</integer>
		<key>mitm</key>
		<integer>46</integer>
		<key>mitm.st</key>
		<integer>54</integer>
		<key>mitmu</key>
		<integer>58</integer>
		<key>mitmu.sst</key>
		<integer>30</integer>
		<key>mitmu.st</key>
		<integer>54</integer>
		<key>mitn</key>
		<integer>46</integer>
		<key>mitn.st</key>
		<integer>50</integer>
		<key>mitnabla</key>
		<integer>78</integer>
		<key>mitnabla.sst</key>
		<integer>67</integer>
		<key>mitnabla.st</key>
		<integer>90</integer>
		<key>mitnu</key>
		<integer>62</integer>
		<key>mitnu.sst</key>
		<integer>30</integer>
		<key>mitnu.st</key>
		<integer>64</integer>
		<key>mito</key>
		<integer>50</integer>
		<key>mito.st</key>
		<integer>52</integer>
		<key>mitomega</key>
		<integer>46</integer>
		<key>mitomega.st</key>
		<integer>46</integer>
		<key>mitomicron</key>
		<integer>50</integer>
		<key>mitomicron.st</key>
		<integer>50</integer>
		<key>mitp</key>
		<integer>52</integer>
		<key>mitp.st</key>
		<integer>52</integer>
		<key>mitpartial</key>
		<integer>48</integer>
		<key>mitpartial.sst</key>
		<integer>40</integer>
		<key>mitpartial.st</key>
		<integer>52</integer>
		<key>mitphi</key>
		<integer>48</integer>
		<key>mitphi.st</key>
		<integer>48</integer>
		<key>mitpi</key>
		<integer>62</integer>
		<key>mitpi.sst</key>
		<integer>51</integer>
		<key>mitpi.st</key>
		<integer>82</integer>
		<key>mitpsi</key>
		<integer>48</integer>
		<key>mitpsi.sst</key>
		<integer>21</integer>
		<key>mitpsi.st</key>
		<integer>52</integer>
		<key>mitq</key>
		<integer>58</integer>
		<key>mitq.st</key>
		<integer>62</integer>
		<key>mitr</key>
		<integer>86</integer>
		<key>mitr.sst</key>
		<integer>50</integer>
		<key>mitr.st</key>
		<integer>80</integer>
		<key>mitrho</key>
		<integer>52</integer>
		<key>mitrho.st</key>
		<integer>48</integer>
		<key>mits</key>
		<integer>64</integer>
		<key>mits.sst</key>
		<integer>27</integer>
		<key>mits.st</key>
		<integer>64</integer>
		<key>mitsigma</key>
		<integer>78</integer>
		<key>mitsigma.sst</key>
		<integer>50</integer>
		<key>mitsigma.st</key>
		<integer>86</integer>
		<key>mitt</key>
		<integer>58</integer>
		<key>mitt.st</key>
		<integer>62</integer>
		<key>mittau</key>
		<integer>80</integer>
		<key>mittau.sst</key>
		<integer>40</integer>
		<key>mittau.st</key>
		<integer>82</integer>
		<key>mittheta</key>
		<integer>46</integer>
		<key>mittheta.sst</key>
		<integer>22</integer>
		<key>mittheta.st</key>
		<integer>52</integer>
		<key>mitu</key>
		<integer>56</integer>
		<key>mitu.st</key>
		<integer>54</integer>
		<key>mitupsilon</key>
		<integer>40</integer>
		<key>mitupsilon.st</key>
		<integer>46</integer>
		<key>mitv</key>
		<integer>82</integer>
		<key>mitv.sst</key>
		<integer>40</integer>
		<key>mitv.st</key>
		<integer>86</integer>
		<key>mitvarTheta</key>
		<integer>54</integer>
		<key>mitvarTheta.sst</key>
		<integer>15</integer>
		<key>mitvarTheta.st</key>
		<integer>50</integer>
		<key>mitvarepsilon</key>
		<integer>64</integer>
		<key>mitvarepsilon.st</key>
		<integer>64</integer>
		<key>mitvarkappa</key>
		<integer>68</integer>
		<key>mitvarkappa.sst</key>
		<integer>50</integer>
		<key>mitvarkappa.st</key>
		<integer>68</integer>
		<key>mitvarphi</key>
		<integer>42</integer>
		<key>mitvarphi.st</key>
		<integer>52</integer>
		<key>mitvarpi</key>
		<integer>90</integer>
		<key>mitvarpi.sst</key>
		<integer>60</integer>
		<key>mitvarpi.st</key>
		<integer>92</integer>
		<key>mitvarrho</key>
		<integer>54</integer>
		<key>mitvarrho.st</key>
		<integer>48</integer>
		<key>mitvarsigma</key>
		<integer>72</integer>
		<key>mitvarsigma.sst</key>
		<integer>58</integer>
		<key>mitvarsigma.st</key>
		<integer>80</integer>
		<key>mitvartheta</key>
		<integer>82</integer>
		<key>mitvartheta.sst</key>
		<integer>30</integer>
		<key>mitvartheta.st</key>
		<integer>92</integer>
		<key>mitw</key>
		<integer>88</integer>
		<key>mitw.sst</key>
		<integer>40</integer>
		<key>mitw.st</key>
		<integer>90</integer>
		<key>mitx</key>
		<integer>94</integer>
		<key>mitx.sst</key>
		<integer>38</integer>
		<key>mitx.st</key>
		<integer>88</integer>
		<key>mitxi</key>
		<integer>78</integer>
		<key>mitxi.sst</key>
		<integer>48</integer>
		<key>mitxi.st</key>
		<integer>84</integer>
		<key>mity</key>
		<integer>92</integer>
		<key>mity.st</key>
		<integer>92</integer>
		<key>mitz</key>
		<integer>62</integer>
		<key>mitz.st</key>
		<integer>64</integer>
		<key>mitzeta</key>
		<integer>78</integer>
		<key>mitzeta.sst</key>
		<integer>74</integer>
		<key>mitzeta.st</key>
		<integer>74</integer>
		<key>mscrA</key>
		<integer>48</integer>
		<key>mscrA.sst</key>
		<integer>48</integer>
		<key>mscrA.st</key>
		<integer>48</integer>
		<key>mscrB</key>
		<integer>68</integer>
		<key>mscrB.sst</key>
		<integer>68</integer>
		<key>mscrB.st</key>
		<integer>68</integer>
		<key>mscrC</key>
		<integer>64</integer>
		<key>mscrC.sst</key>
		<integer>64</integer>
		<key>mscrC.st</key>
		<integer>64</integer>
		<key>mscrD</key>
		<integer>36</integer>
		<key>mscrD.sst</key>
		<integer>36</integer>
		<key>mscrD.st</key>
		<integer>36</integer>
		<key>mscrE</key>
		<integer>80</integer>
		<key>mscrE.sst</key>
		<integer>80</integer>
		<key>mscrE.st</key>
		<integer>80</integer>
		<key>mscrF</key>
		<integer>110</integer>
		<key>mscrF.sst</key>
		<integer>110</integer>
		<key>mscrF.st</key>
		<integer>110</integer>
		<key>mscrG</key>
		<integer>62</integer>
		<key>mscrG.sst</key>
		<integer>62</integer>
		<key>mscrG.st</key>
		<integer>62</integer>
		<key>mscrH</key>
		<integer>42</integer>
		<key>mscrH.sst</key>
		<integer>42</integer>
		<key>mscrH.st</key>
		<integer>42</integer>
		<key>mscrI</key>
		<integer>42</integer>
		<key>mscrI.sst</key>
		<integer>42</integer>
		<key>mscrI.st</key>
		<integer>42</integer>
		<key>mscrJ</key>
		<integer>114</integer>
		<key>mscrJ.sst</key>
		<integer>114</integer>
		<key>mscrJ.st</key>
		<integer>114</integer>
		<key>mscrK</key>
		<integer>32</integer>
		<key>mscrK.sst</key>
		<integer>32</integer>
		<key>mscrK.st</key>
		<integer>32</integer>
		<key>mscrL</key>
		<integer>40</integer>
		<key>mscrL.sst</key>
		<integer>40</integer>
		<key>mscrL.st</key>
		<integer>40</integer>
		<key>mscrM</key>
		<integer>28</integer>
		<key>mscrM.sst</key>
		<integer>28</integer>
		<key>mscrM.st</key>
		<integer>28</integer>
		<key>mscrN</key>
		<integer>118</integer>
		<key>mscrN.sst</key>
		<integer>118</integer>
		<key>mscrN.st</key>
		<integer>118</integer>
		<key>mscrO</key>
		<integer>40</integer>
		<key>mscrO.sst</key>
		<integer>40</integer>
		<key>mscrO.st</key>
		<integer>40</integer>
		<key>mscrP</key>
		<integer>62</integer>
		<key>mscrP.sst</key>
		<integer>62</integer>
		<key>mscrP.st</key>
		<integer>62</integer>
		<key>mscrQ</key>
		<integer>32</integer>
		<key>mscrQ.sst</key>
		<integer>32</integer>
		<key>mscrQ.st</key>
		<integer>32</integer>
		<key>mscrR</key>
		<integer>42</integer>
		<key>mscrR.sst</key>
		<integer>42</integer>
		<key>mscrR.st</key>
		<integer>42</integer>
		<key>mscrS</key>
		<integer>98</integer>
		<key>mscrS.sst</key>
		<integer>98</integer>
		<key>mscrS.st</key>
		<integer>98</integer>
		<key>mscrT</key>
		<integer>130</integer>
		<key>mscrT.sst</key>
		<integer>130</integer>
		<key>mscrT.st</key>
		<integer>130</integer>
		<key>mscrU</key>
		<integer>62</integer>
		<key>mscrU.sst</key>
		<integer>62</integer>
		<key>mscrU.st</key>
		<integer>62</integer>
		<key>mscrV</key>
		<integer>62</integer>
		<key>mscrV.sst</key>
		<integer>62</integer>
		<key>mscrV.st</key>
		<integer>62</integer>
		<key>mscrW</key>
		<integer>62</integer>
		<key>mscrW.sst</key>
		<integer>62</integer>
		<key>mscrW.st</key>
		<integer>62</integer>
		<key>mscrX</key>
		<integer>64</integer>
		<key>mscrX.sst</key>
		<integer>64</integer>
		<key>mscrX.st</key>
		<integer>64</integer>
		<key>mscrY</key>
		<integer>64</integer>
		<key>mscrY.sst</key>
		<integer>64</integer>
		<key>mscrY.st</key>
		<integer>64</integer>
		<key>mscrZ</key>
		<integer>58</integer>
		<key>mscrZ.sst</key>
		<integer>58</integer>
		<key>mscrZ.st</key>
		<integer>58</integer>
		<key>mupf</key>
		<integer>21</integer>
		<key>muppsi</key>
		<integer>40</integer>
		<key>mupr</key>
		<integer>60</integer>
		<key>mupt</key>
		<integer>60</integer>
		<key>mupy</key>
		<integer>20</integer>
		<key>oiiint</key>
		<integer>200</integer>
		<key>oiiint.display</key>
		<integer>400</integer>
		<key>oiiint.up</key>
		<integer>150</integer>
		<key>oiiint.up.display</key>
		<integer>250</integer>
		<key>oiint</key>
		<integer>200</integer>
		<key>oiint.display</key>
		<integer>400</integer>
		<key>oiint.up</key>
		<integer>150</integer>
		<key>oiint.up.display</key>
		<integer>250</integer>
		<key>oint</key>
		<integer>200</integer>
		<key>oint.display</key>
		<integer>400</integer>
		<key>oint.up</key>
		<integer>150</integer>
		<key>oint.up.display</key>
		<integer>250</integer>
		<key>ointctrclockwise</key>
		<integer>180</integer>
		<key>ointctrclockwise.display</key>
		<integer>400</integer>
		<key>ointctrclockwise.up</key>
		<integer>150</integer>
		<key>ointctrclockwise.up.display</key>
		<integer>240</integer>
		<key>sqint</key>
		<integer>150</integer>
		<key>sqint.display</key>
		<integer>400</integer>
		<key>sqint.up</key>
		<integer>150</integer>
		<key>sqint.up.display</key>
		<integer>240</integer>
		<key>sumint.display</key>
		<integer>350</integer>
		<key>sumint.up.display</key>
		<integer>269</integer>
		<key>varointclockwise</key>
		<integer>180</integer>
		<key>varointclockwise.display</key>
		<integer>400</integer>
		<key>varointclockwise.up</key>
		<integer>150</integer>
		<key>varointclockwise.up.display</key>
		<integer>250</integer>
		<key>wp</key>
		<integer>36</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>DDownarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>789</integer>
					<key>endConnector</key>
					<integer>263</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>DDownarrow</string>
					<key>startConnector</key>
					<integer>263</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>0</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>UUparrow.ext</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
			</array>
		</dict>
		<key>Ddownarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>263</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Ddownarrow</string>
					<key>startConnector</key>
					<integer>263</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Uuparrow.ext</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>Downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>263</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Downarrow</string>
					<key>startConnector</key>
					<integer>263</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Uparrow.ext</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>UUparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>UUparrow.ext</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>789</integer>
					<key>endConnector</key>
					<integer>263</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>UUparrow</string>
					<key>startConnector</key>
					<integer>263</integer>
				</dict>
			</array>
		</dict>
		<key>Uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Uparrow.ext</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>263</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Uparrow</string>
					<key>startConnector</key>
					<integer>263</integer>
				</dict>
			</array>
		</dict>
		<key>Uuparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Uuparrow.ext</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Uuparrow</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>Vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>6000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vert.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vert.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>Vvert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>6000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vvert.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vvert.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>cuberoot</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3800</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>cuberoot.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>cuberoot.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>cuberoot.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>263</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>downarrow</string>
					<key>startConnector</key>
					<integer>263</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uparrow.ext</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>fourthroot</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3800</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>fourthroot.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>fourthroot.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>fourthroot.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>int</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1117</integer>
					<key>endConnector</key>
					<integer>372</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>intbottom</string>
					<key>startConnector</key>
					<integer>372</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>intextender</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>0</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>inttop</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
			</array>
		</dict>
		<key>int.up</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1117</integer>
					<key>endConnector</key>
					<integer>372</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>intbottom</string>
					<key>startConnector</key>
					<integer>372</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>intextender</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>0</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>inttop</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
			</array>
		</dict>
		<key>lBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lBrack.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>lbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbracemid</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.tp</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>lbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lbrack.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.tp</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>lceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lceil.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>6000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lceil.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>lfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>6000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lfloor.bot</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lfloor.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>lgroup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lgroup.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lgroup.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lgroup.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>lparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lparen.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.tp</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>rBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rBrack.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>rbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbracemid</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.tp</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>rbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rbrack.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.tp</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>rceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rceil.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>6000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rceil.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>rfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>6000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rfloor.bot</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rfloor.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>rgroup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rgroup.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rgroup.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rgroup.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>rparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rparen.ex</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.tp</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>sqrt</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3800</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrt.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>sqrt.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrt.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uparrow.ext</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>263</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uparrow</string>
					<key>startConnector</key>
					<integer>263</integer>
				</dict>
			</array>
		</dict>
		<key>vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>6000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>vert.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vert.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>Bbbsum</key>
		<array>
			<string>Bbbsum</string>
			<string>Bbbsum.display</string>
		</array>
		<key>DDownarrow</key>
		<array/>
		<key>Ddownarrow</key>
		<array/>
		<key>Downarrow</key>
		<array/>
		<key>UUparrow</key>
		<array/>
		<key>Uparrow</key>
		<array/>
		<key>Uuparrow</key>
		<array/>
		<key>Vert</key>
		<array>
			<string>Vert</string>
			<string>Vert.size1</string>
			<string>Vert.size2</string>
			<string>Vert.size3</string>
			<string>Vert.size4</string>
			<string>Vert.size5</string>
			<string>Vert.size6</string>
			<string>Vert.size7</string>
			<string>Vert.size8</string>
			<string>Vert.size9</string>
			<string>Vert.size10</string>
			<string>Vert.size11</string>
			<string>Vert.size12</string>
			<string>Vert.size13</string>
			<string>Vert.size14</string>
			<string>Vert.size15</string>
		</array>
		<key>Vvert</key>
		<array>
			<string>Vvert</string>
			<string>Vvert.size1</string>
			<string>Vvert.size2</string>
			<string>Vvert.size3</string>
			<string>Vvert.size4</string>
			<string>Vvert.size5</string>
			<string>Vvert.size6</string>
			<string>Vvert.size7</string>
			<string>Vvert.size8</string>
			<string>Vvert.size9</string>
			<string>Vvert.size10</string>
			<string>Vvert.size11</string>
			<string>Vvert.size12</string>
			<string>Vvert.size13</string>
			<string>Vvert.size14</string>
			<string>Vvert.size15</string>
		</array>
		<key>awint</key>
		<array>
			<string>awint</string>
			<string>awint.display</string>
		</array>
		<key>awint.up</key>
		<array>
			<string>awint.up</string>
			<string>awint.up.display</string>
		</array>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.size1</string>
			<string>backslash.size2</string>
			<string>backslash.size3</string>
			<string>backslash.size4</string>
			<string>backslash.size5</string>
			<string>backslash.size6</string>
			<string>backslash.size7</string>
			<string>backslash.size8</string>
			<string>backslash.size9</string>
			<string>backslash.size10</string>
			<string>backslash.size11</string>
			<string>backslash.size12</string>
			<string>backslash.size13</string>
			<string>backslash.size14</string>
			<string>backslash.size15</string>
		</array>
		<key>bigcap</key>
		<array>
			<string>bigcap</string>
			<string>bigcap.display</string>
		</array>
		<key>bigcup</key>
		<array>
			<string>bigcup</string>
			<string>bigcup.display</string>
		</array>
		<key>bigcupdot</key>
		<array>
			<string>bigcupdot</string>
			<string>bigcupdot.display</string>
		</array>
		<key>bigodot</key>
		<array>
			<string>bigodot</string>
			<string>bigodot.display</string>
		</array>
		<key>bigoplus</key>
		<array>
			<string>bigoplus</string>
			<string>bigoplus.display</string>
		</array>
		<key>bigotimes</key>
		<array>
			<string>bigotimes</string>
			<string>bigotimes.display</string>
		</array>
		<key>bigsqcap</key>
		<array>
			<string>bigsqcap</string>
			<string>bigsqcap.display</string>
		</array>
		<key>bigsqcup</key>
		<array>
			<string>bigsqcup</string>
			<string>bigsqcup.display</string>
		</array>
		<key>bigtimes</key>
		<array>
			<string>bigtimes</string>
			<string>bigtimes.display</string>
		</array>
		<key>biguplus</key>
		<array>
			<string>biguplus</string>
			<string>biguplus.display</string>
		</array>
		<key>bigvee</key>
		<array>
			<string>bigvee</string>
			<string>bigvee.display</string>
		</array>
		<key>bigwedge</key>
		<array>
			<string>bigwedge</string>
			<string>bigwedge.display</string>
		</array>
		<key>conjquant</key>
		<array>
			<string>conjquant</string>
			<string>conjquant.display</string>
		</array>
		<key>coprod</key>
		<array>
			<string>coprod</string>
			<string>coprod.display</string>
		</array>
		<key>cuberoot</key>
		<array>
			<string>cuberoot</string>
			<string>cuberoot.size1</string>
			<string>cuberoot.size2</string>
			<string>cuberoot.size3</string>
			<string>cuberoot.size4</string>
			<string>cuberoot.size5</string>
			<string>cuberoot.size6</string>
			<string>cuberoot.size7</string>
			<string>cuberoot.size8</string>
			<string>cuberoot.size9</string>
			<string>cuberoot.size10</string>
			<string>cuberoot.size11</string>
			<string>cuberoot.size12</string>
			<string>cuberoot.size13</string>
			<string>cuberoot.size14</string>
			<string>cuberoot.size15</string>
		</array>
		<key>disjquant</key>
		<array>
			<string>disjquant</string>
			<string>disjquant.display</string>
		</array>
		<key>downarrow</key>
		<array/>
		<key>fint</key>
		<array>
			<string>fint</string>
			<string>fint.display</string>
		</array>
		<key>fint.up</key>
		<array>
			<string>fint.up</string>
			<string>fint.up.display</string>
		</array>
		<key>fourthroot</key>
		<array>
			<string>fourthroot</string>
			<string>fourthroot.size1</string>
			<string>fourthroot.size2</string>
			<string>fourthroot.size3</string>
			<string>fourthroot.size4</string>
			<string>fourthroot.size5</string>
			<string>fourthroot.size6</string>
			<string>fourthroot.size7</string>
			<string>fourthroot.size8</string>
			<string>fourthroot.size9</string>
			<string>fourthroot.size10</string>
			<string>fourthroot.size11</string>
			<string>fourthroot.size12</string>
			<string>fourthroot.size13</string>
			<string>fourthroot.size14</string>
			<string>fourthroot.size15</string>
		</array>
		<key>iiiint</key>
		<array>
			<string>iiiint</string>
			<string>iiiint.display</string>
		</array>
		<key>iiiint.up</key>
		<array>
			<string>iiiint.up</string>
			<string>iiiint.up.display</string>
		</array>
		<key>iiint</key>
		<array>
			<string>iiint</string>
			<string>iiint.display</string>
		</array>
		<key>iiint.up</key>
		<array>
			<string>iiint.up</string>
			<string>iiint.up.display</string>
		</array>
		<key>iint</key>
		<array>
			<string>iint</string>
			<string>iint.display</string>
		</array>
		<key>iint.up</key>
		<array>
			<string>iint.up</string>
			<string>iint.up.display</string>
		</array>
		<key>int</key>
		<array>
			<string>int</string>
			<string>int.display</string>
		</array>
		<key>int.up</key>
		<array>
			<string>int.up</string>
			<string>int.up.display</string>
		</array>
		<key>intBar</key>
		<array>
			<string>intBar</string>
			<string>intBar.display</string>
		</array>
		<key>intBar.up</key>
		<array>
			<string>intBar.up</string>
			<string>intBar.up.display</string>
		</array>
		<key>intbar</key>
		<array>
			<string>intbar</string>
			<string>intbar.display</string>
		</array>
		<key>intbar.up</key>
		<array>
			<string>intbar.up</string>
			<string>intbar.up.display</string>
		</array>
		<key>intclockwise</key>
		<array>
			<string>intclockwise</string>
			<string>intclockwise.display</string>
		</array>
		<key>intclockwise.up</key>
		<array>
			<string>intclockwise.up</string>
			<string>intclockwise.up.display</string>
		</array>
		<key>lAngle</key>
		<array>
			<string>lAngle</string>
			<string>lAngle.size1</string>
			<string>lAngle.size2</string>
			<string>lAngle.size3</string>
			<string>lAngle.size4</string>
			<string>lAngle.size5</string>
			<string>lAngle.size6</string>
			<string>lAngle.size7</string>
			<string>lAngle.size8</string>
			<string>lAngle.size9</string>
			<string>lAngle.size10</string>
			<string>lAngle.size11</string>
			<string>lAngle.size12</string>
			<string>lAngle.size13</string>
			<string>lAngle.size14</string>
			<string>lAngle.size15</string>
		</array>
		<key>lBrack</key>
		<array>
			<string>lBrack</string>
			<string>lBrack.size1</string>
			<string>lBrack.size2</string>
			<string>lBrack.size3</string>
			<string>lBrack.size4</string>
			<string>lBrack.size5</string>
			<string>lBrack.size6</string>
			<string>lBrack.size7</string>
			<string>lBrack.size8</string>
			<string>lBrack.size9</string>
			<string>lBrack.size10</string>
			<string>lBrack.size11</string>
			<string>lBrack.size12</string>
			<string>lBrack.size13</string>
			<string>lBrack.size14</string>
			<string>lBrack.size15</string>
		</array>
		<key>langle</key>
		<array>
			<string>langle</string>
			<string>langle.size1</string>
			<string>langle.size2</string>
			<string>langle.size3</string>
			<string>langle.size4</string>
			<string>langle.size5</string>
			<string>langle.size6</string>
			<string>langle.size7</string>
			<string>langle.size8</string>
			<string>langle.size9</string>
			<string>langle.size10</string>
			<string>langle.size11</string>
			<string>langle.size12</string>
			<string>langle.size13</string>
			<string>langle.size14</string>
			<string>langle.size15</string>
		</array>
		<key>lbrace</key>
		<array>
			<string>lbrace</string>
			<string>lbrace.size1</string>
			<string>lbrace.size2</string>
			<string>lbrace.size3</string>
			<string>lbrace.size4</string>
			<string>lbrace.size5</string>
			<string>lbrace.size6</string>
			<string>lbrace.size7</string>
			<string>lbrace.size8</string>
			<string>lbrace.size9</string>
			<string>lbrace.size10</string>
			<string>lbrace.size11</string>
			<string>lbrace.size12</string>
			<string>lbrace.size13</string>
			<string>lbrace.size14</string>
			<string>lbrace.size15</string>
		</array>
		<key>lbrack</key>
		<array>
			<string>lbrack</string>
			<string>lbrack.size1</string>
			<string>lbrack.size2</string>
			<string>lbrack.size3</string>
			<string>lbrack.size4</string>
			<string>lbrack.size5</string>
			<string>lbrack.size6</string>
			<string>lbrack.size7</string>
			<string>lbrack.size8</string>
			<string>lbrack.size9</string>
			<string>lbrack.size10</string>
			<string>lbrack.size11</string>
			<string>lbrack.size12</string>
			<string>lbrack.size13</string>
			<string>lbrack.size14</string>
			<string>lbrack.size15</string>
		</array>
		<key>lceil</key>
		<array>
			<string>lceil</string>
			<string>lceil.size1</string>
			<string>lceil.size2</string>
			<string>lceil.size3</string>
			<string>lceil.size4</string>
			<string>lceil.size5</string>
			<string>lceil.size6</string>
			<string>lceil.size7</string>
			<string>lceil.size8</string>
			<string>lceil.size9</string>
			<string>lceil.size10</string>
			<string>lceil.size11</string>
			<string>lceil.size12</string>
			<string>lceil.size13</string>
			<string>lceil.size14</string>
			<string>lceil.size15</string>
		</array>
		<key>lfloor</key>
		<array>
			<string>lfloor</string>
			<string>lfloor.size1</string>
			<string>lfloor.size2</string>
			<string>lfloor.size3</string>
			<string>lfloor.size4</string>
			<string>lfloor.size5</string>
			<string>lfloor.size6</string>
			<string>lfloor.size7</string>
			<string>lfloor.size8</string>
			<string>lfloor.size9</string>
			<string>lfloor.size10</string>
			<string>lfloor.size11</string>
			<string>lfloor.size12</string>
			<string>lfloor.size13</string>
			<string>lfloor.size14</string>
			<string>lfloor.size15</string>
		</array>
		<key>lgroup</key>
		<array>
			<string>lgroup</string>
			<string>lgroup.size1</string>
			<string>lgroup.size2</string>
			<string>lgroup.size3</string>
			<string>lgroup.size4</string>
			<string>lgroup.size5</string>
			<string>lgroup.size6</string>
			<string>lgroup.size7</string>
			<string>lgroup.size8</string>
			<string>lgroup.size9</string>
			<string>lgroup.size10</string>
			<string>lgroup.size11</string>
			<string>lgroup.size12</string>
			<string>lgroup.size13</string>
			<string>lgroup.size14</string>
			<string>lgroup.size15</string>
		</array>
		<key>lparen</key>
		<array>
			<string>lparen</string>
			<string>lparen.size1</string>
			<string>lparen.size2</string>
			<string>lparen.size3</string>
			<string>lparen.size4</string>
			<string>lparen.size5</string>
			<string>lparen.size6</string>
			<string>lparen.size7</string>
			<string>lparen.size8</string>
			<string>lparen.size9</string>
			<string>lparen.size10</string>
			<string>lparen.size11</string>
			<string>lparen.size12</string>
			<string>lparen.size13</string>
			<string>lparen.size14</string>
			<string>lparen.size15</string>
		</array>
		<key>mathslash</key>
		<array>
			<string>mathslash</string>
			<string>mathslash.size1</string>
			<string>mathslash.size2</string>
			<string>mathslash.size3</string>
			<string>mathslash.size4</string>
			<string>mathslash.size5</string>
			<string>mathslash.size6</string>
			<string>mathslash.size7</string>
			<string>mathslash.size8</string>
			<string>mathslash.size9</string>
			<string>mathslash.size10</string>
			<string>mathslash.size11</string>
			<string>mathslash.size12</string>
			<string>mathslash.size13</string>
			<string>mathslash.size14</string>
			<string>mathslash.size15</string>
		</array>
		<key>modtwosum</key>
		<array>
			<string>modtwosum</string>
			<string>modtwosum.display</string>
		</array>
		<key>oiiint</key>
		<array>
			<string>oiiint</string>
			<string>oiiint.display</string>
		</array>
		<key>oiiint.up</key>
		<array>
			<string>oiiint.up</string>
			<string>oiiint.up.display</string>
		</array>
		<key>oiint</key>
		<array>
			<string>oiint</string>
			<string>oiint.display</string>
		</array>
		<key>oiint.up</key>
		<array>
			<string>oiint.up</string>
			<string>oiint.up.display</string>
		</array>
		<key>oint</key>
		<array>
			<string>oint</string>
			<string>oint.display</string>
		</array>
		<key>oint.up</key>
		<array>
			<string>oint.up</string>
			<string>oint.up.display</string>
		</array>
		<key>ointctrclockwise</key>
		<array>
			<string>ointctrclockwise</string>
			<string>ointctrclockwise.display</string>
		</array>
		<key>ointctrclockwise.up</key>
		<array>
			<string>ointctrclockwise.up</string>
			<string>ointctrclockwise.up.display</string>
		</array>
		<key>prod</key>
		<array>
			<string>prod</string>
			<string>prod.display</string>
		</array>
		<key>rAngle</key>
		<array>
			<string>rAngle</string>
			<string>rAngle.size1</string>
			<string>rAngle.size2</string>
			<string>rAngle.size3</string>
			<string>rAngle.size4</string>
			<string>rAngle.size5</string>
			<string>rAngle.size6</string>
			<string>rAngle.size7</string>
			<string>rAngle.size8</string>
			<string>rAngle.size9</string>
			<string>rAngle.size10</string>
			<string>rAngle.size11</string>
			<string>rAngle.size12</string>
			<string>rAngle.size13</string>
			<string>rAngle.size14</string>
			<string>rAngle.size15</string>
		</array>
		<key>rBrack</key>
		<array>
			<string>rBrack</string>
			<string>rBrack.size1</string>
			<string>rBrack.size2</string>
			<string>rBrack.size3</string>
			<string>rBrack.size4</string>
			<string>rBrack.size5</string>
			<string>rBrack.size6</string>
			<string>rBrack.size7</string>
			<string>rBrack.size8</string>
			<string>rBrack.size9</string>
			<string>rBrack.size10</string>
			<string>rBrack.size11</string>
			<string>rBrack.size12</string>
			<string>rBrack.size13</string>
			<string>rBrack.size14</string>
			<string>rBrack.size15</string>
		</array>
		<key>rangle</key>
		<array>
			<string>rangle</string>
			<string>rangle.size1</string>
			<string>rangle.size2</string>
			<string>rangle.size3</string>
			<string>rangle.size4</string>
			<string>rangle.size5</string>
			<string>rangle.size6</string>
			<string>rangle.size7</string>
			<string>rangle.size8</string>
			<string>rangle.size9</string>
			<string>rangle.size10</string>
			<string>rangle.size11</string>
			<string>rangle.size12</string>
			<string>rangle.size13</string>
			<string>rangle.size14</string>
			<string>rangle.size15</string>
		</array>
		<key>rbrace</key>
		<array>
			<string>rbrace</string>
			<string>rbrace.size1</string>
			<string>rbrace.size2</string>
			<string>rbrace.size3</string>
			<string>rbrace.size4</string>
			<string>rbrace.size5</string>
			<string>rbrace.size6</string>
			<string>rbrace.size7</string>
			<string>rbrace.size8</string>
			<string>rbrace.size9</string>
			<string>rbrace.size10</string>
			<string>rbrace.size11</string>
			<string>rbrace.size12</string>
			<string>rbrace.size13</string>
			<string>rbrace.size14</string>
			<string>rbrace.size15</string>
		</array>
		<key>rbrack</key>
		<array>
			<string>rbrack</string>
			<string>rbrack.size1</string>
			<string>rbrack.size2</string>
			<string>rbrack.size3</string>
			<string>rbrack.size4</string>
			<string>rbrack.size5</string>
			<string>rbrack.size6</string>
			<string>rbrack.size7</string>
			<string>rbrack.size8</string>
			<string>rbrack.size9</string>
			<string>rbrack.size10</string>
			<string>rbrack.size11</string>
			<string>rbrack.size12</string>
			<string>rbrack.size13</string>
			<string>rbrack.size14</string>
			<string>rbrack.size15</string>
		</array>
		<key>rceil</key>
		<array>
			<string>rceil</string>
			<string>rceil.size1</string>
			<string>rceil.size2</string>
			<string>rceil.size3</string>
			<string>rceil.size4</string>
			<string>rceil.size5</string>
			<string>rceil.size6</string>
			<string>rceil.size7</string>
			<string>rceil.size8</string>
			<string>rceil.size9</string>
			<string>rceil.size10</string>
			<string>rceil.size11</string>
			<string>rceil.size12</string>
			<string>rceil.size13</string>
			<string>rceil.size14</string>
			<string>rceil.size15</string>
		</array>
		<key>rfloor</key>
		<array>
			<string>rfloor</string>
			<string>rfloor.size1</string>
			<string>rfloor.size2</string>
			<string>rfloor.size3</string>
			<string>rfloor.size4</string>
			<string>rfloor.size5</string>
			<string>rfloor.size6</string>
			<string>rfloor.size7</string>
			<string>rfloor.size8</string>
			<string>rfloor.size9</string>
			<string>rfloor.size10</string>
			<string>rfloor.size11</string>
			<string>rfloor.size12</string>
			<string>rfloor.size13</string>
			<string>rfloor.size14</string>
			<string>rfloor.size15</string>
		</array>
		<key>rgroup</key>
		<array>
			<string>rgroup</string>
			<string>rgroup.size1</string>
			<string>rgroup.size2</string>
			<string>rgroup.size3</string>
			<string>rgroup.size4</string>
			<string>rgroup.size5</string>
			<string>rgroup.size6</string>
			<string>rgroup.size7</string>
			<string>rgroup.size8</string>
			<string>rgroup.size9</string>
			<string>rgroup.size10</string>
			<string>rgroup.size11</string>
			<string>rgroup.size12</string>
			<string>rgroup.size13</string>
			<string>rgroup.size14</string>
			<string>rgroup.size15</string>
		</array>
		<key>rparen</key>
		<array>
			<string>rparen</string>
			<string>rparen.size1</string>
			<string>rparen.size2</string>
			<string>rparen.size3</string>
			<string>rparen.size4</string>
			<string>rparen.size5</string>
			<string>rparen.size6</string>
			<string>rparen.size7</string>
			<string>rparen.size8</string>
			<string>rparen.size9</string>
			<string>rparen.size10</string>
			<string>rparen.size11</string>
			<string>rparen.size12</string>
			<string>rparen.size13</string>
			<string>rparen.size14</string>
			<string>rparen.size15</string>
		</array>
		<key>sqint</key>
		<array>
			<string>sqint</string>
			<string>sqint.display</string>
		</array>
		<key>sqint.up</key>
		<array>
			<string>sqint.up</string>
			<string>sqint.up.display</string>
		</array>
		<key>sqrt</key>
		<array>
			<string>sqrt</string>
			<string>sqrt.size1</string>
			<string>sqrt.size2</string>
			<string>sqrt.size3</string>
			<string>sqrt.size4</string>
			<string>sqrt.size5</string>
			<string>sqrt.size6</string>
			<string>sqrt.size7</string>
			<string>sqrt.size8</string>
			<string>sqrt.size9</string>
			<string>sqrt.size10</string>
			<string>sqrt.size11</string>
			<string>sqrt.size12</string>
			<string>sqrt.size13</string>
			<string>sqrt.size14</string>
			<string>sqrt.size15</string>
		</array>
		<key>sum</key>
		<array>
			<string>sum</string>
			<string>sum.display</string>
		</array>
		<key>sumint</key>
		<array>
			<string>sumint</string>
			<string>sumint.display</string>
		</array>
		<key>uparrow</key>
		<array/>
		<key>varointclockwise</key>
		<array>
			<string>varointclockwise</string>
			<string>varointclockwise.display</string>
		</array>
		<key>varointclockwise.up</key>
		<array>
			<string>varointclockwise.up</string>
			<string>varointclockwise.up.display</string>
		</array>
		<key>vert</key>
		<array>
			<string>vert</string>
			<string>vert.size1</string>
			<string>vert.size2</string>
			<string>vert.size3</string>
			<string>vert.size4</string>
			<string>vert.size5</string>
			<string>vert.size6</string>
			<string>vert.size7</string>
			<string>vert.size8</string>
			<string>vert.size9</string>
			<string>vert.size10</string>
			<string>vert.size11</string>
			<string>vert.size12</string>
			<string>vert.size13</string>
			<string>vert.size14</string>
			<string>vert.size15</string>
		</array>
		<key>vertoverlay</key>
		<array>
			<string>vertoverlay</string>
			<string>vertoverlay.size1</string>
			<string>vertoverlay.size2</string>
			<string>vertoverlay.size3</string>
			<string>vertoverlay.size4</string>
			<string>vertoverlay.size5</string>
			<string>vertoverlay.size6</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
