<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>Beta</key>
		<integer>301</integer>
		<key>Chi</key>
		<integer>331</integer>
		<key>Epsilon</key>
		<integer>297</integer>
		<key>Eta</key>
		<integer>416</integer>
		<key>Gamma</key>
		<integer>279</integer>
		<key>Iota</key>
		<integer>168</integer>
		<key>Kappa</key>
		<integer>370</integer>
		<key>Lambda</key>
		<integer>373</integer>
		<key>Mu</key>
		<integer>471</integer>
		<key>Nu</key>
		<integer>415</integer>
		<key>Omega</key>
		<integer>412</integer>
		<key>Omicron</key>
		<integer>393</integer>
		<key>Phi</key>
		<integer>373</integer>
		<key>Pi</key>
		<integer>406</integer>
		<key>Psi</key>
		<integer>435</integer>
		<key>Rho</key>
		<integer>301</integer>
		<key>Sigma</key>
		<integer>344</integer>
		<key>Tau</key>
		<integer>306</integer>
		<key>Theta</key>
		<integer>393</integer>
		<key>Upsilon</key>
		<integer>369</integer>
		<key>Xi</key>
		<integer>346</integer>
		<key>Zeta</key>
		<integer>326</integer>
		<key>acutecomb</key>
		<integer>-143</integer>
		<key>afii61248</key>
		<integer>360</integer>
		<key>gravecomb</key>
		<integer>-190</integer>
		<key>j.dtls</key>
		<integer>60</integer>
		<key>tildecomb</key>
		<integer>-215</integer>
		<key>u1D400</key>
		<integer>390</integer>
		<key>u1D401</key>
		<integer>325</integer>
		<key>u1D402</key>
		<integer>370</integer>
		<key>u1D403</key>
		<integer>410</integer>
		<key>u1D404</key>
		<integer>308</integer>
		<key>u1D405</key>
		<integer>284</integer>
		<key>u1D406</key>
		<integer>412</integer>
		<key>u1D407</key>
		<integer>416</integer>
		<key>u1D408</key>
		<integer>194</integer>
		<key>u1D409</key>
		<integer>170</integer>
		<key>u1D40A</key>
		<integer>401</integer>
		<key>u1D40B</key>
		<integer>308</integer>
		<key>u1D40C</key>
		<integer>500</integer>
		<key>u1D40D</key>
		<integer>416</integer>
		<key>u1D40E</key>
		<integer>417</integer>
		<key>u1D40F</key>
		<integer>316</integer>
		<key>u1D410</key>
		<integer>417</integer>
		<key>u1D411</key>
		<integer>374</integer>
		<key>u1D412</key>
		<integer>308</integer>
		<key>u1D413</key>
		<integer>334</integer>
		<key>u1D414</key>
		<integer>393</integer>
		<key>u1D415</key>
		<integer>392</integer>
		<key>u1D416</key>
		<integer>502</integer>
		<key>u1D417</key>
		<integer>334</integer>
		<key>u1D418</key>
		<integer>338</integer>
		<key>u1D419</key>
		<integer>326</integer>
		<key>u1D41A</key>
		<integer>259</integer>
		<key>u1D41B</key>
		<integer>283</integer>
		<key>u1D41C</key>
		<integer>226</integer>
		<key>u1D41D</key>
		<integer>310</integer>
		<key>u1D41E</key>
		<integer>252</integer>
		<key>u1D41F</key>
		<integer>208</integer>
		<key>u1D420</key>
		<integer>280</integer>
		<key>u1D421</key>
		<integer>306</integer>
		<key>u1D422.dtls</key>
		<integer>166</integer>
		<key>u1D423.dtls</key>
		<integer>115</integer>
		<key>u1D424</key>
		<integer>309</integer>
		<key>u1D425</key>
		<integer>160</integer>
		<key>u1D426</key>
		<integer>444</integer>
		<key>u1D427</key>
		<integer>306</integer>
		<key>u1D428</key>
		<integer>278</integer>
		<key>u1D429</key>
		<integer>298</integer>
		<key>u1D42A</key>
		<integer>320</integer>
		<key>u1D42B</key>
		<integer>210</integer>
		<key>u1D42C</key>
		<integer>222</integer>
		<key>u1D42D</key>
		<integer>173</integer>
		<key>u1D42E</key>
		<integer>304</integer>
		<key>u1D42F</key>
		<integer>278</integer>
		<key>u1D430</key>
		<integer>416</integer>
		<key>u1D431</key>
		<integer>252</integer>
		<key>u1D432</key>
		<integer>278</integer>
		<key>u1D433</key>
		<integer>240</integer>
		<key>u1D434</key>
		<integer>530</integer>
		<key>u1D435</key>
		<integer>292</integer>
		<key>u1D436</key>
		<integer>348</integer>
		<key>u1D437</key>
		<integer>384</integer>
		<key>u1D438</key>
		<integer>300</integer>
		<key>u1D439</key>
		<integer>274</integer>
		<key>u1D43A</key>
		<integer>372</integer>
		<key>u1D43B</key>
		<integer>398</integer>
		<key>u1D43C</key>
		<integer>246</integer>
		<key>u1D43D</key>
		<integer>242</integer>
		<key>u1D43E</key>
		<integer>348</integer>
		<key>u1D43F</key>
		<integer>270</integer>
		<key>u1D440</key>
		<integer>460</integer>
		<key>u1D441</key>
		<integer>403</integer>
		<key>u1D442</key>
		<integer>400</integer>
		<key>u1D443</key>
		<integer>302</integer>
		<key>u1D444</key>
		<integer>400</integer>
		<key>u1D445</key>
		<integer>324</integer>
		<key>u1D446</key>
		<integer>340</integer>
		<key>u1D447</key>
		<integer>344</integer>
		<key>u1D448</key>
		<integer>443</integer>
		<key>u1D449</key>
		<integer>414</integer>
		<key>u1D44A</key>
		<integer>526</integer>
		<key>u1D44B</key>
		<integer>377</integer>
		<key>u1D44C</key>
		<integer>364</integer>
		<key>u1D44D</key>
		<integer>328</integer>
		<key>u1D44E</key>
		<integer>306</integer>
		<key>u1D44F</key>
		<integer>235</integer>
		<key>u1D450</key>
		<integer>207</integer>
		<key>u1D451</key>
		<integer>392</integer>
		<key>u1D452</key>
		<integer>194</integer>
		<key>u1D453</key>
		<integer>445</integer>
		<key>u1D454</key>
		<integer>230</integer>
		<key>u1D456</key>
		<integer>149</integer>
		<key>u1D457</key>
		<integer>98</integer>
		<key>u1D458</key>
		<integer>228</integer>
		<key>u1D459</key>
		<integer>188</integer>
		<key>u1D45A</key>
		<integer>382</integer>
		<key>u1D45B</key>
		<integer>269</integer>
		<key>u1D45C</key>
		<integer>214</integer>
		<key>u1D45D</key>
		<integer>229</integer>
		<key>u1D45E</key>
		<integer>228</integer>
		<key>u1D45F</key>
		<integer>205</integer>
		<key>u1D460</key>
		<integer>211</integer>
		<key>u1D461</key>
		<integer>176</integer>
		<key>u1D462</key>
		<integer>272</integer>
		<key>u1D463</key>
		<integer>249</integer>
		<key>u1D464</key>
		<integer>360</integer>
		<key>u1D465</key>
		<integer>246</integer>
		<key>u1D466</key>
		<integer>241</integer>
		<key>u1D467</key>
		<integer>208</integer>
		<key>u1D468</key>
		<integer>456</integer>
		<key>u1D469</key>
		<integer>380</integer>
		<key>u1D46A</key>
		<integer>452</integer>
		<key>u1D46B</key>
		<integer>410</integer>
		<key>u1D46C</key>
		<integer>380</integer>
		<key>u1D46D</key>
		<integer>372</integer>
		<key>u1D46E</key>
		<integer>480</integer>
		<key>u1D46F</key>
		<integer>427</integer>
		<key>u1D470</key>
		<integer>254</integer>
		<key>u1D471</key>
		<integer>260</integer>
		<key>u1D472</key>
		<integer>384</integer>
		<key>u1D473</key>
		<integer>450</integer>
		<key>u1D475</key>
		<integer>444</integer>
		<key>u1D476</key>
		<integer>480</integer>
		<key>u1D477</key>
		<integer>342</integer>
		<key>u1D478</key>
		<integer>470</integer>
		<key>u1D479</key>
		<integer>350</integer>
		<key>u1D47A</key>
		<integer>330</integer>
		<key>u1D47B</key>
		<integer>378</integer>
		<key>u1D47C</key>
		<integer>454</integer>
		<key>u1D47D</key>
		<integer>444</integer>
		<key>u1D47E</key>
		<integer>570</integer>
		<key>u1D47F</key>
		<integer>448</integer>
		<key>u1D480</key>
		<integer>382</integer>
		<key>u1D481</key>
		<integer>338</integer>
		<key>u1D482</key>
		<integer>282</integer>
		<key>u1D483</key>
		<integer>269</integer>
		<key>u1D484</key>
		<integer>234</integer>
		<key>u1D485</key>
		<integer>294</integer>
		<key>u1D486</key>
		<integer>223</integer>
		<key>u1D487</key>
		<integer>449</integer>
		<key>u1D488</key>
		<integer>240</integer>
		<key>u1D489</key>
		<integer>272</integer>
		<key>u1D48A</key>
		<integer>169</integer>
		<key>u1D48A.dtls</key>
		<integer>160</integer>
		<key>u1D48B</key>
		<integer>130</integer>
		<key>u1D48B.dtls</key>
		<integer>105</integer>
		<key>u1D48C</key>
		<integer>281</integer>
		<key>u1D48D</key>
		<integer>238</integer>
		<key>u1D48E</key>
		<integer>411</integer>
		<key>u1D48F</key>
		<integer>269</integer>
		<key>u1D490</key>
		<integer>275</integer>
		<key>u1D491</key>
		<integer>248</integer>
		<key>u1D492</key>
		<integer>272</integer>
		<key>u1D493</key>
		<integer>216</integer>
		<key>u1D494</key>
		<integer>216</integer>
		<key>u1D495</key>
		<integer>226</integer>
		<key>u1D496</key>
		<integer>272</integer>
		<key>u1D497</key>
		<integer>266</integer>
		<key>u1D498</key>
		<integer>414</integer>
		<key>u1D499</key>
		<integer>246</integer>
		<key>u1D49A</key>
		<integer>277</integer>
		<key>u1D49B</key>
		<integer>250</integer>
		<key>u1D49C</key>
		<integer>970</integer>
		<key>u1D49C.salt</key>
		<integer>558</integer>
		<key>u1D49E</key>
		<integer>720</integer>
		<key>u1D49F</key>
		<integer>611</integer>
		<key>u1D49F.salt</key>
		<integer>452</integer>
		<key>u1D4A2</key>
		<integer>699</integer>
		<key>u1D4A2.salt</key>
		<integer>370</integer>
		<key>u1D4A5</key>
		<integer>820</integer>
		<key>u1D4A5.salt</key>
		<integer>444</integer>
		<key>u1D4A6</key>
		<integer>785</integer>
		<key>u1D4A6.salt</key>
		<integer>473</integer>
		<key>u1D4A9</key>
		<integer>904</integer>
		<key>u1D4A9.salt</key>
		<integer>483</integer>
		<key>u1D4AA</key>
		<integer>622</integer>
		<key>u1D4AA.salt</key>
		<integer>370</integer>
		<key>u1D4AB</key>
		<integer>734</integer>
		<key>u1D4AB.salt</key>
		<integer>376</integer>
		<key>u1D4AC</key>
		<integer>621</integer>
		<key>u1D4AC.salt</key>
		<integer>370</integer>
		<key>u1D4AE</key>
		<integer>804</integer>
		<key>u1D4AE.salt</key>
		<integer>357</integer>
		<key>u1D4AF</key>
		<integer>690</integer>
		<key>u1D4AF.salt</key>
		<integer>441</integer>
		<key>u1D4B0</key>
		<integer>608</integer>
		<key>u1D4B0.salt</key>
		<integer>497</integer>
		<key>u1D4B1</key>
		<integer>770</integer>
		<key>u1D4B1.salt</key>
		<integer>481</integer>
		<key>u1D4B2</key>
		<integer>816</integer>
		<key>u1D4B2.salt</key>
		<integer>650</integer>
		<key>u1D4B3</key>
		<integer>721</integer>
		<key>u1D4B3.salt</key>
		<integer>388</integer>
		<key>u1D4B4</key>
		<integer>748</integer>
		<key>u1D4B4.salt</key>
		<integer>431</integer>
		<key>u1D4B5</key>
		<integer>666</integer>
		<key>u1D4B5.salt</key>
		<integer>455</integer>
		<key>u1D4B6</key>
		<integer>507</integer>
		<key>u1D4B7</key>
		<integer>585</integer>
		<key>u1D4B8</key>
		<integer>445</integer>
		<key>u1D4B9</key>
		<integer>735</integer>
		<key>u1D4BB</key>
		<integer>960</integer>
		<key>u1D4BD</key>
		<integer>681</integer>
		<key>u1D4BE</key>
		<integer>384</integer>
		<key>u1D4BE.dtls</key>
		<integer>293</integer>
		<key>u1D4BF</key>
		<integer>777</integer>
		<key>u1D4BF.dtls</key>
		<integer>335</integer>
		<key>u1D4C0</key>
		<integer>570</integer>
		<key>u1D4C1</key>
		<integer>732</integer>
		<key>u1D4C1.salt</key>
		<integer>367</integer>
		<key>u1D4C2</key>
		<integer>673</integer>
		<key>u1D4C3</key>
		<integer>474</integer>
		<key>u1D4C5</key>
		<integer>784</integer>
		<key>u1D4C6</key>
		<integer>447</integer>
		<key>u1D4C7</key>
		<integer>480</integer>
		<key>u1D4C8</key>
		<integer>403</integer>
		<key>u1D4C9</key>
		<integer>368</integer>
		<key>u1D4CA</key>
		<integer>477</integer>
		<key>u1D4CB</key>
		<integer>436</integer>
		<key>u1D4CC</key>
		<integer>593</integer>
		<key>u1D4CD</key>
		<integer>548</integer>
		<key>u1D4CE</key>
		<integer>720</integer>
		<key>u1D4CF</key>
		<integer>492</integer>
		<key>u1D4D0</key>
		<integer>954</integer>
		<key>u1D4D1</key>
		<integer>814</integer>
		<key>u1D4D2</key>
		<integer>727</integer>
		<key>u1D4D3</key>
		<integer>608</integer>
		<key>u1D4D4</key>
		<integer>618</integer>
		<key>u1D4D5</key>
		<integer>818</integer>
		<key>u1D4D6</key>
		<integer>692</integer>
		<key>u1D4D7</key>
		<integer>736</integer>
		<key>u1D4D8</key>
		<integer>852</integer>
		<key>u1D4D9</key>
		<integer>819</integer>
		<key>u1D4DA</key>
		<integer>789</integer>
		<key>u1D4DB</key>
		<integer>811</integer>
		<key>u1D4DC</key>
		<integer>975</integer>
		<key>u1D4DD</key>
		<integer>905</integer>
		<key>u1D4DE</key>
		<integer>616</integer>
		<key>u1D4DF</key>
		<integer>732</integer>
		<key>u1D4E0</key>
		<integer>588</integer>
		<key>u1D4E1</key>
		<integer>803</integer>
		<key>u1D4E2</key>
		<integer>812</integer>
		<key>u1D4E3</key>
		<integer>766</integer>
		<key>u1D4E4</key>
		<integer>611</integer>
		<key>u1D4E5</key>
		<integer>706</integer>
		<key>u1D4E6</key>
		<integer>836</integer>
		<key>u1D4E7</key>
		<integer>704</integer>
		<key>u1D4E8</key>
		<integer>730</integer>
		<key>u1D4E9</key>
		<integer>757</integer>
		<key>u1D4EA</key>
		<integer>507</integer>
		<key>u1D4EB</key>
		<integer>678</integer>
		<key>u1D4EC</key>
		<integer>436</integer>
		<key>u1D4ED</key>
		<integer>763</integer>
		<key>u1D4EE</key>
		<integer>418</integer>
		<key>u1D4EF</key>
		<integer>977</integer>
		<key>u1D4F0</key>
		<integer>743</integer>
		<key>u1D4F1</key>
		<integer>705</integer>
		<key>u1D4F2</key>
		<integer>355</integer>
		<key>u1D4F2.dtls</key>
		<integer>130</integer>
		<key>u1D4F3</key>
		<integer>771</integer>
		<key>u1D4F3.dtls</key>
		<integer>18</integer>
		<key>u1D4F4</key>
		<integer>745</integer>
		<key>u1D4F5</key>
		<integer>525</integer>
		<key>u1D4F6</key>
		<integer>762</integer>
		<key>u1D4F7</key>
		<integer>476</integer>
		<key>u1D4F8</key>
		<integer>502</integer>
		<key>u1D4F9</key>
		<integer>756</integer>
		<key>u1D4FA</key>
		<integer>583</integer>
		<key>u1D4FB</key>
		<integer>494</integer>
		<key>u1D4FC</key>
		<integer>400</integer>
		<key>u1D4FD</key>
		<integer>492</integer>
		<key>u1D4FE</key>
		<integer>434</integer>
		<key>u1D4FF</key>
		<integer>425</integer>
		<key>u1D500</key>
		<integer>586</integer>
		<key>u1D501</key>
		<integer>539</integer>
		<key>u1D502</key>
		<integer>656</integer>
		<key>u1D503</key>
		<integer>479</integer>
		<key>u1D504</key>
		<integer>348</integer>
		<key>u1D505</key>
		<integer>404</integer>
		<key>u1D507</key>
		<integer>408</integer>
		<key>u1D508</key>
		<integer>342</integer>
		<key>u1D509</key>
		<integer>368</integer>
		<key>u1D50A</key>
		<integer>414</integer>
		<key>u1D50D</key>
		<integer>292</integer>
		<key>u1D50E</key>
		<integer>330</integer>
		<key>u1D50F</key>
		<integer>320</integer>
		<key>u1D510</key>
		<integer>474</integer>
		<key>u1D511</key>
		<integer>366</integer>
		<key>u1D512</key>
		<integer>398</integer>
		<key>u1D513</key>
		<integer>376</integer>
		<key>u1D514</key>
		<integer>420</integer>
		<key>u1D516</key>
		<integer>430</integer>
		<key>u1D517</key>
		<integer>365</integer>
		<key>u1D518</key>
		<integer>366</integer>
		<key>u1D519</key>
		<integer>376</integer>
		<key>u1D51A</key>
		<integer>477</integer>
		<key>u1D51B</key>
		<integer>329</integer>
		<key>u1D51C</key>
		<integer>330</integer>
		<key>u1D51E</key>
		<integer>268</integer>
		<key>u1D51F</key>
		<integer>230</integer>
		<key>u1D520</key>
		<integer>220</integer>
		<key>u1D521</key>
		<integer>250</integer>
		<key>u1D522</key>
		<integer>235</integer>
		<key>u1D523</key>
		<integer>160</integer>
		<key>u1D524</key>
		<integer>248</integer>
		<key>u1D525</key>
		<integer>232</integer>
		<key>u1D526</key>
		<integer>145</integer>
		<key>u1D526.dtls</key>
		<integer>145</integer>
		<key>u1D527</key>
		<integer>130</integer>
		<key>u1D527.dtls</key>
		<integer>130</integer>
		<key>u1D528</key>
		<integer>164</integer>
		<key>u1D529</key>
		<integer>154</integer>
		<key>u1D52A</key>
		<integer>399</integer>
		<key>u1D52B</key>
		<integer>267</integer>
		<key>u1D52C</key>
		<integer>258</integer>
		<key>u1D52D</key>
		<integer>282</integer>
		<key>u1D52E</key>
		<integer>260</integer>
		<key>u1D52F</key>
		<integer>190</integer>
		<key>u1D530</key>
		<integer>232</integer>
		<key>u1D531</key>
		<integer>156</integer>
		<key>u1D532</key>
		<integer>269</integer>
		<key>u1D533</key>
		<integer>231</integer>
		<key>u1D534</key>
		<integer>377</integer>
		<key>u1D535</key>
		<integer>216</integer>
		<key>u1D536</key>
		<integer>250</integer>
		<key>u1D537</key>
		<integer>192</integer>
		<key>u1D538</key>
		<integer>440</integer>
		<key>u1D539</key>
		<integer>366</integer>
		<key>u1D53B</key>
		<integer>449</integer>
		<key>u1D53C</key>
		<integer>356</integer>
		<key>u1D53D</key>
		<integer>338</integer>
		<key>u1D53E</key>
		<integer>375</integer>
		<key>u1D540</key>
		<integer>227</integer>
		<key>u1D541</key>
		<integer>202</integer>
		<key>u1D542</key>
		<integer>429</integer>
		<key>u1D543</key>
		<integer>362</integer>
		<key>u1D544</key>
		<integer>532</integer>
		<key>u1D546</key>
		<integer>393</integer>
		<key>u1D54A</key>
		<integer>264</integer>
		<key>u1D54B</key>
		<integer>369</integer>
		<key>u1D54C</key>
		<integer>450</integer>
		<key>u1D54D</key>
		<integer>422</integer>
		<key>u1D54E</key>
		<integer>556</integer>
		<key>u1D54F</key>
		<integer>390</integer>
		<key>u1D550</key>
		<integer>332</integer>
		<key>u1D552</key>
		<integer>319</integer>
		<key>u1D553</key>
		<integer>334</integer>
		<key>u1D554</key>
		<integer>273</integer>
		<key>u1D555</key>
		<integer>333</integer>
		<key>u1D556</key>
		<integer>273</integer>
		<key>u1D557</key>
		<integer>237</integer>
		<key>u1D558</key>
		<integer>307</integer>
		<key>u1D559</key>
		<integer>337</integer>
		<key>u1D55A</key>
		<integer>194</integer>
		<key>u1D55A.dtls</key>
		<integer>194</integer>
		<key>u1D55B</key>
		<integer>140</integer>
		<key>u1D55B.dtls</key>
		<integer>140</integer>
		<key>u1D55C</key>
		<integer>322</integer>
		<key>u1D55D</key>
		<integer>208</integer>
		<key>u1D55E</key>
		<integer>492</integer>
		<key>u1D55F</key>
		<integer>346</integer>
		<key>u1D560</key>
		<integer>300</integer>
		<key>u1D561</key>
		<integer>348</integer>
		<key>u1D562</key>
		<integer>341</integer>
		<key>u1D563</key>
		<integer>262</integer>
		<key>u1D564</key>
		<integer>248</integer>
		<key>u1D565</key>
		<integer>252</integer>
		<key>u1D566</key>
		<integer>349</integer>
		<key>u1D567</key>
		<integer>335</integer>
		<key>u1D568</key>
		<integer>430</integer>
		<key>u1D569</key>
		<integer>346</integer>
		<key>u1D56A</key>
		<integer>346</integer>
		<key>u1D56B</key>
		<integer>280</integer>
		<key>u1D56C</key>
		<integer>390</integer>
		<key>u1D56D</key>
		<integer>388</integer>
		<key>u1D56E</key>
		<integer>372</integer>
		<key>u1D56F</key>
		<integer>360</integer>
		<key>u1D570</key>
		<integer>374</integer>
		<key>u1D571</key>
		<integer>335</integer>
		<key>u1D572</key>
		<integer>416</integer>
		<key>u1D573</key>
		<integer>371</integer>
		<key>u1D574</key>
		<integer>320</integer>
		<key>u1D575</key>
		<integer>313</integer>
		<key>u1D576</key>
		<integer>373</integer>
		<key>u1D577</key>
		<integer>294</integer>
		<key>u1D578</key>
		<integer>484</integer>
		<key>u1D579</key>
		<integer>419</integer>
		<key>u1D57A</key>
		<integer>375</integer>
		<key>u1D57B</key>
		<integer>398</integer>
		<key>u1D57C</key>
		<integer>400</integer>
		<key>u1D57D</key>
		<integer>416</integer>
		<key>u1D57E</key>
		<integer>367</integer>
		<key>u1D57F</key>
		<integer>352</integer>
		<key>u1D580</key>
		<integer>354</integer>
		<key>u1D581</key>
		<integer>394</integer>
		<key>u1D582</key>
		<integer>503</integer>
		<key>u1D583</key>
		<integer>337</integer>
		<key>u1D584</key>
		<integer>377</integer>
		<key>u1D585</key>
		<integer>310</integer>
		<key>u1D586</key>
		<integer>276</integer>
		<key>u1D587</key>
		<integer>258</integer>
		<key>u1D588</key>
		<integer>208</integer>
		<key>u1D589</key>
		<integer>266</integer>
		<key>u1D58A</key>
		<integer>230</integer>
		<key>u1D58B</key>
		<integer>196</integer>
		<key>u1D58C</key>
		<integer>254</integer>
		<key>u1D58D</key>
		<integer>256</integer>
		<key>u1D58E</key>
		<integer>162</integer>
		<key>u1D58E.dtls</key>
		<integer>162</integer>
		<key>u1D58F</key>
		<integer>162</integer>
		<key>u1D58F.dtls</key>
		<integer>162</integer>
		<key>u1D590</key>
		<integer>198</integer>
		<key>u1D591</key>
		<integer>178</integer>
		<key>u1D592</key>
		<integer>414</integer>
		<key>u1D593</key>
		<integer>272</integer>
		<key>u1D594</key>
		<integer>248</integer>
		<key>u1D595</key>
		<integer>270</integer>
		<key>u1D596</key>
		<integer>256</integer>
		<key>u1D597</key>
		<integer>228</integer>
		<key>u1D598</key>
		<integer>244</integer>
		<key>u1D599</key>
		<integer>170</integer>
		<key>u1D59A</key>
		<integer>276</integer>
		<key>u1D59B</key>
		<integer>252</integer>
		<key>u1D59C</key>
		<integer>370</integer>
		<key>u1D59D</key>
		<integer>219</integer>
		<key>u1D59E</key>
		<integer>236</integer>
		<key>u1D59F</key>
		<integer>190</integer>
		<key>u1D5A0</key>
		<integer>333</integer>
		<key>u1D5A1</key>
		<integer>354</integer>
		<key>u1D5A2</key>
		<integer>328</integer>
		<key>u1D5A3</key>
		<integer>380</integer>
		<key>u1D5A4</key>
		<integer>324</integer>
		<key>u1D5A5</key>
		<integer>310</integer>
		<key>u1D5A6</key>
		<integer>333</integer>
		<key>u1D5A7</key>
		<integer>354</integer>
		<key>u1D5A8</key>
		<integer>138</integer>
		<key>u1D5A9</key>
		<integer>215</integer>
		<key>u1D5AA</key>
		<integer>374</integer>
		<key>u1D5AB</key>
		<integer>296</integer>
		<key>u1D5AC</key>
		<integer>437</integer>
		<key>u1D5AD</key>
		<integer>354</integer>
		<key>u1D5AE</key>
		<integer>368</integer>
		<key>u1D5AF</key>
		<integer>339</integer>
		<key>u1D5B0</key>
		<integer>368</integer>
		<key>u1D5B1</key>
		<integer>356</integer>
		<key>u1D5B2</key>
		<integer>272</integer>
		<key>u1D5B3</key>
		<integer>340</integer>
		<key>u1D5B4</key>
		<integer>344</integer>
		<key>u1D5B5</key>
		<integer>333</integer>
		<key>u1D5B6</key>
		<integer>472</integer>
		<key>u1D5B7</key>
		<integer>333</integer>
		<key>u1D5B8</key>
		<integer>333</integer>
		<key>u1D5B9</key>
		<integer>308</integer>
		<key>u1D5BA</key>
		<integer>222</integer>
		<key>u1D5BB</key>
		<integer>281</integer>
		<key>u1D5BC</key>
		<integer>226</integer>
		<key>u1D5BD</key>
		<integer>235</integer>
		<key>u1D5BE</key>
		<integer>224</integer>
		<key>u1D5BF</key>
		<integer>187</integer>
		<key>u1D5C0</key>
		<integer>256</integer>
		<key>u1D5C1</key>
		<integer>258</integer>
		<key>u1D5C2</key>
		<integer>118</integer>
		<key>u1D5C2.dtls</key>
		<integer>118</integer>
		<key>u1D5C3</key>
		<integer>62</integer>
		<key>u1D5C3.dtls</key>
		<integer>62</integer>
		<key>u1D5C4</key>
		<integer>278</integer>
		<key>u1D5C5</key>
		<integer>118</integer>
		<key>u1D5C6</key>
		<integer>397</integer>
		<key>u1D5C7</key>
		<integer>258</integer>
		<key>u1D5C8</key>
		<integer>250</integer>
		<key>u1D5C9</key>
		<integer>281</integer>
		<key>u1D5CA</key>
		<integer>235</integer>
		<key>u1D5CB</key>
		<integer>204</integer>
		<key>u1D5CC</key>
		<integer>194</integer>
		<key>u1D5CD</key>
		<integer>176</integer>
		<key>u1D5CE</key>
		<integer>258</integer>
		<key>u1D5CF</key>
		<integer>230</integer>
		<key>u1D5D0</key>
		<integer>341</integer>
		<key>u1D5D1</key>
		<integer>230</integer>
		<key>u1D5D2</key>
		<integer>230</integer>
		<key>u1D5D3</key>
		<integer>215</integer>
		<key>u1D5D4</key>
		<integer>366</integer>
		<key>u1D5D5</key>
		<integer>381</integer>
		<key>u1D5D6</key>
		<integer>354</integer>
		<key>u1D5D7</key>
		<integer>412</integer>
		<key>u1D5D8</key>
		<integer>343</integer>
		<key>u1D5D9</key>
		<integer>328</integer>
		<key>u1D5DA</key>
		<integer>360</integer>
		<key>u1D5DB</key>
		<integer>396</integer>
		<key>u1D5DC</key>
		<integer>166</integer>
		<key>u1D5DD</key>
		<integer>236</integer>
		<key>u1D5DE</key>
		<integer>396</integer>
		<key>u1D5DF</key>
		<integer>312</integer>
		<key>u1D5E0</key>
		<integer>488</integer>
		<key>u1D5E1</key>
		<integer>396</integer>
		<key>u1D5E2</key>
		<integer>396</integer>
		<key>u1D5E3</key>
		<integer>366</integer>
		<key>u1D5E4</key>
		<integer>396</integer>
		<key>u1D5E5</key>
		<integer>372</integer>
		<key>u1D5E6</key>
		<integer>299</integer>
		<key>u1D5E7</key>
		<integer>366</integer>
		<key>u1D5E8</key>
		<integer>382</integer>
		<key>u1D5E9</key>
		<integer>366</integer>
		<key>u1D5EA</key>
		<integer>519</integer>
		<key>u1D5EB</key>
		<integer>366</integer>
		<key>u1D5EC</key>
		<integer>452</integer>
		<key>u1D5ED</key>
		<integer>338</integer>
		<key>u1D5EE</key>
		<integer>248</integer>
		<key>u1D5EF</key>
		<integer>292</integer>
		<key>u1D5F0</key>
		<integer>247</integer>
		<key>u1D5F1</key>
		<integer>268</integer>
		<key>u1D5F2</key>
		<integer>355</integer>
		<key>u1D5F3</key>
		<integer>206</integer>
		<key>u1D5F4</key>
		<integer>280</integer>
		<key>u1D5F5</key>
		<integer>280</integer>
		<key>u1D5F6</key>
		<integer>128</integer>
		<key>u1D5F6.dtls</key>
		<integer>128</integer>
		<key>u1D5F7</key>
		<integer>76</integer>
		<key>u1D5F7.dtls</key>
		<integer>76</integer>
		<key>u1D5F8</key>
		<integer>283</integer>
		<key>u1D5F9</key>
		<integer>128</integer>
		<key>u1D5FA</key>
		<integer>433</integer>
		<key>u1D5FB</key>
		<integer>280</integer>
		<key>u1D5FC</key>
		<integer>274</integer>
		<key>u1D5FD</key>
		<integer>292</integer>
		<key>u1D5FE</key>
		<integer>268</integer>
		<key>u1D5FF</key>
		<integer>208</integer>
		<key>u1D600</key>
		<integer>214</integer>
		<key>u1D601</key>
		<integer>196</integer>
		<key>u1D602</key>
		<integer>280</integer>
		<key>u1D603</key>
		<integer>250</integer>
		<key>u1D604</key>
		<integer>372</integer>
		<key>u1D605</key>
		<integer>249</integer>
		<key>u1D606</key>
		<integer>251</integer>
		<key>u1D607</key>
		<integer>236</integer>
		<key>u1D608</key>
		<integer>333</integer>
		<key>u1D609</key>
		<integer>396</integer>
		<key>u1D60A</key>
		<integer>426</integer>
		<key>u1D60B</key>
		<integer>422</integer>
		<key>u1D60C</key>
		<integer>390</integer>
		<key>u1D60D</key>
		<integer>384</integer>
		<key>u1D60E</key>
		<integer>432</integer>
		<key>u1D60F</key>
		<integer>428</integer>
		<key>u1D610</key>
		<integer>212</integer>
		<key>u1D611</key>
		<integer>291</integer>
		<key>u1D612</key>
		<integer>440</integer>
		<key>u1D613</key>
		<integer>304</integer>
		<key>u1D614</key>
		<integer>511</integer>
		<key>u1D615</key>
		<integer>428</integer>
		<key>u1D617</key>
		<integer>393</integer>
		<key>u1D618</key>
		<integer>440</integer>
		<key>u1D619</key>
		<integer>398</integer>
		<key>u1D61A</key>
		<integer>330</integer>
		<key>u1D61B</key>
		<integer>473</integer>
		<key>u1D61C</key>
		<integer>439</integer>
		<key>u1D61D</key>
		<integer>480</integer>
		<key>u1D61E</key>
		<integer>619</integer>
		<key>u1D61F</key>
		<integer>386</integer>
		<key>u1D620</key>
		<integer>480</integer>
		<key>u1D621</key>
		<integer>378</integer>
		<key>u1D622</key>
		<integer>265</integer>
		<key>u1D623</key>
		<integer>308</integer>
		<key>u1D624</key>
		<integer>288</integer>
		<key>u1D625</key>
		<integer>329</integer>
		<key>u1D626</key>
		<integer>274</integer>
		<key>u1D627</key>
		<integer>298</integer>
		<key>u1D628</key>
		<integer>291</integer>
		<key>u1D629</key>
		<integer>293</integer>
		<key>u1D62A</key>
		<integer>194</integer>
		<key>u1D62A.dtls</key>
		<integer>166</integer>
		<key>u1D62B</key>
		<integer>116</integer>
		<key>u1D62B.dtls</key>
		<integer>90</integer>
		<key>u1D62C</key>
		<integer>314</integer>
		<key>u1D62D</key>
		<integer>192</integer>
		<key>u1D62E</key>
		<integer>432</integer>
		<key>u1D62F</key>
		<integer>293</integer>
		<key>u1D630</key>
		<integer>296</integer>
		<key>u1D631</key>
		<integer>288</integer>
		<key>u1D632</key>
		<integer>304</integer>
		<key>u1D633</key>
		<integer>253</integer>
		<key>u1D634</key>
		<integer>235</integer>
		<key>u1D635</key>
		<integer>256</integer>
		<key>u1D636</key>
		<integer>314</integer>
		<key>u1D637</key>
		<integer>324</integer>
		<key>u1D638</key>
		<integer>435</integer>
		<key>u1D639</key>
		<integer>269</integer>
		<key>u1D63A</key>
		<integer>270</integer>
		<key>u1D63B</key>
		<integer>261</integer>
		<key>u1D63C</key>
		<integer>349</integer>
		<key>u1D63D</key>
		<integer>424</integer>
		<key>u1D63E</key>
		<integer>458</integer>
		<key>u1D63F</key>
		<integer>436</integer>
		<key>u1D640</key>
		<integer>375</integer>
		<key>u1D641</key>
		<integer>368</integer>
		<key>u1D642</key>
		<integer>471</integer>
		<key>u1D643</key>
		<integer>427</integer>
		<key>u1D644</key>
		<integer>295</integer>
		<key>u1D645</key>
		<integer>299</integer>
		<key>u1D646</key>
		<integer>432</integer>
		<key>u1D647</key>
		<integer>288</integer>
		<key>u1D648</key>
		<integer>511</integer>
		<key>u1D649</key>
		<integer>427</integer>
		<key>u1D64A</key>
		<integer>458</integer>
		<key>u1D64B</key>
		<integer>406</integer>
		<key>u1D64C</key>
		<integer>458</integer>
		<key>u1D64D</key>
		<integer>412</integer>
		<key>u1D64E</key>
		<integer>334</integer>
		<key>u1D64F</key>
		<integer>479</integer>
		<key>u1D650</key>
		<integer>483</integer>
		<key>u1D651</key>
		<integer>474</integer>
		<key>u1D652</key>
		<integer>614</integer>
		<key>u1D653</key>
		<integer>378</integer>
		<key>u1D654</key>
		<integer>474</integer>
		<key>u1D655</key>
		<integer>379</integer>
		<key>u1D656</key>
		<integer>265</integer>
		<key>u1D657</key>
		<integer>306</integer>
		<key>u1D658</key>
		<integer>290</integer>
		<key>u1D659</key>
		<integer>331</integer>
		<key>u1D65A</key>
		<integer>274</integer>
		<key>u1D65B</key>
		<integer>297</integer>
		<key>u1D65C</key>
		<integer>291</integer>
		<key>u1D65D</key>
		<integer>291</integer>
		<key>u1D65E</key>
		<integer>194</integer>
		<key>u1D65F</key>
		<integer>116</integer>
		<key>u1D660</key>
		<integer>327</integer>
		<key>u1D661</key>
		<integer>192</integer>
		<key>u1D662</key>
		<integer>430</integer>
		<key>u1D663</key>
		<integer>291</integer>
		<key>u1D664</key>
		<integer>296</integer>
		<key>u1D665</key>
		<integer>286</integer>
		<key>u1D666</key>
		<integer>306</integer>
		<key>u1D667</key>
		<integer>253</integer>
		<key>u1D668</key>
		<integer>237</integer>
		<key>u1D669</key>
		<integer>256</integer>
		<key>u1D66A</key>
		<integer>316</integer>
		<key>u1D66B</key>
		<integer>329</integer>
		<key>u1D66C</key>
		<integer>440</integer>
		<key>u1D66D</key>
		<integer>269</integer>
		<key>u1D66E</key>
		<integer>277</integer>
		<key>u1D66F</key>
		<integer>261</integer>
		<key>u1D6A4</key>
		<integer>198</integer>
		<key>u1D6A5</key>
		<integer>204</integer>
		<key>u1D6A8</key>
		<integer>390</integer>
		<key>u1D6A9</key>
		<integer>325</integer>
		<key>u1D6AA</key>
		<integer>284</integer>
		<key>u1D6AB</key>
		<integer>344</integer>
		<key>u1D6AC</key>
		<integer>308</integer>
		<key>u1D6AD</key>
		<integer>326</integer>
		<key>u1D6AE</key>
		<integer>416</integer>
		<key>u1D6AF</key>
		<integer>417</integer>
		<key>u1D6B0</key>
		<integer>194</integer>
		<key>u1D6B1</key>
		<integer>401</integer>
		<key>u1D6B2</key>
		<integer>393</integer>
		<key>u1D6B3</key>
		<integer>500</integer>
		<key>u1D6B4</key>
		<integer>416</integer>
		<key>u1D6B5</key>
		<integer>343</integer>
		<key>u1D6B6</key>
		<integer>417</integer>
		<key>u1D6B7</key>
		<integer>410</integer>
		<key>u1D6B8</key>
		<integer>316</integer>
		<key>u1D6B9</key>
		<integer>417</integer>
		<key>u1D6BA</key>
		<integer>322</integer>
		<key>u1D6BB</key>
		<integer>334</integer>
		<key>u1D6BC</key>
		<integer>378</integer>
		<key>u1D6BD</key>
		<integer>480</integer>
		<key>u1D6BE</key>
		<integer>369</integer>
		<key>u1D6BF</key>
		<integer>468</integer>
		<key>u1D6C0</key>
		<integer>406</integer>
		<key>u1D6C1</key>
		<integer>344</integer>
		<key>u1D6C2</key>
		<integer>348</integer>
		<key>u1D6C3</key>
		<integer>324</integer>
		<key>u1D6C4</key>
		<integer>338</integer>
		<key>u1D6C5</key>
		<integer>297</integer>
		<key>u1D6C6</key>
		<integer>263</integer>
		<key>u1D6C7</key>
		<integer>292</integer>
		<key>u1D6C8</key>
		<integer>300</integer>
		<key>u1D6C9</key>
		<integer>337</integer>
		<key>u1D6CA</key>
		<integer>136</integer>
		<key>u1D6CB</key>
		<integer>268</integer>
		<key>u1D6CC</key>
		<integer>335</integer>
		<key>u1D6CD</key>
		<integer>326</integer>
		<key>u1D6CE</key>
		<integer>302</integer>
		<key>u1D6CF</key>
		<integer>320</integer>
		<key>u1D6D0</key>
		<integer>278</integer>
		<key>u1D6D1</key>
		<integer>346</integer>
		<key>u1D6D2</key>
		<integer>280</integer>
		<key>u1D6D3</key>
		<integer>268</integer>
		<key>u1D6D4</key>
		<integer>307</integer>
		<key>u1D6D5</key>
		<integer>287</integer>
		<key>u1D6D6</key>
		<integer>292</integer>
		<key>u1D6D7</key>
		<integer>408</integer>
		<key>u1D6D8</key>
		<integer>304</integer>
		<key>u1D6D9</key>
		<integer>414</integer>
		<key>u1D6DA</key>
		<integer>460</integer>
		<key>u1D6DB</key>
		<integer>268</integer>
		<key>u1D6DC</key>
		<integer>294</integer>
		<key>u1D6DD</key>
		<integer>322</integer>
		<key>u1D6DE</key>
		<integer>443</integer>
		<key>u1D6DF</key>
		<integer>366</integer>
		<key>u1D6E0</key>
		<integer>369</integer>
		<key>u1D6E1</key>
		<integer>415</integer>
		<key>u1D6E2</key>
		<integer>482</integer>
		<key>u1D6E3</key>
		<integer>292</integer>
		<key>u1D6E4</key>
		<integer>354</integer>
		<key>u1D6E5</key>
		<integer>410</integer>
		<key>u1D6E6</key>
		<integer>354</integer>
		<key>u1D6E7</key>
		<integer>384</integer>
		<key>u1D6E8</key>
		<integer>460</integer>
		<key>u1D6E9</key>
		<integer>395</integer>
		<key>u1D6EA</key>
		<integer>242</integer>
		<key>u1D6EB</key>
		<integer>435</integer>
		<key>u1D6EC</key>
		<integer>454</integer>
		<key>u1D6ED</key>
		<integer>538</integer>
		<key>u1D6EE</key>
		<integer>403</integer>
		<key>u1D6EF</key>
		<integer>372</integer>
		<key>u1D6F0</key>
		<integer>400</integer>
		<key>u1D6F1</key>
		<integer>450</integer>
		<key>u1D6F2</key>
		<integer>355</integer>
		<key>u1D6F3</key>
		<integer>417</integer>
		<key>u1D6F4</key>
		<integer>356</integer>
		<key>u1D6F5</key>
		<integer>344</integer>
		<key>u1D6F6</key>
		<integer>430</integer>
		<key>u1D6F7</key>
		<integer>434</integer>
		<key>u1D6F8</key>
		<integer>399</integer>
		<key>u1D6F9</key>
		<integer>494</integer>
		<key>u1D6FA</key>
		<integer>477</integer>
		<key>u1D6FB</key>
		<integer>405</integer>
		<key>u1D6FC</key>
		<integer>333</integer>
		<key>u1D6FD</key>
		<integer>333</integer>
		<key>u1D6FE</key>
		<integer>320</integer>
		<key>u1D6FF</key>
		<integer>315</integer>
		<key>u1D700</key>
		<integer>309</integer>
		<key>u1D701</key>
		<integer>333</integer>
		<key>u1D702</key>
		<integer>351</integer>
		<key>u1D703</key>
		<integer>401</integer>
		<key>u1D704</key>
		<integer>171</integer>
		<key>u1D705</key>
		<integer>291</integer>
		<key>u1D706</key>
		<integer>358</integer>
		<key>u1D707</key>
		<integer>350</integer>
		<key>u1D708</key>
		<integer>285</integer>
		<key>u1D709</key>
		<integer>377</integer>
		<key>u1D70A</key>
		<integer>241</integer>
		<key>u1D70B</key>
		<integer>383</integer>
		<key>u1D70C</key>
		<integer>347</integer>
		<key>u1D70D</key>
		<integer>322</integer>
		<key>u1D70E</key>
		<integer>313</integer>
		<key>u1D70F</key>
		<integer>308</integer>
		<key>u1D710</key>
		<integer>312</integer>
		<key>u1D711</key>
		<integer>378</integer>
		<key>u1D712</key>
		<integer>375</integer>
		<key>u1D713</key>
		<integer>439</integer>
		<key>u1D714</key>
		<integer>393</integer>
		<key>u1D715</key>
		<integer>373</integer>
		<key>u1D716</key>
		<integer>287</integer>
		<key>u1D717</key>
		<integer>363</integer>
		<key>u1D718</key>
		<integer>464</integer>
		<key>u1D719</key>
		<integer>410</integer>
		<key>u1D71A</key>
		<integer>360</integer>
		<key>u1D71B</key>
		<integer>350</integer>
		<key>u1D71C</key>
		<integer>443</integer>
		<key>u1D71D</key>
		<integer>374</integer>
		<key>u1D71E</key>
		<integer>356</integer>
		<key>u1D71F</key>
		<integer>405</integer>
		<key>u1D720</key>
		<integer>356</integer>
		<key>u1D721</key>
		<integer>412</integer>
		<key>u1D722</key>
		<integer>487</integer>
		<key>u1D723</key>
		<integer>490</integer>
		<key>u1D724</key>
		<integer>254</integer>
		<key>u1D725</key>
		<integer>417</integer>
		<key>u1D726</key>
		<integer>443</integer>
		<key>u1D727</key>
		<integer>500</integer>
		<key>u1D728</key>
		<integer>418</integer>
		<key>u1D729</key>
		<integer>399</integer>
		<key>u1D72A</key>
		<integer>493</integer>
		<key>u1D72B</key>
		<integer>491</integer>
		<key>u1D72C</key>
		<integer>376</integer>
		<key>u1D72D</key>
		<integer>493</integer>
		<key>u1D72E</key>
		<integer>426</integer>
		<key>u1D72F</key>
		<integer>394</integer>
		<key>u1D730</key>
		<integer>423</integer>
		<key>u1D731</key>
		<integer>545</integer>
		<key>u1D732</key>
		<integer>389</integer>
		<key>u1D733</key>
		<integer>526</integer>
		<key>u1D734</key>
		<integer>470</integer>
		<key>u1D736</key>
		<integer>389</integer>
		<key>u1D737</key>
		<integer>449</integer>
		<key>u1D738</key>
		<integer>380</integer>
		<key>u1D739</key>
		<integer>383</integer>
		<key>u1D73A</key>
		<integer>320</integer>
		<key>u1D73B</key>
		<integer>375</integer>
		<key>u1D73C</key>
		<integer>357</integer>
		<key>u1D73D</key>
		<integer>440</integer>
		<key>u1D73E</key>
		<integer>173</integer>
		<key>u1D73F</key>
		<integer>304</integer>
		<key>u1D740</key>
		<integer>330</integer>
		<key>u1D741</key>
		<integer>348</integer>
		<key>u1D742</key>
		<integer>333</integer>
		<key>u1D743</key>
		<integer>405</integer>
		<key>u1D744</key>
		<integer>330</integer>
		<key>u1D745</key>
		<integer>375</integer>
		<key>u1D746</key>
		<integer>339</integer>
		<key>u1D747</key>
		<integer>374</integer>
		<key>u1D748</key>
		<integer>397</integer>
		<key>u1D749</key>
		<integer>310</integer>
		<key>u1D74A</key>
		<integer>307</integer>
		<key>u1D74B</key>
		<integer>470</integer>
		<key>u1D74C</key>
		<integer>330</integer>
		<key>u1D74D</key>
		<integer>490</integer>
		<key>u1D74E</key>
		<integer>479</integer>
		<key>u1D750</key>
		<integer>329</integer>
		<key>u1D751</key>
		<integer>384</integer>
		<key>u1D752</key>
		<integer>490</integer>
		<key>u1D753</key>
		<integer>450</integer>
		<key>u1D754</key>
		<integer>423</integer>
		<key>u1D755</key>
		<integer>472</integer>
		<key>u1D790</key>
		<integer>427</integer>
		<key>u1D791</key>
		<integer>395</integer>
		<key>u1D792</key>
		<integer>402</integer>
		<key>u1D793</key>
		<integer>512</integer>
		<key>u1D794</key>
		<integer>402</integer>
		<key>u1D795</key>
		<integer>395</integer>
		<key>u1D796</key>
		<integer>470</integer>
		<key>u1D797</key>
		<integer>491</integer>
		<key>u1D798</key>
		<integer>225</integer>
		<key>u1D799</key>
		<integer>395</integer>
		<key>u1D79A</key>
		<integer>396</integer>
		<key>u1D79B</key>
		<integer>553</integer>
		<key>u1D79C</key>
		<integer>490</integer>
		<key>u1D79D</key>
		<integer>400</integer>
		<key>u1D79E</key>
		<integer>447</integer>
		<key>u1D79F</key>
		<integer>462</integer>
		<key>u1D7A0</key>
		<integer>400</integer>
		<key>u1D7A1</key>
		<integer>477</integer>
		<key>u1D7A2</key>
		<integer>471</integer>
		<key>u1D7A3</key>
		<integer>418</integer>
		<key>u1D7A4</key>
		<integer>463</integer>
		<key>u1D7A5</key>
		<integer>452</integer>
		<key>u1D7A6</key>
		<integer>383</integer>
		<key>u1D7A7</key>
		<integer>485</integer>
		<key>u1D7A8</key>
		<integer>447</integer>
		<key>u1D7AA</key>
		<integer>292</integer>
		<key>u1D7AB</key>
		<integer>324</integer>
		<key>u1D7AC</key>
		<integer>377</integer>
		<key>u1D7AD</key>
		<integer>278</integer>
		<key>u1D7AE</key>
		<integer>268</integer>
		<key>u1D7AF</key>
		<integer>325</integer>
		<key>u1D7B0</key>
		<integer>324</integer>
		<key>u1D7B1</key>
		<integer>340</integer>
		<key>u1D7B4</key>
		<integer>236</integer>
		<key>u1D7B7</key>
		<integer>340</integer>
		<key>u1D7B8</key>
		<integer>322</integer>
		<key>u1D7B9</key>
		<integer>358</integer>
		<key>u1D7BA</key>
		<integer>316</integer>
		<key>u1D7BB</key>
		<integer>296</integer>
		<key>u1D7BC</key>
		<integer>311</integer>
		<key>u1D7BF</key>
		<integer>390</integer>
		<key>u1D7C0</key>
		<integer>334</integer>
		<key>u1D7C1</key>
		<integer>400</integer>
		<key>u1D7C2</key>
		<integer>385</integer>
		<key>u1D7C4</key>
		<integer>357</integer>
		<key>u1D7C5</key>
		<integer>333</integer>
		<key>uni0302</key>
		<integer>-156</integer>
		<key>uni0304</key>
		<integer>-166</integer>
		<key>uni0305</key>
		<integer>-216</integer>
		<key>uni0306</key>
		<integer>-166</integer>
		<key>uni0307</key>
		<integer>-125</integer>
		<key>uni0308</key>
		<integer>-168</integer>
		<key>uni030A</key>
		<integer>-135</integer>
		<key>uni030C</key>
		<integer>-166</integer>
		<key>uni0332</key>
		<integer>-216</integer>
		<key>uni0333</key>
		<integer>-216</integer>
		<key>uni033F</key>
		<integer>-216</integer>
		<key>uni20D0</key>
		<integer>279</integer>
		<key>uni20D1</key>
		<integer>279</integer>
		<key>uni20D4</key>
		<integer>392</integer>
		<key>uni20D5</key>
		<integer>289</integer>
		<key>uni20D6</key>
		<integer>279</integer>
		<key>uni20D7</key>
		<integer>279</integer>
		<key>uni2102</key>
		<integer>346</integer>
		<key>uni210A</key>
		<integer>564</integer>
		<key>uni210B</key>
		<integer>742</integer>
		<key>uni210B.salt</key>
		<integer>512</integer>
		<key>uni210D</key>
		<integer>474</integer>
		<key>uni210E</key>
		<integer>240</integer>
		<key>uni2110</key>
		<integer>876</integer>
		<key>uni2110.salt</key>
		<integer>342</integer>
		<key>uni2112</key>
		<integer>868</integer>
		<key>uni2112.salt</key>
		<integer>394</integer>
		<key>uni2115</key>
		<integer>476</integer>
		<key>uni2119</key>
		<integer>360</integer>
		<key>uni211A</key>
		<integer>393</integer>
		<key>uni211B</key>
		<integer>762</integer>
		<key>uni211B.salt</key>
		<integer>364</integer>
		<key>uni211D</key>
		<integer>404</integer>
		<key>uni2124</key>
		<integer>402</integer>
		<key>uni2126</key>
		<integer>412</integer>
		<key>uni212C</key>
		<integer>815</integer>
		<key>uni2130</key>
		<integer>618</integer>
		<key>uni2130.salt</key>
		<integer>327</integer>
		<key>uni2131</key>
		<integer>767</integer>
		<key>uni2131.salt</key>
		<integer>452</integer>
		<key>uni2133</key>
		<integer>953</integer>
		<key>uni2133.salt</key>
		<integer>568</integer>
		<key>uni213D</key>
		<integer>274</integer>
		<key>uni213E</key>
		<integer>362</integer>
		<key>uni213F</key>
		<integer>450</integer>
		<key>uni2140</key>
		<integer>344</integer>
		<key>uni2145</key>
		<integer>425</integer>
		<key>uni2146</key>
		<integer>350</integer>
		<key>uni2147</key>
		<integer>274</integer>
		<key>uni2148</key>
		<integer>140</integer>
		<key>uni2149</key>
		<integer>127</integer>
		<key>uni2206</key>
		<integer>344</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>469</integer>
		<key>AxisHeight</key>
		<integer>271</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1500</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1330</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>692</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>120</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>710</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>116</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>500</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>130</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>725</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>116</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>600</integer>
		<key>FractionRuleThickness</key>
		<integer>59</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>600</integer>
		<key>LowerLimitGapMin</key>
		<integer>167</integer>
		<key>MathLeading</key>
		<integer>150</integer>
		<key>MinConnectorOverlap</key>
		<integer>100</integer>
		<key>OverbarExtraAscender</key>
		<integer>75</integer>
		<key>OverbarRuleThickness</key>
		<integer>75</integer>
		<key>OverbarVerticalGap</key>
		<integer>175</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>65</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>174</integer>
		<key>RadicalExtraAscender</key>
		<integer>59</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-556</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>278</integer>
		<key>RadicalRuleThickness</key>
		<integer>59</integer>
		<key>RadicalVerticalGap</key>
		<integer>74</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>73</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>60</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>400</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>60</integer>
		<key>SpaceAfterScript</key>
		<integer>41</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>50</integer>
		<key>StackBottomShiftDown</key>
		<integer>50</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>812</integer>
		<key>StackGapMin</key>
		<integer>348</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>50</integer>
		<key>StackTopShiftUp</key>
		<integer>50</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>80</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>80</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>40</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>50</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>145</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>222</integer>
		<key>SubscriptShiftDown</key>
		<integer>210</integer>
		<key>SubscriptTopMax</key>
		<integer>366</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>230</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>379</integer>
		<key>SuperscriptBottomMin</key>
		<integer>154</integer>
		<key>SuperscriptShiftUp</key>
		<integer>361</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>296</integer>
		<key>UnderbarExtraDescender</key>
		<integer>75</integer>
		<key>UnderbarRuleThickness</key>
		<integer>75</integer>
		<key>UnderbarVerticalGap</key>
		<integer>175</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>200</integer>
		<key>UpperLimitGapMin</key>
		<integer>111</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>arrowboth</key>
		<array>
			<string>arrowboth</string>
		</array>
		<key>arrowdblboth</key>
		<array>
			<string>arrowdblboth</string>
		</array>
		<key>arrowdblleft</key>
		<array>
			<string>arrowdblleft</string>
		</array>
		<key>arrowdblright</key>
		<array>
			<string>arrowdblright</string>
		</array>
		<key>arrowleft</key>
		<array>
			<string>arrowleft</string>
		</array>
		<key>arrowright</key>
		<array>
			<string>arrowright</string>
		</array>
		<key>tildecomb</key>
		<array>
			<string>tildecomb</string>
			<string>tildewide1</string>
			<string>tildewide2</string>
			<string>tildewide3</string>
			<string>tildewide4</string>
			<string>glyph3598</string>
		</array>
		<key>uni0302</key>
		<array>
			<string>uni0302</string>
			<string>hatwide1</string>
			<string>hatwide2</string>
			<string>hatwide3</string>
			<string>hatwide4</string>
			<string>glyph3599</string>
		</array>
		<key>uni0305</key>
		<array>
			<string>uni0305</string>
			<string>glyph3886</string>
			<string>glyph3881</string>
			<string>glyph3877</string>
		</array>
		<key>uni0306</key>
		<array>
			<string>uni0306</string>
			<string>widebreve1</string>
			<string>widebreve2</string>
			<string>widebreve3</string>
			<string>widebreve4</string>
		</array>
		<key>uni030C</key>
		<array>
			<string>uni030C</string>
			<string>caronwide1</string>
			<string>caronwide2</string>
			<string>caronwide3</string>
			<string>caronwide4</string>
			<string>glyph3597</string>
		</array>
		<key>uni0332</key>
		<array>
			<string>uni0332</string>
			<string>glyph3885</string>
			<string>glyph3880</string>
			<string>glyph3876</string>
		</array>
		<key>uni0333</key>
		<array>
			<string>uni0333</string>
			<string>glyph3884</string>
			<string>glyph3879</string>
			<string>glyph3875</string>
		</array>
		<key>uni033F</key>
		<array>
			<string>uni033F</string>
			<string>glyph3883</string>
			<string>glyph3878</string>
			<string>glyph3874</string>
		</array>
		<key>uni20D0</key>
		<array>
			<string>uni20D0</string>
		</array>
		<key>uni20D1</key>
		<array>
			<string>uni20D1</string>
		</array>
		<key>uni20D6</key>
		<array>
			<string>uni20D6</string>
			<string>glyph3674</string>
			<string>glyph3675</string>
			<string>glyph3676</string>
			<string>glyph3677</string>
		</array>
		<key>uni20D7</key>
		<array>
			<string>uni20D7</string>
			<string>glyph3678</string>
			<string>glyph3679</string>
			<string>glyph3680</string>
			<string>glyph3681</string>
		</array>
		<key>uni20E1</key>
		<array>
			<string>uni20E1</string>
		</array>
		<key>uni20E9</key>
		<array>
			<string>uni20E9</string>
		</array>
		<key>uni20EE</key>
		<array/>
		<key>uni20EF</key>
		<array/>
		<key>uni21A4</key>
		<array>
			<string>uni21A4</string>
		</array>
		<key>uni21A6</key>
		<array>
			<string>uni21A6</string>
		</array>
		<key>uni21A9</key>
		<array>
			<string>uni21A9</string>
		</array>
		<key>uni21AA</key>
		<array>
			<string>uni21AA</string>
		</array>
		<key>uni23B4</key>
		<array>
			<string>uni23B4</string>
			<string>glyph3853</string>
			<string>glyph3854</string>
			<string>glyph3855</string>
		</array>
		<key>uni23B5</key>
		<array>
			<string>uni23B5</string>
			<string>glyph3856</string>
			<string>glyph3857</string>
			<string>glyph3858</string>
		</array>
		<key>uni23DC</key>
		<array>
			<string>uni23DC</string>
			<string>glyph3847</string>
			<string>glyph3848</string>
			<string>glyph3849</string>
		</array>
		<key>uni23DD</key>
		<array>
			<string>uni23DD</string>
			<string>glyph3850</string>
			<string>glyph3851</string>
			<string>glyph3852</string>
		</array>
		<key>uni23DE</key>
		<array>
			<string>uni23DE</string>
			<string>glyph4049</string>
			<string>glyph4050</string>
			<string>glyph4051</string>
		</array>
		<key>uni23DF</key>
		<array>
			<string>uni23DF</string>
			<string>glyph4052</string>
			<string>glyph4053</string>
			<string>glyph4054</string>
		</array>
		<key>uni23E0</key>
		<array>
			<string>uni23E0</string>
			<string>glyph3841</string>
			<string>glyph3842</string>
			<string>glyph3843</string>
		</array>
		<key>uni23E1</key>
		<array>
			<string>uni23E1</string>
			<string>glyph3844</string>
			<string>glyph3845</string>
			<string>glyph3846</string>
		</array>
		<key>uni2906</key>
		<array>
			<string>uni2906</string>
		</array>
		<key>uni2907</key>
		<array>
			<string>uni2907</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>existential</key>
		<integer>86</integer>
		<key>glyph2A0CBig</key>
		<integer>307</integer>
		<key>glyph2A0CBigg</key>
		<integer>307</integer>
		<key>glyph2A0Cbigg</key>
		<integer>307</integer>
		<key>glyph2A0DBig</key>
		<integer>307</integer>
		<key>glyph2A0DBigg</key>
		<integer>307</integer>
		<key>glyph2A0Dbigg</key>
		<integer>307</integer>
		<key>glyph2A0EBig</key>
		<integer>307</integer>
		<key>glyph2A0EBigg</key>
		<integer>307</integer>
		<key>glyph2A0Ebigg</key>
		<integer>307</integer>
		<key>glyph2A0FBig</key>
		<integer>307</integer>
		<key>glyph2A0FBigg</key>
		<integer>307</integer>
		<key>glyph2A0Fbigg</key>
		<integer>307</integer>
		<key>glyph2A10Big</key>
		<integer>307</integer>
		<key>glyph2A10Bigg</key>
		<integer>307</integer>
		<key>glyph2A10bigg</key>
		<integer>307</integer>
		<key>glyph2A11Big</key>
		<integer>307</integer>
		<key>glyph2A11Bigg</key>
		<integer>307</integer>
		<key>glyph2A11bigg</key>
		<integer>307</integer>
		<key>glyph2A12Big</key>
		<integer>307</integer>
		<key>glyph2A12Bigg</key>
		<integer>307</integer>
		<key>glyph2A12bigg</key>
		<integer>307</integer>
		<key>glyph2A13Big</key>
		<integer>307</integer>
		<key>glyph2A13Bigg</key>
		<integer>307</integer>
		<key>glyph2A13bigg</key>
		<integer>307</integer>
		<key>glyph2A14Big</key>
		<integer>307</integer>
		<key>glyph2A14Bigg</key>
		<integer>307</integer>
		<key>glyph2A14bigg</key>
		<integer>307</integer>
		<key>glyph2A15Big</key>
		<integer>307</integer>
		<key>glyph2A15Bigg</key>
		<integer>307</integer>
		<key>glyph2A15bigg</key>
		<integer>307</integer>
		<key>glyph2A16Big</key>
		<integer>307</integer>
		<key>glyph2A16Bigg</key>
		<integer>307</integer>
		<key>glyph2A16bigg</key>
		<integer>307</integer>
		<key>glyph2A17Big</key>
		<integer>257</integer>
		<key>glyph2A17Bigg</key>
		<integer>257</integer>
		<key>glyph2A17bigg</key>
		<integer>257</integer>
		<key>glyph2A18Big</key>
		<integer>307</integer>
		<key>glyph2A18Bigg</key>
		<integer>307</integer>
		<key>glyph2A18bigg</key>
		<integer>307</integer>
		<key>glyph2A19Big</key>
		<integer>307</integer>
		<key>glyph2A19Bigg</key>
		<integer>307</integer>
		<key>glyph2A19bigg</key>
		<integer>307</integer>
		<key>glyph2A1ABig</key>
		<integer>307</integer>
		<key>glyph2A1ABigg</key>
		<integer>307</integer>
		<key>glyph2A1Abigg</key>
		<integer>307</integer>
		<key>glyph2A1BBig</key>
		<integer>307</integer>
		<key>glyph2A1BBigg</key>
		<integer>307</integer>
		<key>glyph2A1Bbigg</key>
		<integer>307</integer>
		<key>glyph2A1CBig</key>
		<integer>307</integer>
		<key>glyph2A1CBigg</key>
		<integer>307</integer>
		<key>glyph2A1Cbigg</key>
		<integer>307</integer>
		<key>gradient</key>
		<integer>120</integer>
		<key>integral</key>
		<integer>407</integer>
		<key>integralbig1</key>
		<integer>313</integer>
		<key>integralbig2</key>
		<integer>307</integer>
		<key>integralbig3</key>
		<integer>307</integer>
		<key>integraldblBig</key>
		<integer>307</integer>
		<key>integraldblBigg</key>
		<integer>307</integer>
		<key>integraldblbigg</key>
		<integer>307</integer>
		<key>integraltripleBig</key>
		<integer>307</integer>
		<key>integraltripleBigg</key>
		<integer>307</integer>
		<key>integraltriplebigg</key>
		<integer>307</integer>
		<key>partialdiff</key>
		<integer>120</integer>
		<key>u1D439</key>
		<integer>10</integer>
		<key>u1D43B</key>
		<integer>46</integer>
		<key>u1D43C</key>
		<integer>44</integer>
		<key>u1D43D</key>
		<integer>48</integer>
		<key>u1D43E</key>
		<integer>36</integer>
		<key>u1D441</key>
		<integer>49</integer>
		<key>u1D447</key>
		<integer>50</integer>
		<key>u1D448</key>
		<integer>51</integer>
		<key>u1D449</key>
		<integer>60</integer>
		<key>u1D44A</key>
		<integer>64</integer>
		<key>u1D44B</key>
		<integer>36</integer>
		<key>u1D44C</key>
		<integer>28</integer>
		<key>u1D453</key>
		<integer>23</integer>
		<key>u1D46A</key>
		<integer>72</integer>
		<key>u1D46D</key>
		<integer>100</integer>
		<key>u1D46F</key>
		<integer>110</integer>
		<key>u1D470</key>
		<integer>86</integer>
		<key>u1D471</key>
		<integer>90</integer>
		<key>u1D472</key>
		<integer>86</integer>
		<key>u1D474</key>
		<integer>104</integer>
		<key>u1D475</key>
		<integer>114</integer>
		<key>u1D47B</key>
		<integer>126</integer>
		<key>u1D47C</key>
		<integer>110</integer>
		<key>u1D47D</key>
		<integer>140</integer>
		<key>u1D47E</key>
		<integer>136</integer>
		<key>u1D47F</key>
		<integer>112</integer>
		<key>u1D480</key>
		<integer>126</integer>
		<key>u1D481</key>
		<integer>72</integer>
		<key>u1D487</key>
		<integer>178</integer>
		<key>u1D488</key>
		<integer>92</integer>
		<key>u1D49C</key>
		<integer>216</integer>
		<key>u1D49E</key>
		<integer>183</integer>
		<key>u1D49F</key>
		<integer>158</integer>
		<key>u1D4A2</key>
		<integer>194</integer>
		<key>u1D4A5</key>
		<integer>187</integer>
		<key>u1D4A6</key>
		<integer>200</integer>
		<key>u1D4A9</key>
		<integer>219</integer>
		<key>u1D4AA</key>
		<integer>188</integer>
		<key>u1D4AB</key>
		<integer>141</integer>
		<key>u1D4AC</key>
		<integer>169</integer>
		<key>u1D4AE</key>
		<integer>268</integer>
		<key>u1D4AF</key>
		<integer>368</integer>
		<key>u1D4B0</key>
		<integer>112</integer>
		<key>u1D4B1</key>
		<integer>333</integer>
		<key>u1D4B2</key>
		<integer>374</integer>
		<key>u1D4B3</key>
		<integer>259</integer>
		<key>u1D4B4</key>
		<integer>228</integer>
		<key>u1D4B5</key>
		<integer>153</integer>
		<key>u1D4B7</key>
		<integer>143</integer>
		<key>u1D4B9</key>
		<integer>230</integer>
		<key>u1D4BB</key>
		<integer>217</integer>
		<key>u1D4BD</key>
		<integer>227</integer>
		<key>u1D4BE</key>
		<integer>180</integer>
		<key>u1D4BF</key>
		<integer>176</integer>
		<key>u1D4C1</key>
		<integer>108</integer>
		<key>u1D4D0</key>
		<integer>217</integer>
		<key>u1D4D1</key>
		<integer>169</integer>
		<key>u1D4D2</key>
		<integer>166</integer>
		<key>u1D4D3</key>
		<integer>178</integer>
		<key>u1D4D4</key>
		<integer>172</integer>
		<key>u1D4D5</key>
		<integer>337</integer>
		<key>u1D4D6</key>
		<integer>208</integer>
		<key>u1D4D7</key>
		<integer>260</integer>
		<key>u1D4D8</key>
		<integer>233</integer>
		<key>u1D4D9</key>
		<integer>233</integer>
		<key>u1D4DA</key>
		<integer>233</integer>
		<key>u1D4DB</key>
		<integer>179</integer>
		<key>u1D4DC</key>
		<integer>168</integer>
		<key>u1D4DD</key>
		<integer>233</integer>
		<key>u1D4DE</key>
		<integer>202</integer>
		<key>u1D4DF</key>
		<integer>168</integer>
		<key>u1D4E0</key>
		<integer>176</integer>
		<key>u1D4E1</key>
		<integer>181</integer>
		<key>u1D4E2</key>
		<integer>293</integer>
		<key>u1D4E3</key>
		<integer>367</integer>
		<key>u1D4E4</key>
		<integer>136</integer>
		<key>u1D4E5</key>
		<integer>344</integer>
		<key>u1D4E6</key>
		<integer>329</integer>
		<key>u1D4E7</key>
		<integer>244</integer>
		<key>u1D4E8</key>
		<integer>238</integer>
		<key>u1D4E9</key>
		<integer>148</integer>
		<key>u1D4EB</key>
		<integer>88</integer>
		<key>u1D4EF</key>
		<integer>268</integer>
		<key>u1D4F2</key>
		<integer>230</integer>
		<key>u1D4F3</key>
		<integer>228</integer>
		<key>u1D4F4</key>
		<integer>122</integer>
		<key>u1D4F5</key>
		<integer>126</integer>
		<key>u1D5BF</key>
		<integer>104</integer>
		<key>u1D5F3</key>
		<integer>108</integer>
		<key>u1D609</key>
		<integer>92</integer>
		<key>u1D60A</key>
		<integer>146</integer>
		<key>u1D60B</key>
		<integer>88</integer>
		<key>u1D60C</key>
		<integer>152</integer>
		<key>u1D60D</key>
		<integer>166</integer>
		<key>u1D60E</key>
		<integer>130</integer>
		<key>u1D60F</key>
		<integer>116</integer>
		<key>u1D610</key>
		<integer>116</integer>
		<key>u1D611</key>
		<integer>126</integer>
		<key>u1D612</key>
		<integer>154</integer>
		<key>u1D614</key>
		<integer>110</integer>
		<key>u1D615</key>
		<integer>114</integer>
		<key>u1D616</key>
		<integer>88</integer>
		<key>u1D617</key>
		<integer>114</integer>
		<key>u1D618</key>
		<integer>88</integer>
		<key>u1D619</key>
		<integer>118</integer>
		<key>u1D61A</key>
		<integer>114</integer>
		<key>u1D61B</key>
		<integer>172</integer>
		<key>u1D61C</key>
		<integer>116</integer>
		<key>u1D61D</key>
		<integer>196</integer>
		<key>u1D61E</key>
		<integer>196</integer>
		<key>u1D61F</key>
		<integer>154</integer>
		<key>u1D620</key>
		<integer>208</integer>
		<key>u1D621</key>
		<integer>152</integer>
		<key>u1D623</key>
		<integer>82</integer>
		<key>u1D624</key>
		<integer>118</integer>
		<key>u1D625</key>
		<integer>128</integer>
		<key>u1D626</key>
		<integer>90</integer>
		<key>u1D627</key>
		<integer>252</integer>
		<key>u1D628</key>
		<integer>134</integer>
		<key>u1D62A</key>
		<integer>132</integer>
		<key>u1D62B</key>
		<integer>126</integer>
		<key>u1D62C</key>
		<integer>118</integer>
		<key>u1D62D</key>
		<integer>128</integer>
		<key>u1D630</key>
		<integer>84</integer>
		<key>u1D631</key>
		<integer>82</integer>
		<key>u1D632</key>
		<integer>78</integer>
		<key>u1D633</key>
		<integer>146</integer>
		<key>u1D634</key>
		<integer>114</integer>
		<key>u1D635</key>
		<integer>112</integer>
		<key>u1D636</key>
		<integer>76</integer>
		<key>u1D637</key>
		<integer>142</integer>
		<key>u1D638</key>
		<integer>142</integer>
		<key>u1D639</key>
		<integer>140</integer>
		<key>u1D63A</key>
		<integer>142</integer>
		<key>u1D63B</key>
		<integer>122</integer>
		<key>u1D63D</key>
		<integer>94</integer>
		<key>u1D63E</key>
		<integer>146</integer>
		<key>u1D63F</key>
		<integer>88</integer>
		<key>u1D640</key>
		<integer>152</integer>
		<key>u1D641</key>
		<integer>166</integer>
		<key>u1D642</key>
		<integer>130</integer>
		<key>u1D643</key>
		<integer>116</integer>
		<key>u1D644</key>
		<integer>116</integer>
		<key>u1D645</key>
		<integer>126</integer>
		<key>u1D646</key>
		<integer>154</integer>
		<key>u1D648</key>
		<integer>110</integer>
		<key>u1D649</key>
		<integer>114</integer>
		<key>u1D64A</key>
		<integer>88</integer>
		<key>u1D64B</key>
		<integer>114</integer>
		<key>u1D64C</key>
		<integer>88</integer>
		<key>u1D64D</key>
		<integer>118</integer>
		<key>u1D64E</key>
		<integer>114</integer>
		<key>u1D64F</key>
		<integer>172</integer>
		<key>u1D650</key>
		<integer>116</integer>
		<key>u1D651</key>
		<integer>196</integer>
		<key>u1D652</key>
		<integer>196</integer>
		<key>u1D653</key>
		<integer>154</integer>
		<key>u1D654</key>
		<integer>208</integer>
		<key>u1D655</key>
		<integer>152</integer>
		<key>u1D656</key>
		<integer>66</integer>
		<key>u1D657</key>
		<integer>100</integer>
		<key>u1D658</key>
		<integer>140</integer>
		<key>u1D659</key>
		<integer>151</integer>
		<key>u1D65A</key>
		<integer>108</integer>
		<key>u1D65B</key>
		<integer>273</integer>
		<key>u1D65C</key>
		<integer>152</integer>
		<key>u1D65D</key>
		<integer>70</integer>
		<key>u1D65E</key>
		<integer>154</integer>
		<key>u1D65F</key>
		<integer>148</integer>
		<key>u1D660</key>
		<integer>168</integer>
		<key>u1D661</key>
		<integer>151</integer>
		<key>u1D662</key>
		<integer>70</integer>
		<key>u1D663</key>
		<integer>70</integer>
		<key>u1D664</key>
		<integer>103</integer>
		<key>u1D665</key>
		<integer>100</integer>
		<key>u1D666</key>
		<integer>100</integer>
		<key>u1D667</key>
		<integer>169</integer>
		<key>u1D668</key>
		<integer>137</integer>
		<key>u1D669</key>
		<integer>134</integer>
		<key>u1D66A</key>
		<integer>98</integer>
		<key>u1D66B</key>
		<integer>174</integer>
		<key>u1D66C</key>
		<integer>172</integer>
		<key>u1D66D</key>
		<integer>186</integer>
		<key>u1D66E</key>
		<integer>175</integer>
		<key>u1D66F</key>
		<integer>145</integer>
		<key>u1D6E4</key>
		<integer>104</integer>
		<key>u1D6E8</key>
		<integer>86</integer>
		<key>u1D6EA</key>
		<integer>84</integer>
		<key>u1D6EB</key>
		<integer>78</integer>
		<key>u1D6EE</key>
		<integer>88</integer>
		<key>u1D6F1</key>
		<integer>98</integer>
		<key>u1D6F5</key>
		<integer>86</integer>
		<key>u1D6F6</key>
		<integer>107</integer>
		<key>u1D6F8</key>
		<integer>76</integer>
		<key>u1D6F9</key>
		<integer>102</integer>
		<key>u1D714</key>
		<integer>55</integer>
		<key>u1D715</key>
		<integer>52</integer>
		<key>u1D718</key>
		<integer>143</integer>
		<key>u1D71A</key>
		<integer>77</integer>
		<key>u1D71E</key>
		<integer>76</integer>
		<key>u1D720</key>
		<integer>76</integer>
		<key>u1D721</key>
		<integer>78</integer>
		<key>u1D722</key>
		<integer>86</integer>
		<key>u1D724</key>
		<integer>84</integer>
		<key>u1D725</key>
		<integer>82</integer>
		<key>u1D727</key>
		<integer>92</integer>
		<key>u1D728</key>
		<integer>88</integer>
		<key>u1D72B</key>
		<integer>92</integer>
		<key>u1D72C</key>
		<integer>83</integer>
		<key>u1D72F</key>
		<integer>104</integer>
		<key>u1D730</key>
		<integer>106</integer>
		<key>u1D732</key>
		<integer>104</integer>
		<key>u1D733</key>
		<integer>118</integer>
		<key>u1D735</key>
		<integer>96</integer>
		<key>u1D73F</key>
		<integer>70</integer>
		<key>u1D743</key>
		<integer>88</integer>
		<key>u1D748</key>
		<integer>92</integer>
		<key>u1D749</key>
		<integer>74</integer>
		<key>u1D74C</key>
		<integer>78</integer>
		<key>u1D752</key>
		<integer>183</integer>
		<key>u1D754</key>
		<integer>68</integer>
		<key>u1D792</key>
		<integer>72</integer>
		<key>u1D7A3</key>
		<integer>76</integer>
		<key>u1D7AA</key>
		<integer>74</integer>
		<key>u1D7AC</key>
		<integer>86</integer>
		<key>u1D7AF</key>
		<integer>42</integer>
		<key>u1D7B5</key>
		<integer>42</integer>
		<key>u1D7B6</key>
		<integer>37</integer>
		<key>u1D7BB</key>
		<integer>46</integer>
		<key>u1D7BC</key>
		<integer>40</integer>
		<key>uni210B</key>
		<integer>259</integer>
		<key>uni2110</key>
		<integer>235</integer>
		<key>uni2112</key>
		<integer>185</integer>
		<key>uni211B</key>
		<integer>191</integer>
		<key>uni212C</key>
		<integer>191</integer>
		<key>uni2130</key>
		<integer>191</integer>
		<key>uni2131</key>
		<integer>311</integer>
		<key>uni2133</key>
		<integer>188</integer>
		<key>uni2206</key>
		<integer>120</integer>
		<key>uni222C</key>
		<integer>407</integer>
		<key>uni222D</key>
		<integer>307</integer>
		<key>uni222E</key>
		<integer>307</integer>
		<key>uni222EBig</key>
		<integer>307</integer>
		<key>uni222EBigg</key>
		<integer>307</integer>
		<key>uni222Ebigg</key>
		<integer>307</integer>
		<key>uni222F</key>
		<integer>307</integer>
		<key>uni222FBig</key>
		<integer>307</integer>
		<key>uni222FBigg</key>
		<integer>207</integer>
		<key>uni222Fbigg</key>
		<integer>307</integer>
		<key>uni2230</key>
		<integer>307</integer>
		<key>uni2230Big</key>
		<integer>307</integer>
		<key>uni2230Bigg</key>
		<integer>307</integer>
		<key>uni2230bigg</key>
		<integer>307</integer>
		<key>uni2231</key>
		<integer>307</integer>
		<key>uni2231Big</key>
		<integer>307</integer>
		<key>uni2231Bigg</key>
		<integer>307</integer>
		<key>uni2231bigg</key>
		<integer>307</integer>
		<key>uni2232</key>
		<integer>307</integer>
		<key>uni2232Big</key>
		<integer>307</integer>
		<key>uni2232Bigg</key>
		<integer>307</integer>
		<key>uni2232bigg</key>
		<integer>307</integer>
		<key>uni2233</key>
		<integer>307</integer>
		<key>uni2233Big</key>
		<integer>307</integer>
		<key>uni2233Bigg</key>
		<integer>307</integer>
		<key>uni2233bigg</key>
		<integer>307</integer>
		<key>uni2A0C</key>
		<integer>307</integer>
		<key>uni2A0D</key>
		<integer>307</integer>
		<key>uni2A0E</key>
		<integer>307</integer>
		<key>uni2A0F</key>
		<integer>307</integer>
		<key>uni2A10</key>
		<integer>307</integer>
		<key>uni2A11</key>
		<integer>307</integer>
		<key>uni2A12</key>
		<integer>207</integer>
		<key>uni2A13</key>
		<integer>307</integer>
		<key>uni2A14</key>
		<integer>307</integer>
		<key>uni2A15</key>
		<integer>307</integer>
		<key>uni2A16</key>
		<integer>307</integer>
		<key>uni2A17</key>
		<integer>257</integer>
		<key>uni2A18</key>
		<integer>307</integer>
		<key>uni2A19</key>
		<integer>307</integer>
		<key>uni2A1A</key>
		<integer>307</integer>
		<key>uni2A1B</key>
		<integer>307</integer>
		<key>uni2A1C</key>
		<integer>307</integer>
		<key>universal</key>
		<integer>120</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>arrowdbldown</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>886</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>601</integer>
					<key>endConnector</key>
					<integer>227</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph3799</string>
					<key>startConnector</key>
					<integer>227</integer>
				</dict>
			</array>
		</dict>
		<key>arrowdblup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>601</integer>
					<key>endConnector</key>
					<integer>227</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph3799</string>
					<key>startConnector</key>
					<integer>227</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>886</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
			</array>
		</dict>
		<key>arrowdown</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>886</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>601</integer>
					<key>endConnector</key>
					<integer>227</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph3798</string>
					<key>startConnector</key>
					<integer>227</integer>
				</dict>
			</array>
		</dict>
		<key>arrowup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>601</integer>
					<key>endConnector</key>
					<integer>227</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph3798</string>
					<key>startConnector</key>
					<integer>227</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>886</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
			</array>
		</dict>
		<key>bar</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2315</integer>
					<key>endConnector</key>
					<integer>772</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>barBigg</string>
					<key>startConnector</key>
					<integer>772</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2315</integer>
					<key>endConnector</key>
					<integer>772</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>barBigg</string>
					<key>startConnector</key>
					<integer>772</integer>
				</dict>
			</array>
		</dict>
		<key>braceleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>715</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>326</integer>
					<key>endConnector</key>
					<integer>10</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>25</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>757</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A8</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>326</integer>
					<key>endConnector</key>
					<integer>25</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>10</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>721</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
			</array>
		</dict>
		<key>braceright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>715</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>326</integer>
					<key>endConnector</key>
					<integer>10</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>25</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>757</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AC</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>326</integer>
					<key>endConnector</key>
					<integer>25</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>10</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>721</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
			</array>
		</dict>
		<key>bracketleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>324</integer>
					<key>endConnector</key>
					<integer>15</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
			</array>
		</dict>
		<key>bracketright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>324</integer>
					<key>endConnector</key>
					<integer>15</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
			</array>
		</dict>
		<key>integral</key>
		<dict>
			<key>italic</key>
			<integer>407</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1413</integer>
					<key>endConnector</key>
					<integer>10</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>integralbt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1126</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AE</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1413</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>integraltp</string>
					<key>startConnector</key>
					<integer>10</integer>
				</dict>
			</array>
		</dict>
		<key>parenleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>884</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>326</integer>
					<key>endConnector</key>
					<integer>15</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>884</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
			</array>
		</dict>
		<key>parenright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>884</integer>
					<key>endConnector</key>
					<integer>35</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>326</integer>
					<key>endConnector</key>
					<integer>15</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>884</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>35</integer>
				</dict>
			</array>
		</dict>
		<key>radical</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1388</integer>
					<key>endConnector</key>
					<integer>1265</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23B7</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1016</integer>
					<key>endConnector</key>
					<integer>965</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni20D3</string>
					<key>startConnector</key>
					<integer>965</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>494</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>glyph1113975</string>
					<key>startConnector</key>
					<integer>165</integer>
				</dict>
			</array>
		</dict>
		<key>uni2016</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2315</integer>
					<key>endConnector</key>
					<integer>772</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>midBigg</string>
					<key>startConnector</key>
					<integer>772</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2315</integer>
					<key>endConnector</key>
					<integer>772</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>midBigg</string>
					<key>startConnector</key>
					<integer>772</integer>
				</dict>
			</array>
		</dict>
		<key>uni2045</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>284</integer>
					<key>endConnector</key>
					<integer>30</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>glyph4039</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>10</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph4042</string>
					<key>startConnector</key>
					<integer>25</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>423</integer>
					<key>endConnector</key>
					<integer>30</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>glyph3939</string>
					<key>startConnector</key>
					<integer>30</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>25</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph4042</string>
					<key>startConnector</key>
					<integer>10</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>284</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>glyph4038</string>
					<key>startConnector</key>
					<integer>30</integer>
				</dict>
			</array>
		</dict>
		<key>uni2046</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>284</integer>
					<key>endConnector</key>
					<integer>30</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>glyph4041</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>10</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph3938</string>
					<key>startConnector</key>
					<integer>25</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>423</integer>
					<key>endConnector</key>
					<integer>30</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>glyph3940</string>
					<key>startConnector</key>
					<integer>30</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>25</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>glyph3938</string>
					<key>startConnector</key>
					<integer>10</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>284</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>glyph4040</string>
					<key>startConnector</key>
					<integer>30</integer>
				</dict>
			</array>
		</dict>
		<key>uni2223</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>885</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2223</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>885</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2223</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
			</array>
		</dict>
		<key>uni2225</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>885</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2225</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>885</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2225</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
			</array>
		</dict>
		<key>uni2308</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1132</integer>
					<key>endConnector</key>
					<integer>377</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>377</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>296</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>296</integer>
				</dict>
			</array>
		</dict>
		<key>uni2309</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1132</integer>
					<key>endConnector</key>
					<integer>377</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>377</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>296</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>296</integer>
				</dict>
			</array>
		</dict>
		<key>uni230A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>296</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>296</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1132</integer>
					<key>endConnector</key>
					<integer>377</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>377</integer>
				</dict>
			</array>
		</dict>
		<key>uni230B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>888</integer>
					<key>endConnector</key>
					<integer>296</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>296</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1132</integer>
					<key>endConnector</key>
					<integer>377</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>377</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>arrowdbldown</key>
		<array>
			<string>arrowdbldown</string>
		</array>
		<key>arrowdblup</key>
		<array>
			<string>arrowdblup</string>
		</array>
		<key>arrowdown</key>
		<array>
			<string>arrowdown</string>
		</array>
		<key>arrowup</key>
		<array>
			<string>arrowup</string>
		</array>
		<key>bar</key>
		<array>
			<string>bar</string>
			<string>barbig</string>
			<string>barBig</string>
			<string>barbigg</string>
			<string>barBigg</string>
			<string>barbiggg</string>
			<string>barBiggg</string>
		</array>
		<key>braceleft</key>
		<array>
			<string>braceleft</string>
			<string>braceBigl</string>
			<string>bracebiggl</string>
			<string>braceBiggl</string>
		</array>
		<key>braceright</key>
		<array>
			<string>braceright</string>
			<string>braceBigr</string>
			<string>bracebiggr</string>
			<string>braceBiggr</string>
		</array>
		<key>bracketleft</key>
		<array>
			<string>bracketleft</string>
			<string>bracketBigl</string>
			<string>bracketbiggl</string>
			<string>bracketBiggl</string>
		</array>
		<key>bracketright</key>
		<array>
			<string>bracketright</string>
			<string>bracketBigr</string>
			<string>bracketbiggr</string>
			<string>bracketBiggr</string>
		</array>
		<key>fraction</key>
		<array>
			<string>fraction</string>
			<string>fractionbig</string>
			<string>fractionBig</string>
			<string>fractionbigg</string>
			<string>fractionBigg</string>
		</array>
		<key>integral</key>
		<array>
			<string>integral</string>
			<string>integralbig1</string>
			<string>integralbig2</string>
			<string>integralbig3</string>
		</array>
		<key>intersection</key>
		<array>
			<string>intersection</string>
			<string>intersectionbig1</string>
			<string>intersectionbig1h</string>
			<string>intersectionbig2</string>
		</array>
		<key>parenleft</key>
		<array>
			<string>parenleft</string>
			<string>parenBigl</string>
			<string>parenbiggl</string>
			<string>parenBiggl</string>
		</array>
		<key>parenright</key>
		<array>
			<string>parenright</string>
			<string>parenBigr</string>
			<string>parenbiggr</string>
			<string>parenBiggr</string>
		</array>
		<key>product</key>
		<array>
			<string>product</string>
			<string>productbig1</string>
			<string>productbig2</string>
			<string>productbig3</string>
		</array>
		<key>radical</key>
		<array>
			<string>radical</string>
			<string>radicalbig</string>
			<string>radicalBig</string>
			<string>radicalbigg</string>
			<string>radicalBigg</string>
		</array>
		<key>slash</key>
		<array>
			<string>slash</string>
			<string>fractionbig</string>
			<string>fractionBig</string>
			<string>fractionbigg</string>
			<string>fractionBigg</string>
		</array>
		<key>summation</key>
		<array>
			<string>summation</string>
			<string>sumbig1</string>
			<string>sumbig2</string>
			<string>sumbig3</string>
		</array>
		<key>uni2016</key>
		<array>
			<string>uni2016</string>
			<string>midbig</string>
			<string>midBig</string>
			<string>midbigg</string>
			<string>midBigg</string>
		</array>
		<key>uni2045</key>
		<array>
			<string>uni2045</string>
			<string>glyph4043</string>
			<string>glyph4044</string>
			<string>glyph4045</string>
		</array>
		<key>uni2046</key>
		<array>
			<string>uni2046</string>
			<string>glyph4046</string>
			<string>glyph4047</string>
			<string>glyph4048</string>
		</array>
		<key>uni2210</key>
		<array>
			<string>uni2210</string>
			<string>coproductbig1</string>
			<string>coproductbig2</string>
			<string>coproductbig3</string>
		</array>
		<key>uni2223</key>
		<array>
			<string>uni2223</string>
		</array>
		<key>uni2225</key>
		<array>
			<string>uni2225</string>
		</array>
		<key>uni222C</key>
		<array>
			<string>uni222C</string>
			<string>integraldblBig</string>
			<string>integraldblbigg</string>
			<string>integraldblBigg</string>
		</array>
		<key>uni222D</key>
		<array>
			<string>uni222D</string>
			<string>integraltripleBig</string>
			<string>integraltriplebigg</string>
			<string>integraltripleBigg</string>
		</array>
		<key>uni222E</key>
		<array>
			<string>uni222E</string>
			<string>uni222EBig</string>
			<string>uni222Ebigg</string>
			<string>uni222EBigg</string>
		</array>
		<key>uni222F</key>
		<array>
			<string>uni222F</string>
			<string>uni222FBig</string>
			<string>uni222Fbigg</string>
			<string>uni222FBigg</string>
		</array>
		<key>uni2230</key>
		<array>
			<string>uni2230</string>
			<string>uni2230Big</string>
			<string>uni2230bigg</string>
			<string>uni2230Bigg</string>
		</array>
		<key>uni2231</key>
		<array>
			<string>uni2231</string>
			<string>uni2231Big</string>
			<string>uni2231bigg</string>
			<string>uni2231Bigg</string>
		</array>
		<key>uni2232</key>
		<array>
			<string>uni2232</string>
			<string>uni2232Big</string>
			<string>uni2232bigg</string>
			<string>uni2232Bigg</string>
		</array>
		<key>uni2233</key>
		<array>
			<string>uni2233</string>
			<string>uni2233Big</string>
			<string>uni2233bigg</string>
			<string>uni2233Bigg</string>
		</array>
		<key>uni22C0</key>
		<array>
			<string>uni22C0</string>
			<string>bigand1</string>
			<string>bigand2</string>
		</array>
		<key>uni22C1</key>
		<array>
			<string>uni22C1</string>
			<string>bigor1</string>
			<string>bigor2</string>
		</array>
		<key>uni22C2</key>
		<array>
			<string>uni22C2</string>
			<string>intersectionbig1</string>
			<string>intersectionbig2</string>
		</array>
		<key>uni22C3</key>
		<array>
			<string>uni22C3</string>
			<string>unionbig1</string>
			<string>unionbig1h</string>
			<string>unionbig2</string>
		</array>
		<key>uni2308</key>
		<array>
			<string>uni2308</string>
			<string>ceilingBigl</string>
			<string>ceilingbiggl</string>
			<string>ceilingBiggl</string>
		</array>
		<key>uni2309</key>
		<array>
			<string>uni2309</string>
			<string>ceilingBigr</string>
			<string>ceilingbiggr</string>
			<string>ceilingBiggr</string>
		</array>
		<key>uni230A</key>
		<array>
			<string>uni230A</string>
			<string>floorBigl</string>
			<string>floorbiggl</string>
			<string>floorBiggl</string>
		</array>
		<key>uni230B</key>
		<array>
			<string>uni230B</string>
			<string>floorBigr</string>
			<string>floorbiggr</string>
			<string>floorBiggr</string>
		</array>
		<key>uni27C5</key>
		<array>
			<string>uni27C5</string>
			<string>glyph3587</string>
			<string>glyph3589</string>
			<string>glyph3591</string>
			<string>glyph3593</string>
			<string>glyph3595</string>
		</array>
		<key>uni27C6</key>
		<array>
			<string>uni27C6</string>
			<string>glyph3588</string>
			<string>glyph3590</string>
			<string>glyph3592</string>
			<string>glyph3594</string>
			<string>glyph3596</string>
		</array>
		<key>uni27E6</key>
		<array>
			<string>uni27E6</string>
			<string>glyph1114074</string>
			<string>glyph1114075</string>
			<string>glyph1114076</string>
			<string>glyph1114077</string>
		</array>
		<key>uni27E7</key>
		<array>
			<string>uni27E7</string>
			<string>glyph1114078</string>
			<string>glyph1114079</string>
			<string>glyph1114080</string>
			<string>glyph1114081</string>
		</array>
		<key>uni27E8</key>
		<array>
			<string>uni27E8</string>
			<string>anglebracketBigl</string>
			<string>anglebracketbiggl</string>
			<string>anglebracketBiggl</string>
		</array>
		<key>uni27E9</key>
		<array>
			<string>uni27E9</string>
			<string>anglebracketBigr</string>
			<string>anglebracketbiggr</string>
			<string>anglebracketBiggr</string>
		</array>
		<key>uni27EA</key>
		<array>
			<string>uni27EA</string>
			<string>danglebracketBigl</string>
			<string>danglebracketbiggl</string>
			<string>danglebracketBiggl</string>
		</array>
		<key>uni27EB</key>
		<array>
			<string>uni27EB</string>
			<string>danglebracketBigr</string>
			<string>dangelbracketbiggr</string>
			<string>danglebracketBiggr</string>
		</array>
		<key>uni29FC</key>
		<array>
			<string>uni29FC</string>
			<string>glyph3800</string>
			<string>glyph3801</string>
			<string>glyph3802</string>
		</array>
		<key>uni29FD</key>
		<array>
			<string>uni29FD</string>
			<string>glyph3803</string>
			<string>glyph3804</string>
			<string>glyph3805</string>
		</array>
		<key>uni2A00</key>
		<array>
			<string>uni2A00</string>
			<string>odotbig1</string>
			<string>odotbig2</string>
		</array>
		<key>uni2A01</key>
		<array>
			<string>uni2A01</string>
			<string>oplusbig1</string>
			<string>oplusbig2</string>
		</array>
		<key>uni2A02</key>
		<array>
			<string>uni2A02</string>
			<string>otimesbig1</string>
			<string>otimesbig2</string>
		</array>
		<key>uni2A03</key>
		<array>
			<string>uni2A03</string>
			<string>multisetmulbig1</string>
			<string>multisetmulbig2</string>
		</array>
		<key>uni2A04</key>
		<array>
			<string>uni2A04</string>
			<string>multisetsumbig1</string>
			<string>multisetsumbig2</string>
		</array>
		<key>uni2A05</key>
		<array>
			<string>uni2A05</string>
			<string>scapbig1</string>
			<string>scapbig2</string>
		</array>
		<key>uni2A06</key>
		<array>
			<string>uni2A06</string>
			<string>scupbig1</string>
			<string>scupbig2</string>
		</array>
		<key>uni2A07</key>
		<array>
			<string>uni2A07</string>
			<string>twoandbig1</string>
			<string>twoandbig2</string>
		</array>
		<key>uni2A08</key>
		<array>
			<string>uni2A08</string>
			<string>twoorbig1</string>
			<string>twoorbig2</string>
		</array>
		<key>uni2A09</key>
		<array>
			<string>uni2A09</string>
			<string>bigtimes1</string>
			<string>bigtimes2</string>
		</array>
		<key>uni2A0C</key>
		<array>
			<string>uni2A0C</string>
			<string>glyph2A0CBig</string>
			<string>glyph2A0Cbigg</string>
			<string>glyph2A0CBigg</string>
		</array>
		<key>uni2A0D</key>
		<array>
			<string>uni2A0D</string>
			<string>glyph2A0DBig</string>
			<string>glyph2A0Dbigg</string>
			<string>glyph2A0DBigg</string>
		</array>
		<key>uni2A0E</key>
		<array>
			<string>uni2A0E</string>
			<string>glyph2A0EBig</string>
			<string>glyph2A0Ebigg</string>
			<string>glyph2A0EBigg</string>
		</array>
		<key>uni2A0F</key>
		<array>
			<string>uni2A0F</string>
			<string>glyph2A0FBig</string>
			<string>glyph2A0Fbigg</string>
			<string>glyph2A0FBigg</string>
		</array>
		<key>uni2A10</key>
		<array>
			<string>uni2A10</string>
			<string>glyph2A10Big</string>
			<string>glyph2A10bigg</string>
			<string>glyph2A10Bigg</string>
		</array>
		<key>uni2A11</key>
		<array>
			<string>uni2A11</string>
			<string>glyph2A11Big</string>
			<string>glyph2A11bigg</string>
			<string>glyph2A11Bigg</string>
		</array>
		<key>uni2A12</key>
		<array>
			<string>uni2A12</string>
			<string>glyph2A12Big</string>
			<string>glyph2A12bigg</string>
			<string>glyph2A12Bigg</string>
		</array>
		<key>uni2A13</key>
		<array>
			<string>uni2A13</string>
			<string>glyph2A13Big</string>
			<string>glyph2A13bigg</string>
			<string>glyph2A13Bigg</string>
		</array>
		<key>uni2A14</key>
		<array>
			<string>uni2A14</string>
			<string>glyph2A14Big</string>
			<string>glyph2A14bigg</string>
			<string>glyph2A14Bigg</string>
		</array>
		<key>uni2A15</key>
		<array>
			<string>uni2A15</string>
			<string>glyph2A15Big</string>
			<string>glyph2A15bigg</string>
			<string>glyph2A15Bigg</string>
		</array>
		<key>uni2A16</key>
		<array>
			<string>uni2A16</string>
			<string>glyph2A16Big</string>
			<string>glyph2A16bigg</string>
			<string>glyph2A16Bigg</string>
		</array>
		<key>uni2A17</key>
		<array>
			<string>uni2A17</string>
			<string>glyph2A17Big</string>
			<string>glyph2A17bigg</string>
			<string>glyph2A17Bigg</string>
		</array>
		<key>uni2A18</key>
		<array>
			<string>uni2A18</string>
			<string>glyph2A18Big</string>
			<string>glyph2A18bigg</string>
			<string>glyph2A18Bigg</string>
		</array>
		<key>uni2A19</key>
		<array>
			<string>uni2A19</string>
			<string>glyph2A19Big</string>
			<string>glyph2A19bigg</string>
			<string>glyph2A19Bigg</string>
		</array>
		<key>uni2A1A</key>
		<array>
			<string>glyph2A1ABig</string>
			<string>glyph2A1Abigg</string>
			<string>glyph2A1ABigg</string>
		</array>
		<key>uni2A1B</key>
		<array>
			<string>uni2A1B</string>
			<string>glyph2A1BBig</string>
			<string>glyph2A1Bbigg</string>
			<string>glyph2A1BBigg</string>
		</array>
		<key>uni2A1C</key>
		<array>
			<string>uni2A1C</string>
			<string>glyph2A1CBig</string>
			<string>glyph2A1Cbigg</string>
			<string>glyph2A1CBigg</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
