<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>A</key>
		<integer>318</integer>
		<key>A.ssty1</key>
		<integer>345</integer>
		<key>A.ssty2</key>
		<integer>385</integer>
		<key>Alpha</key>
		<integer>318</integer>
		<key>Alpha.ssty1</key>
		<integer>345</integer>
		<key>Alpha.ssty2</key>
		<integer>385</integer>
		<key>B</key>
		<integer>322</integer>
		<key>B.ssty1</key>
		<integer>334</integer>
		<key>B.ssty2</key>
		<integer>376</integer>
		<key>Beta</key>
		<integer>322</integer>
		<key>Beta.ssty1</key>
		<integer>334</integer>
		<key>Beta.ssty2</key>
		<integer>376</integer>
		<key>C</key>
		<integer>380</integer>
		<key>C.ssty1</key>
		<integer>391</integer>
		<key>C.ssty2</key>
		<integer>425</integer>
		<key>Chi</key>
		<integer>292</integer>
		<key>Chi.ssty1</key>
		<integer>330</integer>
		<key>Chi.ssty2</key>
		<integer>356</integer>
		<key>D</key>
		<integer>361</integer>
		<key>D.ssty1</key>
		<integer>370</integer>
		<key>D.ssty2</key>
		<integer>403</integer>
		<key>E</key>
		<integer>305</integer>
		<key>E.ssty1</key>
		<integer>314</integer>
		<key>E.ssty2</key>
		<integer>344</integer>
		<key>Epsilon</key>
		<integer>305</integer>
		<key>Epsilon.ssty1</key>
		<integer>314</integer>
		<key>Epsilon.ssty2</key>
		<integer>344</integer>
		<key>Eta</key>
		<integer>371</integer>
		<key>Eta.ssty1</key>
		<integer>389</integer>
		<key>Eta.ssty2</key>
		<integer>422</integer>
		<key>F</key>
		<integer>298</integer>
		<key>F.ssty1</key>
		<integer>307</integer>
		<key>F.ssty2</key>
		<integer>343</integer>
		<key>G</key>
		<integer>405</integer>
		<key>G.ssty1</key>
		<integer>414</integer>
		<key>G.ssty2</key>
		<integer>436</integer>
		<key>Gamma</key>
		<integer>305</integer>
		<key>Gamma.ssty1</key>
		<integer>295</integer>
		<key>Gamma.ssty2</key>
		<integer>335</integer>
		<key>H</key>
		<integer>371</integer>
		<key>H.ssty1</key>
		<integer>389</integer>
		<key>H.ssty2</key>
		<integer>422</integer>
		<key>I</key>
		<integer>170</integer>
		<key>I.ssty1</key>
		<integer>209</integer>
		<key>I.ssty2</key>
		<integer>253</integer>
		<key>Iota</key>
		<integer>170</integer>
		<key>Iota.ssty1</key>
		<integer>209</integer>
		<key>Iota.ssty2</key>
		<integer>253</integer>
		<key>J</key>
		<integer>235</integer>
		<key>J.ssty1</key>
		<integer>271</integer>
		<key>J.ssty2</key>
		<integer>315</integer>
		<key>K</key>
		<integer>340</integer>
		<key>K.ssty1</key>
		<integer>369</integer>
		<key>K.ssty2</key>
		<integer>400</integer>
		<key>Kappa</key>
		<integer>340</integer>
		<key>Kappa.ssty1</key>
		<integer>369</integer>
		<key>Kappa.ssty2</key>
		<integer>400</integer>
		<key>L</key>
		<integer>140</integer>
		<key>L.ssty1</key>
		<integer>163</integer>
		<key>L.ssty2</key>
		<integer>214</integer>
		<key>Lambda</key>
		<integer>304</integer>
		<key>Lambda.ssty1</key>
		<integer>336</integer>
		<key>Lambda.ssty2</key>
		<integer>348</integer>
		<key>M</key>
		<integer>454</integer>
		<key>M.ssty1</key>
		<integer>478</integer>
		<key>M.ssty2</key>
		<integer>510</integer>
		<key>Mu</key>
		<integer>454</integer>
		<key>Mu.ssty1</key>
		<integer>478</integer>
		<key>Mu.ssty2</key>
		<integer>510</integer>
		<key>N</key>
		<integer>380</integer>
		<key>N.ssty1</key>
		<integer>408</integer>
		<key>N.ssty2</key>
		<integer>443</integer>
		<key>Nu</key>
		<integer>380</integer>
		<key>Nu.ssty1</key>
		<integer>408</integer>
		<key>Nu.ssty2</key>
		<integer>443</integer>
		<key>O</key>
		<integer>391</integer>
		<key>O.ssty1</key>
		<integer>403</integer>
		<key>O.ssty2</key>
		<integer>430</integer>
		<key>Omicron</key>
		<integer>391</integer>
		<key>Omicron.ssty1</key>
		<integer>403</integer>
		<key>Omicron.ssty2</key>
		<integer>430</integer>
		<key>P</key>
		<integer>317</integer>
		<key>P.ssty1</key>
		<integer>330</integer>
		<key>P.ssty2</key>
		<integer>376</integer>
		<key>Phi</key>
		<integer>402</integer>
		<key>Phi.ssty1</key>
		<integer>466</integer>
		<key>Phi.ssty2</key>
		<integer>528</integer>
		<key>Pi</key>
		<integer>366</integer>
		<key>Pi.ssty1</key>
		<integer>380</integer>
		<key>Pi.ssty2</key>
		<integer>416</integer>
		<key>Psi</key>
		<integer>402</integer>
		<key>Psi.ssty1</key>
		<integer>418</integer>
		<key>Psi.ssty2</key>
		<integer>485</integer>
		<key>Q</key>
		<integer>391</integer>
		<key>Q.ssty1</key>
		<integer>403</integer>
		<key>Q.ssty2</key>
		<integer>430</integer>
		<key>R</key>
		<integer>311</integer>
		<key>R.ssty1</key>
		<integer>336</integer>
		<key>R.ssty2</key>
		<integer>374</integer>
		<key>Rho</key>
		<integer>317</integer>
		<key>Rho.ssty1</key>
		<integer>330</integer>
		<key>Rho.ssty2</key>
		<integer>376</integer>
		<key>S</key>
		<integer>285</integer>
		<key>S.ssty1</key>
		<integer>320</integer>
		<key>S.ssty2</key>
		<integer>368</integer>
		<key>Sigma</key>
		<integer>285</integer>
		<key>Sigma.ssty1</key>
		<integer>312</integer>
		<key>Sigma.ssty2</key>
		<integer>349</integer>
		<key>T</key>
		<integer>278</integer>
		<key>T.ssty1</key>
		<integer>286</integer>
		<key>T.ssty2</key>
		<integer>306</integer>
		<key>Tau</key>
		<integer>278</integer>
		<key>Tau.ssty1</key>
		<integer>286</integer>
		<key>Tau.ssty2</key>
		<integer>306</integer>
		<key>Theta</key>
		<integer>391</integer>
		<key>Theta.ssty1</key>
		<integer>401</integer>
		<key>Theta.ssty2</key>
		<integer>430</integer>
		<key>U</key>
		<integer>366</integer>
		<key>U.ssty1</key>
		<integer>385</integer>
		<key>U.ssty2</key>
		<integer>417</integer>
		<key>Upsilon</key>
		<integer>283</integer>
		<key>Upsilon.ssty1</key>
		<integer>309</integer>
		<key>Upsilon.ssty2</key>
		<integer>325</integer>
		<key>V</key>
		<integer>303</integer>
		<key>V.ssty1</key>
		<integer>330</integer>
		<key>V.ssty2</key>
		<integer>344</integer>
		<key>W</key>
		<integer>465</integer>
		<key>W.ssty1</key>
		<integer>496</integer>
		<key>W.ssty2</key>
		<integer>525</integer>
		<key>X</key>
		<integer>292</integer>
		<key>X.ssty1</key>
		<integer>330</integer>
		<key>X.ssty2</key>
		<integer>356</integer>
		<key>Xi</key>
		<integer>278</integer>
		<key>Xi.ssty1</key>
		<integer>309</integer>
		<key>Xi.ssty2</key>
		<integer>342</integer>
		<key>Y</key>
		<integer>283</integer>
		<key>Y.ssty1</key>
		<integer>309</integer>
		<key>Y.ssty2</key>
		<integer>325</integer>
		<key>Z</key>
		<integer>290</integer>
		<key>Z.ssty1</key>
		<integer>329</integer>
		<key>Z.ssty2</key>
		<integer>373</integer>
		<key>Zeta</key>
		<integer>290</integer>
		<key>Zeta.ssty1</key>
		<integer>329</integer>
		<key>Zeta.ssty2</key>
		<integer>373</integer>
		<key>a</key>
		<integer>281</integer>
		<key>a.ssty1</key>
		<integer>309</integer>
		<key>a.ssty2</key>
		<integer>360</integer>
		<key>acutecomb</key>
		<integer>93</integer>
		<key>alpha</key>
		<integer>311</integer>
		<key>alpha.ssty1</key>
		<integer>338</integer>
		<key>alpha.ssty2</key>
		<integer>382</integer>
		<key>b</key>
		<integer>129</integer>
		<key>b.ssty1</key>
		<integer>151</integer>
		<key>b.ssty2</key>
		<integer>195</integer>
		<key>beta</key>
		<integer>324</integer>
		<key>beta.ssty1</key>
		<integer>323</integer>
		<key>beta.ssty2</key>
		<integer>365</integer>
		<key>c</key>
		<integer>284</integer>
		<key>c.ssty1</key>
		<integer>324</integer>
		<key>c.ssty2</key>
		<integer>388</integer>
		<key>chi</key>
		<integer>307</integer>
		<key>chi.ssty1</key>
		<integer>337</integer>
		<key>chi.ssty2</key>
		<integer>370</integer>
		<key>d</key>
		<integer>488</integer>
		<key>d.ssty1</key>
		<integer>513</integer>
		<key>d.ssty2</key>
		<integer>577</integer>
		<key>delta</key>
		<integer>302</integer>
		<key>delta.ssty1</key>
		<integer>334</integer>
		<key>delta.ssty2</key>
		<integer>392</integer>
		<key>dotbelowcomb</key>
		<integer>77</integer>
		<key>dotlessi</key>
		<integer>129</integer>
		<key>dotlessi.ssty1</key>
		<integer>150</integer>
		<key>dotlessi.ssty2</key>
		<integer>175</integer>
		<key>e</key>
		<integer>285</integer>
		<key>e.ssty1</key>
		<integer>322</integer>
		<key>e.ssty2</key>
		<integer>382</integer>
		<key>epsilon</key>
		<integer>268</integer>
		<key>epsilon.ssty1</key>
		<integer>295</integer>
		<key>epsilon.ssty2</key>
		<integer>351</integer>
		<key>eta</key>
		<integer>326</integer>
		<key>eta.ssty1</key>
		<integer>340</integer>
		<key>eta.ssty2</key>
		<integer>378</integer>
		<key>f</key>
		<integer>252</integer>
		<key>f.ssty1</key>
		<integer>277</integer>
		<key>f.ssty2</key>
		<integer>299</integer>
		<key>g</key>
		<integer>297</integer>
		<key>g.ssty1</key>
		<integer>319</integer>
		<key>g.ssty2</key>
		<integer>371</integer>
		<key>gamma</key>
		<integer>259</integer>
		<key>gamma.ssty1</key>
		<integer>288</integer>
		<key>gamma.ssty2</key>
		<integer>286</integer>
		<key>gravecomb</key>
		<integer>101</integer>
		<key>h</key>
		<integer>129</integer>
		<key>h.ssty1</key>
		<integer>151</integer>
		<key>h.ssty2</key>
		<integer>195</integer>
		<key>hookabovecomb</key>
		<integer>120</integer>
		<key>i</key>
		<integer>129</integer>
		<key>i.ssty1</key>
		<integer>149</integer>
		<key>i.ssty2</key>
		<integer>174</integer>
		<key>iota</key>
		<integer>130</integer>
		<key>iota.ssty1</key>
		<integer>140</integer>
		<key>iota.ssty2</key>
		<integer>161</integer>
		<key>j</key>
		<integer>204</integer>
		<key>j.ssty1</key>
		<integer>235</integer>
		<key>j.ssty2</key>
		<integer>260</integer>
		<key>k</key>
		<integer>129</integer>
		<key>k.ssty1</key>
		<integer>151</integer>
		<key>k.ssty2</key>
		<integer>195</integer>
		<key>kappa</key>
		<integer>297</integer>
		<key>kappa.ssty1</key>
		<integer>302</integer>
		<key>kappa.ssty2</key>
		<integer>362</integer>
		<key>l</key>
		<integer>129</integer>
		<key>l.ssty1</key>
		<integer>150</integer>
		<key>l.ssty2</key>
		<integer>176</integer>
		<key>lambda</key>
		<integer>277</integer>
		<key>lambda.ssty1</key>
		<integer>280</integer>
		<key>lambda.ssty2</key>
		<integer>305</integer>
		<key>m</key>
		<integer>479</integer>
		<key>m.ssty1</key>
		<integer>500</integer>
		<key>m.ssty2</key>
		<integer>543</integer>
		<key>n</key>
		<integer>309</integer>
		<key>n.ssty1</key>
		<integer>327</integer>
		<key>n.ssty2</key>
		<integer>362</integer>
		<key>nu</key>
		<integer>274</integer>
		<key>nu.ssty1</key>
		<integer>288</integer>
		<key>nu.ssty2</key>
		<integer>321</integer>
		<key>o</key>
		<integer>303</integer>
		<key>o.ssty1</key>
		<integer>337</integer>
		<key>o.ssty2</key>
		<integer>395</integer>
		<key>omega</key>
		<integer>389</integer>
		<key>omega.ssty1</key>
		<integer>419</integer>
		<key>omega.ssty2</key>
		<integer>459</integer>
		<key>omicron</key>
		<integer>303</integer>
		<key>omicron.ssty1</key>
		<integer>337</integer>
		<key>omicron.ssty2</key>
		<integer>395</integer>
		<key>p</key>
		<integer>332</integer>
		<key>p.ssty1</key>
		<integer>357</integer>
		<key>p.ssty2</key>
		<integer>391</integer>
		<key>partialdiff</key>
		<integer>246</integer>
		<key>partialdiff.rtlm</key>
		<integer>306</integer>
		<key>partialdiff.ssty1</key>
		<integer>273</integer>
		<key>partialdiff.ssty2</key>
		<integer>319</integer>
		<key>phi</key>
		<integer>363</integer>
		<key>phi.ssty1</key>
		<integer>422</integer>
		<key>phi.ssty2</key>
		<integer>489</integer>
		<key>pi</key>
		<integer>330</integer>
		<key>pi.ssty1</key>
		<integer>361</integer>
		<key>pi.ssty2</key>
		<integer>408</integer>
		<key>psi</key>
		<integer>381</integer>
		<key>psi.ssty1</key>
		<integer>400</integer>
		<key>psi.ssty2</key>
		<integer>447</integer>
		<key>q</key>
		<integer>308</integer>
		<key>q.ssty1</key>
		<integer>331</integer>
		<key>q.ssty2</key>
		<integer>385</integer>
		<key>r</key>
		<integer>240</integer>
		<key>r.ssty1</key>
		<integer>257</integer>
		<key>r.ssty2</key>
		<integer>296</integer>
		<key>rho</key>
		<integer>303</integer>
		<key>rho.ssty1</key>
		<integer>331</integer>
		<key>rho.ssty2</key>
		<integer>358</integer>
		<key>s</key>
		<integer>240</integer>
		<key>s.ssty1</key>
		<integer>270</integer>
		<key>s.ssty2</key>
		<integer>323</integer>
		<key>sigma</key>
		<integer>309</integer>
		<key>sigma.ssty1</key>
		<integer>336</integer>
		<key>sigma.ssty2</key>
		<integer>382</integer>
		<key>t</key>
		<integer>154</integer>
		<key>t.ssty1</key>
		<integer>177</integer>
		<key>t.ssty2</key>
		<integer>201</integer>
		<key>tau</key>
		<integer>240</integer>
		<key>tau.ssty1</key>
		<integer>261</integer>
		<key>tau.ssty2</key>
		<integer>286</integer>
		<key>theta</key>
		<integer>298</integer>
		<key>theta.ssty1</key>
		<integer>332</integer>
		<key>theta.ssty2</key>
		<integer>390</integer>
		<key>tildecomb</key>
		<integer>184</integer>
		<key>tildecomb.s00</key>
		<integer>183</integer>
		<key>tildecomb.s01</key>
		<integer>406</integer>
		<key>tildecomb.s02</key>
		<integer>629</integer>
		<key>tildecomb.s03</key>
		<integer>853</integer>
		<key>tildecomb.s04</key>
		<integer>1076</integer>
		<key>tildecomb.s05</key>
		<integer>1300</integer>
		<key>u</key>
		<integer>309</integer>
		<key>u.ssty1</key>
		<integer>326</integer>
		<key>u.ssty2</key>
		<integer>360</integer>
		<key>u1D49C</key>
		<integer>999</integer>
		<key>u1D49C.ssty1</key>
		<integer>1006</integer>
		<key>u1D49C.ssty2</key>
		<integer>1012</integer>
		<key>u1D49C.uv001</key>
		<integer>478</integer>
		<key>u1D49E</key>
		<integer>576</integer>
		<key>u1D49E.ssty1</key>
		<integer>579</integer>
		<key>u1D49E.ssty2</key>
		<integer>582</integer>
		<key>u1D49E.uv001</key>
		<integer>465</integer>
		<key>u1D49F</key>
		<integer>465</integer>
		<key>u1D49F.ssty1</key>
		<integer>467</integer>
		<key>u1D49F.ssty2</key>
		<integer>468</integer>
		<key>u1D49F.uv001</key>
		<integer>455</integer>
		<key>u1D4A2</key>
		<integer>364</integer>
		<key>u1D4A2.ssty1</key>
		<integer>363</integer>
		<key>u1D4A2.ssty2</key>
		<integer>362</integer>
		<key>u1D4A2.uv001</key>
		<integer>422</integer>
		<key>u1D4A5</key>
		<integer>715</integer>
		<key>u1D4A5.ssty1</key>
		<integer>722</integer>
		<key>u1D4A5.ssty2</key>
		<integer>729</integer>
		<key>u1D4A5.uv001</key>
		<integer>491</integer>
		<key>u1D4A6</key>
		<integer>879</integer>
		<key>u1D4A6.ssty1</key>
		<integer>895</integer>
		<key>u1D4A6.ssty2</key>
		<integer>910</integer>
		<key>u1D4A6.uv001</key>
		<integer>447</integer>
		<key>u1D4A9</key>
		<integer>844</integer>
		<key>u1D4A9.ssty1</key>
		<integer>846</integer>
		<key>u1D4A9.ssty2</key>
		<integer>848</integer>
		<key>u1D4A9.uv001</key>
		<integer>680</integer>
		<key>u1D4AA</key>
		<integer>616</integer>
		<key>u1D4AA.ssty1</key>
		<integer>620</integer>
		<key>u1D4AA.ssty2</key>
		<integer>624</integer>
		<key>u1D4AA.uv001</key>
		<integer>523</integer>
		<key>u1D4AB</key>
		<integer>783</integer>
		<key>u1D4AB.ssty1</key>
		<integer>787</integer>
		<key>u1D4AB.ssty2</key>
		<integer>791</integer>
		<key>u1D4AB.uv001</key>
		<integer>472</integer>
		<key>u1D4AC</key>
		<integer>592</integer>
		<key>u1D4AC.ssty1</key>
		<integer>596</integer>
		<key>u1D4AC.ssty2</key>
		<integer>600</integer>
		<key>u1D4AC.uv001</key>
		<integer>449</integer>
		<key>u1D4AE</key>
		<integer>787</integer>
		<key>u1D4AE.ssty1</key>
		<integer>793</integer>
		<key>u1D4AE.ssty2</key>
		<integer>799</integer>
		<key>u1D4AE.uv001</key>
		<integer>365</integer>
		<key>u1D4AF</key>
		<integer>759</integer>
		<key>u1D4AF.ssty1</key>
		<integer>763</integer>
		<key>u1D4AF.ssty2</key>
		<integer>767</integer>
		<key>u1D4AF.uv001</key>
		<integer>497</integer>
		<key>u1D4B0</key>
		<integer>730</integer>
		<key>u1D4B0.ssty1</key>
		<integer>736</integer>
		<key>u1D4B0.ssty2</key>
		<integer>741</integer>
		<key>u1D4B0.uv001</key>
		<integer>388</integer>
		<key>u1D4B1</key>
		<integer>646</integer>
		<key>u1D4B1.ssty1</key>
		<integer>650</integer>
		<key>u1D4B1.ssty2</key>
		<integer>654</integer>
		<key>u1D4B1.uv001</key>
		<integer>463</integer>
		<key>u1D4B2</key>
		<integer>974</integer>
		<key>u1D4B2.ssty1</key>
		<integer>981</integer>
		<key>u1D4B2.ssty2</key>
		<integer>987</integer>
		<key>u1D4B2.uv001</key>
		<integer>578</integer>
		<key>u1D4B3</key>
		<integer>738</integer>
		<key>u1D4B3.ssty1</key>
		<integer>744</integer>
		<key>u1D4B3.ssty2</key>
		<integer>749</integer>
		<key>u1D4B3.uv001</key>
		<integer>502</integer>
		<key>u1D4B4</key>
		<integer>663</integer>
		<key>u1D4B4.ssty1</key>
		<integer>669</integer>
		<key>u1D4B4.ssty2</key>
		<integer>674</integer>
		<key>u1D4B4.uv001</key>
		<integer>375</integer>
		<key>u1D4B5</key>
		<integer>444</integer>
		<key>u1D4B5.ssty1</key>
		<integer>447</integer>
		<key>u1D4B5.ssty2</key>
		<integer>450</integer>
		<key>u1D4B5.uv001</key>
		<integer>423</integer>
		<key>u1D4B6</key>
		<integer>339</integer>
		<key>u1D4B6.ssty1</key>
		<integer>331</integer>
		<key>u1D4B6.ssty2</key>
		<integer>400</integer>
		<key>u1D4B7</key>
		<integer>377</integer>
		<key>u1D4B7.ssty1</key>
		<integer>368</integer>
		<key>u1D4B7.ssty2</key>
		<integer>361</integer>
		<key>u1D4B8</key>
		<integer>321</integer>
		<key>u1D4B8.ssty1</key>
		<integer>310</integer>
		<key>u1D4B8.ssty2</key>
		<integer>423</integer>
		<key>u1D4B9</key>
		<integer>607</integer>
		<key>u1D4B9.ssty1</key>
		<integer>606</integer>
		<key>u1D4B9.ssty2</key>
		<integer>608</integer>
		<key>u1D4BB</key>
		<integer>517</integer>
		<key>u1D4BB.ssty1</key>
		<integer>529</integer>
		<key>u1D4BB.ssty2</key>
		<integer>542</integer>
		<key>u1D4BD</key>
		<integer>305</integer>
		<key>u1D4BD.ssty1</key>
		<integer>315</integer>
		<key>u1D4BD.ssty2</key>
		<integer>326</integer>
		<key>u1D4BE</key>
		<integer>312</integer>
		<key>u1D4BE.dtls</key>
		<integer>237</integer>
		<key>u1D4BE.dtls.ssty1</key>
		<integer>235</integer>
		<key>u1D4BE.dtls.ssty2</key>
		<integer>234</integer>
		<key>u1D4BE.ssty1</key>
		<integer>309</integer>
		<key>u1D4BE.ssty2</key>
		<integer>337</integer>
		<key>u1D4BF</key>
		<integer>375</integer>
		<key>u1D4BF.dtls</key>
		<integer>321</integer>
		<key>u1D4BF.dtls.ssty1</key>
		<integer>332</integer>
		<key>u1D4BF.dtls.ssty2</key>
		<integer>344</integer>
		<key>u1D4BF.ssty1</key>
		<integer>385</integer>
		<key>u1D4BF.ssty2</key>
		<integer>452</integer>
		<key>u1D4C0</key>
		<integer>306</integer>
		<key>u1D4C0.ssty1</key>
		<integer>318</integer>
		<key>u1D4C0.ssty2</key>
		<integer>330</integer>
		<key>u1D4C1</key>
		<integer>378</integer>
		<key>u1D4C1.ssty1</key>
		<integer>379</integer>
		<key>u1D4C1.ssty2</key>
		<integer>381</integer>
		<key>u1D4C2</key>
		<integer>441</integer>
		<key>u1D4C2.ssty1</key>
		<integer>448</integer>
		<key>u1D4C2.ssty2</key>
		<integer>541</integer>
		<key>u1D4C3</key>
		<integer>342</integer>
		<key>u1D4C3.ssty1</key>
		<integer>338</integer>
		<key>u1D4C3.ssty2</key>
		<integer>420</integer>
		<key>u1D4C5</key>
		<integer>425</integer>
		<key>u1D4C5.ssty1</key>
		<integer>427</integer>
		<key>u1D4C5.ssty2</key>
		<integer>429</integer>
		<key>u1D4C6</key>
		<integer>330</integer>
		<key>u1D4C6.ssty1</key>
		<integer>325</integer>
		<key>u1D4C6.ssty2</key>
		<integer>364</integer>
		<key>u1D4C7</key>
		<integer>213</integer>
		<key>u1D4C7.ssty1</key>
		<integer>211</integer>
		<key>u1D4C7.ssty2</key>
		<integer>214</integer>
		<key>u1D4C8</key>
		<integer>213</integer>
		<key>u1D4C8.ssty1</key>
		<integer>212</integer>
		<key>u1D4C8.ssty2</key>
		<integer>261</integer>
		<key>u1D4C9</key>
		<integer>420</integer>
		<key>u1D4C9.ssty1</key>
		<integer>418</integer>
		<key>u1D4C9.ssty2</key>
		<integer>415</integer>
		<key>u1D4CA</key>
		<integer>347</integer>
		<key>u1D4CA.ssty1</key>
		<integer>350</integer>
		<key>u1D4CA.ssty2</key>
		<integer>412</integer>
		<key>u1D4CB</key>
		<integer>339</integer>
		<key>u1D4CB.ssty1</key>
		<integer>332</integer>
		<key>u1D4CB.ssty2</key>
		<integer>416</integer>
		<key>u1D4CC</key>
		<integer>486</integer>
		<key>u1D4CC.ssty1</key>
		<integer>486</integer>
		<key>u1D4CC.ssty2</key>
		<integer>598</integer>
		<key>u1D4CD</key>
		<integer>313</integer>
		<key>u1D4CD.ssty1</key>
		<integer>299</integer>
		<key>u1D4CD.ssty2</key>
		<integer>384</integer>
		<key>u1D4CE</key>
		<integer>400</integer>
		<key>u1D4CE.ssty1</key>
		<integer>410</integer>
		<key>u1D4CE.ssty2</key>
		<integer>524</integer>
		<key>u1D4CF</key>
		<integer>296</integer>
		<key>u1D4CF.ssty1</key>
		<integer>291</integer>
		<key>u1D4CF.ssty2</key>
		<integer>363</integer>
		<key>u1D4D0</key>
		<integer>1026</integer>
		<key>u1D4D1</key>
		<integer>604</integer>
		<key>u1D4D2</key>
		<integer>571</integer>
		<key>u1D4D3</key>
		<integer>473</integer>
		<key>u1D4D4</key>
		<integer>510</integer>
		<key>u1D4D5</key>
		<integer>690</integer>
		<key>u1D4D6</key>
		<integer>372</integer>
		<key>u1D4D7</key>
		<integer>886</integer>
		<key>u1D4D8</key>
		<integer>671</integer>
		<key>u1D4D9</key>
		<integer>731</integer>
		<key>u1D4DA</key>
		<integer>805</integer>
		<key>u1D4DB</key>
		<integer>591</integer>
		<key>u1D4DC</key>
		<integer>933</integer>
		<key>u1D4DD</key>
		<integer>889</integer>
		<key>u1D4DE</key>
		<integer>618</integer>
		<key>u1D4DF</key>
		<integer>791</integer>
		<key>u1D4E0</key>
		<integer>596</integer>
		<key>u1D4E1</key>
		<integer>634</integer>
		<key>u1D4E2</key>
		<integer>819</integer>
		<key>u1D4E3</key>
		<integer>755</integer>
		<key>u1D4E4</key>
		<integer>632</integer>
		<key>u1D4E5</key>
		<integer>510</integer>
		<key>u1D4E6</key>
		<integer>679</integer>
		<key>u1D4E7</key>
		<integer>746</integer>
		<key>u1D4E8</key>
		<integer>571</integer>
		<key>u1D4E9</key>
		<integer>465</integer>
		<key>u1D4EA</key>
		<integer>323</integer>
		<key>u1D4EB</key>
		<integer>361</integer>
		<key>u1D4EC</key>
		<integer>305</integer>
		<key>u1D4ED</key>
		<integer>608</integer>
		<key>u1D4EE</key>
		<integer>281</integer>
		<key>u1D4EF</key>
		<integer>542</integer>
		<key>u1D4F0</key>
		<integer>386</integer>
		<key>u1D4F1</key>
		<integer>326</integer>
		<key>u1D4F2</key>
		<integer>307</integer>
		<key>u1D4F3</key>
		<integer>392</integer>
		<key>u1D4F4</key>
		<integer>330</integer>
		<key>u1D4F5</key>
		<integer>381</integer>
		<key>u1D4F6</key>
		<integer>452</integer>
		<key>u1D4F7</key>
		<integer>336</integer>
		<key>u1D4F8</key>
		<integer>287</integer>
		<key>u1D4F9</key>
		<integer>429</integer>
		<key>u1D4FA</key>
		<integer>319</integer>
		<key>u1D4FB</key>
		<integer>209</integer>
		<key>u1D4FC</key>
		<integer>212</integer>
		<key>u1D4FD</key>
		<integer>415</integer>
		<key>u1D4FE</key>
		<integer>352</integer>
		<key>u1D4FF</key>
		<integer>331</integer>
		<key>u1D500</key>
		<integer>493</integer>
		<key>u1D501</key>
		<integer>284</integer>
		<key>u1D502</key>
		<integer>417</integer>
		<key>u1D503</key>
		<integer>287</integer>
		<key>u1D504</key>
		<integer>314</integer>
		<key>u1D504.ssty1</key>
		<integer>343</integer>
		<key>u1D504.ssty2</key>
		<integer>382</integer>
		<key>u1D505</key>
		<integer>384</integer>
		<key>u1D505.ssty1</key>
		<integer>402</integer>
		<key>u1D505.ssty2</key>
		<integer>433</integer>
		<key>u1D507</key>
		<integer>370</integer>
		<key>u1D507.ssty1</key>
		<integer>384</integer>
		<key>u1D507.ssty2</key>
		<integer>410</integer>
		<key>u1D508</key>
		<integer>335</integer>
		<key>u1D508.ssty1</key>
		<integer>368</integer>
		<key>u1D508.ssty2</key>
		<integer>414</integer>
		<key>u1D509</key>
		<integer>312</integer>
		<key>u1D509.ssty1</key>
		<integer>340</integer>
		<key>u1D509.ssty2</key>
		<integer>378</integer>
		<key>u1D50A</key>
		<integer>366</integer>
		<key>u1D50A.ssty1</key>
		<integer>387</integer>
		<key>u1D50A.ssty2</key>
		<integer>423</integer>
		<key>u1D50D</key>
		<integer>263</integer>
		<key>u1D50D.ssty1</key>
		<integer>286</integer>
		<key>u1D50D.ssty2</key>
		<integer>323</integer>
		<key>u1D50E</key>
		<integer>395</integer>
		<key>u1D50E.ssty1</key>
		<integer>429</integer>
		<key>u1D50E.ssty2</key>
		<integer>477</integer>
		<key>u1D50F</key>
		<integer>279</integer>
		<key>u1D50F.ssty1</key>
		<integer>306</integer>
		<key>u1D50F.ssty2</key>
		<integer>345</integer>
		<key>u1D510</key>
		<integer>475</integer>
		<key>u1D510.ssty1</key>
		<integer>514</integer>
		<key>u1D510.ssty2</key>
		<integer>566</integer>
		<key>u1D511</key>
		<integer>382</integer>
		<key>u1D511.ssty1</key>
		<integer>420</integer>
		<key>u1D511.ssty2</key>
		<integer>469</integer>
		<key>u1D512</key>
		<integer>445</integer>
		<key>u1D512.ssty1</key>
		<integer>459</integer>
		<key>u1D512.ssty2</key>
		<integer>488</integer>
		<key>u1D513</key>
		<integer>334</integer>
		<key>u1D513.ssty1</key>
		<integer>358</integer>
		<key>u1D513.ssty2</key>
		<integer>394</integer>
		<key>u1D514</key>
		<integer>398</integer>
		<key>u1D514.ssty1</key>
		<integer>430</integer>
		<key>u1D514.ssty2</key>
		<integer>475</integer>
		<key>u1D516</key>
		<integer>426</integer>
		<key>u1D516.ssty1</key>
		<integer>451</integer>
		<key>u1D516.ssty2</key>
		<integer>496</integer>
		<key>u1D517</key>
		<integer>326</integer>
		<key>u1D517.ssty1</key>
		<integer>346</integer>
		<key>u1D517.ssty2</key>
		<integer>378</integer>
		<key>u1D518</key>
		<integer>301</integer>
		<key>u1D518.ssty1</key>
		<integer>324</integer>
		<key>u1D518.ssty2</key>
		<integer>359</integer>
		<key>u1D519</key>
		<integer>352</integer>
		<key>u1D519.ssty1</key>
		<integer>373</integer>
		<key>u1D519.ssty2</key>
		<integer>405</integer>
		<key>u1D51A</key>
		<integer>454</integer>
		<key>u1D51A.ssty1</key>
		<integer>475</integer>
		<key>u1D51A.ssty2</key>
		<integer>509</integer>
		<key>u1D51B</key>
		<integer>337</integer>
		<key>u1D51B.ssty1</key>
		<integer>366</integer>
		<key>u1D51B.ssty2</key>
		<integer>408</integer>
		<key>u1D51C</key>
		<integer>354</integer>
		<key>u1D51C.ssty1</key>
		<integer>380</integer>
		<key>u1D51C.ssty2</key>
		<integer>419</integer>
		<key>u1D51E</key>
		<integer>241</integer>
		<key>u1D51E.ssty1</key>
		<integer>265</integer>
		<key>u1D51E.ssty2</key>
		<integer>303</integer>
		<key>u1D51F</key>
		<integer>180</integer>
		<key>u1D51F.ssty1</key>
		<integer>201</integer>
		<key>u1D51F.ssty2</key>
		<integer>233</integer>
		<key>u1D520</key>
		<integer>176</integer>
		<key>u1D520.ssty1</key>
		<integer>204</integer>
		<key>u1D520.ssty2</key>
		<integer>248</integer>
		<key>u1D521</key>
		<integer>187</integer>
		<key>u1D521.ssty1</key>
		<integer>211</integer>
		<key>u1D521.ssty2</key>
		<integer>246</integer>
		<key>u1D522</key>
		<integer>204</integer>
		<key>u1D522.ssty1</key>
		<integer>234</integer>
		<key>u1D522.ssty2</key>
		<integer>281</integer>
		<key>u1D523</key>
		<integer>204</integer>
		<key>u1D523.ssty1</key>
		<integer>233</integer>
		<key>u1D523.ssty2</key>
		<integer>276</integer>
		<key>u1D524</key>
		<integer>229</integer>
		<key>u1D524.ssty1</key>
		<integer>255</integer>
		<key>u1D524.ssty2</key>
		<integer>297</integer>
		<key>u1D525</key>
		<integer>169</integer>
		<key>u1D525.ssty1</key>
		<integer>189</integer>
		<key>u1D525.ssty2</key>
		<integer>219</integer>
		<key>u1D526</key>
		<integer>132</integer>
		<key>u1D526.dtls</key>
		<integer>111</integer>
		<key>u1D526.dtls.ssty1</key>
		<integer>135</integer>
		<key>u1D526.dtls.ssty2</key>
		<integer>168</integer>
		<key>u1D526.ssty1</key>
		<integer>160</integer>
		<key>u1D526.ssty2</key>
		<integer>200</integer>
		<key>u1D527</key>
		<integer>137</integer>
		<key>u1D527.dtls</key>
		<integer>127</integer>
		<key>u1D527.dtls.ssty1</key>
		<integer>154</integer>
		<key>u1D527.dtls.ssty2</key>
		<integer>196</integer>
		<key>u1D527.ssty1</key>
		<integer>160</integer>
		<key>u1D527.ssty2</key>
		<integer>197</integer>
		<key>u1D528</key>
		<integer>173</integer>
		<key>u1D528.ssty1</key>
		<integer>197</integer>
		<key>u1D528.ssty2</key>
		<integer>231</integer>
		<key>u1D529</key>
		<integer>133</integer>
		<key>u1D529.ssty1</key>
		<integer>156</integer>
		<key>u1D529.ssty2</key>
		<integer>191</integer>
		<key>u1D52A</key>
		<integer>334</integer>
		<key>u1D52A.ssty1</key>
		<integer>365</integer>
		<key>u1D52A.ssty2</key>
		<integer>407</integer>
		<key>u1D52B</key>
		<integer>226</integer>
		<key>u1D52B.ssty1</key>
		<integer>253</integer>
		<key>u1D52B.ssty2</key>
		<integer>290</integer>
		<key>u1D52C</key>
		<integer>240</integer>
		<key>u1D52C.ssty1</key>
		<integer>269</integer>
		<key>u1D52C.ssty2</key>
		<integer>313</integer>
		<key>u1D52D</key>
		<integer>271</integer>
		<key>u1D52D.ssty1</key>
		<integer>304</integer>
		<key>u1D52D.ssty2</key>
		<integer>353</integer>
		<key>u1D52E</key>
		<integer>197</integer>
		<key>u1D52E.ssty1</key>
		<integer>222</integer>
		<key>u1D52E.ssty2</key>
		<integer>259</integer>
		<key>u1D52F</key>
		<integer>181</integer>
		<key>u1D52F.ssty1</key>
		<integer>208</integer>
		<key>u1D52F.ssty2</key>
		<integer>248</integer>
		<key>u1D530</key>
		<integer>236</integer>
		<key>u1D530.ssty1</key>
		<integer>262</integer>
		<key>u1D530.ssty2</key>
		<integer>306</integer>
		<key>u1D531</key>
		<integer>138</integer>
		<key>u1D531.ssty1</key>
		<integer>164</integer>
		<key>u1D531.ssty2</key>
		<integer>203</integer>
		<key>u1D532</key>
		<integer>224</integer>
		<key>u1D532.ssty1</key>
		<integer>251</integer>
		<key>u1D532.ssty2</key>
		<integer>291</integer>
		<key>u1D533</key>
		<integer>225</integer>
		<key>u1D533.ssty1</key>
		<integer>248</integer>
		<key>u1D533.ssty2</key>
		<integer>285</integer>
		<key>u1D534</key>
		<integer>313</integer>
		<key>u1D534.ssty1</key>
		<integer>344</integer>
		<key>u1D534.ssty2</key>
		<integer>387</integer>
		<key>u1D535</key>
		<integer>215</integer>
		<key>u1D535.ssty1</key>
		<integer>243</integer>
		<key>u1D535.ssty2</key>
		<integer>285</integer>
		<key>u1D536</key>
		<integer>194</integer>
		<key>u1D536.ssty1</key>
		<integer>216</integer>
		<key>u1D536.ssty2</key>
		<integer>249</integer>
		<key>u1D537</key>
		<integer>152</integer>
		<key>u1D537.ssty1</key>
		<integer>173</integer>
		<key>u1D537.ssty2</key>
		<integer>207</integer>
		<key>u1D538</key>
		<integer>366</integer>
		<key>u1D538.ssty1</key>
		<integer>364</integer>
		<key>u1D538.ssty2</key>
		<integer>349</integer>
		<key>u1D539</key>
		<integer>319</integer>
		<key>u1D539.ssty1</key>
		<integer>330</integer>
		<key>u1D539.ssty2</key>
		<integer>402</integer>
		<key>u1D53B</key>
		<integer>327</integer>
		<key>u1D53B.ssty1</key>
		<integer>309</integer>
		<key>u1D53B.ssty2</key>
		<integer>432</integer>
		<key>u1D53C</key>
		<integer>295</integer>
		<key>u1D53C.ssty1</key>
		<integer>304</integer>
		<key>u1D53C.ssty2</key>
		<integer>346</integer>
		<key>u1D53D</key>
		<integer>306</integer>
		<key>u1D53D.ssty1</key>
		<integer>319</integer>
		<key>u1D53D.ssty2</key>
		<integer>415</integer>
		<key>u1D53E</key>
		<integer>412</integer>
		<key>u1D53E.ssty1</key>
		<integer>446</integer>
		<key>u1D53E.ssty2</key>
		<integer>371</integer>
		<key>u1D540</key>
		<integer>163</integer>
		<key>u1D540.ssty1</key>
		<integer>170</integer>
		<key>u1D540.ssty2</key>
		<integer>207</integer>
		<key>u1D541</key>
		<integer>257</integer>
		<key>u1D541.ssty1</key>
		<integer>286</integer>
		<key>u1D541.ssty2</key>
		<integer>276</integer>
		<key>u1D542</key>
		<integer>352</integer>
		<key>u1D542.ssty1</key>
		<integer>367</integer>
		<key>u1D542.ssty2</key>
		<integer>373</integer>
		<key>u1D543</key>
		<integer>165</integer>
		<key>u1D543.ssty1</key>
		<integer>171</integer>
		<key>u1D543.ssty2</key>
		<integer>207</integer>
		<key>u1D544</key>
		<integer>451</integer>
		<key>u1D544.ssty1</key>
		<integer>426</integer>
		<key>u1D544.ssty2</key>
		<integer>470</integer>
		<key>u1D546</key>
		<integer>379</integer>
		<key>u1D546.ssty1</key>
		<integer>412</integer>
		<key>u1D546.ssty2</key>
		<integer>464</integer>
		<key>u1D54A</key>
		<integer>274</integer>
		<key>u1D54A.ssty1</key>
		<integer>235</integer>
		<key>u1D54A.ssty2</key>
		<integer>276</integer>
		<key>u1D54B</key>
		<integer>294</integer>
		<key>u1D54B.ssty1</key>
		<integer>318</integer>
		<key>u1D54B.ssty2</key>
		<integer>339</integer>
		<key>u1D54C</key>
		<integer>354</integer>
		<key>u1D54C.ssty1</key>
		<integer>313</integer>
		<key>u1D54C.ssty2</key>
		<integer>352</integer>
		<key>u1D54D</key>
		<integer>370</integer>
		<key>u1D54D.ssty1</key>
		<integer>296</integer>
		<key>u1D54D.ssty2</key>
		<integer>321</integer>
		<key>u1D54E</key>
		<integer>506</integer>
		<key>u1D54E.ssty1</key>
		<integer>428</integer>
		<key>u1D54E.ssty2</key>
		<integer>473</integer>
		<key>u1D54F</key>
		<integer>368</integer>
		<key>u1D54F.ssty1</key>
		<integer>273</integer>
		<key>u1D54F.ssty2</key>
		<integer>307</integer>
		<key>u1D550</key>
		<integer>353</integer>
		<key>u1D550.ssty1</key>
		<integer>312</integer>
		<key>u1D550.ssty2</key>
		<integer>339</integer>
		<key>u1D552</key>
		<integer>305</integer>
		<key>u1D552.ssty1</key>
		<integer>291</integer>
		<key>u1D552.ssty2</key>
		<integer>296</integer>
		<key>u1D553</key>
		<integer>161</integer>
		<key>u1D553.ssty1</key>
		<integer>163</integer>
		<key>u1D553.ssty2</key>
		<integer>163</integer>
		<key>u1D554</key>
		<integer>288</integer>
		<key>u1D554.ssty1</key>
		<integer>244</integer>
		<key>u1D554.ssty2</key>
		<integer>251</integer>
		<key>u1D555</key>
		<integer>506</integer>
		<key>u1D555.ssty1</key>
		<integer>524</integer>
		<key>u1D555.ssty2</key>
		<integer>530</integer>
		<key>u1D556</key>
		<integer>283</integer>
		<key>u1D556.ssty1</key>
		<integer>287</integer>
		<key>u1D556.ssty2</key>
		<integer>292</integer>
		<key>u1D557</key>
		<integer>270</integer>
		<key>u1D557.ssty1</key>
		<integer>221</integer>
		<key>u1D557.ssty2</key>
		<integer>231</integer>
		<key>u1D558</key>
		<integer>306</integer>
		<key>u1D558.ssty1</key>
		<integer>312</integer>
		<key>u1D558.ssty2</key>
		<integer>318</integer>
		<key>u1D559</key>
		<integer>161</integer>
		<key>u1D559.ssty1</key>
		<integer>163</integer>
		<key>u1D559.ssty2</key>
		<integer>163</integer>
		<key>u1D55A</key>
		<integer>161</integer>
		<key>u1D55A.dtls</key>
		<integer>161</integer>
		<key>u1D55A.dtls.ssty1</key>
		<integer>163</integer>
		<key>u1D55A.dtls.ssty2</key>
		<integer>163</integer>
		<key>u1D55A.ssty1</key>
		<integer>162</integer>
		<key>u1D55A.ssty2</key>
		<integer>163</integer>
		<key>u1D55B</key>
		<integer>194</integer>
		<key>u1D55B.dtls</key>
		<integer>176</integer>
		<key>u1D55B.dtls.ssty1</key>
		<integer>186</integer>
		<key>u1D55B.dtls.ssty2</key>
		<integer>195</integer>
		<key>u1D55B.ssty1</key>
		<integer>172</integer>
		<key>u1D55B.ssty2</key>
		<integer>180</integer>
		<key>u1D55C</key>
		<integer>339</integer>
		<key>u1D55C.ssty1</key>
		<integer>163</integer>
		<key>u1D55C.ssty2</key>
		<integer>163</integer>
		<key>u1D55D</key>
		<integer>161</integer>
		<key>u1D55D.ssty1</key>
		<integer>163</integer>
		<key>u1D55D.ssty2</key>
		<integer>163</integer>
		<key>u1D55E</key>
		<integer>476</integer>
		<key>u1D55E.ssty1</key>
		<integer>486</integer>
		<key>u1D55E.ssty2</key>
		<integer>494</integer>
		<key>u1D55F</key>
		<integer>306</integer>
		<key>u1D55F.ssty1</key>
		<integer>315</integer>
		<key>u1D55F.ssty2</key>
		<integer>321</integer>
		<key>u1D560</key>
		<integer>290</integer>
		<key>u1D560.ssty1</key>
		<integer>298</integer>
		<key>u1D560.ssty2</key>
		<integer>305</integer>
		<key>u1D561</key>
		<integer>339</integer>
		<key>u1D561.ssty1</key>
		<integer>442</integer>
		<key>u1D561.ssty2</key>
		<integer>454</integer>
		<key>u1D562</key>
		<integer>345</integer>
		<key>u1D562.ssty1</key>
		<integer>329</integer>
		<key>u1D562.ssty2</key>
		<integer>333</integer>
		<key>u1D563</key>
		<integer>260</integer>
		<key>u1D563.ssty1</key>
		<integer>298</integer>
		<key>u1D563.ssty2</key>
		<integer>303</integer>
		<key>u1D564</key>
		<integer>254</integer>
		<key>u1D564.ssty1</key>
		<integer>215</integer>
		<key>u1D564.ssty2</key>
		<integer>224</integer>
		<key>u1D565</key>
		<integer>197</integer>
		<key>u1D565.ssty1</key>
		<integer>174</integer>
		<key>u1D565.ssty2</key>
		<integer>182</integer>
		<key>u1D566</key>
		<integer>312</integer>
		<key>u1D566.ssty1</key>
		<integer>328</integer>
		<key>u1D566.ssty2</key>
		<integer>319</integer>
		<key>u1D567</key>
		<integer>376</integer>
		<key>u1D567.ssty1</key>
		<integer>291</integer>
		<key>u1D567.ssty2</key>
		<integer>309</integer>
		<key>u1D568</key>
		<integer>518</integer>
		<key>u1D568.ssty1</key>
		<integer>436</integer>
		<key>u1D568.ssty2</key>
		<integer>455</integer>
		<key>u1D569</key>
		<integer>342</integer>
		<key>u1D569.ssty1</key>
		<integer>262</integer>
		<key>u1D569.ssty2</key>
		<integer>282</integer>
		<key>u1D56A</key>
		<integer>378</integer>
		<key>u1D56A.ssty1</key>
		<integer>339</integer>
		<key>u1D56A.ssty2</key>
		<integer>362</integer>
		<key>u1D56B</key>
		<integer>264</integer>
		<key>u1D56B.ssty1</key>
		<integer>312</integer>
		<key>u1D56B.ssty2</key>
		<integer>319</integer>
		<key>u1D56C</key>
		<integer>353</integer>
		<key>u1D56D</key>
		<integer>423</integer>
		<key>u1D56E</key>
		<integer>360</integer>
		<key>u1D56F</key>
		<integer>362</integer>
		<key>u1D570</key>
		<integer>389</integer>
		<key>u1D571</key>
		<integer>365</integer>
		<key>u1D572</key>
		<integer>374</integer>
		<key>u1D573</key>
		<integer>431</integer>
		<key>u1D574</key>
		<integer>302</integer>
		<key>u1D575</key>
		<integer>335</integer>
		<key>u1D576</key>
		<integer>456</integer>
		<key>u1D577</key>
		<integer>320</integer>
		<key>u1D578</key>
		<integer>556</integer>
		<key>u1D579</key>
		<integer>460</integer>
		<key>u1D57A</key>
		<integer>425</integer>
		<key>u1D57B</key>
		<integer>381</integer>
		<key>u1D57C</key>
		<integer>414</integer>
		<key>u1D57D</key>
		<integer>428</integer>
		<key>u1D57E</key>
		<integer>459</integer>
		<key>u1D57F</key>
		<integer>332</integer>
		<key>u1D580</key>
		<integer>325</integer>
		<key>u1D581</key>
		<integer>394</integer>
		<key>u1D582</key>
		<integer>504</integer>
		<key>u1D583</key>
		<integer>390</integer>
		<key>u1D584</key>
		<integer>390</integer>
		<key>u1D585</key>
		<integer>329</integer>
		<key>u1D586</key>
		<integer>273</integer>
		<key>u1D587</key>
		<integer>206</integer>
		<key>u1D588</key>
		<integer>218</integer>
		<key>u1D589</key>
		<integer>224</integer>
		<key>u1D58A</key>
		<integer>230</integer>
		<key>u1D58B</key>
		<integer>241</integer>
		<key>u1D58C</key>
		<integer>252</integer>
		<key>u1D58D</key>
		<integer>188</integer>
		<key>u1D58E</key>
		<integer>168</integer>
		<key>u1D58E.001</key>
		<integer>168</integer>
		<key>u1D58F</key>
		<integer>159</integer>
		<key>u1D58F.001</key>
		<integer>159</integer>
		<key>u1D590</key>
		<integer>215</integer>
		<key>u1D591</key>
		<integer>159</integer>
		<key>u1D592</key>
		<integer>391</integer>
		<key>u1D593</key>
		<integer>267</integer>
		<key>u1D594</key>
		<integer>274</integer>
		<key>u1D595</key>
		<integer>309</integer>
		<key>u1D596</key>
		<integer>229</integer>
		<key>u1D597</key>
		<integer>216</integer>
		<key>u1D598</key>
		<integer>269</integer>
		<key>u1D599</key>
		<integer>175</integer>
		<key>u1D59A</key>
		<integer>265</integer>
		<key>u1D59B</key>
		<integer>252</integer>
		<key>u1D59C</key>
		<integer>368</integer>
		<key>u1D59D</key>
		<integer>254</integer>
		<key>u1D59E</key>
		<integer>207</integer>
		<key>u1D59F</key>
		<integer>170</integer>
		<key>u1D5D4</key>
		<integer>345</integer>
		<key>u1D5D5</key>
		<integer>341</integer>
		<key>u1D5D6</key>
		<integer>377</integer>
		<key>u1D5D7</key>
		<integer>370</integer>
		<key>u1D5D8</key>
		<integer>301</integer>
		<key>u1D5D9</key>
		<integer>298</integer>
		<key>u1D5DA</key>
		<integer>400</integer>
		<key>u1D5DB</key>
		<integer>383</integer>
		<key>u1D5DC</key>
		<integer>195</integer>
		<key>u1D5DD</key>
		<integer>260</integer>
		<key>u1D5DE</key>
		<integer>350</integer>
		<key>u1D5DF</key>
		<integer>170</integer>
		<key>u1D5E0</key>
		<integer>471</integer>
		<key>u1D5E1</key>
		<integer>407</integer>
		<key>u1D5E2</key>
		<integer>398</integer>
		<key>u1D5E3</key>
		<integer>325</integer>
		<key>u1D5E4</key>
		<integer>398</integer>
		<key>u1D5E5</key>
		<integer>338</integer>
		<key>u1D5E6</key>
		<integer>284</integer>
		<key>u1D5E7</key>
		<integer>290</integer>
		<key>u1D5E8</key>
		<integer>378</integer>
		<key>u1D5E9</key>
		<integer>326</integer>
		<key>u1D5EA</key>
		<integer>484</integer>
		<key>u1D5EB</key>
		<integer>334</integer>
		<key>u1D5EC</key>
		<integer>312</integer>
		<key>u1D5ED</key>
		<integer>295</integer>
		<key>u1D5EE</key>
		<integer>302</integer>
		<key>u1D5EF</key>
		<integer>151</integer>
		<key>u1D5F0</key>
		<integer>287</integer>
		<key>u1D5F1</key>
		<integer>478</integer>
		<key>u1D5F2</key>
		<integer>296</integer>
		<key>u1D5F3</key>
		<integer>269</integer>
		<key>u1D5F4</key>
		<integer>314</integer>
		<key>u1D5F5</key>
		<integer>151</integer>
		<key>u1D5F6</key>
		<integer>153</integer>
		<key>u1D5F6.dtls</key>
		<integer>153</integer>
		<key>u1D5F7</key>
		<integer>237</integer>
		<key>u1D5F7.dtls</key>
		<integer>237</integer>
		<key>u1D5F8</key>
		<integer>151</integer>
		<key>u1D5F9</key>
		<integer>153</integer>
		<key>u1D5FA</key>
		<integer>501</integer>
		<key>u1D5FB</key>
		<integer>329</integer>
		<key>u1D5FC</key>
		<integer>310</integer>
		<key>u1D5FD</key>
		<integer>340</integer>
		<key>u1D5FE</key>
		<integer>317</integer>
		<key>u1D5FF</key>
		<integer>254</integer>
		<key>u1D600</key>
		<integer>249</integer>
		<key>u1D601</key>
		<integer>198</integer>
		<key>u1D602</key>
		<integer>329</integer>
		<key>u1D603</key>
		<integer>281</integer>
		<key>u1D604</key>
		<integer>428</integer>
		<key>u1D605</key>
		<integer>288</integer>
		<key>u1D606</key>
		<integer>285</integer>
		<key>u1D607</key>
		<integer>244</integer>
		<key>u1D608</key>
		<integer>433</integer>
		<key>u1D608.ssty1</key>
		<integer>490</integer>
		<key>u1D608.ssty2</key>
		<integer>498</integer>
		<key>u1D609</key>
		<integer>396</integer>
		<key>u1D609.ssty1</key>
		<integer>426</integer>
		<key>u1D609.ssty2</key>
		<integer>409</integer>
		<key>u1D60A</key>
		<integer>444</integer>
		<key>u1D60A.ssty1</key>
		<integer>461</integer>
		<key>u1D60A.ssty2</key>
		<integer>462</integer>
		<key>u1D60B</key>
		<integer>429</integer>
		<key>u1D60B.ssty1</key>
		<integer>458</integer>
		<key>u1D60B.ssty2</key>
		<integer>435</integer>
		<key>u1D60C</key>
		<integer>370</integer>
		<key>u1D60C.ssty1</key>
		<integer>400</integer>
		<key>u1D60C.ssty2</key>
		<integer>389</integer>
		<key>u1D60D</key>
		<integer>404</integer>
		<key>u1D60D.ssty1</key>
		<integer>438</integer>
		<key>u1D60D.ssty2</key>
		<integer>448</integer>
		<key>u1D60E</key>
		<integer>464</integer>
		<key>u1D60E.ssty1</key>
		<integer>476</integer>
		<key>u1D60E.ssty2</key>
		<integer>472</integer>
		<key>u1D60F</key>
		<integer>453</integer>
		<key>u1D60F.ssty1</key>
		<integer>482</integer>
		<key>u1D60F.ssty2</key>
		<integer>466</integer>
		<key>u1D610</key>
		<integer>297</integer>
		<key>u1D610.ssty1</key>
		<integer>355</integer>
		<key>u1D610.ssty2</key>
		<integer>377</integer>
		<key>u1D611</key>
		<integer>443</integer>
		<key>u1D611.ssty1</key>
		<integer>490</integer>
		<key>u1D611.ssty2</key>
		<integer>499</integer>
		<key>u1D612</key>
		<integer>402</integer>
		<key>u1D612.ssty1</key>
		<integer>448</integer>
		<key>u1D612.ssty2</key>
		<integer>437</integer>
		<key>u1D613</key>
		<integer>246</integer>
		<key>u1D613.ssty1</key>
		<integer>280</integer>
		<key>u1D613.ssty2</key>
		<integer>258</integer>
		<key>u1D614</key>
		<integer>502</integer>
		<key>u1D614.ssty1</key>
		<integer>534</integer>
		<key>u1D614.ssty2</key>
		<integer>524</integer>
		<key>u1D615</key>
		<integer>454</integer>
		<key>u1D615.ssty1</key>
		<integer>491</integer>
		<key>u1D615.ssty2</key>
		<integer>484</integer>
		<key>u1D616</key>
		<integer>446</integer>
		<key>u1D616.ssty1</key>
		<integer>456</integer>
		<key>u1D616.ssty2</key>
		<integer>452</integer>
		<key>u1D617</key>
		<integer>373</integer>
		<key>u1D617.ssty1</key>
		<integer>412</integer>
		<key>u1D617.ssty2</key>
		<integer>400</integer>
		<key>u1D618</key>
		<integer>446</integer>
		<key>u1D618.ssty1</key>
		<integer>456</integer>
		<key>u1D618.ssty2</key>
		<integer>452</integer>
		<key>u1D619</key>
		<integer>382</integer>
		<key>u1D619.ssty1</key>
		<integer>407</integer>
		<key>u1D619.ssty2</key>
		<integer>405</integer>
		<key>u1D61A</key>
		<integer>338</integer>
		<key>u1D61A.ssty1</key>
		<integer>353</integer>
		<key>u1D61A.ssty2</key>
		<integer>354</integer>
		<key>u1D61B</key>
		<integer>376</integer>
		<key>u1D61B.ssty1</key>
		<integer>387</integer>
		<key>u1D61B.ssty2</key>
		<integer>398</integer>
		<key>u1D61C</key>
		<integer>434</integer>
		<key>u1D61C.ssty1</key>
		<integer>444</integer>
		<key>u1D61C.ssty2</key>
		<integer>423</integer>
		<key>u1D61D</key>
		<integer>359</integer>
		<key>u1D61D.ssty1</key>
		<integer>376</integer>
		<key>u1D61D.ssty2</key>
		<integer>382</integer>
		<key>u1D61E</key>
		<integer>523</integer>
		<key>u1D61E.ssty1</key>
		<integer>549</integer>
		<key>u1D61E.ssty2</key>
		<integer>545</integer>
		<key>u1D61F</key>
		<integer>414</integer>
		<key>u1D61F.ssty1</key>
		<integer>446</integer>
		<key>u1D61F.ssty2</key>
		<integer>467</integer>
		<key>u1D620</key>
		<integer>350</integer>
		<key>u1D620.ssty1</key>
		<integer>378</integer>
		<key>u1D620.ssty2</key>
		<integer>386</integer>
		<key>u1D621</key>
		<integer>371</integer>
		<key>u1D621.ssty1</key>
		<integer>400</integer>
		<key>u1D621.ssty2</key>
		<integer>406</integer>
		<key>u1D622</key>
		<integer>364</integer>
		<key>u1D622.ssty1</key>
		<integer>381</integer>
		<key>u1D622.ssty2</key>
		<integer>411</integer>
		<key>u1D623</key>
		<integer>242</integer>
		<key>u1D623.ssty1</key>
		<integer>272</integer>
		<key>u1D623.ssty2</key>
		<integer>300</integer>
		<key>u1D624</key>
		<integer>306</integer>
		<key>u1D624.ssty1</key>
		<integer>336</integer>
		<key>u1D624.ssty2</key>
		<integer>369</integer>
		<key>u1D625</key>
		<integer>555</integer>
		<key>u1D625.ssty1</key>
		<integer>572</integer>
		<key>u1D625.ssty2</key>
		<integer>612</integer>
		<key>u1D626</key>
		<integer>314</integer>
		<key>u1D626.ssty1</key>
		<integer>363</integer>
		<key>u1D626.ssty2</key>
		<integer>413</integer>
		<key>u1D627</key>
		<integer>481</integer>
		<key>u1D627.ssty1</key>
		<integer>514</integer>
		<key>u1D627.ssty2</key>
		<integer>541</integer>
		<key>u1D628</key>
		<integer>337</integer>
		<key>u1D628.ssty1</key>
		<integer>365</integer>
		<key>u1D628.ssty2</key>
		<integer>396</integer>
		<key>u1D629</key>
		<integer>242</integer>
		<key>u1D629.ssty1</key>
		<integer>272</integer>
		<key>u1D629.ssty2</key>
		<integer>300</integer>
		<key>u1D62A</key>
		<integer>229</integer>
		<key>u1D62A.ssty1</key>
		<integer>275</integer>
		<key>u1D62A.ssty2</key>
		<integer>310</integer>
		<key>u1D62B</key>
		<integer>375</integer>
		<key>u1D62B.ssty1</key>
		<integer>418</integer>
		<key>u1D62B.ssty2</key>
		<integer>446</integer>
		<key>u1D62C</key>
		<integer>242</integer>
		<key>u1D62C.ssty1</key>
		<integer>272</integer>
		<key>u1D62C.ssty2</key>
		<integer>300</integer>
		<key>u1D62D</key>
		<integer>231</integer>
		<key>u1D62D.ssty1</key>
		<integer>272</integer>
		<key>u1D62D.ssty2</key>
		<integer>300</integer>
		<key>u1D62E</key>
		<integer>506</integer>
		<key>u1D62E.ssty1</key>
		<integer>531</integer>
		<key>u1D62E.ssty2</key>
		<integer>564</integer>
		<key>u1D62F</key>
		<integer>349</integer>
		<key>u1D62F.ssty1</key>
		<integer>382</integer>
		<key>u1D62F.ssty2</key>
		<integer>401</integer>
		<key>u1D630</key>
		<integer>322</integer>
		<key>u1D630.ssty1</key>
		<integer>355</integer>
		<key>u1D630.ssty2</key>
		<integer>403</integer>
		<key>u1D631</key>
		<integer>389</integer>
		<key>u1D631.ssty1</key>
		<integer>421</integer>
		<key>u1D631.ssty2</key>
		<integer>460</integer>
		<key>u1D632</key>
		<integer>347</integer>
		<key>u1D632.ssty1</key>
		<integer>379</integer>
		<key>u1D632.ssty2</key>
		<integer>421</integer>
		<key>u1D633</key>
		<integer>288</integer>
		<key>u1D633.ssty1</key>
		<integer>315</integer>
		<key>u1D633.ssty2</key>
		<integer>340</integer>
		<key>u1D634</key>
		<integer>262</integer>
		<key>u1D634.ssty1</key>
		<integer>283</integer>
		<key>u1D634.ssty2</key>
		<integer>312</integer>
		<key>u1D635</key>
		<integer>249</integer>
		<key>u1D635.ssty1</key>
		<integer>296</integer>
		<key>u1D635.ssty2</key>
		<integer>334</integer>
		<key>u1D636</key>
		<integer>347</integer>
		<key>u1D636.ssty1</key>
		<integer>383</integer>
		<key>u1D636.ssty2</key>
		<integer>429</integer>
		<key>u1D637</key>
		<integer>272</integer>
		<key>u1D637.ssty1</key>
		<integer>305</integer>
		<key>u1D637.ssty2</key>
		<integer>335</integer>
		<key>u1D638</key>
		<integer>409</integer>
		<key>u1D638.ssty1</key>
		<integer>438</integer>
		<key>u1D638.ssty2</key>
		<integer>462</integer>
		<key>u1D639</key>
		<integer>336</integer>
		<key>u1D639.ssty1</key>
		<integer>357</integer>
		<key>u1D639.ssty2</key>
		<integer>386</integer>
		<key>u1D63A</key>
		<integer>386</integer>
		<key>u1D63A.ssty1</key>
		<integer>406</integer>
		<key>u1D63A.ssty2</key>
		<integer>422</integer>
		<key>u1D63B</key>
		<integer>295</integer>
		<key>u1D63B.ssty1</key>
		<integer>315</integer>
		<key>u1D63B.ssty2</key>
		<integer>342</integer>
		<key>u1D63C</key>
		<integer>468</integer>
		<key>u1D63D</key>
		<integer>402</integer>
		<key>u1D63E</key>
		<integer>436</integer>
		<key>u1D63F</key>
		<integer>429</integer>
		<key>u1D640</key>
		<integer>368</integer>
		<key>u1D641</key>
		<integer>360</integer>
		<key>u1D642</key>
		<integer>454</integer>
		<key>u1D643</key>
		<integer>440</integer>
		<key>u1D644</key>
		<integer>308</integer>
		<key>u1D645</key>
		<integer>439</integer>
		<key>u1D646</key>
		<integer>422</integer>
		<key>u1D647</key>
		<integer>261</integer>
		<key>u1D648</key>
		<integer>517</integer>
		<key>u1D649</key>
		<integer>477</integer>
		<key>u1D64A</key>
		<integer>459</integer>
		<key>u1D64B</key>
		<integer>385</integer>
		<key>u1D64C</key>
		<integer>459</integer>
		<key>u1D64D</key>
		<integer>399</integer>
		<key>u1D64E</key>
		<integer>352</integer>
		<key>u1D64F</key>
		<integer>360</integer>
		<key>u1D650</key>
		<integer>436</integer>
		<key>u1D651</key>
		<integer>383</integer>
		<key>u1D652</key>
		<integer>541</integer>
		<key>u1D653</key>
		<integer>438</integer>
		<key>u1D654</key>
		<integer>366</integer>
		<key>u1D655</key>
		<integer>393</integer>
		<key>u1D656</key>
		<integer>371</integer>
		<key>u1D657</key>
		<integer>264</integer>
		<key>u1D658</key>
		<integer>328</integer>
		<key>u1D659</key>
		<integer>544</integer>
		<key>u1D65A</key>
		<integer>343</integer>
		<key>u1D65B</key>
		<integer>491</integer>
		<key>u1D65C</key>
		<integer>352</integer>
		<key>u1D65D</key>
		<integer>264</integer>
		<key>u1D65E</key>
		<integer>262</integer>
		<key>u1D65E.dtls</key>
		<integer>242</integer>
		<key>u1D65F</key>
		<integer>411</integer>
		<key>u1D65F.dtls</key>
		<integer>391</integer>
		<key>u1D660</key>
		<integer>264</integer>
		<key>u1D661</key>
		<integer>264</integer>
		<key>u1D662</key>
		<integer>528</integer>
		<key>u1D663</key>
		<integer>362</integer>
		<key>u1D664</key>
		<integer>343</integer>
		<key>u1D665</key>
		<integer>409</integer>
		<key>u1D666</key>
		<integer>355</integer>
		<key>u1D667</key>
		<integer>293</integer>
		<key>u1D668</key>
		<integer>282</integer>
		<key>u1D669</key>
		<integer>288</integer>
		<key>u1D66A</key>
		<integer>360</integer>
		<key>u1D66B</key>
		<integer>299</integer>
		<key>u1D66C</key>
		<integer>442</integer>
		<key>u1D66D</key>
		<integer>366</integer>
		<key>u1D66E</key>
		<integer>388</integer>
		<key>u1D66F</key>
		<integer>309</integer>
		<key>u1D670</key>
		<integer>340</integer>
		<key>u1D671</key>
		<integer>340</integer>
		<key>u1D672</key>
		<integer>363</integer>
		<key>u1D673</key>
		<integer>317</integer>
		<key>u1D674</key>
		<integer>366</integer>
		<key>u1D675</key>
		<integer>377</integer>
		<key>u1D676</key>
		<integer>362</integer>
		<key>u1D677</key>
		<integer>340</integer>
		<key>u1D678</key>
		<integer>341</integer>
		<key>u1D679</key>
		<integer>460</integer>
		<key>u1D67A</key>
		<integer>340</integer>
		<key>u1D67B</key>
		<integer>201</integer>
		<key>u1D67C</key>
		<integer>340</integer>
		<key>u1D67D</key>
		<integer>345</integer>
		<key>u1D67E</key>
		<integer>340</integer>
		<key>u1D67F</key>
		<integer>350</integer>
		<key>u1D680</key>
		<integer>340</integer>
		<key>u1D681</key>
		<integer>339</integer>
		<key>u1D682</key>
		<integer>343</integer>
		<key>u1D683</key>
		<integer>340</integer>
		<key>u1D684</key>
		<integer>340</integer>
		<key>u1D685</key>
		<integer>341</integer>
		<key>u1D686</key>
		<integer>340</integer>
		<key>u1D687</key>
		<integer>340</integer>
		<key>u1D688</key>
		<integer>340</integer>
		<key>u1D689</key>
		<integer>352</integer>
		<key>u1D68A</key>
		<integer>345</integer>
		<key>u1D68B</key>
		<integer>160</integer>
		<key>u1D68C</key>
		<integer>388</integer>
		<key>u1D68D</key>
		<integer>514</integer>
		<key>u1D68E</key>
		<integer>344</integer>
		<key>u1D68F</key>
		<integer>436</integer>
		<key>u1D690</key>
		<integer>340</integer>
		<key>u1D691</key>
		<integer>160</integer>
		<key>u1D692</key>
		<integer>347</integer>
		<key>u1D692.dtls</key>
		<integer>310</integer>
		<key>u1D693</key>
		<integer>380</integer>
		<key>u1D693.dtls</key>
		<integer>347</integer>
		<key>u1D694</key>
		<integer>190</integer>
		<key>u1D695</key>
		<integer>340</integer>
		<key>u1D696</key>
		<integer>366</integer>
		<key>u1D697</key>
		<integer>352</integer>
		<key>u1D698</key>
		<integer>340</integer>
		<key>u1D699</key>
		<integer>350</integer>
		<key>u1D69A</key>
		<integer>320</integer>
		<key>u1D69B</key>
		<integer>350</integer>
		<key>u1D69C</key>
		<integer>345</integer>
		<key>u1D69D</key>
		<integer>295</integer>
		<key>u1D69E</key>
		<integer>340</integer>
		<key>u1D69F</key>
		<integer>340</integer>
		<key>u1D6A0</key>
		<integer>341</integer>
		<key>u1D6A1</key>
		<integer>340</integer>
		<key>u1D6A2</key>
		<integer>340</integer>
		<key>u1D6A3</key>
		<integer>350</integer>
		<key>u1D6A4</key>
		<integer>219</integer>
		<key>u1D6A4.ssty1</key>
		<integer>247</integer>
		<key>u1D6A4.ssty2</key>
		<integer>292</integer>
		<key>u1D6A5</key>
		<integer>365</integer>
		<key>u1D6A5.ssty1</key>
		<integer>400</integer>
		<key>u1D6A5.ssty2</key>
		<integer>410</integer>
		<key>u1D6E2</key>
		<integer>433</integer>
		<key>u1D6E2.ssty1</key>
		<integer>490</integer>
		<key>u1D6E2.ssty2</key>
		<integer>498</integer>
		<key>u1D6E3</key>
		<integer>396</integer>
		<key>u1D6E3.ssty1</key>
		<integer>426</integer>
		<key>u1D6E3.ssty2</key>
		<integer>409</integer>
		<key>u1D6E4</key>
		<integer>387</integer>
		<key>u1D6E4.ssty1</key>
		<integer>419</integer>
		<key>u1D6E4.ssty2</key>
		<integer>429</integer>
		<key>u1D6E5</key>
		<integer>444</integer>
		<key>u1D6E5.ssty1</key>
		<integer>494</integer>
		<key>u1D6E5.ssty2</key>
		<integer>496</integer>
		<key>u1D6E6</key>
		<integer>370</integer>
		<key>u1D6E6.ssty1</key>
		<integer>400</integer>
		<key>u1D6E6.ssty2</key>
		<integer>389</integer>
		<key>u1D6E7</key>
		<integer>371</integer>
		<key>u1D6E7.ssty1</key>
		<integer>400</integer>
		<key>u1D6E7.ssty2</key>
		<integer>406</integer>
		<key>u1D6E8</key>
		<integer>453</integer>
		<key>u1D6E8.ssty1</key>
		<integer>482</integer>
		<key>u1D6E8.ssty2</key>
		<integer>466</integer>
		<key>u1D6E9</key>
		<integer>446</integer>
		<key>u1D6E9.ssty1</key>
		<integer>456</integer>
		<key>u1D6E9.ssty2</key>
		<integer>452</integer>
		<key>u1D6EA</key>
		<integer>297</integer>
		<key>u1D6EA.ssty1</key>
		<integer>355</integer>
		<key>u1D6EA.ssty2</key>
		<integer>377</integer>
		<key>u1D6EB</key>
		<integer>402</integer>
		<key>u1D6EB.ssty1</key>
		<integer>448</integer>
		<key>u1D6EB.ssty2</key>
		<integer>437</integer>
		<key>u1D6EC</key>
		<integer>436</integer>
		<key>u1D6EC.ssty1</key>
		<integer>477</integer>
		<key>u1D6EC.ssty2</key>
		<integer>482</integer>
		<key>u1D6ED</key>
		<integer>502</integer>
		<key>u1D6ED.ssty1</key>
		<integer>534</integer>
		<key>u1D6ED.ssty2</key>
		<integer>524</integer>
		<key>u1D6EE</key>
		<integer>454</integer>
		<key>u1D6EE.ssty1</key>
		<integer>491</integer>
		<key>u1D6EE.ssty2</key>
		<integer>484</integer>
		<key>u1D6EF</key>
		<integer>384</integer>
		<key>u1D6EF.ssty1</key>
		<integer>400</integer>
		<key>u1D6EF.ssty2</key>
		<integer>409</integer>
		<key>u1D6F0</key>
		<integer>446</integer>
		<key>u1D6F0.ssty1</key>
		<integer>456</integer>
		<key>u1D6F0.ssty2</key>
		<integer>452</integer>
		<key>u1D6F1</key>
		<integer>444</integer>
		<key>u1D6F1.ssty1</key>
		<integer>468</integer>
		<key>u1D6F1.ssty2</key>
		<integer>452</integer>
		<key>u1D6F2</key>
		<integer>373</integer>
		<key>u1D6F2.ssty1</key>
		<integer>412</integer>
		<key>u1D6F2.ssty2</key>
		<integer>400</integer>
		<key>u1D6F3</key>
		<integer>454</integer>
		<key>u1D6F4</key>
		<integer>392</integer>
		<key>u1D6F4.ssty1</key>
		<integer>423</integer>
		<key>u1D6F4.ssty2</key>
		<integer>429</integer>
		<key>u1D6F5</key>
		<integer>376</integer>
		<key>u1D6F5.ssty1</key>
		<integer>387</integer>
		<key>u1D6F5.ssty2</key>
		<integer>398</integer>
		<key>u1D6F6</key>
		<integer>350</integer>
		<key>u1D6F6.ssty1</key>
		<integer>378</integer>
		<key>u1D6F6.ssty2</key>
		<integer>386</integer>
		<key>u1D6F7</key>
		<integer>541</integer>
		<key>u1D6F7.ssty1</key>
		<integer>561</integer>
		<key>u1D6F7.ssty2</key>
		<integer>567</integer>
		<key>u1D6F8</key>
		<integer>414</integer>
		<key>u1D6F8.ssty1</key>
		<integer>446</integer>
		<key>u1D6F8.ssty2</key>
		<integer>467</integer>
		<key>u1D6F9</key>
		<integer>545</integer>
		<key>u1D6F9.ssty1</key>
		<integer>559</integer>
		<key>u1D6F9.ssty2</key>
		<integer>585</integer>
		<key>u1D6FA</key>
		<integer>457</integer>
		<key>u1D6FA.ssty1</key>
		<integer>485</integer>
		<key>u1D6FA.ssty2</key>
		<integer>508</integer>
		<key>u1D6FB</key>
		<integer>343</integer>
		<key>u1D6FB.ssty1</key>
		<integer>382</integer>
		<key>u1D6FB.ssty2</key>
		<integer>383</integer>
		<key>u1D6FC</key>
		<integer>304</integer>
		<key>u1D6FC.ssty1</key>
		<integer>329</integer>
		<key>u1D6FC.ssty2</key>
		<integer>360</integer>
		<key>u1D6FD</key>
		<integer>444</integer>
		<key>u1D6FD.ssty1</key>
		<integer>473</integer>
		<key>u1D6FD.ssty2</key>
		<integer>496</integer>
		<key>u1D6FE</key>
		<integer>282</integer>
		<key>u1D6FE.ssty1</key>
		<integer>309</integer>
		<key>u1D6FE.ssty2</key>
		<integer>338</integer>
		<key>u1D6FF</key>
		<integer>422</integer>
		<key>u1D6FF.ssty1</key>
		<integer>463</integer>
		<key>u1D6FF.ssty2</key>
		<integer>499</integer>
		<key>u1D700</key>
		<integer>282</integer>
		<key>u1D700.ssty1</key>
		<integer>310</integer>
		<key>u1D700.ssty2</key>
		<integer>333</integer>
		<key>u1D701</key>
		<integer>347</integer>
		<key>u1D701.ssty1</key>
		<integer>379</integer>
		<key>u1D701.ssty2</key>
		<integer>405</integer>
		<key>u1D702</key>
		<integer>313</integer>
		<key>u1D702.ssty1</key>
		<integer>342</integer>
		<key>u1D702.ssty2</key>
		<integer>359</integer>
		<key>u1D703</key>
		<integer>407</integer>
		<key>u1D703.ssty1</key>
		<integer>446</integer>
		<key>u1D703.ssty2</key>
		<integer>487</integer>
		<key>u1D704</key>
		<integer>188</integer>
		<key>u1D704.ssty1</key>
		<integer>238</integer>
		<key>u1D704.ssty2</key>
		<integer>293</integer>
		<key>u1D705</key>
		<integer>321</integer>
		<key>u1D705.ssty1</key>
		<integer>366</integer>
		<key>u1D705.ssty2</key>
		<integer>384</integer>
		<key>u1D706</key>
		<integer>257</integer>
		<key>u1D706.ssty1</key>
		<integer>290</integer>
		<key>u1D706.ssty2</key>
		<integer>330</integer>
		<key>u1D707</key>
		<integer>419</integer>
		<key>u1D707.ssty1</key>
		<integer>455</integer>
		<key>u1D707.ssty2</key>
		<integer>491</integer>
		<key>u1D708</key>
		<integer>262</integer>
		<key>u1D708.ssty1</key>
		<integer>290</integer>
		<key>u1D708.ssty2</key>
		<integer>319</integer>
		<key>u1D709</key>
		<integer>365</integer>
		<key>u1D709.ssty1</key>
		<integer>399</integer>
		<key>u1D709.ssty2</key>
		<integer>432</integer>
		<key>u1D70A</key>
		<integer>322</integer>
		<key>u1D70A.ssty1</key>
		<integer>355</integer>
		<key>u1D70A.ssty2</key>
		<integer>403</integer>
		<key>u1D70B</key>
		<integer>357</integer>
		<key>u1D70B.ssty1</key>
		<integer>408</integer>
		<key>u1D70B.ssty2</key>
		<integer>443</integer>
		<key>u1D70C</key>
		<integer>366</integer>
		<key>u1D70C.ssty1</key>
		<integer>402</integer>
		<key>u1D70C.ssty2</key>
		<integer>444</integer>
		<key>u1D70D</key>
		<integer>326</integer>
		<key>u1D70D.ssty1</key>
		<integer>360</integer>
		<key>u1D70D.ssty2</key>
		<integer>399</integer>
		<key>u1D70E</key>
		<integer>351</integer>
		<key>u1D70E.ssty1</key>
		<integer>382</integer>
		<key>u1D70E.ssty2</key>
		<integer>413</integer>
		<key>u1D70F</key>
		<integer>274</integer>
		<key>u1D70F.ssty1</key>
		<integer>311</integer>
		<key>u1D70F.ssty2</key>
		<integer>343</integer>
		<key>u1D710</key>
		<integer>322</integer>
		<key>u1D710.ssty1</key>
		<integer>339</integer>
		<key>u1D710.ssty2</key>
		<integer>365</integer>
		<key>u1D711</key>
		<integer>417</integer>
		<key>u1D711.ssty1</key>
		<integer>479</integer>
		<key>u1D711.ssty2</key>
		<integer>538</integer>
		<key>u1D712</key>
		<integer>411</integer>
		<key>u1D712.ssty1</key>
		<integer>450</integer>
		<key>u1D712.ssty2</key>
		<integer>473</integer>
		<key>u1D713</key>
		<integer>474</integer>
		<key>u1D713.ssty1</key>
		<integer>528</integer>
		<key>u1D713.ssty2</key>
		<integer>574</integer>
		<key>u1D714</key>
		<integer>457</integer>
		<key>u1D714.ssty1</key>
		<integer>486</integer>
		<key>u1D714.ssty2</key>
		<integer>520</integer>
		<key>u1D715</key>
		<integer>371</integer>
		<key>u1D715.rtlm</key>
		<integer>251</integer>
		<key>u1D715.ssty1</key>
		<integer>413</integer>
		<key>u1D715.ssty2</key>
		<integer>482</integer>
		<key>u1D716</key>
		<integer>344</integer>
		<key>u1D716.ssty1</key>
		<integer>378</integer>
		<key>u1D716.ssty2</key>
		<integer>415</integer>
		<key>u1D717</key>
		<integer>385</integer>
		<key>u1D717.ssty1</key>
		<integer>427</integer>
		<key>u1D717.ssty2</key>
		<integer>455</integer>
		<key>u1D718</key>
		<integer>400</integer>
		<key>u1D718.ssty1</key>
		<integer>450</integer>
		<key>u1D718.ssty2</key>
		<integer>496</integer>
		<key>u1D719</key>
		<integer>460</integer>
		<key>u1D719.ssty1</key>
		<integer>533</integer>
		<key>u1D719.ssty2</key>
		<integer>605</integer>
		<key>u1D71A</key>
		<integer>402</integer>
		<key>u1D71A.ssty1</key>
		<integer>452</integer>
		<key>u1D71B</key>
		<integer>464</integer>
		<key>u1D71B.ssty1</key>
		<integer>531</integer>
		<key>u1D71B.ssty2</key>
		<integer>575</integer>
		<key>u1D756</key>
		<integer>342</integer>
		<key>u1D757</key>
		<integer>333</integer>
		<key>u1D758</key>
		<integer>345</integer>
		<key>u1D759</key>
		<integer>348</integer>
		<key>u1D75A</key>
		<integer>338</integer>
		<key>u1D75B</key>
		<integer>312</integer>
		<key>u1D75C</key>
		<integer>371</integer>
		<key>u1D75D</key>
		<integer>394</integer>
		<key>u1D75E</key>
		<integer>153</integer>
		<key>u1D75F</key>
		<integer>370</integer>
		<key>u1D760</key>
		<integer>316</integer>
		<key>u1D761</key>
		<integer>435</integer>
		<key>u1D762</key>
		<integer>336</integer>
		<key>u1D763</key>
		<integer>305</integer>
		<key>u1D764</key>
		<integer>394</integer>
		<key>u1D765</key>
		<integer>361</integer>
		<key>u1D766</key>
		<integer>325</integer>
		<key>u1D767</key>
		<integer>394</integer>
		<key>u1D768</key>
		<integer>295</integer>
		<key>u1D769</key>
		<integer>296</integer>
		<key>u1D76A</key>
		<integer>332</integer>
		<key>u1D76B</key>
		<integer>371</integer>
		<key>u1D76C</key>
		<integer>338</integer>
		<key>u1D76D</key>
		<integer>405</integer>
		<key>u1D76E</key>
		<integer>384</integer>
		<key>u1D76F</key>
		<integer>348</integer>
		<key>u1D770</key>
		<integer>324</integer>
		<key>u1D771</key>
		<integer>329</integer>
		<key>u1D772</key>
		<integer>265</integer>
		<key>u1D773</key>
		<integer>296</integer>
		<key>u1D774</key>
		<integer>272</integer>
		<key>u1D775</key>
		<integer>234</integer>
		<key>u1D776</key>
		<integer>309</integer>
		<key>u1D777</key>
		<integer>297</integer>
		<key>u1D778</key>
		<integer>136</integer>
		<key>u1D779</key>
		<integer>294</integer>
		<key>u1D77A</key>
		<integer>231</integer>
		<key>u1D77B</key>
		<integer>298</integer>
		<key>u1D77C</key>
		<integer>263</integer>
		<key>u1D77D</key>
		<integer>271</integer>
		<key>u1D77E</key>
		<integer>304</integer>
		<key>u1D77F</key>
		<integer>333</integer>
		<key>u1D780</key>
		<integer>321</integer>
		<key>u1D781</key>
		<integer>297</integer>
		<key>u1D782</key>
		<integer>296</integer>
		<key>u1D783</key>
		<integer>220</integer>
		<key>u1D784</key>
		<integer>290</integer>
		<key>u1D785</key>
		<integer>364</integer>
		<key>u1D786</key>
		<integer>287</integer>
		<key>u1D787</key>
		<integer>374</integer>
		<key>u1D788</key>
		<integer>384</integer>
		<key>u1D789</key>
		<integer>250</integer>
		<key>u1D789.rtlm</key>
		<integer>314</integer>
		<key>u1D78A</key>
		<integer>281</integer>
		<key>u1D78B</key>
		<integer>307</integer>
		<key>u1D78C</key>
		<integer>331</integer>
		<key>u1D78D</key>
		<integer>373</integer>
		<key>u1D78E</key>
		<integer>316</integer>
		<key>u1D78F</key>
		<integer>383</integer>
		<key>u1D790</key>
		<integer>409</integer>
		<key>u1D791</key>
		<integer>392</integer>
		<key>u1D792</key>
		<integer>535</integer>
		<key>u1D793</key>
		<integer>401</integer>
		<key>u1D794</key>
		<integer>381</integer>
		<key>u1D795</key>
		<integer>409</integer>
		<key>u1D796</key>
		<integer>436</integer>
		<key>u1D797</key>
		<integer>437</integer>
		<key>u1D798</key>
		<integer>229</integer>
		<key>u1D799</key>
		<integer>407</integer>
		<key>u1D79A</key>
		<integer>358</integer>
		<key>u1D79B</key>
		<integer>509</integer>
		<key>u1D79C</key>
		<integer>407</integer>
		<key>u1D79D</key>
		<integer>371</integer>
		<key>u1D79E</key>
		<integer>436</integer>
		<key>u1D79F</key>
		<integer>437</integer>
		<key>u1D7A0</key>
		<integer>490</integer>
		<key>u1D7A1</key>
		<integer>437</integer>
		<key>u1D7A2</key>
		<integer>375</integer>
		<key>u1D7A3</key>
		<integer>399</integer>
		<key>u1D7A4</key>
		<integer>362</integer>
		<key>u1D7A5</key>
		<integer>408</integer>
		<key>u1D7A6</key>
		<integer>387</integer>
		<key>u1D7A7</key>
		<integer>522</integer>
		<key>u1D7A8</key>
		<integer>410</integer>
		<key>u1D7A9</key>
		<integer>382</integer>
		<key>u1D7AA</key>
		<integer>341</integer>
		<key>u1D7AB</key>
		<integer>404</integer>
		<key>u1D7AC</key>
		<integer>346</integer>
		<key>u1D7AD</key>
		<integer>316</integer>
		<key>u1D7AE</key>
		<integer>279</integer>
		<key>u1D7AF</key>
		<integer>259</integer>
		<key>u1D7B0</key>
		<integer>251</integer>
		<key>u1D7B1</key>
		<integer>316</integer>
		<key>u1D7B2</key>
		<integer>174</integer>
		<key>u1D7B3</key>
		<integer>342</integer>
		<key>u1D7B4</key>
		<integer>271</integer>
		<key>u1D7B5</key>
		<integer>489</integer>
		<key>u1D7B6</key>
		<integer>320</integer>
		<key>u1D7B7</key>
		<integer>248</integer>
		<key>u1D7B8</key>
		<integer>324</integer>
		<key>u1D7B9</key>
		<integer>390</integer>
		<key>u1D7BA</key>
		<integer>478</integer>
		<key>u1D7BB</key>
		<integer>222</integer>
		<key>u1D7BC</key>
		<integer>426</integer>
		<key>u1D7BD</key>
		<integer>297</integer>
		<key>u1D7BE</key>
		<integer>345</integer>
		<key>u1D7BF</key>
		<integer>388</integer>
		<key>u1D7C0</key>
		<integer>396</integer>
		<key>u1D7C1</key>
		<integer>428</integer>
		<key>u1D7C2</key>
		<integer>392</integer>
		<key>u1D7C3</key>
		<integer>339</integer>
		<key>u1D7C3.rtlm</key>
		<integer>239</integer>
		<key>u1D7C4</key>
		<integer>296</integer>
		<key>u1D7C5</key>
		<integer>327</integer>
		<key>u1D7C6</key>
		<integer>365</integer>
		<key>u1D7C7</key>
		<integer>390</integer>
		<key>u1D7C8</key>
		<integer>360</integer>
		<key>u1D7C9</key>
		<integer>426</integer>
		<key>u1D7CA</key>
		<integer>296</integer>
		<key>u1D7CB</key>
		<integer>304</integer>
		<key>u1EE05</key>
		<integer>232</integer>
		<key>u1EE0F</key>
		<integer>252</integer>
		<key>u1EE1B</key>
		<integer>212</integer>
		<key>u1EE4F</key>
		<integer>252</integer>
		<key>u1EE5B</key>
		<integer>212</integer>
		<key>u1EE85</key>
		<integer>367</integer>
		<key>u1EE8F</key>
		<integer>252</integer>
		<key>u1EE9B</key>
		<integer>212</integer>
		<key>uni0237</key>
		<integer>204</integer>
		<key>uni0237.ssty1</key>
		<integer>235</integer>
		<key>uni0237.ssty2</key>
		<integer>277</integer>
		<key>uni0302</key>
		<integer>167</integer>
		<key>uni0302.s00</key>
		<integer>169</integer>
		<key>uni0302.s01</key>
		<integer>381</integer>
		<key>uni0302.s02</key>
		<integer>593</integer>
		<key>uni0302.s03</key>
		<integer>875</integer>
		<key>uni0302.s04</key>
		<integer>1158</integer>
		<key>uni0302.s05</key>
		<integer>1300</integer>
		<key>uni0304</key>
		<integer>189</integer>
		<key>uni0305</key>
		<integer>209</integer>
		<key>uni0305.s00</key>
		<integer>209</integer>
		<key>uni0306</key>
		<integer>195</integer>
		<key>uni0307</key>
		<integer>51</integer>
		<key>uni0308</key>
		<integer>140</integer>
		<key>uni030A</key>
		<integer>149</integer>
		<key>uni030B</key>
		<integer>220</integer>
		<key>uni030C</key>
		<integer>167</integer>
		<key>uni030C.s00</key>
		<integer>169</integer>
		<key>uni030C.s01</key>
		<integer>381</integer>
		<key>uni030C.s02</key>
		<integer>593</integer>
		<key>uni030C.s03</key>
		<integer>875</integer>
		<key>uni030C.s04</key>
		<integer>1158</integer>
		<key>uni030C.s05</key>
		<integer>1300</integer>
		<key>uni0311</key>
		<integer>180</integer>
		<key>uni0312</key>
		<integer>0</integer>
		<key>uni0315</key>
		<integer>0</integer>
		<key>uni0326</key>
		<integer>116</integer>
		<key>uni0327</key>
		<integer>112</integer>
		<key>uni032C</key>
		<integer>192</integer>
		<key>uni032D</key>
		<integer>194</integer>
		<key>uni032E</key>
		<integer>180</integer>
		<key>uni032F</key>
		<integer>181</integer>
		<key>uni0330</key>
		<integer>184</integer>
		<key>uni0330.s00</key>
		<integer>183</integer>
		<key>uni0330.s01</key>
		<integer>406</integer>
		<key>uni0330.s02</key>
		<integer>630</integer>
		<key>uni0330.s03</key>
		<integer>853</integer>
		<key>uni0330.s04</key>
		<integer>1076</integer>
		<key>uni0330.s05</key>
		<integer>1300</integer>
		<key>uni0331</key>
		<integer>174</integer>
		<key>uni0338</key>
		<integer>214</integer>
		<key>uni033A</key>
		<integer>203</integer>
		<key>uni033F</key>
		<integer>209</integer>
		<key>uni033F.s00</key>
		<integer>209</integer>
		<key>uni0346</key>
		<integer>243</integer>
		<key>uni034D</key>
		<integer>289</integer>
		<key>uni0394</key>
		<integer>291</integer>
		<key>uni0394.ssty1</key>
		<integer>341</integer>
		<key>uni0394.ssty2</key>
		<integer>387</integer>
		<key>uni03A9</key>
		<integer>391</integer>
		<key>uni03A9.ssty1</key>
		<integer>394</integer>
		<key>uni03A9.ssty2</key>
		<integer>445</integer>
		<key>uni03BC</key>
		<integer>312</integer>
		<key>uni03BC.ssty1</key>
		<integer>363</integer>
		<key>uni03BC.ssty2</key>
		<integer>398</integer>
		<key>uni03C2</key>
		<integer>242</integer>
		<key>uni03C2.ssty1</key>
		<integer>268</integer>
		<key>uni03C2.ssty2</key>
		<integer>316</integer>
		<key>uni03D1</key>
		<integer>316</integer>
		<key>uni03D5</key>
		<integer>363</integer>
		<key>uni03D6</key>
		<integer>422</integer>
		<key>uni03DC</key>
		<integer>301</integer>
		<key>uni03DD</key>
		<integer>256</integer>
		<key>uni03F0</key>
		<integer>330</integer>
		<key>uni03F1</key>
		<integer>303</integer>
		<key>uni03F4</key>
		<integer>391</integer>
		<key>uni03F5</key>
		<integer>248</integer>
		<key>uni1D46</key>
		<integer>326</integer>
		<key>uni1D46.ssty1</key>
		<integer>327</integer>
		<key>uni1D46.ssty2</key>
		<integer>338</integer>
		<key>uni20D0</key>
		<integer>224</integer>
		<key>uni20D0.s00</key>
		<integer>224</integer>
		<key>uni20D0.s01</key>
		<integer>394</integer>
		<key>uni20D0.s02</key>
		<integer>564</integer>
		<key>uni20D0.s03</key>
		<integer>734</integer>
		<key>uni20D0.s04</key>
		<integer>904</integer>
		<key>uni20D0.s05</key>
		<integer>1074</integer>
		<key>uni20D1</key>
		<integer>224</integer>
		<key>uni20D1.s00</key>
		<integer>224</integer>
		<key>uni20D1.s01</key>
		<integer>394</integer>
		<key>uni20D1.s02</key>
		<integer>564</integer>
		<key>uni20D1.s03</key>
		<integer>734</integer>
		<key>uni20D1.s04</key>
		<integer>904</integer>
		<key>uni20D1.s05</key>
		<integer>1074</integer>
		<key>uni20D4</key>
		<integer>304</integer>
		<key>uni20D5</key>
		<integer>304</integer>
		<key>uni20D6</key>
		<integer>233</integer>
		<key>uni20D6.s00</key>
		<integer>224</integer>
		<key>uni20D6.s01</key>
		<integer>394</integer>
		<key>uni20D6.s02</key>
		<integer>564</integer>
		<key>uni20D6.s03</key>
		<integer>734</integer>
		<key>uni20D6.s04</key>
		<integer>904</integer>
		<key>uni20D6.s05</key>
		<integer>1074</integer>
		<key>uni20D7</key>
		<integer>229</integer>
		<key>uni20D7.s00</key>
		<integer>224</integer>
		<key>uni20D7.s01</key>
		<integer>394</integer>
		<key>uni20D7.s02</key>
		<integer>564</integer>
		<key>uni20D7.s03</key>
		<integer>734</integer>
		<key>uni20D7.s04</key>
		<integer>904</integer>
		<key>uni20D7.s05</key>
		<integer>1074</integer>
		<key>uni20DB</key>
		<integer>242</integer>
		<key>uni20DC</key>
		<integer>338</integer>
		<key>uni20E1</key>
		<integer>298</integer>
		<key>uni20E7</key>
		<integer>-264</integer>
		<key>uni20E8</key>
		<integer>-1</integer>
		<key>uni20E9</key>
		<integer>3</integer>
		<key>uni20EA</key>
		<integer>0</integer>
		<key>uni20EC</key>
		<integer>137</integer>
		<key>uni20EC.s00</key>
		<integer>224</integer>
		<key>uni20EC.s01</key>
		<integer>394</integer>
		<key>uni20EC.s02</key>
		<integer>564</integer>
		<key>uni20EC.s03</key>
		<integer>734</integer>
		<key>uni20EC.s04</key>
		<integer>904</integer>
		<key>uni20EC.s05</key>
		<integer>1074</integer>
		<key>uni20ED</key>
		<integer>237</integer>
		<key>uni20ED.s00</key>
		<integer>224</integer>
		<key>uni20ED.s01</key>
		<integer>394</integer>
		<key>uni20ED.s02</key>
		<integer>564</integer>
		<key>uni20ED.s03</key>
		<integer>734</integer>
		<key>uni20ED.s04</key>
		<integer>904</integer>
		<key>uni20ED.s05</key>
		<integer>1074</integer>
		<key>uni20EE</key>
		<integer>242</integer>
		<key>uni20EE.s00</key>
		<integer>224</integer>
		<key>uni20EE.s01</key>
		<integer>394</integer>
		<key>uni20EE.s02</key>
		<integer>564</integer>
		<key>uni20EE.s03</key>
		<integer>734</integer>
		<key>uni20EE.s04</key>
		<integer>904</integer>
		<key>uni20EE.s05</key>
		<integer>1074</integer>
		<key>uni20EF</key>
		<integer>122</integer>
		<key>uni20EF.s00</key>
		<integer>224</integer>
		<key>uni20EF.s01</key>
		<integer>394</integer>
		<key>uni20EF.s02</key>
		<integer>564</integer>
		<key>uni20EF.s03</key>
		<integer>734</integer>
		<key>uni20EF.s04</key>
		<integer>904</integer>
		<key>uni20EF.s05</key>
		<integer>1074</integer>
		<key>uni20F0</key>
		<integer>0</integer>
		<key>uni2102</key>
		<integer>393</integer>
		<key>uni2102.ssty1</key>
		<integer>425</integer>
		<key>uni2102.ssty2</key>
		<integer>407</integer>
		<key>uni210A</key>
		<integer>363</integer>
		<key>uni210A.ssty1</key>
		<integer>375</integer>
		<key>uni210A.ssty2</key>
		<integer>386</integer>
		<key>uni210B</key>
		<integer>848</integer>
		<key>uni210B.ssty1</key>
		<integer>853</integer>
		<key>uni210B.ssty2</key>
		<integer>857</integer>
		<key>uni210B.uv001</key>
		<integer>664</integer>
		<key>uni210C</key>
		<integer>379</integer>
		<key>uni210C.ssty1</key>
		<integer>410</integer>
		<key>uni210C.ssty2</key>
		<integer>453</integer>
		<key>uni210D</key>
		<integer>388</integer>
		<key>uni210D.ssty1</key>
		<integer>400</integer>
		<key>uni210D.ssty2</key>
		<integer>422</integer>
		<key>uni210F</key>
		<integer>242</integer>
		<key>uni210F.ssty1</key>
		<integer>272</integer>
		<key>uni210F.ssty2</key>
		<integer>300</integer>
		<key>uni2110</key>
		<integer>647</integer>
		<key>uni2110.ssty1</key>
		<integer>654</integer>
		<key>uni2110.ssty2</key>
		<integer>660</integer>
		<key>uni2110.uv001</key>
		<integer>462</integer>
		<key>uni2111</key>
		<integer>281</integer>
		<key>uni2111.ssty1</key>
		<integer>297</integer>
		<key>uni2111.ssty2</key>
		<integer>327</integer>
		<key>uni2112</key>
		<integer>678</integer>
		<key>uni2112.ssty1</key>
		<integer>682</integer>
		<key>uni2112.ssty2</key>
		<integer>686</integer>
		<key>uni2112.uv001</key>
		<integer>354</integer>
		<key>uni2115</key>
		<integer>389</integer>
		<key>uni2115.ssty1</key>
		<integer>289</integer>
		<key>uni2115.ssty2</key>
		<integer>339</integer>
		<key>uni2119</key>
		<integer>313</integer>
		<key>uni2119.ssty1</key>
		<integer>467</integer>
		<key>uni2119.ssty2</key>
		<integer>520</integer>
		<key>uni211A</key>
		<integer>393</integer>
		<key>uni211A.ssty1</key>
		<integer>404</integer>
		<key>uni211A.ssty2</key>
		<integer>441</integer>
		<key>uni211B</key>
		<integer>631</integer>
		<key>uni211B.ssty1</key>
		<integer>636</integer>
		<key>uni211B.ssty2</key>
		<integer>641</integer>
		<key>uni211B.uv001</key>
		<integer>445</integer>
		<key>uni211C</key>
		<integer>365</integer>
		<key>uni211C.ssty1</key>
		<integer>400</integer>
		<key>uni211C.ssty2</key>
		<integer>445</integer>
		<key>uni211D</key>
		<integer>327</integer>
		<key>uni211D.ssty1</key>
		<integer>378</integer>
		<key>uni211D.ssty2</key>
		<integer>428</integer>
		<key>uni2124</key>
		<integer>338</integer>
		<key>uni2124.ssty1</key>
		<integer>413</integer>
		<key>uni2124.ssty2</key>
		<integer>453</integer>
		<key>uni2128</key>
		<integer>297</integer>
		<key>uni2128.ssty1</key>
		<integer>324</integer>
		<key>uni2128.ssty2</key>
		<integer>366</integer>
		<key>uni212C</key>
		<integer>592</integer>
		<key>uni212C.ssty1</key>
		<integer>597</integer>
		<key>uni212C.ssty2</key>
		<integer>602</integer>
		<key>uni212C.uv001</key>
		<integer>363</integer>
		<key>uni212D</key>
		<integer>313</integer>
		<key>uni212D.ssty1</key>
		<integer>344</integer>
		<key>uni212D.ssty2</key>
		<integer>387</integer>
		<key>uni212F</key>
		<integer>317</integer>
		<key>uni212F.ssty1</key>
		<integer>298</integer>
		<key>uni212F.ssty2</key>
		<integer>281</integer>
		<key>uni2130</key>
		<integer>500</integer>
		<key>uni2130.ssty1</key>
		<integer>506</integer>
		<key>uni2130.ssty2</key>
		<integer>512</integer>
		<key>uni2130.uv001</key>
		<integer>410</integer>
		<key>uni2131</key>
		<integer>689</integer>
		<key>uni2131.ssty1</key>
		<integer>695</integer>
		<key>uni2131.ssty2</key>
		<integer>701</integer>
		<key>uni2131.uv001</key>
		<integer>482</integer>
		<key>uni2133</key>
		<integer>954</integer>
		<key>uni2133.ssty1</key>
		<integer>961</integer>
		<key>uni2133.ssty2</key>
		<integer>967</integer>
		<key>uni2133.uv001</key>
		<integer>747</integer>
		<key>uni2134</key>
		<integer>288</integer>
		<key>uni2134.ssty1</key>
		<integer>289</integer>
		<key>uni2134.ssty2</key>
		<integer>287</integer>
		<key>uni213C</key>
		<integer>444</integer>
		<key>uni213D</key>
		<integer>298</integer>
		<key>uni213E</key>
		<integer>325</integer>
		<key>uni213F</key>
		<integer>388</integer>
		<key>uni2145</key>
		<integer>449</integer>
		<key>uni2146</key>
		<integer>339</integer>
		<key>uni2147</key>
		<integer>363</integer>
		<key>uni2148</key>
		<integer>268</integer>
		<key>uni2148.dtls</key>
		<integer>220</integer>
		<key>uni2148.dtls.ssty1</key>
		<integer>265</integer>
		<key>uni2148.dtls.ssty2</key>
		<integer>264</integer>
		<key>uni2148.ssty1</key>
		<integer>315</integer>
		<key>uni2148.ssty2</key>
		<integer>315</integer>
		<key>uni2149</key>
		<integer>432</integer>
		<key>uni2149.dtls</key>
		<integer>397</integer>
		<key>uni2149.dtls.ssty1</key>
		<integer>416</integer>
		<key>uni2149.dtls.ssty2</key>
		<integer>410</integer>
		<key>uni2149.ssty1</key>
		<integer>456</integer>
		<key>uni2149.ssty2</key>
		<integer>456</integer>
		<key>uni2207</key>
		<integer>290</integer>
		<key>uni2207.ssty1</key>
		<integer>340</integer>
		<key>uni2207.ssty2</key>
		<integer>386</integer>
		<key>uni23B4</key>
		<integer>331</integer>
		<key>uni23B4.s01</key>
		<integer>665</integer>
		<key>uni23B4.s02</key>
		<integer>998</integer>
		<key>uni23B4.s03</key>
		<integer>1332</integer>
		<key>uni23B4.s04</key>
		<integer>1666</integer>
		<key>uni23B4.s05</key>
		<integer>2000</integer>
		<key>uni23B5</key>
		<integer>331</integer>
		<key>uni23B5.s01</key>
		<integer>665</integer>
		<key>uni23B5.s02</key>
		<integer>998</integer>
		<key>uni23B5.s03</key>
		<integer>1332</integer>
		<key>uni23B5.s04</key>
		<integer>1666</integer>
		<key>uni23B5.s05</key>
		<integer>2000</integer>
		<key>uni23DC</key>
		<integer>331</integer>
		<key>uni23DC.s01</key>
		<integer>665</integer>
		<key>uni23DC.s02</key>
		<integer>998</integer>
		<key>uni23DC.s03</key>
		<integer>1332</integer>
		<key>uni23DC.s04</key>
		<integer>1666</integer>
		<key>uni23DC.s05</key>
		<integer>2000</integer>
		<key>uni23DD</key>
		<integer>331</integer>
		<key>uni23DD.s01</key>
		<integer>665</integer>
		<key>uni23DD.s02</key>
		<integer>998</integer>
		<key>uni23DD.s03</key>
		<integer>1332</integer>
		<key>uni23DD.s04</key>
		<integer>1666</integer>
		<key>uni23DD.s05</key>
		<integer>2000</integer>
		<key>uni23DE</key>
		<integer>331</integer>
		<key>uni23DE.s01</key>
		<integer>665</integer>
		<key>uni23DE.s02</key>
		<integer>998</integer>
		<key>uni23DE.s03</key>
		<integer>1332</integer>
		<key>uni23DE.s04</key>
		<integer>1666</integer>
		<key>uni23DE.s05</key>
		<integer>2000</integer>
		<key>uni23DF</key>
		<integer>331</integer>
		<key>uni23DF.s01</key>
		<integer>665</integer>
		<key>uni23DF.s02</key>
		<integer>998</integer>
		<key>uni23DF.s03</key>
		<integer>1332</integer>
		<key>uni23DF.s04</key>
		<integer>1666</integer>
		<key>uni23DF.s05</key>
		<integer>2000</integer>
		<key>uni23E0</key>
		<integer>331</integer>
		<key>uni23E0.s01</key>
		<integer>665</integer>
		<key>uni23E0.s02</key>
		<integer>998</integer>
		<key>uni23E0.s03</key>
		<integer>1332</integer>
		<key>uni23E0.s04</key>
		<integer>1666</integer>
		<key>uni23E0.s05</key>
		<integer>2000</integer>
		<key>uni23E1</key>
		<integer>331</integer>
		<key>uni23E1.s01</key>
		<integer>665</integer>
		<key>uni23E1.s02</key>
		<integer>998</integer>
		<key>uni23E1.s03</key>
		<integer>1332</integer>
		<key>uni23E1.s04</key>
		<integer>1666</integer>
		<key>uni23E1.s05</key>
		<integer>2000</integer>
		<key>upsilon</key>
		<integer>308</integer>
		<key>upsilon.ssty1</key>
		<integer>311</integer>
		<key>upsilon.ssty2</key>
		<integer>352</integer>
		<key>v</key>
		<integer>252</integer>
		<key>v.ssty1</key>
		<integer>273</integer>
		<key>v.ssty2</key>
		<integer>292</integer>
		<key>w</key>
		<integer>393</integer>
		<key>w.ssty1</key>
		<integer>422</integer>
		<key>w.ssty2</key>
		<integer>453</integer>
		<key>x</key>
		<integer>263</integer>
		<key>x.ssty1</key>
		<integer>296</integer>
		<key>x.ssty2</key>
		<integer>335</integer>
		<key>xi</key>
		<integer>239</integer>
		<key>xi.ssty1</key>
		<integer>270</integer>
		<key>xi.ssty2</key>
		<integer>312</integer>
		<key>y</key>
		<integer>255</integer>
		<key>y.ssty1</key>
		<integer>276</integer>
		<key>y.ssty2</key>
		<integer>296</integer>
		<key>z</key>
		<integer>235</integer>
		<key>z.ssty1</key>
		<integer>264</integer>
		<key>z.ssty2</key>
		<integer>313</integer>
		<key>zeta</key>
		<integer>242</integer>
		<key>zeta.ssty1</key>
		<integer>282</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>556</integer>
		<key>AxisHeight</key>
		<integer>278</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>2300</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>2300</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>714</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>673</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>71</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>320</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>704</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>71</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>529</integer>
		<key>FractionRuleThickness</key>
		<integer>71</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>630</integer>
		<key>LowerLimitGapMin</key>
		<integer>100</integer>
		<key>MathLeading</key>
		<integer>150</integer>
		<key>MinConnectorOverlap</key>
		<integer>100</integer>
		<key>OverbarExtraAscender</key>
		<integer>71</integer>
		<key>OverbarRuleThickness</key>
		<integer>71</integer>
		<key>OverbarVerticalGap</key>
		<integer>150</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>56</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>190</integer>
		<key>RadicalExtraAscender</key>
		<integer>71</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-450</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>51</integer>
		<key>RadicalRuleThickness</key>
		<integer>71</integer>
		<key>RadicalVerticalGap</key>
		<integer>89</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>60</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>50</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>350</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>71</integer>
		<key>SpaceAfterScript</key>
		<integer>40</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>673</integer>
		<key>StackBottomShiftDown</key>
		<integer>320</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>336</integer>
		<key>StackGapMin</key>
		<integer>178</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>704</integer>
		<key>StackTopShiftUp</key>
		<integer>529</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>559</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>100</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>100</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>244</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>160</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>140</integer>
		<key>SubscriptShiftDown</key>
		<integer>210</integer>
		<key>SubscriptTopMax</key>
		<integer>430</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>240</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>430</integer>
		<key>SuperscriptBottomMin</key>
		<integer>134</integer>
		<key>SuperscriptShiftUp</key>
		<integer>390</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>280</integer>
		<key>UnderbarExtraDescender</key>
		<integer>71</integer>
		<key>UnderbarRuleThickness</key>
		<integer>71</integer>
		<key>UnderbarVerticalGap</key>
		<integer>150</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>244</integer>
		<key>UpperLimitGapMin</key>
		<integer>100</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>arrowleft</key>
		<array/>
		<key>arrowright</key>
		<array/>
		<key>tildecomb</key>
		<array>
			<string>tildecomb.s00</string>
			<string>tildecomb.s01</string>
			<string>tildecomb.s02</string>
			<string>tildecomb.s03</string>
			<string>tildecomb.s04</string>
			<string>tildecomb.s05</string>
		</array>
		<key>u1EEF1</key>
		<array>
			<string>u1EEF1</string>
			<string>u1EEF1.s01</string>
			<string>u1EEF1.s02</string>
			<string>u1EEF1.s03</string>
			<string>u1EEF1.s04</string>
			<string>u1EEF1.s05</string>
		</array>
		<key>uni0302</key>
		<array>
			<string>uni0302.s00</string>
			<string>uni0302.s01</string>
			<string>uni0302.s02</string>
			<string>uni0302.s03</string>
			<string>uni0302.s04</string>
			<string>uni0302.s05</string>
		</array>
		<key>uni0305</key>
		<array>
			<string>uni0305.s00</string>
		</array>
		<key>uni030C</key>
		<array>
			<string>uni030C</string>
			<string>uni030C.s01</string>
			<string>uni030C.s02</string>
			<string>uni030C.s03</string>
			<string>uni030C.s04</string>
			<string>uni030C.s05</string>
		</array>
		<key>uni0330</key>
		<array>
			<string>uni0330.s00</string>
			<string>uni0330.s01</string>
			<string>uni0330.s02</string>
			<string>uni0330.s03</string>
			<string>uni0330.s04</string>
			<string>uni0330.s05</string>
		</array>
		<key>uni0332</key>
		<array>
			<string>uni0332.s00</string>
		</array>
		<key>uni0333</key>
		<array>
			<string>uni0333.s00</string>
		</array>
		<key>uni033F</key>
		<array>
			<string>uni033F.s00</string>
		</array>
		<key>uni034D</key>
		<array>
			<string>uni034D</string>
		</array>
		<key>uni20D0</key>
		<array>
			<string>uni20D0.s00</string>
			<string>uni20D0.s01</string>
			<string>uni20D0.s02</string>
			<string>uni20D0.s03</string>
			<string>uni20D0.s04</string>
			<string>uni20D0.s05</string>
		</array>
		<key>uni20D1</key>
		<array>
			<string>uni20D1.s00</string>
			<string>uni20D1.s01</string>
			<string>uni20D1.s02</string>
			<string>uni20D1.s03</string>
			<string>uni20D1.s04</string>
			<string>uni20D1.s05</string>
		</array>
		<key>uni20D6</key>
		<array>
			<string>uni20D6.s00</string>
			<string>uni20D6.s01</string>
			<string>uni20D6.s02</string>
			<string>uni20D6.s03</string>
			<string>uni20D6.s04</string>
			<string>uni20D6.s05</string>
		</array>
		<key>uni20D7</key>
		<array>
			<string>uni20D7</string>
			<string>uni20D7.s01</string>
			<string>uni20D7.s02</string>
			<string>uni20D7.s03</string>
			<string>uni20D7.s04</string>
			<string>uni20D7.s05</string>
		</array>
		<key>uni20E1</key>
		<array>
			<string>uni20E1</string>
		</array>
		<key>uni20EC</key>
		<array>
			<string>uni20EC.s00</string>
			<string>uni20EC.s01</string>
			<string>uni20EC.s02</string>
			<string>uni20EC.s03</string>
			<string>uni20EC.s04</string>
			<string>uni20EC.s05</string>
		</array>
		<key>uni20ED</key>
		<array>
			<string>uni20ED.s00</string>
			<string>uni20ED.s01</string>
			<string>uni20ED.s02</string>
			<string>uni20ED.s03</string>
			<string>uni20ED.s04</string>
			<string>uni20ED.s05</string>
		</array>
		<key>uni20EE</key>
		<array>
			<string>uni20EE.s00</string>
			<string>uni20EE.s01</string>
			<string>uni20EE.s02</string>
			<string>uni20EE.s03</string>
			<string>uni20EE.s04</string>
			<string>uni20EE.s05</string>
		</array>
		<key>uni20EF</key>
		<array>
			<string>uni20EF.s00</string>
			<string>uni20EF.s01</string>
			<string>uni20EF.s02</string>
			<string>uni20EF.s03</string>
			<string>uni20EF.s04</string>
			<string>uni20EF.s05</string>
		</array>
		<key>uni23B4</key>
		<array>
			<string>uni23B4</string>
			<string>uni23B4.s01</string>
			<string>uni23B4.s02</string>
			<string>uni23B4.s03</string>
			<string>uni23B4.s04</string>
			<string>uni23B4.s05</string>
		</array>
		<key>uni23B5</key>
		<array>
			<string>uni23B5</string>
			<string>uni23B5.s01</string>
			<string>uni23B5.s02</string>
			<string>uni23B5.s03</string>
			<string>uni23B5.s04</string>
			<string>uni23B5.s05</string>
		</array>
		<key>uni23DC</key>
		<array>
			<string>uni23DC</string>
			<string>uni23DC.s01</string>
			<string>uni23DC.s02</string>
			<string>uni23DC.s03</string>
			<string>uni23DC.s04</string>
			<string>uni23DC.s05</string>
		</array>
		<key>uni23DD</key>
		<array>
			<string>uni23DD</string>
			<string>uni23DD.s01</string>
			<string>uni23DD.s02</string>
			<string>uni23DD.s03</string>
			<string>uni23DD.s04</string>
			<string>uni23DD.s05</string>
		</array>
		<key>uni23DE</key>
		<array>
			<string>uni23DE</string>
			<string>uni23DE.s01</string>
			<string>uni23DE.s02</string>
			<string>uni23DE.s03</string>
			<string>uni23DE.s04</string>
			<string>uni23DE.s05</string>
		</array>
		<key>uni23DF</key>
		<array>
			<string>uni23DF</string>
			<string>uni23DF.s01</string>
			<string>uni23DF.s02</string>
			<string>uni23DF.s03</string>
			<string>uni23DF.s04</string>
			<string>uni23DF.s05</string>
		</array>
		<key>uni23E0</key>
		<array>
			<string>uni23E0</string>
			<string>uni23E0.s01</string>
			<string>uni23E0.s02</string>
			<string>uni23E0.s03</string>
			<string>uni23E0.s04</string>
			<string>uni23E0.s05</string>
		</array>
		<key>uni23E1</key>
		<array>
			<string>uni23E1</string>
			<string>uni23E1.s01</string>
			<string>uni23E1.s02</string>
			<string>uni23E1.s03</string>
			<string>uni23E1.s04</string>
			<string>uni23E1.s05</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>f</key>
		<integer>63</integer>
		<key>f.ssty1</key>
		<integer>71</integer>
		<key>f.ssty2</key>
		<integer>87</integer>
		<key>u1D49C</key>
		<integer>40</integer>
		<key>u1D49C.ssty1</key>
		<integer>40</integer>
		<key>u1D49C.ssty2</key>
		<integer>40</integer>
		<key>u1D4A5</key>
		<integer>30</integer>
		<key>u1D4A5.ssty1</key>
		<integer>30</integer>
		<key>u1D4A5.ssty2</key>
		<integer>30</integer>
		<key>u1D4A5.uv001</key>
		<integer>45</integer>
		<key>u1D4A6.uv001</key>
		<integer>65</integer>
		<key>u1D4A9.uv001</key>
		<integer>95</integer>
		<key>u1D4AC.uv001</key>
		<integer>40</integer>
		<key>u1D4AF</key>
		<integer>30</integer>
		<key>u1D4AF.ssty1</key>
		<integer>30</integer>
		<key>u1D4AF.ssty2</key>
		<integer>30</integer>
		<key>u1D4AF.uv001</key>
		<integer>45</integer>
		<key>u1D4B1</key>
		<integer>35</integer>
		<key>u1D4B1.ssty1</key>
		<integer>35</integer>
		<key>u1D4B1.ssty2</key>
		<integer>35</integer>
		<key>u1D4B2</key>
		<integer>25</integer>
		<key>u1D4B2.ssty1</key>
		<integer>25</integer>
		<key>u1D4B2.ssty2</key>
		<integer>25</integer>
		<key>u1D4B3.uv001</key>
		<integer>25</integer>
		<key>u1D4B4</key>
		<integer>30</integer>
		<key>u1D4B4.ssty1</key>
		<integer>30</integer>
		<key>u1D4B4.ssty2</key>
		<integer>30</integer>
		<key>u1D4B9</key>
		<integer>51</integer>
		<key>u1D4B9.ssty1</key>
		<integer>60</integer>
		<key>u1D4B9.ssty2</key>
		<integer>68</integer>
		<key>u1D4BB</key>
		<integer>38</integer>
		<key>u1D4BB.ssty1</key>
		<integer>45</integer>
		<key>u1D4BB.ssty2</key>
		<integer>51</integer>
		<key>u1D4BF.ssty1</key>
		<integer>21</integer>
		<key>u1D4BF.ssty2</key>
		<integer>21</integer>
		<key>u1D4C1</key>
		<integer>30</integer>
		<key>u1D4C1.ssty1</key>
		<integer>30</integer>
		<key>u1D4C1.ssty2</key>
		<integer>30</integer>
		<key>u1D4C9</key>
		<integer>30</integer>
		<key>u1D4C9.ssty1</key>
		<integer>28</integer>
		<key>u1D4C9.ssty2</key>
		<integer>25</integer>
		<key>u1D4CD</key>
		<integer>22</integer>
		<key>u1D4D0</key>
		<integer>40</integer>
		<key>u1D4D5</key>
		<integer>30</integer>
		<key>u1D4D7</key>
		<integer>30</integer>
		<key>u1D4D8</key>
		<integer>40</integer>
		<key>u1D4D9</key>
		<integer>30</integer>
		<key>u1D4E3</key>
		<integer>30</integer>
		<key>u1D4E5</key>
		<integer>35</integer>
		<key>u1D4E6</key>
		<integer>25</integer>
		<key>u1D4E8</key>
		<integer>30</integer>
		<key>u1D4ED</key>
		<integer>68</integer>
		<key>u1D4EF</key>
		<integer>51</integer>
		<key>u1D4F3</key>
		<integer>21</integer>
		<key>u1D4F5</key>
		<integer>30</integer>
		<key>u1D4FD</key>
		<integer>25</integer>
		<key>u1D53D.ssty1</key>
		<integer>5</integer>
		<key>u1D54B.ssty1</key>
		<integer>3</integer>
		<key>u1D54D.ssty1</key>
		<integer>3</integer>
		<key>u1D550.ssty1</key>
		<integer>3</integer>
		<key>u1D557</key>
		<integer>49</integer>
		<key>u1D557.ssty1</key>
		<integer>49</integer>
		<key>u1D557.ssty2</key>
		<integer>49</integer>
		<key>u1D58B</key>
		<integer>-10</integer>
		<key>u1D5F3</key>
		<integer>65</integer>
		<key>u1D60A</key>
		<integer>65</integer>
		<key>u1D60A.ssty1</key>
		<integer>73</integer>
		<key>u1D60A.ssty2</key>
		<integer>60</integer>
		<key>u1D60C</key>
		<integer>60</integer>
		<key>u1D60C.ssty1</key>
		<integer>68</integer>
		<key>u1D60C.ssty2</key>
		<integer>54</integer>
		<key>u1D60D</key>
		<integer>97</integer>
		<key>u1D60D.ssty1</key>
		<integer>105</integer>
		<key>u1D60D.ssty2</key>
		<integer>64</integer>
		<key>u1D60F</key>
		<integer>24</integer>
		<key>u1D60F.ssty1</key>
		<integer>32</integer>
		<key>u1D60F.ssty2</key>
		<integer>25</integer>
		<key>u1D610</key>
		<integer>77</integer>
		<key>u1D610.ssty1</key>
		<integer>85</integer>
		<key>u1D610.ssty2</key>
		<integer>75</integer>
		<key>u1D611</key>
		<integer>29</integer>
		<key>u1D611.ssty1</key>
		<integer>37</integer>
		<key>u1D611.ssty2</key>
		<integer>26</integer>
		<key>u1D612</key>
		<integer>109</integer>
		<key>u1D612.ssty1</key>
		<integer>117</integer>
		<key>u1D612.ssty2</key>
		<integer>112</integer>
		<key>u1D614</key>
		<integer>23</integer>
		<key>u1D614.ssty1</key>
		<integer>31</integer>
		<key>u1D614.ssty2</key>
		<integer>25</integer>
		<key>u1D615</key>
		<integer>25</integer>
		<key>u1D615.ssty1</key>
		<integer>33</integer>
		<key>u1D615.ssty2</key>
		<integer>25</integer>
		<key>u1D617.ssty2</key>
		<integer>25</integer>
		<key>u1D61A</key>
		<integer>34</integer>
		<key>u1D61A.ssty1</key>
		<integer>42</integer>
		<key>u1D61A.ssty2</key>
		<integer>32</integer>
		<key>u1D61B</key>
		<integer>108</integer>
		<key>u1D61B.ssty1</key>
		<integer>116</integer>
		<key>u1D61B.ssty2</key>
		<integer>91</integer>
		<key>u1D61C</key>
		<integer>31</integer>
		<key>u1D61C.ssty1</key>
		<integer>39</integer>
		<key>u1D61C.ssty2</key>
		<integer>40</integer>
		<key>u1D61D</key>
		<integer>112</integer>
		<key>u1D61D.ssty1</key>
		<integer>120</integer>
		<key>u1D61D.ssty2</key>
		<integer>111</integer>
		<key>u1D61E</key>
		<integer>107</integer>
		<key>u1D61E.ssty1</key>
		<integer>115</integer>
		<key>u1D61E.ssty2</key>
		<integer>114</integer>
		<key>u1D61F</key>
		<integer>102</integer>
		<key>u1D61F.ssty1</key>
		<integer>110</integer>
		<key>u1D61F.ssty2</key>
		<integer>98</integer>
		<key>u1D620</key>
		<integer>112</integer>
		<key>u1D620.ssty1</key>
		<integer>120</integer>
		<key>u1D620.ssty2</key>
		<integer>112</integer>
		<key>u1D621</key>
		<integer>62</integer>
		<key>u1D621.ssty1</key>
		<integer>70</integer>
		<key>u1D621.ssty2</key>
		<integer>69</integer>
		<key>u1D624</key>
		<integer>28</integer>
		<key>u1D624.ssty1</key>
		<integer>36</integer>
		<key>u1D624.ssty2</key>
		<integer>62</integer>
		<key>u1D625</key>
		<integer>38</integer>
		<key>u1D625.ssty1</key>
		<integer>56</integer>
		<key>u1D625.ssty2</key>
		<integer>72</integer>
		<key>u1D627</key>
		<integer>145</integer>
		<key>u1D627.ssty1</key>
		<integer>163</integer>
		<key>u1D627.ssty2</key>
		<integer>179</integer>
		<key>u1D62A</key>
		<integer>32</integer>
		<key>u1D62A.ssty1</key>
		<integer>50</integer>
		<key>u1D62A.ssty2</key>
		<integer>66</integer>
		<key>u1D62B</key>
		<integer>31</integer>
		<key>u1D62B.ssty1</key>
		<integer>49</integer>
		<key>u1D62B.ssty2</key>
		<integer>65</integer>
		<key>u1D62C</key>
		<integer>50</integer>
		<key>u1D62C.ssty1</key>
		<integer>68</integer>
		<key>u1D62C.ssty2</key>
		<integer>84</integer>
		<key>u1D62D</key>
		<integer>39</integer>
		<key>u1D62D.ssty1</key>
		<integer>57</integer>
		<key>u1D62D.ssty2</key>
		<integer>73</integer>
		<key>u1D633</key>
		<integer>51</integer>
		<key>u1D633.ssty1</key>
		<integer>69</integer>
		<key>u1D633.ssty2</key>
		<integer>85</integer>
		<key>u1D635</key>
		<integer>52</integer>
		<key>u1D635.ssty1</key>
		<integer>70</integer>
		<key>u1D635.ssty2</key>
		<integer>96</integer>
		<key>u1D637</key>
		<integer>68</integer>
		<key>u1D637.ssty1</key>
		<integer>86</integer>
		<key>u1D637.ssty2</key>
		<integer>112</integer>
		<key>u1D638</key>
		<integer>58</integer>
		<key>u1D638.ssty1</key>
		<integer>76</integer>
		<key>u1D638.ssty2</key>
		<integer>92</integer>
		<key>u1D639</key>
		<integer>47</integer>
		<key>u1D639.ssty1</key>
		<integer>65</integer>
		<key>u1D639.ssty2</key>
		<integer>81</integer>
		<key>u1D63A</key>
		<integer>68</integer>
		<key>u1D63A.ssty1</key>
		<integer>86</integer>
		<key>u1D63A.ssty2</key>
		<integer>102</integer>
		<key>u1D63B.ssty1</key>
		<integer>22</integer>
		<key>u1D63B.ssty2</key>
		<integer>47</integer>
		<key>u1D63E</key>
		<integer>60</integer>
		<key>u1D640</key>
		<integer>54</integer>
		<key>u1D641</key>
		<integer>63</integer>
		<key>u1D643</key>
		<integer>25</integer>
		<key>u1D644</key>
		<integer>75</integer>
		<key>u1D645</key>
		<integer>26</integer>
		<key>u1D646</key>
		<integer>112</integer>
		<key>u1D648</key>
		<integer>25</integer>
		<key>u1D649</key>
		<integer>25</integer>
		<key>u1D64B</key>
		<integer>25</integer>
		<key>u1D64E</key>
		<integer>32</integer>
		<key>u1D64F</key>
		<integer>91</integer>
		<key>u1D650</key>
		<integer>29</integer>
		<key>u1D651</key>
		<integer>110</integer>
		<key>u1D652</key>
		<integer>114</integer>
		<key>u1D653</key>
		<integer>98</integer>
		<key>u1D654</key>
		<integer>112</integer>
		<key>u1D655</key>
		<integer>70</integer>
		<key>u1D658</key>
		<integer>30</integer>
		<key>u1D659</key>
		<integer>48</integer>
		<key>u1D65B</key>
		<integer>129</integer>
		<key>u1D65E</key>
		<integer>42</integer>
		<key>u1D65F</key>
		<integer>43</integer>
		<key>u1D660</key>
		<integer>69</integer>
		<key>u1D661</key>
		<integer>49</integer>
		<key>u1D667</key>
		<integer>57</integer>
		<key>u1D669</key>
		<integer>39</integer>
		<key>u1D66B</key>
		<integer>78</integer>
		<key>u1D66C</key>
		<integer>71</integer>
		<key>u1D66D</key>
		<integer>69</integer>
		<key>u1D66E</key>
		<integer>72</integer>
		<key>u1D66F</key>
		<integer>37</integer>
		<key>u1D6E4</key>
		<integer>97</integer>
		<key>u1D6E4.ssty1</key>
		<integer>105</integer>
		<key>u1D6E4.ssty2</key>
		<integer>64</integer>
		<key>u1D6E6</key>
		<integer>60</integer>
		<key>u1D6E6.ssty1</key>
		<integer>68</integer>
		<key>u1D6E6.ssty2</key>
		<integer>54</integer>
		<key>u1D6E7</key>
		<integer>62</integer>
		<key>u1D6E7.ssty1</key>
		<integer>70</integer>
		<key>u1D6E7.ssty2</key>
		<integer>69</integer>
		<key>u1D6E8</key>
		<integer>24</integer>
		<key>u1D6E8.ssty1</key>
		<integer>32</integer>
		<key>u1D6E8.ssty2</key>
		<integer>25</integer>
		<key>u1D6EA</key>
		<integer>77</integer>
		<key>u1D6EA.ssty1</key>
		<integer>85</integer>
		<key>u1D6EA.ssty2</key>
		<integer>75</integer>
		<key>u1D6EB</key>
		<integer>109</integer>
		<key>u1D6EB.ssty1</key>
		<integer>117</integer>
		<key>u1D6EB.ssty2</key>
		<integer>112</integer>
		<key>u1D6ED</key>
		<integer>23</integer>
		<key>u1D6ED.ssty1</key>
		<integer>31</integer>
		<key>u1D6ED.ssty2</key>
		<integer>25</integer>
		<key>u1D6EE</key>
		<integer>25</integer>
		<key>u1D6EE.ssty1</key>
		<integer>33</integer>
		<key>u1D6EE.ssty2</key>
		<integer>25</integer>
		<key>u1D6EF</key>
		<integer>41</integer>
		<key>u1D6EF.ssty1</key>
		<integer>49</integer>
		<key>u1D6EF.ssty2</key>
		<integer>36</integer>
		<key>u1D6F1</key>
		<integer>24</integer>
		<key>u1D6F1.ssty1</key>
		<integer>32</integer>
		<key>u1D6F1.ssty2</key>
		<integer>25</integer>
		<key>u1D6F2.ssty2</key>
		<integer>25</integer>
		<key>u1D6F4</key>
		<integer>63</integer>
		<key>u1D6F4.ssty1</key>
		<integer>71</integer>
		<key>u1D6F4.ssty2</key>
		<integer>70</integer>
		<key>u1D6F5</key>
		<integer>108</integer>
		<key>u1D6F5.ssty1</key>
		<integer>116</integer>
		<key>u1D6F5.ssty2</key>
		<integer>91</integer>
		<key>u1D6F6</key>
		<integer>112</integer>
		<key>u1D6F6.ssty1</key>
		<integer>120</integer>
		<key>u1D6F6.ssty2</key>
		<integer>112</integer>
		<key>u1D6F8</key>
		<integer>102</integer>
		<key>u1D6F8.ssty1</key>
		<integer>110</integer>
		<key>u1D6F8.ssty2</key>
		<integer>98</integer>
		<key>u1D6F9</key>
		<integer>31</integer>
		<key>u1D6F9.ssty1</key>
		<integer>39</integer>
		<key>u1D6F9.ssty2</key>
		<integer>30</integer>
		<key>u1D6FB</key>
		<integer>48</integer>
		<key>u1D6FB.ssty1</key>
		<integer>47</integer>
		<key>u1D6FB.ssty2</key>
		<integer>19</integer>
		<key>u1D6FD.ssty2</key>
		<integer>43</integer>
		<key>u1D6FE.ssty1</key>
		<integer>30</integer>
		<key>u1D6FE.ssty2</key>
		<integer>60</integer>
		<key>u1D6FF</key>
		<integer>31</integer>
		<key>u1D6FF.ssty1</key>
		<integer>49</integer>
		<key>u1D6FF.ssty2</key>
		<integer>65</integer>
		<key>u1D700</key>
		<integer>31</integer>
		<key>u1D700.ssty1</key>
		<integer>49</integer>
		<key>u1D700.ssty2</key>
		<integer>65</integer>
		<key>u1D701</key>
		<integer>132</integer>
		<key>u1D701.ssty1</key>
		<integer>150</integer>
		<key>u1D701.ssty2</key>
		<integer>166</integer>
		<key>u1D703.ssty2</key>
		<integer>40</integer>
		<key>u1D705</key>
		<integer>56</integer>
		<key>u1D705.ssty1</key>
		<integer>74</integer>
		<key>u1D705.ssty2</key>
		<integer>65</integer>
		<key>u1D708</key>
		<integer>70</integer>
		<key>u1D708.ssty1</key>
		<integer>88</integer>
		<key>u1D708.ssty2</key>
		<integer>94</integer>
		<key>u1D709</key>
		<integer>88</integer>
		<key>u1D709.ssty1</key>
		<integer>106</integer>
		<key>u1D709.ssty2</key>
		<integer>112</integer>
		<key>u1D70B</key>
		<integer>68</integer>
		<key>u1D70B.ssty1</key>
		<integer>86</integer>
		<key>u1D70B.ssty2</key>
		<integer>92</integer>
		<key>u1D70D.ssty1</key>
		<integer>46</integer>
		<key>u1D70E</key>
		<integer>59</integer>
		<key>u1D70E.ssty1</key>
		<integer>77</integer>
		<key>u1D70E.ssty2</key>
		<integer>83</integer>
		<key>u1D70F</key>
		<integer>69</integer>
		<key>u1D70F.ssty1</key>
		<integer>87</integer>
		<key>u1D70F.ssty2</key>
		<integer>93</integer>
		<key>u1D710.ssty1</key>
		<integer>20</integer>
		<key>u1D712.ssty1</key>
		<integer>30</integer>
		<key>u1D712.ssty2</key>
		<integer>60</integer>
		<key>u1D716</key>
		<integer>28</integer>
		<key>u1D716.ssty1</key>
		<integer>46</integer>
		<key>u1D716.ssty2</key>
		<integer>72</integer>
		<key>u1D717</key>
		<integer>43</integer>
		<key>u1D717.ssty1</key>
		<integer>61</integer>
		<key>u1D717.ssty2</key>
		<integer>67</integer>
		<key>u1D71B</key>
		<integer>68</integer>
		<key>u1D71B.ssty1</key>
		<integer>86</integer>
		<key>u1D71B.ssty2</key>
		<integer>112</integer>
		<key>u1D782</key>
		<integer>22</integer>
		<key>u1D792</key>
		<integer>88</integer>
		<key>u1D794</key>
		<integer>22</integer>
		<key>u1D795</key>
		<integer>29</integer>
		<key>u1D799</key>
		<integer>85</integer>
		<key>u1D79D</key>
		<integer>31</integer>
		<key>u1D7A2</key>
		<integer>37</integer>
		<key>u1D7A3</key>
		<integer>69</integer>
		<key>u1D7A4</key>
		<integer>70</integer>
		<key>u1D7A6</key>
		<integer>73</integer>
		<key>u1D7A9</key>
		<integer>20</integer>
		<key>u1D7AC</key>
		<integer>41</integer>
		<key>u1D7AF</key>
		<integer>32</integer>
		<key>u1D7B3</key>
		<integer>58</integer>
		<key>u1D7B6</key>
		<integer>48</integer>
		<key>u1D7B9</key>
		<integer>23</integer>
		<key>u1D7BC</key>
		<integer>40</integer>
		<key>u1D7BD</key>
		<integer>39</integer>
		<key>u1D7C0</key>
		<integer>46</integer>
		<key>u1D7C5</key>
		<integer>24</integer>
		<key>u1D7C6</key>
		<integer>41</integer>
		<key>u1D7C9</key>
		<integer>24</integer>
		<key>uni210B</key>
		<integer>30</integer>
		<key>uni210B.ssty1</key>
		<integer>30</integer>
		<key>uni210B.ssty2</key>
		<integer>30</integer>
		<key>uni210B.uv001</key>
		<integer>75</integer>
		<key>uni2110</key>
		<integer>40</integer>
		<key>uni2110.ssty1</key>
		<integer>40</integer>
		<key>uni2110.ssty2</key>
		<integer>40</integer>
		<key>uni2110.uv001</key>
		<integer>45</integer>
		<key>uni211B.uv001</key>
		<integer>45</integer>
		<key>uni2131</key>
		<integer>30</integer>
		<key>uni2131.ssty1</key>
		<integer>30</integer>
		<key>uni2131.ssty2</key>
		<integer>30</integer>
		<key>uni2133.uv001</key>
		<integer>80</integer>
		<key>uni2146</key>
		<integer>83</integer>
		<key>uni2147</key>
		<integer>21</integer>
		<key>uni222B</key>
		<integer>274</integer>
		<key>uni222B.rtlm</key>
		<integer>274</integer>
		<key>uni222B.rtlm.s01</key>
		<integer>500</integer>
		<key>uni222B.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni222B.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni222B.s01</key>
		<integer>500</integer>
		<key>uni222B.ss02</key>
		<integer>90</integer>
		<key>uni222B.ss02.s01</key>
		<integer>90</integer>
		<key>uni222C</key>
		<integer>234</integer>
		<key>uni222C.rtlm</key>
		<integer>234</integer>
		<key>uni222C.rtlm.s01</key>
		<integer>440</integer>
		<key>uni222C.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni222C.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni222C.s01</key>
		<integer>440</integer>
		<key>uni222C.ss02</key>
		<integer>90</integer>
		<key>uni222C.ss02.s01</key>
		<integer>90</integer>
		<key>uni222D</key>
		<integer>194</integer>
		<key>uni222D.rtlm</key>
		<integer>194</integer>
		<key>uni222D.rtlm.s01</key>
		<integer>380</integer>
		<key>uni222D.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni222D.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni222D.s01</key>
		<integer>380</integer>
		<key>uni222D.ss02</key>
		<integer>90</integer>
		<key>uni222D.ss02.s01</key>
		<integer>90</integer>
		<key>uni222E</key>
		<integer>274</integer>
		<key>uni222E.rtlm</key>
		<integer>274</integer>
		<key>uni222E.rtlm.s01</key>
		<integer>500</integer>
		<key>uni222E.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni222E.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni222E.s01</key>
		<integer>500</integer>
		<key>uni222E.ss02</key>
		<integer>90</integer>
		<key>uni222E.ss02.s01</key>
		<integer>90</integer>
		<key>uni222F</key>
		<integer>234</integer>
		<key>uni222F.rtlm</key>
		<integer>234</integer>
		<key>uni222F.rtlm.s01</key>
		<integer>440</integer>
		<key>uni222F.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni222F.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni222F.s01</key>
		<integer>440</integer>
		<key>uni222F.ss02</key>
		<integer>90</integer>
		<key>uni222F.ss02.s01</key>
		<integer>90</integer>
		<key>uni2230</key>
		<integer>194</integer>
		<key>uni2230.rtlm</key>
		<integer>194</integer>
		<key>uni2230.rtlm.s01</key>
		<integer>380</integer>
		<key>uni2230.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2230.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2230.s01</key>
		<integer>380</integer>
		<key>uni2230.ss02</key>
		<integer>90</integer>
		<key>uni2230.ss02.s01</key>
		<integer>90</integer>
		<key>uni2231</key>
		<integer>274</integer>
		<key>uni2231.rtlm</key>
		<integer>274</integer>
		<key>uni2231.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2231.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2231.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2231.s01</key>
		<integer>500</integer>
		<key>uni2231.ss02</key>
		<integer>90</integer>
		<key>uni2231.ss02.s01</key>
		<integer>90</integer>
		<key>uni2232</key>
		<integer>274</integer>
		<key>uni2232.rtlm</key>
		<integer>274</integer>
		<key>uni2232.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2232.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2232.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2232.s01</key>
		<integer>500</integer>
		<key>uni2232.ss02</key>
		<integer>90</integer>
		<key>uni2232.ss02.s01</key>
		<integer>90</integer>
		<key>uni2233</key>
		<integer>274</integer>
		<key>uni2233.rtlm</key>
		<integer>274</integer>
		<key>uni2233.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2233.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2233.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2233.s01</key>
		<integer>500</integer>
		<key>uni2233.ss02</key>
		<integer>90</integer>
		<key>uni2233.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0C</key>
		<integer>154</integer>
		<key>uni2A0C.rtlm</key>
		<integer>154</integer>
		<key>uni2A0C.rtlm.s01</key>
		<integer>320</integer>
		<key>uni2A0C.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A0C.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0C.s01</key>
		<integer>320</integer>
		<key>uni2A0C.ss02</key>
		<integer>90</integer>
		<key>uni2A0C.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0D</key>
		<integer>274</integer>
		<key>uni2A0D.rtlm</key>
		<integer>274</integer>
		<key>uni2A0D.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A0D.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A0D.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0D.s01</key>
		<integer>500</integer>
		<key>uni2A0D.ss02</key>
		<integer>90</integer>
		<key>uni2A0D.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0E</key>
		<integer>274</integer>
		<key>uni2A0E.rtlm</key>
		<integer>274</integer>
		<key>uni2A0E.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A0E.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A0E.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0E.s01</key>
		<integer>500</integer>
		<key>uni2A0E.ss02</key>
		<integer>90</integer>
		<key>uni2A0E.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0F</key>
		<integer>274</integer>
		<key>uni2A0F.rtlm</key>
		<integer>274</integer>
		<key>uni2A0F.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A0F.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A0F.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A0F.s01</key>
		<integer>500</integer>
		<key>uni2A0F.ss02</key>
		<integer>90</integer>
		<key>uni2A0F.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A10</key>
		<integer>274</integer>
		<key>uni2A10.rtlm</key>
		<integer>274</integer>
		<key>uni2A10.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A10.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A10.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A10.s01</key>
		<integer>500</integer>
		<key>uni2A10.ss02</key>
		<integer>90</integer>
		<key>uni2A10.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A11</key>
		<integer>274</integer>
		<key>uni2A11.rtlm</key>
		<integer>274</integer>
		<key>uni2A11.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A11.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A11.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A11.s01</key>
		<integer>500</integer>
		<key>uni2A11.ss02</key>
		<integer>90</integer>
		<key>uni2A11.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A12</key>
		<integer>274</integer>
		<key>uni2A12.rtlm</key>
		<integer>274</integer>
		<key>uni2A12.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A12.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A12.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A12.s01</key>
		<integer>500</integer>
		<key>uni2A12.ss02</key>
		<integer>90</integer>
		<key>uni2A12.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A13</key>
		<integer>274</integer>
		<key>uni2A13.rtlm</key>
		<integer>274</integer>
		<key>uni2A13.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A13.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A13.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A13.s01</key>
		<integer>500</integer>
		<key>uni2A13.ss02</key>
		<integer>90</integer>
		<key>uni2A13.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A14</key>
		<integer>274</integer>
		<key>uni2A14.rtlm</key>
		<integer>274</integer>
		<key>uni2A14.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A14.rtlm.ss02</key>
		<integer>97</integer>
		<key>uni2A14.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A14.s01</key>
		<integer>500</integer>
		<key>uni2A14.ss02</key>
		<integer>90</integer>
		<key>uni2A14.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A15</key>
		<integer>274</integer>
		<key>uni2A15.rtlm</key>
		<integer>274</integer>
		<key>uni2A15.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A15.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A15.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A15.s01</key>
		<integer>500</integer>
		<key>uni2A15.ss02</key>
		<integer>90</integer>
		<key>uni2A15.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A16</key>
		<integer>274</integer>
		<key>uni2A16.rtlm</key>
		<integer>274</integer>
		<key>uni2A16.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A16.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A16.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A16.s01</key>
		<integer>500</integer>
		<key>uni2A16.ss02</key>
		<integer>90</integer>
		<key>uni2A16.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A17</key>
		<integer>274</integer>
		<key>uni2A17.rtlm</key>
		<integer>274</integer>
		<key>uni2A17.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A17.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A17.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A17.s01</key>
		<integer>500</integer>
		<key>uni2A17.ss02</key>
		<integer>90</integer>
		<key>uni2A17.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A18</key>
		<integer>274</integer>
		<key>uni2A18.rtlm</key>
		<integer>274</integer>
		<key>uni2A18.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A18.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A18.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A18.s01</key>
		<integer>500</integer>
		<key>uni2A18.ss02</key>
		<integer>90</integer>
		<key>uni2A18.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A19</key>
		<integer>274</integer>
		<key>uni2A19.rtlm</key>
		<integer>274</integer>
		<key>uni2A19.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A19.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A19.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A19.s01</key>
		<integer>500</integer>
		<key>uni2A19.ss02</key>
		<integer>90</integer>
		<key>uni2A19.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A1A</key>
		<integer>274</integer>
		<key>uni2A1A.rtlm</key>
		<integer>274</integer>
		<key>uni2A1A.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A1A.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A1A.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A1A.s01</key>
		<integer>500</integer>
		<key>uni2A1A.ss02</key>
		<integer>90</integer>
		<key>uni2A1A.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A1B</key>
		<integer>274</integer>
		<key>uni2A1B.rtlm</key>
		<integer>274</integer>
		<key>uni2A1B.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A1B.rtlm.ss02</key>
		<integer>90</integer>
		<key>uni2A1B.rtlm.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A1B.s01</key>
		<integer>500</integer>
		<key>uni2A1B.ss02</key>
		<integer>90</integer>
		<key>uni2A1B.ss02.s01</key>
		<integer>90</integer>
		<key>uni2A1C</key>
		<integer>274</integer>
		<key>uni2A1C.rtlm</key>
		<integer>274</integer>
		<key>uni2A1C.rtlm.s01</key>
		<integer>500</integer>
		<key>uni2A1C.rtlm.ss02</key>
		<integer>0</integer>
		<key>uni2A1C.rtlm.ss02.s01</key>
		<integer>0</integer>
		<key>uni2A1C.s01</key>
		<integer>444</integer>
		<key>uni2A1C.ss02</key>
		<integer>0</integer>
		<key>uni2A1C.ss02.s01</key>
		<integer>0</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>arrowdbldown</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblup.x</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
			</array>
		</dict>
		<key>arrowdblup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblup.x</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
			</array>
		</dict>
		<key>arrowdown</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowup.x</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
			</array>
		</dict>
		<key>arrowup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowup.x</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>arrowupdn</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowup.x</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>bar</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>bar.x</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>bar.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
			</array>
		</dict>
		<key>braceleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>903</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>395</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A8</string>
					<key>startConnector</key>
					<integer>394</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>903</integer>
				</dict>
			</array>
		</dict>
		<key>braceright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>903</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>395</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AC</string>
					<key>startConnector</key>
					<integer>394</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>903</integer>
				</dict>
			</array>
		</dict>
		<key>bracketleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>947</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>947</integer>
				</dict>
			</array>
		</dict>
		<key>bracketright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>947</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>947</integer>
				</dict>
			</array>
		</dict>
		<key>parenleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>parenright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>uni2016</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2016.x</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2016.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
			</array>
		</dict>
		<key>uni21D5</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>660</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>788</integer>
					<key>endConnector</key>
					<integer>788</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblup.x</string>
					<key>startConnector</key>
					<integer>788</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>910</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup</string>
					<key>startConnector</key>
					<integer>660</integer>
				</dict>
			</array>
		</dict>
		<key>uni221A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1538</integer>
					<key>endConnector</key>
					<integer>1333</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23B7</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1070</integer>
					<key>endConnector</key>
					<integer>1070</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221A.x</string>
					<key>startConnector</key>
					<integer>1070</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.t</string>
					<key>startConnector</key>
					<integer>998</integer>
				</dict>
			</array>
		</dict>
		<key>uni221A.rtlm</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1538</integer>
					<key>endConnector</key>
					<integer>1272</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.rtlm.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1070</integer>
					<key>endConnector</key>
					<integer>1070</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221A.rtlm.x</string>
					<key>startConnector</key>
					<integer>1070</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.rtlm.t</string>
					<key>startConnector</key>
					<integer>998</integer>
				</dict>
			</array>
		</dict>
		<key>uni222B</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2321</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AE</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2320</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni222B.rtlm</key>
		<dict>
			<key>italic</key>
			<integer>-562</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2321.rtlm</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AE.rtlm</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2320.rtlm</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni222B.rtlm.ss02</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2321.rtlm</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AE.rtlm</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2320.rtlm</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni222B.ss02</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222B.ss02.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni222B.ss02.x</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222B.ss02.t</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni222C</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222C.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni222C.x</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222C.t</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni222C.ss02</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222C.ss02.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni222C.ss02.x</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222C.ss02.t</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni222D</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222D.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni222D.x</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222D.t</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni222D.ss02</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222D.ss02.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni222D.ss02.x</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni222D.ss02.t</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni2308</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>947</integer>
				</dict>
			</array>
		</dict>
		<key>uni2309</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>947</integer>
				</dict>
			</array>
		</dict>
		<key>uni230A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>947</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
			</array>
		</dict>
		<key>uni230B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>947</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
			</array>
		</dict>
		<key>uni23B0</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>903</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>903</integer>
				</dict>
			</array>
		</dict>
		<key>uni23B1</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>903</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>903</integer>
				</dict>
			</array>
		</dict>
		<key>uni2772</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>843</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2772.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2772.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2772.t</string>
					<key>startConnector</key>
					<integer>843</integer>
				</dict>
			</array>
		</dict>
		<key>uni2773</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>843</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2773.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2773.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2773.t</string>
					<key>startConnector</key>
					<integer>843</integer>
				</dict>
			</array>
		</dict>
		<key>uni27E6</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>977</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E6.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27E6.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E6.t</string>
					<key>startConnector</key>
					<integer>977</integer>
				</dict>
			</array>
		</dict>
		<key>uni27E7</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>977</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E7.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27E7.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E7.t</string>
					<key>startConnector</key>
					<integer>977</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EC</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>741</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EC.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27EC.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EC.t</string>
					<key>startConnector</key>
					<integer>741</integer>
				</dict>
			</array>
		</dict>
		<key>uni27ED</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>741</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27ED.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27ED.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27ED.t</string>
					<key>startConnector</key>
					<integer>741</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EE</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EF</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>uni2980</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2980.x</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2980.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
			</array>
		</dict>
		<key>uni2983</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>975</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2983.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2983.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2983.m</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2983.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2983.t</string>
					<key>startConnector</key>
					<integer>975</integer>
				</dict>
			</array>
		</dict>
		<key>uni2984</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>882</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2984.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2984.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>395</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2984.m</string>
					<key>startConnector</key>
					<integer>394</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2984.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2984.t</string>
					<key>startConnector</key>
					<integer>881</integer>
				</dict>
			</array>
		</dict>
		<key>uni2987</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>355</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2987.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2987.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2987.t</string>
					<key>startConnector</key>
					<integer>355</integer>
				</dict>
			</array>
		</dict>
		<key>uni2988</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>355</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2988.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2988.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2988.t</string>
					<key>startConnector</key>
					<integer>355</integer>
				</dict>
			</array>
		</dict>
		<key>uni298B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>826</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni298B.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>947</integer>
				</dict>
			</array>
		</dict>
		<key>uni298C</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>826</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni298C.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>947</integer>
				</dict>
			</array>
		</dict>
		<key>uni298D</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>947</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni298D.t</string>
					<key>startConnector</key>
					<integer>1289</integer>
				</dict>
			</array>
		</dict>
		<key>uni298E</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>947</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni298E.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>1289</integer>
				</dict>
			</array>
		</dict>
		<key>uni298F</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1289</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni298F.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>947</integer>
				</dict>
			</array>
		</dict>
		<key>uni2990</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>947</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2990.t</string>
					<key>startConnector</key>
					<integer>1289</integer>
				</dict>
			</array>
		</dict>
		<key>uni2993</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2993.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2993.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2993.m</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2993.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2993.t</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>uni2994</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2994.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2994.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2994.m</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2994.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2994.t</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>uni2995</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2995.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2995.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2995.m</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2995.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2995.t</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>uni2996</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>627</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2996.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2996.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2996.m</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2996.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2996.t</string>
					<key>startConnector</key>
					<integer>627</integer>
				</dict>
			</array>
		</dict>
		<key>uni2997</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>598</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2997.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2997.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2997.t</string>
					<key>startConnector</key>
					<integer>598</integer>
				</dict>
			</array>
		</dict>
		<key>uni2998</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>598</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2998.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>1069</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2998.x</string>
					<key>startConnector</key>
					<integer>1069</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1069</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2998.t</string>
					<key>startConnector</key>
					<integer>598</integer>
				</dict>
			</array>
		</dict>
		<key>uni2A0C</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2A0C.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2A0C.x</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2A0C.t</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
		<key>uni2A0C.ss02</key>
		<dict>
			<key>italic</key>
			<integer>90</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1178</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2A0C.ss02.b</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1362</integer>
					<key>endConnector</key>
					<integer>1362</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2A0C.ss02.x</string>
					<key>startConnector</key>
					<integer>1362</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1475</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2A0C.ss02.t</string>
					<key>startConnector</key>
					<integer>1291</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>arrowdbldown</key>
		<array/>
		<key>arrowdblup</key>
		<array/>
		<key>arrowdown</key>
		<array>
			<string>arrowdown</string>
		</array>
		<key>arrowup</key>
		<array/>
		<key>arrowupdn</key>
		<array/>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.s01</string>
			<string>backslash.s02</string>
			<string>backslash.s03</string>
			<string>backslash.s04</string>
			<string>backslash.s05</string>
			<string>backslash.s06</string>
			<string>backslash.s07</string>
			<string>backslash.s08</string>
			<string>backslash.s09</string>
			<string>backslash.s10</string>
			<string>backslash.s11</string>
			<string>backslash.s12</string>
		</array>
		<key>bar</key>
		<array>
			<string>bar</string>
			<string>bar.s01</string>
			<string>bar.s02</string>
			<string>bar.s03</string>
			<string>bar.s04</string>
			<string>bar.s05</string>
			<string>bar.s06</string>
			<string>bar.s07</string>
			<string>bar.s08</string>
			<string>bar.s09</string>
			<string>bar.s10</string>
			<string>bar.s11</string>
			<string>bar.s12</string>
		</array>
		<key>braceleft</key>
		<array>
			<string>braceleft</string>
			<string>braceleft.s01</string>
			<string>braceleft.s02</string>
			<string>braceleft.s03</string>
			<string>braceleft.s04</string>
			<string>braceleft.s05</string>
			<string>braceleft.s06</string>
			<string>braceleft.s07</string>
			<string>braceleft.s08</string>
			<string>braceleft.s09</string>
			<string>braceleft.s10</string>
			<string>braceleft.s11</string>
			<string>braceleft.s12</string>
		</array>
		<key>braceright</key>
		<array>
			<string>braceright</string>
			<string>braceright.s01</string>
			<string>braceright.s02</string>
			<string>braceright.s03</string>
			<string>braceright.s04</string>
			<string>braceright.s05</string>
			<string>braceright.s06</string>
			<string>braceright.s07</string>
			<string>braceright.s08</string>
			<string>braceright.s09</string>
			<string>braceright.s10</string>
			<string>braceright.s11</string>
			<string>braceright.s12</string>
		</array>
		<key>bracketleft</key>
		<array>
			<string>bracketleft</string>
			<string>bracketleft.s01</string>
			<string>bracketleft.s02</string>
			<string>bracketleft.s03</string>
			<string>bracketleft.s04</string>
			<string>bracketleft.s05</string>
			<string>bracketleft.s06</string>
			<string>bracketleft.s07</string>
			<string>bracketleft.s08</string>
			<string>bracketleft.s09</string>
			<string>bracketleft.s10</string>
			<string>bracketleft.s11</string>
			<string>bracketleft.s12</string>
		</array>
		<key>bracketright</key>
		<array>
			<string>bracketright</string>
			<string>bracketright.s01</string>
			<string>bracketright.s02</string>
			<string>bracketright.s03</string>
			<string>bracketright.s04</string>
			<string>bracketright.s05</string>
			<string>bracketright.s06</string>
			<string>bracketright.s07</string>
			<string>bracketright.s08</string>
			<string>bracketright.s09</string>
			<string>bracketright.s10</string>
			<string>bracketright.s11</string>
			<string>bracketright.s12</string>
		</array>
		<key>fraction</key>
		<array>
			<string>fraction</string>
			<string>slash</string>
			<string>slash.s01</string>
			<string>slash.s02</string>
			<string>slash.s03</string>
			<string>slash.s04</string>
			<string>slash.s05</string>
			<string>slash.s06</string>
			<string>slash.s07</string>
			<string>slash.s08</string>
			<string>slash.s09</string>
			<string>slash.s10</string>
			<string>slash.s11</string>
			<string>slash.s12</string>
		</array>
		<key>parenleft</key>
		<array>
			<string>parenleft</string>
			<string>parenleft.s01</string>
			<string>parenleft.s02</string>
			<string>parenleft.s03</string>
			<string>parenleft.s04</string>
			<string>parenleft.s05</string>
			<string>parenleft.s06</string>
			<string>parenleft.s07</string>
			<string>parenleft.s08</string>
			<string>parenleft.s09</string>
			<string>parenleft.s10</string>
			<string>parenleft.s11</string>
			<string>parenleft.s12</string>
		</array>
		<key>parenright</key>
		<array>
			<string>parenright</string>
			<string>parenright.s01</string>
			<string>parenright.s02</string>
			<string>parenright.s03</string>
			<string>parenright.s04</string>
			<string>parenright.s05</string>
			<string>parenright.s06</string>
			<string>parenright.s07</string>
			<string>parenright.s08</string>
			<string>parenright.s09</string>
			<string>parenright.s10</string>
			<string>parenright.s11</string>
			<string>parenright.s12</string>
		</array>
		<key>slash</key>
		<array>
			<string>slash</string>
			<string>slash.s01</string>
			<string>slash.s02</string>
			<string>slash.s03</string>
			<string>slash.s04</string>
			<string>slash.s05</string>
			<string>slash.s06</string>
			<string>slash.s07</string>
			<string>slash.s08</string>
			<string>slash.s09</string>
			<string>slash.s10</string>
			<string>slash.s11</string>
			<string>slash.s12</string>
		</array>
		<key>u1EEF0</key>
		<array>
			<string>u1EEF0</string>
			<string>u1EEF0.s01</string>
		</array>
		<key>uni0606</key>
		<array>
			<string>uni0606</string>
			<string>uni0606.s01</string>
			<string>uni0606.s02</string>
			<string>uni0606.s03</string>
			<string>uni0606.s04</string>
		</array>
		<key>uni0607</key>
		<array>
			<string>uni0607</string>
			<string>uni0607.s01</string>
			<string>uni0607.s02</string>
			<string>uni0607.s03</string>
			<string>uni0607.s04</string>
		</array>
		<key>uni2016</key>
		<array>
			<string>uni2016</string>
			<string>uni2016.s01</string>
			<string>uni2016.s02</string>
			<string>uni2016.s03</string>
			<string>uni2016.s04</string>
			<string>uni2016.s05</string>
			<string>uni2016.s06</string>
			<string>uni2016.s07</string>
			<string>uni2016.s08</string>
			<string>uni2016.s09</string>
			<string>uni2016.s10</string>
			<string>uni2016.s11</string>
			<string>uni2016.s12</string>
		</array>
		<key>uni2140</key>
		<array>
			<string>uni2140</string>
			<string>uni2140.s01</string>
		</array>
		<key>uni2140.rtlm</key>
		<array>
			<string>uni2140.rtlm</string>
			<string>uni2140.rtlm.s01</string>
		</array>
		<key>uni21D5</key>
		<array/>
		<key>uni220F</key>
		<array>
			<string>uni220F</string>
			<string>uni220F.s01</string>
		</array>
		<key>uni2210</key>
		<array>
			<string>uni2210</string>
			<string>uni2210.s01</string>
		</array>
		<key>uni2211</key>
		<array>
			<string>uni2211</string>
			<string>uni2211.s01</string>
		</array>
		<key>uni2211.rtlm</key>
		<array>
			<string>uni2211.rtlm</string>
			<string>uni2211.rtlm.s01</string>
		</array>
		<key>uni221A</key>
		<array>
			<string>uni221A</string>
			<string>uni221A.s01</string>
			<string>uni221A.s02</string>
			<string>uni221A.s03</string>
			<string>uni221A.s04</string>
			<string>uni221A.s05</string>
		</array>
		<key>uni221A.rtlm</key>
		<array>
			<string>uni221A.rtlm</string>
			<string>uni221A.rtlm.s01</string>
			<string>uni221A.rtlm.s02</string>
			<string>uni221A.rtlm.s03</string>
			<string>uni221A.rtlm.s04</string>
			<string>uni221A.rtlm.s05</string>
		</array>
		<key>uni221B</key>
		<array>
			<string>uni221B</string>
			<string>uni221B.s01</string>
			<string>uni221B.s02</string>
			<string>uni221B.s03</string>
			<string>uni221B.s04</string>
		</array>
		<key>uni221B.rtlm</key>
		<array>
			<string>uni221B.rtlm</string>
			<string>uni221B.rtlm.s01</string>
			<string>uni221B.rtlm.s02</string>
			<string>uni221B.rtlm.s03</string>
			<string>uni221B.rtlm.s04</string>
		</array>
		<key>uni221C</key>
		<array>
			<string>uni221C</string>
			<string>uni221C.s01</string>
			<string>uni221C.s02</string>
			<string>uni221C.s03</string>
			<string>uni221C.s04</string>
		</array>
		<key>uni221C.rtlm</key>
		<array>
			<string>uni221C.rtlm</string>
			<string>uni221C.rtlm.s01</string>
			<string>uni221C.rtlm.s02</string>
			<string>uni221C.rtlm.s03</string>
			<string>uni221C.rtlm.s04</string>
		</array>
		<key>uni222B</key>
		<array>
			<string>uni222B</string>
			<string>uni222B.s01</string>
		</array>
		<key>uni222B.rtlm</key>
		<array>
			<string>uni222B.rtlm</string>
			<string>uni222B.rtlm.s01</string>
		</array>
		<key>uni222B.rtlm.ss02</key>
		<array>
			<string>uni222B.rtlm.ss02</string>
			<string>uni222B.rtlm.ss02.s01</string>
		</array>
		<key>uni222B.ss02</key>
		<array>
			<string>uni222B.ss02</string>
			<string>uni222B.ss02.s01</string>
		</array>
		<key>uni222C</key>
		<array>
			<string>uni222C</string>
			<string>uni222C.s01</string>
		</array>
		<key>uni222C.rtlm</key>
		<array>
			<string>uni222C.rtlm</string>
			<string>uni222C.rtlm.s01</string>
		</array>
		<key>uni222C.rtlm.ss02</key>
		<array>
			<string>uni222C.rtlm.ss02</string>
			<string>uni222C.rtlm.ss02.s01</string>
		</array>
		<key>uni222C.ss02</key>
		<array>
			<string>uni222C.ss02</string>
			<string>uni222C.ss02.s01</string>
		</array>
		<key>uni222D</key>
		<array>
			<string>uni222D</string>
			<string>uni222D.s01</string>
		</array>
		<key>uni222D.rtlm</key>
		<array>
			<string>uni222D.rtlm</string>
			<string>uni222D.rtlm.s01</string>
		</array>
		<key>uni222D.rtlm.ss02</key>
		<array>
			<string>uni222D.rtlm.ss02</string>
			<string>uni222D.rtlm.ss02.s01</string>
		</array>
		<key>uni222D.ss02</key>
		<array>
			<string>uni222D.ss02</string>
			<string>uni222D.ss02.s01</string>
		</array>
		<key>uni222E</key>
		<array>
			<string>uni222E</string>
			<string>uni222E.s01</string>
		</array>
		<key>uni222E.rtlm</key>
		<array>
			<string>uni222E.rtlm</string>
			<string>uni222E.rtlm.s01</string>
		</array>
		<key>uni222E.rtlm.ss02</key>
		<array>
			<string>uni222E.rtlm.ss02</string>
			<string>uni222E.rtlm.ss02.s01</string>
		</array>
		<key>uni222E.ss02</key>
		<array>
			<string>uni222E.ss02</string>
			<string>uni222E.ss02.s01</string>
		</array>
		<key>uni222F</key>
		<array>
			<string>uni222F</string>
			<string>uni222F.s01</string>
		</array>
		<key>uni222F.rtlm</key>
		<array>
			<string>uni222F.rtlm</string>
			<string>uni222F.rtlm.s01</string>
		</array>
		<key>uni222F.rtlm.ss02</key>
		<array>
			<string>uni222F.rtlm.ss02</string>
			<string>uni222F.rtlm.ss02.s01</string>
		</array>
		<key>uni222F.ss02</key>
		<array>
			<string>uni222F.ss02</string>
			<string>uni222F.ss02.s01</string>
		</array>
		<key>uni2230</key>
		<array>
			<string>uni2230</string>
			<string>uni2230.s01</string>
		</array>
		<key>uni2230.rtlm</key>
		<array>
			<string>uni2230.rtlm</string>
			<string>uni2230.rtlm.s01</string>
		</array>
		<key>uni2230.rtlm.ss02</key>
		<array>
			<string>uni2230.rtlm.ss02</string>
			<string>uni2230.rtlm.ss02.s01</string>
		</array>
		<key>uni2230.ss02</key>
		<array>
			<string>uni2230.ss02</string>
			<string>uni2230.ss02.s01</string>
		</array>
		<key>uni2231</key>
		<array>
			<string>uni2231</string>
			<string>uni2231.s01</string>
		</array>
		<key>uni2231.rtlm</key>
		<array>
			<string>uni2231.rtlm</string>
			<string>uni2231.rtlm.s01</string>
		</array>
		<key>uni2231.rtlm.ss02</key>
		<array>
			<string>uni2231.rtlm.ss02</string>
			<string>uni2231.rtlm.ss02.s01</string>
		</array>
		<key>uni2231.ss02</key>
		<array>
			<string>uni2231.ss02</string>
			<string>uni2231.ss02.s01</string>
		</array>
		<key>uni2232</key>
		<array>
			<string>uni2232</string>
			<string>uni2232.s01</string>
		</array>
		<key>uni2232.rtlm</key>
		<array>
			<string>uni2232.rtlm</string>
			<string>uni2232.rtlm.s01</string>
		</array>
		<key>uni2232.rtlm.ss02</key>
		<array>
			<string>uni2232.rtlm.ss02</string>
			<string>uni2232.rtlm.ss02.s01</string>
		</array>
		<key>uni2232.ss02</key>
		<array>
			<string>uni2232.ss02</string>
			<string>uni2232.ss02.s01</string>
		</array>
		<key>uni2233</key>
		<array>
			<string>uni2233</string>
			<string>uni2233.s01</string>
		</array>
		<key>uni2233.rtlm</key>
		<array>
			<string>uni2233.rtlm</string>
			<string>uni2233.rtlm.s01</string>
		</array>
		<key>uni2233.rtlm.ss02</key>
		<array>
			<string>uni2233.rtlm.ss02</string>
			<string>uni2233.rtlm.ss02.s01</string>
		</array>
		<key>uni2233.ss02</key>
		<array>
			<string>uni2233.ss02</string>
			<string>uni2233.ss02.s01</string>
		</array>
		<key>uni22C0</key>
		<array>
			<string>uni22C0</string>
			<string>uni22C0.s01</string>
		</array>
		<key>uni22C1</key>
		<array>
			<string>uni22C1</string>
			<string>uni22C1.s01</string>
		</array>
		<key>uni22C2</key>
		<array>
			<string>uni22C2</string>
			<string>uni22C2.s01</string>
		</array>
		<key>uni22C3</key>
		<array>
			<string>uni22C3</string>
			<string>uni22C3.s01</string>
		</array>
		<key>uni2308</key>
		<array>
			<string>uni2308</string>
			<string>uni2308.s01</string>
			<string>uni2308.s02</string>
			<string>uni2308.s03</string>
			<string>uni2308.s04</string>
			<string>uni2308.s05</string>
			<string>uni2308.s06</string>
			<string>uni2308.s07</string>
			<string>uni2308.s08</string>
			<string>uni2308.s09</string>
			<string>uni2308.s10</string>
			<string>uni2308.s11</string>
			<string>uni2308.s12</string>
		</array>
		<key>uni2309</key>
		<array>
			<string>uni2309</string>
			<string>uni2309.s01</string>
			<string>uni2309.s02</string>
			<string>uni2309.s03</string>
			<string>uni2309.s04</string>
			<string>uni2309.s05</string>
			<string>uni2309.s06</string>
			<string>uni2309.s07</string>
			<string>uni2309.s08</string>
			<string>uni2309.s09</string>
			<string>uni2309.s10</string>
			<string>uni2309.s11</string>
			<string>uni2309.s12</string>
		</array>
		<key>uni230A</key>
		<array>
			<string>uni230A</string>
			<string>uni230A.s01</string>
			<string>uni230A.s02</string>
			<string>uni230A.s03</string>
			<string>uni230A.s04</string>
			<string>uni230A.s05</string>
			<string>uni230A.s06</string>
			<string>uni230A.s07</string>
			<string>uni230A.s08</string>
			<string>uni230A.s09</string>
			<string>uni230A.s10</string>
			<string>uni230A.s11</string>
			<string>uni230A.s12</string>
		</array>
		<key>uni230B</key>
		<array>
			<string>uni230B</string>
			<string>uni230B.s01</string>
			<string>uni230B.s02</string>
			<string>uni230B.s03</string>
			<string>uni230B.s04</string>
			<string>uni230B.s05</string>
			<string>uni230B.s06</string>
			<string>uni230B.s07</string>
			<string>uni230B.s08</string>
			<string>uni230B.s09</string>
			<string>uni230B.s10</string>
			<string>uni230B.s11</string>
			<string>uni230B.s12</string>
		</array>
		<key>uni23B0</key>
		<array>
			<string>uni23B0</string>
			<string>uni23B0.s01</string>
			<string>uni23B0.s02</string>
			<string>uni23B0.s03</string>
			<string>uni23B0.s04</string>
			<string>uni23B0.s05</string>
			<string>uni23B0.s06</string>
			<string>uni23B0.s07</string>
			<string>uni23B0.s08</string>
			<string>uni23B0.s09</string>
			<string>uni23B0.s10</string>
			<string>uni23B0.s11</string>
			<string>uni23B0.s12</string>
		</array>
		<key>uni23B1</key>
		<array>
			<string>uni23B1</string>
			<string>uni23B1.s01</string>
			<string>uni23B1.s02</string>
			<string>uni23B1.s03</string>
			<string>uni23B1.s04</string>
			<string>uni23B1.s05</string>
			<string>uni23B1.s06</string>
			<string>uni23B1.s07</string>
			<string>uni23B1.s08</string>
			<string>uni23B1.s09</string>
			<string>uni23B1.s10</string>
			<string>uni23B1.s11</string>
			<string>uni23B1.s12</string>
		</array>
		<key>uni2772</key>
		<array>
			<string>uni2772</string>
			<string>uni2772.s01</string>
			<string>uni2772.s02</string>
			<string>uni2772.s03</string>
			<string>uni2772.s04</string>
			<string>uni2772.s05</string>
			<string>uni2772.s06</string>
			<string>uni2772.s07</string>
			<string>uni2772.s08</string>
			<string>uni2772.s09</string>
			<string>uni2772.s10</string>
			<string>uni2772.s11</string>
			<string>uni2772.s12</string>
		</array>
		<key>uni2773</key>
		<array>
			<string>uni2773</string>
			<string>uni2773.s01</string>
			<string>uni2773.s02</string>
			<string>uni2773.s03</string>
			<string>uni2773.s04</string>
			<string>uni2773.s05</string>
			<string>uni2773.s06</string>
			<string>uni2773.s07</string>
			<string>uni2773.s08</string>
			<string>uni2773.s09</string>
			<string>uni2773.s10</string>
			<string>uni2773.s11</string>
			<string>uni2773.s12</string>
		</array>
		<key>uni27C5</key>
		<array>
			<string>uni27C5</string>
			<string>uni27C5.s01</string>
			<string>uni27C5.s02</string>
			<string>uni27C5.s03</string>
			<string>uni27C5.s04</string>
			<string>uni27C5.s05</string>
			<string>uni27C5.s06</string>
			<string>uni27C5.s07</string>
			<string>uni27C5.s08</string>
			<string>uni27C5.s09</string>
			<string>uni27C5.s10</string>
			<string>uni27C5.s11</string>
			<string>uni27C5.s12</string>
		</array>
		<key>uni27C6</key>
		<array>
			<string>uni27C6</string>
			<string>uni27C6.s01</string>
			<string>uni27C6.s02</string>
			<string>uni27C6.s03</string>
			<string>uni27C6.s04</string>
			<string>uni27C6.s05</string>
			<string>uni27C6.s06</string>
			<string>uni27C6.s07</string>
			<string>uni27C6.s08</string>
			<string>uni27C6.s09</string>
			<string>uni27C6.s10</string>
			<string>uni27C6.s11</string>
			<string>uni27C6.s12</string>
		</array>
		<key>uni27E6</key>
		<array>
			<string>uni27E6</string>
			<string>uni27E6.s01</string>
			<string>uni27E6.s02</string>
			<string>uni27E6.s03</string>
			<string>uni27E6.s04</string>
			<string>uni27E6.s05</string>
			<string>uni27E6.s06</string>
			<string>uni27E6.s07</string>
			<string>uni27E6.s08</string>
			<string>uni27E6.s09</string>
			<string>uni27E6.s10</string>
			<string>uni27E6.s11</string>
			<string>uni27E6.s12</string>
		</array>
		<key>uni27E7</key>
		<array>
			<string>uni27E7</string>
			<string>uni27E7.s01</string>
			<string>uni27E7.s02</string>
			<string>uni27E7.s03</string>
			<string>uni27E7.s04</string>
			<string>uni27E7.s05</string>
			<string>uni27E7.s06</string>
			<string>uni27E7.s07</string>
			<string>uni27E7.s08</string>
			<string>uni27E7.s09</string>
			<string>uni27E7.s10</string>
			<string>uni27E7.s11</string>
			<string>uni27E7.s12</string>
		</array>
		<key>uni27E8</key>
		<array>
			<string>uni27E8</string>
			<string>uni27E8.s01</string>
			<string>uni27E8.s02</string>
			<string>uni27E8.s03</string>
			<string>uni27E8.s04</string>
			<string>uni27E8.s05</string>
			<string>uni27E8.s06</string>
			<string>uni27E8.s07</string>
			<string>uni27E8.s08</string>
			<string>uni27E8.s09</string>
			<string>uni27E8.s10</string>
			<string>uni27E8.s11</string>
			<string>uni27E8.s12</string>
			<string>uni27E8.s13</string>
			<string>uni27E8.s14</string>
			<string>uni27E8.s15</string>
			<string>uni27E8.s16</string>
			<string>uni27E8.s17</string>
			<string>uni27E8.s18</string>
			<string>uni27E8.s19</string>
			<string>uni27E8.s20</string>
		</array>
		<key>uni27E8.s20</key>
		<array>
			<string>uni27E8</string>
			<string>uni27E8.s01</string>
			<string>uni27E8.s02</string>
			<string>uni27E8.s03</string>
			<string>uni27E8.s04</string>
			<string>uni27E8.s05</string>
			<string>uni27E8.s06</string>
			<string>uni27E8.s07</string>
			<string>uni27E8.s08</string>
			<string>uni27E8.s09</string>
			<string>uni27E8.s10</string>
			<string>uni27E8.s11</string>
			<string>uni27E8.s12</string>
		</array>
		<key>uni27E9</key>
		<array>
			<string>uni27E9</string>
			<string>uni27E9.s01</string>
			<string>uni27E9.s02</string>
			<string>uni27E9.s03</string>
			<string>uni27E9.s04</string>
			<string>uni27E9.s05</string>
			<string>uni27E9.s06</string>
			<string>uni27E9.s07</string>
			<string>uni27E9.s08</string>
			<string>uni27E9.s09</string>
			<string>uni27E9.s10</string>
			<string>uni27E9.s11</string>
			<string>uni27E9.s12</string>
			<string>uni27E9.s13</string>
			<string>uni27E9.s14</string>
			<string>uni27E9.s15</string>
			<string>uni27E9.s16</string>
			<string>uni27E9.s17</string>
			<string>uni27E9.s18</string>
			<string>uni27E9.s19</string>
			<string>uni27E9.s20</string>
		</array>
		<key>uni27E9.s20</key>
		<array>
			<string>uni27E9</string>
			<string>uni27E9.s01</string>
			<string>uni27E9.s02</string>
			<string>uni27E9.s03</string>
			<string>uni27E9.s04</string>
			<string>uni27E9.s05</string>
			<string>uni27E9.s06</string>
			<string>uni27E9.s07</string>
			<string>uni27E9.s08</string>
			<string>uni27E9.s09</string>
			<string>uni27E9.s10</string>
			<string>uni27E9.s11</string>
			<string>uni27E9.s12</string>
		</array>
		<key>uni27EA</key>
		<array>
			<string>uni27EA</string>
			<string>uni27EA.s01</string>
			<string>uni27EA.s02</string>
			<string>uni27EA.s03</string>
			<string>uni27EA.s04</string>
			<string>uni27EA.s05</string>
			<string>uni27EA.s06</string>
			<string>uni27EA.s07</string>
			<string>uni27EA.s08</string>
			<string>uni27EA.s09</string>
			<string>uni27EA.s10</string>
			<string>uni27EA.s11</string>
			<string>uni27EA.s12</string>
		</array>
		<key>uni27EB</key>
		<array>
			<string>uni27EB</string>
			<string>uni27EB.s01</string>
			<string>uni27EB.s02</string>
			<string>uni27EB.s03</string>
			<string>uni27EB.s04</string>
			<string>uni27EB.s05</string>
			<string>uni27EB.s06</string>
			<string>uni27EB.s07</string>
			<string>uni27EB.s08</string>
			<string>uni27EB.s09</string>
			<string>uni27EB.s10</string>
			<string>uni27EB.s11</string>
			<string>uni27EB.s12</string>
		</array>
		<key>uni27EC</key>
		<array>
			<string>uni27EC</string>
			<string>uni27EC.s01</string>
			<string>uni27EC.s02</string>
			<string>uni27EC.s03</string>
			<string>uni27EC.s04</string>
			<string>uni27EC.s05</string>
			<string>uni27EC.s06</string>
			<string>uni27EC.s07</string>
			<string>uni27EC.s08</string>
			<string>uni27EC.s09</string>
			<string>uni27EC.s10</string>
			<string>uni27EC.s11</string>
			<string>uni27EC.s12</string>
		</array>
		<key>uni27ED</key>
		<array>
			<string>uni27ED</string>
			<string>uni27ED.s01</string>
			<string>uni27ED.s02</string>
			<string>uni27ED.s03</string>
			<string>uni27ED.s04</string>
			<string>uni27ED.s05</string>
			<string>uni27ED.s06</string>
			<string>uni27ED.s07</string>
			<string>uni27ED.s08</string>
			<string>uni27ED.s09</string>
			<string>uni27ED.s10</string>
			<string>uni27ED.s11</string>
			<string>uni27ED.s12</string>
		</array>
		<key>uni27EE</key>
		<array>
			<string>uni27EE</string>
			<string>uni27EE.s01</string>
			<string>uni27EE.s02</string>
			<string>uni27EE.s03</string>
			<string>uni27EE.s04</string>
			<string>uni27EE.s05</string>
			<string>uni27EE.s06</string>
			<string>uni27EE.s07</string>
			<string>uni27EE.s08</string>
			<string>uni27EE.s09</string>
			<string>uni27EE.s10</string>
			<string>uni27EE.s11</string>
			<string>uni27EE.s12</string>
		</array>
		<key>uni27EF</key>
		<array>
			<string>uni27EF</string>
			<string>uni27EF.s01</string>
			<string>uni27EF.s02</string>
			<string>uni27EF.s03</string>
			<string>uni27EF.s04</string>
			<string>uni27EF.s05</string>
			<string>uni27EF.s06</string>
			<string>uni27EF.s07</string>
			<string>uni27EF.s08</string>
			<string>uni27EF.s09</string>
			<string>uni27EF.s10</string>
			<string>uni27EF.s11</string>
			<string>uni27EF.s12</string>
		</array>
		<key>uni2980</key>
		<array>
			<string>uni2980</string>
			<string>uni2980.s01</string>
			<string>uni2980.s02</string>
			<string>uni2980.s03</string>
			<string>uni2980.s04</string>
			<string>uni2980.s05</string>
			<string>uni2980.s06</string>
			<string>uni2980.s07</string>
			<string>uni2980.s08</string>
			<string>uni2980.s09</string>
			<string>uni2980.s10</string>
			<string>uni2980.s11</string>
			<string>uni2980.s12</string>
		</array>
		<key>uni2983</key>
		<array>
			<string>uni2983</string>
			<string>uni2983.s01</string>
			<string>uni2983.s02</string>
			<string>uni2983.s03</string>
			<string>uni2983.s04</string>
			<string>uni2983.s05</string>
			<string>uni2983.s06</string>
			<string>uni2983.s07</string>
			<string>uni2983.s08</string>
			<string>uni2983.s09</string>
			<string>uni2983.s10</string>
			<string>uni2983.s11</string>
			<string>uni2983.s12</string>
		</array>
		<key>uni2984</key>
		<array>
			<string>uni2984</string>
			<string>uni2984.s01</string>
			<string>uni2984.s02</string>
			<string>uni2984.s03</string>
			<string>uni2984.s04</string>
			<string>uni2984.s05</string>
			<string>uni2984.s06</string>
			<string>uni2984.s07</string>
			<string>uni2984.s08</string>
			<string>uni2984.s09</string>
			<string>uni2984.s10</string>
			<string>uni2984.s11</string>
			<string>uni2984.s12</string>
		</array>
		<key>uni2985</key>
		<array>
			<string>uni2985</string>
			<string>uni2985.s01</string>
			<string>uni2985.s02</string>
			<string>uni2985.s03</string>
			<string>uni2985.s04</string>
			<string>uni2985.s05</string>
			<string>uni2985.s06</string>
			<string>uni2985.s07</string>
			<string>uni2985.s08</string>
			<string>uni2985.s09</string>
			<string>uni2985.s10</string>
			<string>uni2985.s11</string>
			<string>uni2985.s12</string>
		</array>
		<key>uni2986</key>
		<array>
			<string>uni2986</string>
			<string>uni2986.s01</string>
			<string>uni2986.s02</string>
			<string>uni2986.s03</string>
			<string>uni2986.s04</string>
			<string>uni2986.s05</string>
			<string>uni2986.s06</string>
			<string>uni2986.s07</string>
			<string>uni2986.s08</string>
			<string>uni2986.s09</string>
			<string>uni2986.s10</string>
			<string>uni2986.s11</string>
			<string>uni2986.s12</string>
		</array>
		<key>uni2987</key>
		<array>
			<string>uni2987</string>
			<string>uni2987.s01</string>
			<string>uni2987.s02</string>
			<string>uni2987.s03</string>
			<string>uni2987.s04</string>
			<string>uni2987.s05</string>
			<string>uni2987.s06</string>
			<string>uni2987.s07</string>
			<string>uni2987.s08</string>
			<string>uni2987.s09</string>
			<string>uni2987.s10</string>
			<string>uni2987.s11</string>
			<string>uni2987.s12</string>
		</array>
		<key>uni2988</key>
		<array>
			<string>uni2988</string>
			<string>uni2988.s01</string>
			<string>uni2988.s02</string>
			<string>uni2988.s03</string>
			<string>uni2988.s04</string>
			<string>uni2988.s05</string>
			<string>uni2988.s06</string>
			<string>uni2988.s07</string>
			<string>uni2988.s08</string>
			<string>uni2988.s09</string>
			<string>uni2988.s10</string>
			<string>uni2988.s11</string>
			<string>uni2988.s12</string>
		</array>
		<key>uni2989</key>
		<array>
			<string>uni2989</string>
			<string>uni2989.s01</string>
			<string>uni2989.s02</string>
			<string>uni2989.s03</string>
			<string>uni2989.s04</string>
			<string>uni2989.s05</string>
			<string>uni2989.s06</string>
			<string>uni2989.s07</string>
			<string>uni2989.s08</string>
			<string>uni2989.s09</string>
			<string>uni2989.s10</string>
			<string>uni2989.s11</string>
			<string>uni2989.s12</string>
		</array>
		<key>uni298A</key>
		<array>
			<string>uni298A</string>
			<string>uni298A.s01</string>
			<string>uni298A.s02</string>
			<string>uni298A.s03</string>
			<string>uni298A.s04</string>
			<string>uni298A.s05</string>
			<string>uni298A.s06</string>
			<string>uni298A.s07</string>
			<string>uni298A.s08</string>
			<string>uni298A.s09</string>
			<string>uni298A.s10</string>
			<string>uni298A.s11</string>
			<string>uni298A.s12</string>
		</array>
		<key>uni298B</key>
		<array>
			<string>uni298B</string>
			<string>uni298B.s01</string>
			<string>uni298B.s02</string>
			<string>uni298B.s03</string>
			<string>uni298B.s04</string>
			<string>uni298B.s05</string>
			<string>uni298B.s06</string>
			<string>uni298B.s07</string>
			<string>uni298B.s08</string>
			<string>uni298B.s09</string>
			<string>uni298B.s10</string>
			<string>uni298B.s11</string>
			<string>uni298B.s12</string>
		</array>
		<key>uni298C</key>
		<array>
			<string>uni298C</string>
			<string>uni298C.s01</string>
			<string>uni298C.s02</string>
			<string>uni298C.s03</string>
			<string>uni298C.s04</string>
			<string>uni298C.s05</string>
			<string>uni298C.s06</string>
			<string>uni298C.s07</string>
			<string>uni298C.s08</string>
			<string>uni298C.s09</string>
			<string>uni298C.s10</string>
			<string>uni298C.s11</string>
			<string>uni298C.s12</string>
		</array>
		<key>uni298D</key>
		<array>
			<string>uni298D</string>
			<string>uni298D.s01</string>
			<string>uni298D.s02</string>
			<string>uni298D.s03</string>
			<string>uni298D.s04</string>
			<string>uni298D.s05</string>
			<string>uni298D.s06</string>
			<string>uni298D.s07</string>
			<string>uni298D.s08</string>
			<string>uni298D.s09</string>
			<string>uni298D.s10</string>
			<string>uni298D.s11</string>
			<string>uni298D.s12</string>
		</array>
		<key>uni298E</key>
		<array>
			<string>uni298E</string>
			<string>uni298E.s01</string>
			<string>uni298E.s02</string>
			<string>uni298E.s03</string>
			<string>uni298E.s04</string>
			<string>uni298E.s05</string>
			<string>uni298E.s06</string>
			<string>uni298E.s07</string>
			<string>uni298E.s08</string>
			<string>uni298E.s09</string>
			<string>uni298E.s10</string>
			<string>uni298E.s11</string>
			<string>uni298E.s12</string>
		</array>
		<key>uni298F</key>
		<array>
			<string>uni298F</string>
			<string>uni298F.s01</string>
			<string>uni298F.s02</string>
			<string>uni298F.s03</string>
			<string>uni298F.s04</string>
			<string>uni298F.s05</string>
			<string>uni298F.s06</string>
			<string>uni298F.s07</string>
			<string>uni298F.s08</string>
			<string>uni298F.s09</string>
			<string>uni298F.s10</string>
			<string>uni298F.s11</string>
			<string>uni298F.s12</string>
		</array>
		<key>uni2990</key>
		<array>
			<string>uni2990</string>
			<string>uni2990.s01</string>
			<string>uni2990.s02</string>
			<string>uni2990.s03</string>
			<string>uni2990.s04</string>
			<string>uni2990.s05</string>
			<string>uni2990.s06</string>
			<string>uni2990.s07</string>
			<string>uni2990.s08</string>
			<string>uni2990.s09</string>
			<string>uni2990.s10</string>
			<string>uni2990.s11</string>
			<string>uni2990.s12</string>
		</array>
		<key>uni2991</key>
		<array>
			<string>uni2991</string>
			<string>uni2991.s01</string>
			<string>uni2991.s02</string>
			<string>uni2991.s03</string>
			<string>uni2991.s04</string>
			<string>uni2991.s05</string>
			<string>uni2991.s06</string>
			<string>uni2991.s07</string>
			<string>uni2991.s08</string>
			<string>uni2991.s09</string>
			<string>uni2991.s10</string>
			<string>uni2991.s11</string>
			<string>uni2991.s12</string>
			<string>uni2991.s13</string>
			<string>uni2991.s14</string>
			<string>uni2991.s15</string>
			<string>uni2991.s16</string>
			<string>uni2991.s17</string>
			<string>uni2991.s18</string>
			<string>uni2991.s19</string>
			<string>uni2991.s20</string>
		</array>
		<key>uni2991.s20</key>
		<array>
			<string>uni2991</string>
			<string>uni2991.s01</string>
			<string>uni2991.s02</string>
			<string>uni2991.s03</string>
			<string>uni2991.s04</string>
			<string>uni2991.s05</string>
			<string>uni2991.s06</string>
			<string>uni2991.s07</string>
			<string>uni2991.s08</string>
			<string>uni2991.s09</string>
			<string>uni2991.s10</string>
			<string>uni2991.s11</string>
			<string>uni2991.s12</string>
		</array>
		<key>uni2992</key>
		<array>
			<string>uni2992</string>
			<string>uni2992.s01</string>
			<string>uni2992.s02</string>
			<string>uni2992.s03</string>
			<string>uni2992.s04</string>
			<string>uni2992.s05</string>
			<string>uni2992.s06</string>
			<string>uni2992.s07</string>
			<string>uni2992.s08</string>
			<string>uni2992.s09</string>
			<string>uni2992.s10</string>
			<string>uni2992.s11</string>
			<string>uni2992.s12</string>
			<string>uni2992.s13</string>
			<string>uni2992.s14</string>
			<string>uni2992.s15</string>
			<string>uni2992.s16</string>
			<string>uni2992.s17</string>
			<string>uni2992.s18</string>
			<string>uni2992.s19</string>
			<string>uni2992.s20</string>
		</array>
		<key>uni2992.s20</key>
		<array>
			<string>uni2992</string>
			<string>uni2992.s01</string>
			<string>uni2992.s02</string>
			<string>uni2992.s03</string>
			<string>uni2992.s04</string>
			<string>uni2992.s05</string>
			<string>uni2992.s06</string>
			<string>uni2992.s07</string>
			<string>uni2992.s08</string>
			<string>uni2992.s09</string>
			<string>uni2992.s10</string>
			<string>uni2992.s11</string>
			<string>uni2992.s12</string>
		</array>
		<key>uni2993</key>
		<array>
			<string>uni2993</string>
			<string>uni2993.s01</string>
			<string>uni2993.s02</string>
			<string>uni2993.s03</string>
			<string>uni2993.s04</string>
			<string>uni2993.s05</string>
			<string>uni2993.s06</string>
			<string>uni2993.s07</string>
			<string>uni2993.s08</string>
			<string>uni2993.s09</string>
			<string>uni2993.s10</string>
			<string>uni2993.s11</string>
			<string>uni2993.s12</string>
		</array>
		<key>uni2994</key>
		<array>
			<string>uni2994</string>
			<string>uni2994.s01</string>
			<string>uni2994.s02</string>
			<string>uni2994.s03</string>
			<string>uni2994.s04</string>
			<string>uni2994.s05</string>
			<string>uni2994.s06</string>
			<string>uni2994.s07</string>
			<string>uni2994.s08</string>
			<string>uni2994.s09</string>
			<string>uni2994.s10</string>
			<string>uni2994.s11</string>
			<string>uni2994.s12</string>
		</array>
		<key>uni2995</key>
		<array>
			<string>uni2995</string>
			<string>uni2995.s01</string>
			<string>uni2995.s02</string>
			<string>uni2995.s03</string>
			<string>uni2995.s04</string>
			<string>uni2995.s05</string>
			<string>uni2995.s06</string>
			<string>uni2995.s07</string>
			<string>uni2995.s08</string>
			<string>uni2995.s09</string>
			<string>uni2995.s10</string>
			<string>uni2995.s11</string>
			<string>uni2995.s12</string>
		</array>
		<key>uni2996</key>
		<array>
			<string>uni2996</string>
			<string>uni2996.s01</string>
			<string>uni2996.s02</string>
			<string>uni2996.s03</string>
			<string>uni2996.s04</string>
			<string>uni2996.s05</string>
			<string>uni2996.s06</string>
			<string>uni2996.s07</string>
			<string>uni2996.s08</string>
			<string>uni2996.s09</string>
			<string>uni2996.s10</string>
			<string>uni2996.s11</string>
			<string>uni2996.s12</string>
		</array>
		<key>uni2997</key>
		<array>
			<string>uni2997</string>
			<string>uni2997.s01</string>
			<string>uni2997.s02</string>
			<string>uni2997.s03</string>
			<string>uni2997.s04</string>
			<string>uni2997.s05</string>
			<string>uni2997.s06</string>
			<string>uni2997.s07</string>
			<string>uni2997.s08</string>
			<string>uni2997.s09</string>
			<string>uni2997.s10</string>
			<string>uni2997.s11</string>
			<string>uni2997.s12</string>
		</array>
		<key>uni2998</key>
		<array>
			<string>uni2998</string>
			<string>uni2998.s01</string>
			<string>uni2998.s02</string>
			<string>uni2998.s03</string>
			<string>uni2998.s04</string>
			<string>uni2998.s05</string>
			<string>uni2998.s06</string>
			<string>uni2998.s07</string>
			<string>uni2998.s08</string>
			<string>uni2998.s09</string>
			<string>uni2998.s10</string>
			<string>uni2998.s11</string>
			<string>uni2998.s12</string>
		</array>
		<key>uni299A</key>
		<array>
			<string>uni299A</string>
			<string>uni299A.s01</string>
			<string>uni299A.s02</string>
			<string>uni299A.s03</string>
			<string>uni299A.s04</string>
			<string>uni299A.s05</string>
			<string>uni299A.s06</string>
			<string>uni299A.s07</string>
			<string>uni299A.s08</string>
			<string>uni299A.s09</string>
			<string>uni299A.s10</string>
			<string>uni299A.s11</string>
			<string>uni299A.s12</string>
		</array>
		<key>uni29D8</key>
		<array>
			<string>uni29D8</string>
			<string>uni29D8.s01</string>
			<string>uni29D8.s02</string>
			<string>uni29D8.s03</string>
			<string>uni29D8.s04</string>
			<string>uni29D8.s05</string>
			<string>uni29D8.s06</string>
			<string>uni29D8.s07</string>
			<string>uni29D8.s08</string>
			<string>uni29D8.s09</string>
			<string>uni29D8.s10</string>
			<string>uni29D8.s11</string>
			<string>uni29D8.s12</string>
		</array>
		<key>uni29D9</key>
		<array>
			<string>uni29D9</string>
			<string>uni29D9.s01</string>
			<string>uni29D9.s02</string>
			<string>uni29D9.s03</string>
			<string>uni29D9.s04</string>
			<string>uni29D9.s05</string>
			<string>uni29D9.s06</string>
			<string>uni29D9.s07</string>
			<string>uni29D9.s08</string>
			<string>uni29D9.s09</string>
			<string>uni29D9.s10</string>
			<string>uni29D9.s11</string>
			<string>uni29D9.s12</string>
		</array>
		<key>uni29DA</key>
		<array>
			<string>uni29DA</string>
			<string>uni29DA.s01</string>
			<string>uni29DA.s02</string>
			<string>uni29DA.s03</string>
			<string>uni29DA.s04</string>
			<string>uni29DA.s05</string>
			<string>uni29DA.s06</string>
			<string>uni29DA.s07</string>
			<string>uni29DA.s08</string>
			<string>uni29DA.s09</string>
			<string>uni29DA.s10</string>
			<string>uni29DA.s11</string>
			<string>uni29DA.s12</string>
		</array>
		<key>uni29DB</key>
		<array>
			<string>uni29DB</string>
			<string>uni29DB.s01</string>
			<string>uni29DB.s02</string>
			<string>uni29DB.s03</string>
			<string>uni29DB.s04</string>
			<string>uni29DB.s05</string>
			<string>uni29DB.s06</string>
			<string>uni29DB.s07</string>
			<string>uni29DB.s08</string>
			<string>uni29DB.s09</string>
			<string>uni29DB.s10</string>
			<string>uni29DB.s11</string>
			<string>uni29DB.s12</string>
		</array>
		<key>uni29F8</key>
		<array>
			<string>uni29F8</string>
			<string>uni29F8.s01</string>
		</array>
		<key>uni29F9</key>
		<array>
			<string>uni29F9</string>
			<string>uni29F9.s01</string>
		</array>
		<key>uni29FC</key>
		<array>
			<string>uni29FC</string>
			<string>uni29FC.s01</string>
			<string>uni29FC.s02</string>
			<string>uni29FC.s03</string>
			<string>uni29FC.s04</string>
			<string>uni29FC.s05</string>
			<string>uni29FC.s06</string>
			<string>uni29FC.s07</string>
			<string>uni29FC.s08</string>
			<string>uni29FC.s09</string>
			<string>uni29FC.s10</string>
			<string>uni29FC.s11</string>
			<string>uni29FC.s12</string>
		</array>
		<key>uni29FD</key>
		<array>
			<string>uni29FD</string>
			<string>uni29FD.s01</string>
			<string>uni29FD.s02</string>
			<string>uni29FD.s03</string>
			<string>uni29FD.s04</string>
			<string>uni29FD.s05</string>
			<string>uni29FD.s06</string>
			<string>uni29FD.s07</string>
			<string>uni29FD.s08</string>
			<string>uni29FD.s09</string>
			<string>uni29FD.s10</string>
			<string>uni29FD.s11</string>
			<string>uni29FD.s12</string>
		</array>
		<key>uni2A00</key>
		<array>
			<string>uni2A00</string>
			<string>uni2A00.s01</string>
		</array>
		<key>uni2A01</key>
		<array>
			<string>uni2A01</string>
			<string>uni2A01.s01</string>
		</array>
		<key>uni2A02</key>
		<array>
			<string>uni2A02</string>
			<string>uni2A02.s01</string>
		</array>
		<key>uni2A03</key>
		<array>
			<string>uni2A03</string>
			<string>uni2A03.s01</string>
		</array>
		<key>uni2A04</key>
		<array>
			<string>uni2A04</string>
			<string>uni2A04.s01</string>
		</array>
		<key>uni2A05</key>
		<array>
			<string>uni2A05</string>
			<string>uni2A05.s01</string>
		</array>
		<key>uni2A06</key>
		<array>
			<string>uni2A06</string>
			<string>uni2A06.s01</string>
		</array>
		<key>uni2A07</key>
		<array>
			<string>uni2A07</string>
			<string>uni2A07.s01</string>
		</array>
		<key>uni2A08</key>
		<array>
			<string>uni2A08</string>
			<string>uni2A08.s01</string>
		</array>
		<key>uni2A09</key>
		<array>
			<string>uni2A09</string>
			<string>uni2A09.s01</string>
		</array>
		<key>uni2A0A</key>
		<array>
			<string>uni2A0A</string>
			<string>uni2A0A.s01</string>
		</array>
		<key>uni2A0A.rtlm</key>
		<array>
			<string>uni2A0A.rtlm</string>
			<string>uni2A0A.rtlm.s01</string>
		</array>
		<key>uni2A0B</key>
		<array>
			<string>uni2A0B</string>
			<string>uni2A0B.s01</string>
		</array>
		<key>uni2A0B.rtlm</key>
		<array>
			<string>uni2A0B.rtlm</string>
			<string>uni2A0B.rtlm.s01</string>
		</array>
		<key>uni2A0C</key>
		<array>
			<string>uni2A0C</string>
			<string>uni2A0C.s01</string>
		</array>
		<key>uni2A0C.rtlm</key>
		<array>
			<string>uni2A0C.rtlm</string>
			<string>uni2A0C.rtlm.s01</string>
		</array>
		<key>uni2A0C.rtlm.ss02</key>
		<array>
			<string>uni2A0C.rtlm.ss02</string>
			<string>uni2A0C.rtlm.ss02.s01</string>
		</array>
		<key>uni2A0C.ss02</key>
		<array>
			<string>uni2A0C.ss02</string>
			<string>uni2A0C.ss02.s01</string>
		</array>
		<key>uni2A0D</key>
		<array>
			<string>uni2A0D</string>
			<string>uni2A0D.s01</string>
		</array>
		<key>uni2A0D.rtlm</key>
		<array>
			<string>uni2A0D.rtlm</string>
			<string>uni2A0D.rtlm.s01</string>
		</array>
		<key>uni2A0D.rtlm.ss02</key>
		<array>
			<string>uni2A0D.rtlm.ss02</string>
			<string>uni2A0D.rtlm.ss02.s01</string>
		</array>
		<key>uni2A0D.ss02</key>
		<array>
			<string>uni2A0D.ss02</string>
			<string>uni2A0D.ss02.s01</string>
		</array>
		<key>uni2A0E</key>
		<array>
			<string>uni2A0E</string>
			<string>uni2A0E.s01</string>
		</array>
		<key>uni2A0E.rtlm</key>
		<array>
			<string>uni2A0E.rtlm</string>
			<string>uni2A0E.rtlm.s01</string>
		</array>
		<key>uni2A0E.rtlm.ss02</key>
		<array>
			<string>uni2A0E.rtlm.ss02</string>
			<string>uni2A0E.rtlm.ss02.s01</string>
		</array>
		<key>uni2A0E.ss02</key>
		<array>
			<string>uni2A0E.ss02</string>
			<string>uni2A0E.ss02.s01</string>
		</array>
		<key>uni2A0F</key>
		<array>
			<string>uni2A0F</string>
			<string>uni2A0F.s01</string>
		</array>
		<key>uni2A0F.rtlm</key>
		<array>
			<string>uni2A0F.rtlm</string>
			<string>uni2A0F.rtlm.s01</string>
		</array>
		<key>uni2A0F.rtlm.ss02</key>
		<array>
			<string>uni2A0F.rtlm.ss02</string>
			<string>uni2A0F.rtlm.ss02.s01</string>
		</array>
		<key>uni2A0F.ss02</key>
		<array>
			<string>uni2A0F.ss02</string>
			<string>uni2A0F.ss02.s01</string>
		</array>
		<key>uni2A10</key>
		<array>
			<string>uni2A10</string>
			<string>uni2A10.s01</string>
		</array>
		<key>uni2A10.rtlm</key>
		<array>
			<string>uni2A10.rtlm</string>
			<string>uni2A10.rtlm.s01</string>
		</array>
		<key>uni2A10.rtlm.ss02</key>
		<array>
			<string>uni2A10.rtlm.ss02</string>
			<string>uni2A10.rtlm.ss02.s01</string>
		</array>
		<key>uni2A10.ss02</key>
		<array>
			<string>uni2A10.ss02</string>
			<string>uni2A10.ss02.s01</string>
		</array>
		<key>uni2A11</key>
		<array>
			<string>uni2A11</string>
			<string>uni2A11.s01</string>
		</array>
		<key>uni2A11.rtlm</key>
		<array>
			<string>uni2A11.rtlm</string>
			<string>uni2A11.rtlm.s01</string>
		</array>
		<key>uni2A11.rtlm.ss02</key>
		<array>
			<string>uni2A11.rtlm.ss02</string>
			<string>uni2A11.rtlm.ss02.s01</string>
		</array>
		<key>uni2A11.ss02</key>
		<array>
			<string>uni2A11.ss02</string>
			<string>uni2A11.ss02.s01</string>
		</array>
		<key>uni2A12</key>
		<array>
			<string>uni2A12</string>
			<string>uni2A12.s01</string>
		</array>
		<key>uni2A12.rtlm</key>
		<array>
			<string>uni2A12.rtlm</string>
			<string>uni2A12.rtlm.s01</string>
		</array>
		<key>uni2A12.rtlm.ss02</key>
		<array>
			<string>uni2A12.rtlm.ss02</string>
			<string>uni2A12.rtlm.ss02.s01</string>
		</array>
		<key>uni2A12.ss02</key>
		<array>
			<string>uni2A12.ss02</string>
			<string>uni2A12.ss02.s01</string>
		</array>
		<key>uni2A13</key>
		<array>
			<string>uni2A13</string>
			<string>uni2A13.s01</string>
		</array>
		<key>uni2A13.rtlm</key>
		<array>
			<string>uni2A13.rtlm</string>
			<string>uni2A13.rtlm.s01</string>
		</array>
		<key>uni2A13.rtlm.ss02</key>
		<array>
			<string>uni2A13.rtlm.ss02</string>
			<string>uni2A13.rtlm.ss02.s01</string>
		</array>
		<key>uni2A13.ss02</key>
		<array>
			<string>uni2A13.ss02</string>
			<string>uni2A13.ss02.s01</string>
		</array>
		<key>uni2A14</key>
		<array>
			<string>uni2A14</string>
			<string>uni2A14.s01</string>
		</array>
		<key>uni2A14.rtlm</key>
		<array>
			<string>uni2A14.rtlm</string>
			<string>uni2A14.rtlm.s01</string>
		</array>
		<key>uni2A14.rtlm.ss02</key>
		<array>
			<string>uni2A14.rtlm.ss02</string>
			<string>uni2A14.rtlm.ss02.s01</string>
		</array>
		<key>uni2A14.ss02</key>
		<array>
			<string>uni2A14.ss02</string>
			<string>uni2A14.ss02.s01</string>
		</array>
		<key>uni2A15</key>
		<array>
			<string>uni2A15</string>
			<string>uni2A15.s01</string>
		</array>
		<key>uni2A15.rtlm</key>
		<array>
			<string>uni2A15.rtlm</string>
			<string>uni2A15.rtlm.s01</string>
		</array>
		<key>uni2A15.rtlm.ss02</key>
		<array>
			<string>uni2A15.rtlm.ss02</string>
			<string>uni2A15.rtlm.ss02.s01</string>
		</array>
		<key>uni2A15.ss02</key>
		<array>
			<string>uni2A15.ss02</string>
			<string>uni2A15.ss02.s01</string>
		</array>
		<key>uni2A16</key>
		<array>
			<string>uni2A16</string>
			<string>uni2A16.s01</string>
		</array>
		<key>uni2A16.rtlm</key>
		<array>
			<string>uni2A16.rtlm</string>
			<string>uni2A16.rtlm.s01</string>
		</array>
		<key>uni2A16.rtlm.ss02</key>
		<array>
			<string>uni2A16.rtlm.ss02</string>
			<string>uni2A16.rtlm.ss02.s01</string>
		</array>
		<key>uni2A16.ss02</key>
		<array>
			<string>uni2A16.ss02</string>
			<string>uni2A16.ss02.s01</string>
		</array>
		<key>uni2A17</key>
		<array>
			<string>uni2A17</string>
			<string>uni2A17.s01</string>
		</array>
		<key>uni2A17.rtlm</key>
		<array>
			<string>uni2A17.rtlm</string>
			<string>uni2A17.rtlm.s01</string>
		</array>
		<key>uni2A17.rtlm.ss02</key>
		<array>
			<string>uni2A17.rtlm.ss02</string>
			<string>uni2A17.rtlm.ss02.s01</string>
		</array>
		<key>uni2A17.ss02</key>
		<array>
			<string>uni2A17.ss02</string>
			<string>uni2A17.ss02.s01</string>
		</array>
		<key>uni2A18</key>
		<array>
			<string>uni2A18</string>
			<string>uni2A18.s01</string>
		</array>
		<key>uni2A18.rtlm</key>
		<array>
			<string>uni2A18.rtlm</string>
			<string>uni2A18.rtlm.s01</string>
		</array>
		<key>uni2A18.rtlm.ss02</key>
		<array>
			<string>uni2A18.rtlm.ss02</string>
			<string>uni2A18.rtlm.ss02.s01</string>
		</array>
		<key>uni2A18.ss02</key>
		<array>
			<string>uni2A18.ss02</string>
			<string>uni2A18.ss02.s01</string>
		</array>
		<key>uni2A19</key>
		<array>
			<string>uni2A19</string>
			<string>uni2A19.s01</string>
		</array>
		<key>uni2A19.rtlm</key>
		<array>
			<string>uni2A19.rtlm</string>
			<string>uni2A19.rtlm.s01</string>
		</array>
		<key>uni2A19.rtlm.ss02</key>
		<array>
			<string>uni2A19.rtlm.ss02</string>
			<string>uni2A19.rtlm.ss02.s01</string>
		</array>
		<key>uni2A19.ss02</key>
		<array>
			<string>uni2A19.ss02</string>
			<string>uni2A19.ss02.s01</string>
		</array>
		<key>uni2A1A</key>
		<array>
			<string>uni2A1A</string>
			<string>uni2A1A.s01</string>
		</array>
		<key>uni2A1A.rtlm</key>
		<array>
			<string>uni2A1A.rtlm</string>
			<string>uni2A1A.rtlm.s01</string>
		</array>
		<key>uni2A1A.rtlm.ss02</key>
		<array>
			<string>uni2A1A.rtlm.ss02</string>
			<string>uni2A1A.rtlm.ss02.s01</string>
		</array>
		<key>uni2A1A.ss02</key>
		<array>
			<string>uni2A1A.ss02</string>
			<string>uni2A1A.ss02.s01</string>
		</array>
		<key>uni2A1B</key>
		<array>
			<string>uni2A1B</string>
			<string>uni2A1B.s01</string>
		</array>
		<key>uni2A1B.rtlm</key>
		<array>
			<string>uni2A1B.rtlm</string>
			<string>uni2A1B.rtlm.s01</string>
		</array>
		<key>uni2A1B.rtlm.ss02</key>
		<array>
			<string>uni2A1B.rtlm.ss02</string>
			<string>uni2A1B.rtlm.ss02.s01</string>
		</array>
		<key>uni2A1B.ss02</key>
		<array>
			<string>uni2A1B.ss02</string>
			<string>uni2A1B.ss02.s01</string>
		</array>
		<key>uni2A1C</key>
		<array>
			<string>uni2A1C</string>
			<string>uni2A1C.s01</string>
		</array>
		<key>uni2A1C.rtlm</key>
		<array>
			<string>uni2A1C.rtlm</string>
			<string>uni2A1C.rtlm.s01</string>
		</array>
		<key>uni2A1C.rtlm.ss02</key>
		<array>
			<string>uni2A1C.rtlm.ss02</string>
			<string>uni2A1C.rtlm.ss02.s01</string>
		</array>
		<key>uni2A1C.ss02</key>
		<array>
			<string>uni2A1C.ss02</string>
			<string>uni2A1C.ss02.s01</string>
		</array>
		<key>uni2A1D</key>
		<array>
			<string>uni2A1D</string>
			<string>uni2A1D.s01</string>
		</array>
		<key>uni2A1E</key>
		<array>
			<string>uni2A1E</string>
			<string>uni2A1E.s01</string>
		</array>
		<key>uni2A1E.rtlm</key>
		<array>
			<string>uni2A1E.rtlm</string>
			<string>uni2A1E.rtlm.s01</string>
		</array>
		<key>uni2A1F</key>
		<array>
			<string>uni2A1F</string>
			<string>uni2A1F.s01</string>
		</array>
		<key>uni2A1F.rtlm</key>
		<array>
			<string>uni2A1F.rtlm</string>
			<string>uni2A1F.rtlm.s01</string>
		</array>
		<key>uni2A20</key>
		<array>
			<string>uni2A20</string>
			<string>uni2A20.s01</string>
		</array>
		<key>uni2A20.rtlm</key>
		<array>
			<string>uni2A20.rtlm</string>
			<string>uni2A20.rtlm.s01</string>
		</array>
		<key>uni2AFC</key>
		<array>
			<string>uni2AFC</string>
			<string>uni2AFC.s01</string>
		</array>
		<key>uni2AFF</key>
		<array>
			<string>uni2AFF</string>
			<string>uni2AFF.s01</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
