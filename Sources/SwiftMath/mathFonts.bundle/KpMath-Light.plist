<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>Planckconst</key>
		<integer>300</integer>
		<key>Planckconst.sst</key>
		<integer>300</integer>
		<key>Planckconst.st</key>
		<integer>300</integer>
		<key>acutedbl</key>
		<integer>-220</integer>
		<key>bar</key>
		<integer>-220</integer>
		<key>candracomb</key>
		<integer>-219</integer>
		<key>ddddot</key>
		<integer>-406</integer>
		<key>dddot</key>
		<integer>-312</integer>
		<key>ddot</key>
		<integer>-220</integer>
		<key>dot</key>
		<integer>-220</integer>
		<key>dotlessi</key>
		<integer>148</integer>
		<key>dotlessj</key>
		<integer>146</integer>
		<key>ell</key>
		<integer>371</integer>
		<key>hookabovecomb</key>
		<integer>-220</integer>
		<key>hslash</key>
		<integer>300</integer>
		<key>hslash.sst</key>
		<integer>300</integer>
		<key>hslash.st</key>
		<integer>300</integer>
		<key>imath</key>
		<integer>198</integer>
		<key>imath.sst</key>
		<integer>198</integer>
		<key>imath.st</key>
		<integer>198</integer>
		<key>jmath</key>
		<integer>243</integer>
		<key>jmath.sst</key>
		<integer>243</integer>
		<key>jmath.st</key>
		<integer>243</integer>
		<key>mbfL</key>
		<integer>239</integer>
		<key>mbfL.sst</key>
		<integer>239</integer>
		<key>mbfL.st</key>
		<integer>239</integer>
		<key>mbfalpha</key>
		<integer>268</integer>
		<key>mbfalpha.sst</key>
		<integer>296</integer>
		<key>mbfalpha.st</key>
		<integer>296</integer>
		<key>mbfb</key>
		<integer>240</integer>
		<key>mbfb.sst</key>
		<integer>240</integer>
		<key>mbfb.st</key>
		<integer>240</integer>
		<key>mbfbeta</key>
		<integer>232</integer>
		<key>mbfbeta.sst</key>
		<integer>262</integer>
		<key>mbfbeta.st</key>
		<integer>262</integer>
		<key>mbfcalA</key>
		<integer>661</integer>
		<key>mbfcalB</key>
		<integer>448</integer>
		<key>mbfcalC</key>
		<integer>350</integer>
		<key>mbfcalD</key>
		<integer>476</integer>
		<key>mbfcalE</key>
		<integer>406</integer>
		<key>mbfcalF</key>
		<integer>562</integer>
		<key>mbfcalG</key>
		<integer>419</integer>
		<key>mbfcalH</key>
		<integer>542</integer>
		<key>mbfcalI</key>
		<integer>478</integer>
		<key>mbfcalJ</key>
		<integer>619</integer>
		<key>mbfcalK</key>
		<integer>416</integer>
		<key>mbfcalL</key>
		<integer>462</integer>
		<key>mbfcalM</key>
		<integer>740</integer>
		<key>mbfcalN</key>
		<integer>672</integer>
		<key>mbfcalO</key>
		<integer>566</integer>
		<key>mbfcalP</key>
		<integer>494</integer>
		<key>mbfcalQ</key>
		<integer>570</integer>
		<key>mbfcalR</key>
		<integer>496</integer>
		<key>mbfcalS</key>
		<integer>471</integer>
		<key>mbfcalT</key>
		<integer>522</integer>
		<key>mbfcalU</key>
		<integer>478</integer>
		<key>mbfcalV</key>
		<integer>434</integer>
		<key>mbfcalW</key>
		<integer>683</integer>
		<key>mbfcalX</key>
		<integer>568</integer>
		<key>mbfcalY</key>
		<integer>510</integer>
		<key>mbfcalZ</key>
		<integer>529</integer>
		<key>mbfchi</key>
		<integer>287</integer>
		<key>mbfchi.sst</key>
		<integer>317</integer>
		<key>mbfchi.st</key>
		<integer>317</integer>
		<key>mbfd</key>
		<integer>342</integer>
		<key>mbfd.sst</key>
		<integer>342</integer>
		<key>mbfd.st</key>
		<integer>342</integer>
		<key>mbfdelta</key>
		<integer>245</integer>
		<key>mbfdelta.sst</key>
		<integer>275</integer>
		<key>mbfdelta.st</key>
		<integer>275</integer>
		<key>mbfdotlessi</key>
		<integer>147</integer>
		<key>mbfdotlessj</key>
		<integer>170</integer>
		<key>mbfepsilon</key>
		<integer>259</integer>
		<key>mbfepsilon.sst</key>
		<integer>289</integer>
		<key>mbfepsilon.st</key>
		<integer>289</integer>
		<key>mbfeta</key>
		<integer>274</integer>
		<key>mbfeta.sst</key>
		<integer>304</integer>
		<key>mbfeta.st</key>
		<integer>304</integer>
		<key>mbff</key>
		<integer>210</integer>
		<key>mbff.sst</key>
		<integer>210</integer>
		<key>mbff.st</key>
		<integer>210</integer>
		<key>mbfgamma</key>
		<integer>344</integer>
		<key>mbfgamma.sst</key>
		<integer>374</integer>
		<key>mbfgamma.st</key>
		<integer>374</integer>
		<key>mbfh</key>
		<integer>233</integer>
		<key>mbfh.sst</key>
		<integer>233</integer>
		<key>mbfh.st</key>
		<integer>233</integer>
		<key>mbfi</key>
		<integer>147</integer>
		<key>mbfi.sst</key>
		<integer>155</integer>
		<key>mbfi.st</key>
		<integer>155</integer>
		<key>mbfimath</key>
		<integer>195</integer>
		<key>mbfiota</key>
		<integer>122</integer>
		<key>mbfiota.sst</key>
		<integer>162</integer>
		<key>mbfiota.st</key>
		<integer>162</integer>
		<key>mbfitA</key>
		<integer>474</integer>
		<key>mbfitA.sst</key>
		<integer>474</integer>
		<key>mbfitA.st</key>
		<integer>474</integer>
		<key>mbfitAlpha</key>
		<integer>498</integer>
		<key>mbfitAlpha.sst</key>
		<integer>468</integer>
		<key>mbfitAlpha.st</key>
		<integer>468</integer>
		<key>mbfitB</key>
		<integer>438</integer>
		<key>mbfitB.sst</key>
		<integer>438</integer>
		<key>mbfitB.st</key>
		<integer>438</integer>
		<key>mbfitBeta</key>
		<integer>416</integer>
		<key>mbfitBeta.sst</key>
		<integer>416</integer>
		<key>mbfitBeta.st</key>
		<integer>416</integer>
		<key>mbfitC</key>
		<integer>512</integer>
		<key>mbfitC.sst</key>
		<integer>512</integer>
		<key>mbfitC.st</key>
		<integer>512</integer>
		<key>mbfitChi</key>
		<integer>440</integer>
		<key>mbfitChi.sst</key>
		<integer>476</integer>
		<key>mbfitChi.st</key>
		<integer>476</integer>
		<key>mbfitD</key>
		<integer>528</integer>
		<key>mbfitD.sst</key>
		<integer>528</integer>
		<key>mbfitD.st</key>
		<integer>528</integer>
		<key>mbfitDelta</key>
		<integer>454</integer>
		<key>mbfitDelta.sst</key>
		<integer>454</integer>
		<key>mbfitDelta.st</key>
		<integer>454</integer>
		<key>mbfitE</key>
		<integer>408</integer>
		<key>mbfitE.sst</key>
		<integer>408</integer>
		<key>mbfitE.st</key>
		<integer>408</integer>
		<key>mbfitEpsilon</key>
		<integer>414</integer>
		<key>mbfitEpsilon.sst</key>
		<integer>414</integer>
		<key>mbfitEpsilon.st</key>
		<integer>414</integer>
		<key>mbfitEta</key>
		<integer>462</integer>
		<key>mbfitEta.sst</key>
		<integer>504</integer>
		<key>mbfitEta.st</key>
		<integer>504</integer>
		<key>mbfitF</key>
		<integer>424</integer>
		<key>mbfitF.sst</key>
		<integer>424</integer>
		<key>mbfitF.st</key>
		<integer>424</integer>
		<key>mbfitG</key>
		<integer>522</integer>
		<key>mbfitG.sst</key>
		<integer>522</integer>
		<key>mbfitG.st</key>
		<integer>522</integer>
		<key>mbfitGamma</key>
		<integer>388</integer>
		<key>mbfitGamma.sst</key>
		<integer>412</integer>
		<key>mbfitGamma.st</key>
		<integer>412</integer>
		<key>mbfitH</key>
		<integer>514</integer>
		<key>mbfitH.sst</key>
		<integer>514</integer>
		<key>mbfitH.st</key>
		<integer>514</integer>
		<key>mbfitI</key>
		<integer>320</integer>
		<key>mbfitI.sst</key>
		<integer>320</integer>
		<key>mbfitI.st</key>
		<integer>320</integer>
		<key>mbfitIota</key>
		<integer>266</integer>
		<key>mbfitIota.sst</key>
		<integer>326</integer>
		<key>mbfitIota.st</key>
		<integer>326</integer>
		<key>mbfitJ</key>
		<integer>356</integer>
		<key>mbfitJ.sst</key>
		<integer>356</integer>
		<key>mbfitJ.st</key>
		<integer>356</integer>
		<key>mbfitK</key>
		<integer>514</integer>
		<key>mbfitK.sst</key>
		<integer>514</integer>
		<key>mbfitK.st</key>
		<integer>514</integer>
		<key>mbfitKappa</key>
		<integer>476</integer>
		<key>mbfitKappa.sst</key>
		<integer>490</integer>
		<key>mbfitKappa.st</key>
		<integer>490</integer>
		<key>mbfitL</key>
		<integer>336</integer>
		<key>mbfitL.sst</key>
		<integer>336</integer>
		<key>mbfitL.st</key>
		<integer>336</integer>
		<key>mbfitLambda</key>
		<integer>502</integer>
		<key>mbfitLambda.sst</key>
		<integer>502</integer>
		<key>mbfitLambda.st</key>
		<integer>502</integer>
		<key>mbfitM</key>
		<integer>570</integer>
		<key>mbfitM.sst</key>
		<integer>570</integer>
		<key>mbfitM.st</key>
		<integer>570</integer>
		<key>mbfitMu</key>
		<integer>540</integer>
		<key>mbfitMu.sst</key>
		<integer>540</integer>
		<key>mbfitMu.st</key>
		<integer>540</integer>
		<key>mbfitN</key>
		<integer>482</integer>
		<key>mbfitN.sst</key>
		<integer>482</integer>
		<key>mbfitN.st</key>
		<integer>482</integer>
		<key>mbfitNu</key>
		<integer>518</integer>
		<key>mbfitNu.sst</key>
		<integer>518</integer>
		<key>mbfitNu.st</key>
		<integer>518</integer>
		<key>mbfitO</key>
		<integer>496</integer>
		<key>mbfitO.sst</key>
		<integer>496</integer>
		<key>mbfitO.st</key>
		<integer>496</integer>
		<key>mbfitOmega</key>
		<integer>470</integer>
		<key>mbfitOmega.sst</key>
		<integer>508</integer>
		<key>mbfitOmega.st</key>
		<integer>508</integer>
		<key>mbfitOmicron</key>
		<integer>456</integer>
		<key>mbfitOmicron.sst</key>
		<integer>486</integer>
		<key>mbfitOmicron.st</key>
		<integer>486</integer>
		<key>mbfitP</key>
		<integer>432</integer>
		<key>mbfitP.sst</key>
		<integer>432</integer>
		<key>mbfitP.st</key>
		<integer>432</integer>
		<key>mbfitPhi</key>
		<integer>462</integer>
		<key>mbfitPhi.sst</key>
		<integer>606</integer>
		<key>mbfitPhi.st</key>
		<integer>474</integer>
		<key>mbfitPi</key>
		<integer>506</integer>
		<key>mbfitPi.sst</key>
		<integer>526</integer>
		<key>mbfitPi.st</key>
		<integer>526</integer>
		<key>mbfitPsi</key>
		<integer>478</integer>
		<key>mbfitPsi.sst</key>
		<integer>604</integer>
		<key>mbfitPsi.st</key>
		<integer>496</integer>
		<key>mbfitQ</key>
		<integer>480</integer>
		<key>mbfitQ.sst</key>
		<integer>480</integer>
		<key>mbfitQ.st</key>
		<integer>480</integer>
		<key>mbfitR</key>
		<integer>442</integer>
		<key>mbfitR.sst</key>
		<integer>442</integer>
		<key>mbfitR.st</key>
		<integer>442</integer>
		<key>mbfitRho</key>
		<integer>411</integer>
		<key>mbfitRho.sst</key>
		<integer>421</integer>
		<key>mbfitRho.st</key>
		<integer>421</integer>
		<key>mbfitS</key>
		<integer>372</integer>
		<key>mbfitS.sst</key>
		<integer>372</integer>
		<key>mbfitS.st</key>
		<integer>372</integer>
		<key>mbfitSigma</key>
		<integer>444</integer>
		<key>mbfitSigma.sst</key>
		<integer>474</integer>
		<key>mbfitSigma.st</key>
		<integer>474</integer>
		<key>mbfitT</key>
		<integer>410</integer>
		<key>mbfitT.sst</key>
		<integer>410</integer>
		<key>mbfitT.st</key>
		<integer>410</integer>
		<key>mbfitTau</key>
		<integer>350</integer>
		<key>mbfitTau.sst</key>
		<integer>444</integer>
		<key>mbfitTau.st</key>
		<integer>444</integer>
		<key>mbfitTheta</key>
		<integer>470</integer>
		<key>mbfitTheta.sst</key>
		<integer>500</integer>
		<key>mbfitTheta.st</key>
		<integer>500</integer>
		<key>mbfitU</key>
		<integer>496</integer>
		<key>mbfitU.sst</key>
		<integer>496</integer>
		<key>mbfitU.st</key>
		<integer>496</integer>
		<key>mbfitUpsilon</key>
		<integer>365</integer>
		<key>mbfitUpsilon.sst</key>
		<integer>474</integer>
		<key>mbfitUpsilon.st</key>
		<integer>474</integer>
		<key>mbfitV</key>
		<integer>496</integer>
		<key>mbfitV.sst</key>
		<integer>496</integer>
		<key>mbfitV.st</key>
		<integer>496</integer>
		<key>mbfitW</key>
		<integer>636</integer>
		<key>mbfitW.sst</key>
		<integer>636</integer>
		<key>mbfitW.st</key>
		<integer>636</integer>
		<key>mbfitX</key>
		<integer>500</integer>
		<key>mbfitX.sst</key>
		<integer>500</integer>
		<key>mbfitX.st</key>
		<integer>500</integer>
		<key>mbfitXi</key>
		<integer>420</integer>
		<key>mbfitXi.sst</key>
		<integer>456</integer>
		<key>mbfitXi.st</key>
		<integer>456</integer>
		<key>mbfitY</key>
		<integer>432</integer>
		<key>mbfitY.sst</key>
		<integer>432</integer>
		<key>mbfitY.st</key>
		<integer>432</integer>
		<key>mbfitZ</key>
		<integer>510</integer>
		<key>mbfitZ.sst</key>
		<integer>510</integer>
		<key>mbfitZ.st</key>
		<integer>510</integer>
		<key>mbfitZeta</key>
		<integer>434</integer>
		<key>mbfitZeta.sst</key>
		<integer>474</integer>
		<key>mbfitZeta.st</key>
		<integer>474</integer>
		<key>mbfita</key>
		<integer>340</integer>
		<key>mbfita.sst</key>
		<integer>340</integer>
		<key>mbfita.st</key>
		<integer>340</integer>
		<key>mbfitalpha</key>
		<integer>374</integer>
		<key>mbfitalpha.sst</key>
		<integer>374</integer>
		<key>mbfitalpha.st</key>
		<integer>374</integer>
		<key>mbfitb</key>
		<integer>290</integer>
		<key>mbfitb.sst</key>
		<integer>290</integer>
		<key>mbfitb.st</key>
		<integer>290</integer>
		<key>mbfitbeta</key>
		<integer>416</integer>
		<key>mbfitbeta.sst</key>
		<integer>416</integer>
		<key>mbfitbeta.st</key>
		<integer>416</integer>
		<key>mbfitc</key>
		<integer>316</integer>
		<key>mbfitc.sst</key>
		<integer>316</integer>
		<key>mbfitc.st</key>
		<integer>316</integer>
		<key>mbfitchi</key>
		<integer>387</integer>
		<key>mbfitchi.sst</key>
		<integer>387</integer>
		<key>mbfitchi.st</key>
		<integer>387</integer>
		<key>mbfitd</key>
		<integer>442</integer>
		<key>mbfitd.sst</key>
		<integer>442</integer>
		<key>mbfitd.st</key>
		<integer>442</integer>
		<key>mbfitdelta</key>
		<integer>329</integer>
		<key>mbfitdelta.sst</key>
		<integer>371</integer>
		<key>mbfitdelta.st</key>
		<integer>371</integer>
		<key>mbfite</key>
		<integer>323</integer>
		<key>mbfite.sst</key>
		<integer>323</integer>
		<key>mbfite.st</key>
		<integer>323</integer>
		<key>mbfitepsilon</key>
		<integer>298</integer>
		<key>mbfitepsilon.sst</key>
		<integer>350</integer>
		<key>mbfitepsilon.st</key>
		<integer>350</integer>
		<key>mbfiteta</key>
		<integer>335</integer>
		<key>mbfiteta.sst</key>
		<integer>400</integer>
		<key>mbfiteta.st</key>
		<integer>400</integer>
		<key>mbfitf</key>
		<integer>440</integer>
		<key>mbfitf.sst</key>
		<integer>440</integer>
		<key>mbfitf.st</key>
		<integer>440</integer>
		<key>mbfitg</key>
		<integer>344</integer>
		<key>mbfitg.sst</key>
		<integer>344</integer>
		<key>mbfitg.st</key>
		<integer>344</integer>
		<key>mbfitgamma</key>
		<integer>321</integer>
		<key>mbfitgamma.sst</key>
		<integer>416</integer>
		<key>mbfitgamma.st</key>
		<integer>416</integer>
		<key>mbfith</key>
		<integer>302</integer>
		<key>mbfith.sst</key>
		<integer>302</integer>
		<key>mbfith.st</key>
		<integer>302</integer>
		<key>mbfiti</key>
		<integer>239</integer>
		<key>mbfiti.sst</key>
		<integer>259</integer>
		<key>mbfiti.st</key>
		<integer>245</integer>
		<key>mbfitiota</key>
		<integer>204</integer>
		<key>mbfitiota.sst</key>
		<integer>254</integer>
		<key>mbfitiota.st</key>
		<integer>254</integer>
		<key>mbfitj</key>
		<integer>300</integer>
		<key>mbfitj.sst</key>
		<integer>318</integer>
		<key>mbfitj.st</key>
		<integer>292</integer>
		<key>mbfitk</key>
		<integer>306</integer>
		<key>mbfitk.sst</key>
		<integer>306</integer>
		<key>mbfitk.st</key>
		<integer>306</integer>
		<key>mbfitkappa</key>
		<integer>339</integer>
		<key>mbfitkappa.sst</key>
		<integer>379</integer>
		<key>mbfitkappa.st</key>
		<integer>379</integer>
		<key>mbfitl</key>
		<integer>210</integer>
		<key>mbfitl.sst</key>
		<integer>382</integer>
		<key>mbfitl.st</key>
		<integer>382</integer>
		<key>mbfitlambda</key>
		<integer>367</integer>
		<key>mbfitlambda.sst</key>
		<integer>377</integer>
		<key>mbfitlambda.st</key>
		<integer>377</integer>
		<key>mbfitm</key>
		<integer>515</integer>
		<key>mbfitm.sst</key>
		<integer>515</integer>
		<key>mbfitm.st</key>
		<integer>515</integer>
		<key>mbfitmu</key>
		<integer>364</integer>
		<key>mbfitmu.sst</key>
		<integer>400</integer>
		<key>mbfitmu.st</key>
		<integer>400</integer>
		<key>mbfitn</key>
		<integer>368</integer>
		<key>mbfitn.sst</key>
		<integer>368</integer>
		<key>mbfitn.st</key>
		<integer>368</integer>
		<key>mbfitnabla</key>
		<integer>387</integer>
		<key>mbfitnabla.sst</key>
		<integer>387</integer>
		<key>mbfitnabla.st</key>
		<integer>387</integer>
		<key>mbfitnu</key>
		<integer>322</integer>
		<key>mbfitnu.sst</key>
		<integer>386</integer>
		<key>mbfitnu.st</key>
		<integer>386</integer>
		<key>mbfito</key>
		<integer>332</integer>
		<key>mbfito.sst</key>
		<integer>332</integer>
		<key>mbfito.st</key>
		<integer>332</integer>
		<key>mbfitomega</key>
		<integer>414</integer>
		<key>mbfitomega.sst</key>
		<integer>450</integer>
		<key>mbfitomega.st</key>
		<integer>450</integer>
		<key>mbfitomicron</key>
		<integer>304</integer>
		<key>mbfitomicron.sst</key>
		<integer>334</integer>
		<key>mbfitomicron.st</key>
		<integer>334</integer>
		<key>mbfitp</key>
		<integer>372</integer>
		<key>mbfitp.sst</key>
		<integer>372</integer>
		<key>mbfitp.st</key>
		<integer>372</integer>
		<key>mbfitpartial</key>
		<integer>336</integer>
		<key>mbfitpartial.sst</key>
		<integer>336</integer>
		<key>mbfitpartial.st</key>
		<integer>336</integer>
		<key>mbfitphi</key>
		<integer>432</integer>
		<key>mbfitphi.sst</key>
		<integer>484</integer>
		<key>mbfitphi.st</key>
		<integer>484</integer>
		<key>mbfitpi</key>
		<integer>398</integer>
		<key>mbfitpi.sst</key>
		<integer>408</integer>
		<key>mbfitpi.st</key>
		<integer>408</integer>
		<key>mbfitpsi</key>
		<integer>431</integer>
		<key>mbfitpsi.sst</key>
		<integer>498</integer>
		<key>mbfitpsi.st</key>
		<integer>498</integer>
		<key>mbfitq</key>
		<integer>362</integer>
		<key>mbfitq.sst</key>
		<integer>362</integer>
		<key>mbfitq.st</key>
		<integer>362</integer>
		<key>mbfitr</key>
		<integer>310</integer>
		<key>mbfitr.sst</key>
		<integer>310</integer>
		<key>mbfitr.st</key>
		<integer>310</integer>
		<key>mbfitrho</key>
		<integer>356</integer>
		<key>mbfitrho.sst</key>
		<integer>366</integer>
		<key>mbfitrho.st</key>
		<integer>366</integer>
		<key>mbfits</key>
		<integer>285</integer>
		<key>mbfits.sst</key>
		<integer>285</integer>
		<key>mbfits.st</key>
		<integer>285</integer>
		<key>mbfitsansA</key>
		<integer>400</integer>
		<key>mbfitsansAlpha</key>
		<integer>459</integer>
		<key>mbfitsansB</key>
		<integer>372</integer>
		<key>mbfitsansBeta</key>
		<integer>372</integer>
		<key>mbfitsansC</key>
		<integer>421</integer>
		<key>mbfitsansChi</key>
		<integer>450</integer>
		<key>mbfitsansD</key>
		<integer>390</integer>
		<key>mbfitsansDelta</key>
		<integer>468</integer>
		<key>mbfitsansE</key>
		<integer>362</integer>
		<key>mbfitsansEpsilon</key>
		<integer>378</integer>
		<key>mbfitsansEta</key>
		<integer>421</integer>
		<key>mbfitsansF</key>
		<integer>355</integer>
		<key>mbfitsansG</key>
		<integer>429</integer>
		<key>mbfitsansGamma</key>
		<integer>348</integer>
		<key>mbfitsansH</key>
		<integer>428</integer>
		<key>mbfitsansI</key>
		<integer>225</integer>
		<key>mbfitsansIota</key>
		<integer>205</integer>
		<key>mbfitsansJ</key>
		<integer>402</integer>
		<key>mbfitsansK</key>
		<integer>390</integer>
		<key>mbfitsansKappa</key>
		<integer>400</integer>
		<key>mbfitsansL</key>
		<integer>278</integer>
		<key>mbfitsansLambda</key>
		<integer>504</integer>
		<key>mbfitsansM</key>
		<integer>480</integer>
		<key>mbfitsansMu</key>
		<integer>490</integer>
		<key>mbfitsansN</key>
		<integer>438</integer>
		<key>mbfitsansNu</key>
		<integer>444</integer>
		<key>mbfitsansO</key>
		<integer>446</integer>
		<key>mbfitsansOmega</key>
		<integer>488</integer>
		<key>mbfitsansOmicron</key>
		<integer>432</integer>
		<key>mbfitsansP</key>
		<integer>366</integer>
		<key>mbfitsansPhi</key>
		<integer>484</integer>
		<key>mbfitsansPi</key>
		<integer>479</integer>
		<key>mbfitsansPsi</key>
		<integer>466</integer>
		<key>mbfitsansQ</key>
		<integer>468</integer>
		<key>mbfitsansR</key>
		<integer>367</integer>
		<key>mbfitsansRho</key>
		<integer>386</integer>
		<key>mbfitsansS</key>
		<integer>360</integer>
		<key>mbfitsansSigma</key>
		<integer>450</integer>
		<key>mbfitsansT</key>
		<integer>368</integer>
		<key>mbfitsansTau</key>
		<integer>338</integer>
		<key>mbfitsansTheta</key>
		<integer>444</integer>
		<key>mbfitsansU</key>
		<integer>424</integer>
		<key>mbfitsansUpsilon</key>
		<integer>381</integer>
		<key>mbfitsansV</key>
		<integer>387</integer>
		<key>mbfitsansW</key>
		<integer>546</integer>
		<key>mbfitsansX</key>
		<integer>400</integer>
		<key>mbfitsansXi</key>
		<integer>442</integer>
		<key>mbfitsansY</key>
		<integer>375</integer>
		<key>mbfitsansZ</key>
		<integer>342</integer>
		<key>mbfitsansZeta</key>
		<integer>406</integer>
		<key>mbfitsansa</key>
		<integer>328</integer>
		<key>mbfitsansalpha</key>
		<integer>313</integer>
		<key>mbfitsansb</key>
		<integer>284</integer>
		<key>mbfitsansbeta</key>
		<integer>408</integer>
		<key>mbfitsansc</key>
		<integer>322</integer>
		<key>mbfitsanschi</key>
		<integer>383</integer>
		<key>mbfitsansd</key>
		<integer>438</integer>
		<key>mbfitsansdelta</key>
		<integer>319</integer>
		<key>mbfitsanse</key>
		<integer>308</integer>
		<key>mbfitsansepsilon</key>
		<integer>304</integer>
		<key>mbfitsanseta</key>
		<integer>296</integer>
		<key>mbfitsansf</key>
		<integer>306</integer>
		<key>mbfitsansg</key>
		<integer>338</integer>
		<key>mbfitsansgamma</key>
		<integer>364</integer>
		<key>mbfitsansh</key>
		<integer>274</integer>
		<key>mbfitsansi</key>
		<integer>204</integer>
		<key>mbfitsansiota</key>
		<integer>150</integer>
		<key>mbfitsansj</key>
		<integer>238</integer>
		<key>mbfitsansk</key>
		<integer>256</integer>
		<key>mbfitsanskappa</key>
		<integer>301</integer>
		<key>mbfitsansl</key>
		<integer>237</integer>
		<key>mbfitsanslambda</key>
		<integer>346</integer>
		<key>mbfitsansm</key>
		<integer>504</integer>
		<key>mbfitsansmu</key>
		<integer>342</integer>
		<key>mbfitsansn</key>
		<integer>360</integer>
		<key>mbfitsansnabla</key>
		<integer>354</integer>
		<key>mbfitsansnu</key>
		<integer>308</integer>
		<key>mbfitsanso</key>
		<integer>322</integer>
		<key>mbfitsansomega</key>
		<integer>372</integer>
		<key>mbfitsansomicron</key>
		<integer>316</integer>
		<key>mbfitsansp</key>
		<integer>368</integer>
		<key>mbfitsanspartial</key>
		<integer>384</integer>
		<key>mbfitsansphi</key>
		<integer>402</integer>
		<key>mbfitsanspi</key>
		<integer>341</integer>
		<key>mbfitsanspsi</key>
		<integer>422</integer>
		<key>mbfitsansq</key>
		<integer>330</integer>
		<key>mbfitsansr</key>
		<integer>292</integer>
		<key>mbfitsansrho</key>
		<integer>320</integer>
		<key>mbfitsanss</key>
		<integer>290</integer>
		<key>mbfitsanssigma</key>
		<integer>300</integer>
		<key>mbfitsanst</key>
		<integer>248</integer>
		<key>mbfitsanstau</key>
		<integer>289</integer>
		<key>mbfitsanstheta</key>
		<integer>360</integer>
		<key>mbfitsansu</key>
		<integer>352</integer>
		<key>mbfitsansupsilon</key>
		<integer>306</integer>
		<key>mbfitsansv</key>
		<integer>313</integer>
		<key>mbfitsansvarTheta</key>
		<integer>500</integer>
		<key>mbfitsansvarepsilon</key>
		<integer>278</integer>
		<key>mbfitsansvarphi</key>
		<integer>404</integer>
		<key>mbfitsansvarpi</key>
		<integer>376</integer>
		<key>mbfitsansvarrho</key>
		<integer>315</integer>
		<key>mbfitsansvarsigma</key>
		<integer>303</integer>
		<key>mbfitsansvartheta</key>
		<integer>366</integer>
		<key>mbfitsansw</key>
		<integer>420</integer>
		<key>mbfitsansx</key>
		<integer>320</integer>
		<key>mbfitsansxi</key>
		<integer>332</integer>
		<key>mbfitsansy</key>
		<integer>325</integer>
		<key>mbfitsansz</key>
		<integer>303</integer>
		<key>mbfitsanszeta</key>
		<integer>292</integer>
		<key>mbfitsigma</key>
		<integer>295</integer>
		<key>mbfitsigma.sst</key>
		<integer>343</integer>
		<key>mbfitsigma.st</key>
		<integer>343</integer>
		<key>mbfitt</key>
		<integer>282</integer>
		<key>mbfitt.sst</key>
		<integer>282</integer>
		<key>mbfitt.st</key>
		<integer>282</integer>
		<key>mbfittau</key>
		<integer>298</integer>
		<key>mbfittau.sst</key>
		<integer>354</integer>
		<key>mbfittau.st</key>
		<integer>354</integer>
		<key>mbfittheta</key>
		<integer>366</integer>
		<key>mbfittheta.sst</key>
		<integer>416</integer>
		<key>mbfittheta.st</key>
		<integer>416</integer>
		<key>mbfitu</key>
		<integer>374</integer>
		<key>mbfitu.sst</key>
		<integer>374</integer>
		<key>mbfitu.st</key>
		<integer>374</integer>
		<key>mbfitupsilon</key>
		<integer>336</integer>
		<key>mbfitupsilon.sst</key>
		<integer>376</integer>
		<key>mbfitupsilon.st</key>
		<integer>376</integer>
		<key>mbfitv</key>
		<integer>373</integer>
		<key>mbfitv.sst</key>
		<integer>373</integer>
		<key>mbfitv.st</key>
		<integer>373</integer>
		<key>mbfitvarTheta</key>
		<integer>470</integer>
		<key>mbfitvarTheta.sst</key>
		<integer>500</integer>
		<key>mbfitvarTheta.st</key>
		<integer>500</integer>
		<key>mbfitvarepsilon</key>
		<integer>314</integer>
		<key>mbfitvarepsilon.sst</key>
		<integer>314</integer>
		<key>mbfitvarepsilon.st</key>
		<integer>314</integer>
		<key>mbfitvarkappa</key>
		<integer>364</integer>
		<key>mbfitvarkappa.sst</key>
		<integer>360</integer>
		<key>mbfitvarkappa.st</key>
		<integer>360</integer>
		<key>mbfitvarphi</key>
		<integer>404</integer>
		<key>mbfitvarphi.sst</key>
		<integer>490</integer>
		<key>mbfitvarphi.st</key>
		<integer>490</integer>
		<key>mbfitvarpi</key>
		<integer>406</integer>
		<key>mbfitvarpi.sst</key>
		<integer>446</integer>
		<key>mbfitvarpi.st</key>
		<integer>446</integer>
		<key>mbfitvarrho</key>
		<integer>351</integer>
		<key>mbfitvarrho.sst</key>
		<integer>361</integer>
		<key>mbfitvarrho.st</key>
		<integer>361</integer>
		<key>mbfitvarsigma</key>
		<integer>338</integer>
		<key>mbfitvarsigma.sst</key>
		<integer>348</integer>
		<key>mbfitvarsigma.st</key>
		<integer>348</integer>
		<key>mbfitvartheta</key>
		<integer>380</integer>
		<key>mbfitvartheta.sst</key>
		<integer>400</integer>
		<key>mbfitvartheta.st</key>
		<integer>400</integer>
		<key>mbfitw</key>
		<integer>474</integer>
		<key>mbfitw.sst</key>
		<integer>474</integer>
		<key>mbfitw.st</key>
		<integer>474</integer>
		<key>mbfitx</key>
		<integer>327</integer>
		<key>mbfitx.sst</key>
		<integer>327</integer>
		<key>mbfitx.st</key>
		<integer>327</integer>
		<key>mbfitxi</key>
		<integer>380</integer>
		<key>mbfitxi.sst</key>
		<integer>400</integer>
		<key>mbfitxi.st</key>
		<integer>400</integer>
		<key>mbfity</key>
		<integer>348</integer>
		<key>mbfity.sst</key>
		<integer>348</integer>
		<key>mbfity.st</key>
		<integer>348</integer>
		<key>mbfitz</key>
		<integer>332</integer>
		<key>mbfitz.sst</key>
		<integer>332</integer>
		<key>mbfitz.st</key>
		<integer>332</integer>
		<key>mbfitzeta</key>
		<integer>320</integer>
		<key>mbfitzeta.sst</key>
		<integer>354</integer>
		<key>mbfitzeta.st</key>
		<integer>354</integer>
		<key>mbfj</key>
		<integer>166</integer>
		<key>mbfj.sst</key>
		<integer>156</integer>
		<key>mbfj.st</key>
		<integer>156</integer>
		<key>mbfjmath</key>
		<integer>234</integer>
		<key>mbfk</key>
		<integer>242</integer>
		<key>mbfk.sst</key>
		<integer>242</integer>
		<key>mbfk.st</key>
		<integer>242</integer>
		<key>mbfkappa</key>
		<integer>288</integer>
		<key>mbfkappa.sst</key>
		<integer>328</integer>
		<key>mbfkappa.st</key>
		<integer>328</integer>
		<key>mbfl</key>
		<integer>162</integer>
		<key>mbfl.sst</key>
		<integer>162</integer>
		<key>mbfl.st</key>
		<integer>162</integer>
		<key>mbflambda</key>
		<integer>210</integer>
		<key>mbflambda.sst</key>
		<integer>246</integer>
		<key>mbflambda.st</key>
		<integer>246</integer>
		<key>mbfmu</key>
		<integer>291</integer>
		<key>mbfmu.sst</key>
		<integer>321</integer>
		<key>mbfmu.st</key>
		<integer>321</integer>
		<key>mbfnu</key>
		<integer>299</integer>
		<key>mbfnu.sst</key>
		<integer>329</integer>
		<key>mbfnu.st</key>
		<integer>329</integer>
		<key>mbfomega</key>
		<integer>355</integer>
		<key>mbfomega.sst</key>
		<integer>375</integer>
		<key>mbfomega.st</key>
		<integer>375</integer>
		<key>mbfomicron</key>
		<integer>270</integer>
		<key>mbfomicron.sst</key>
		<integer>260</integer>
		<key>mbfomicron.st</key>
		<integer>260</integer>
		<key>mbfpartial</key>
		<integer>318</integer>
		<key>mbfpartial.sst</key>
		<integer>318</integer>
		<key>mbfpartial.st</key>
		<integer>318</integer>
		<key>mbfphi</key>
		<integer>325</integer>
		<key>mbfphi.sst</key>
		<integer>352</integer>
		<key>mbfphi.st</key>
		<integer>352</integer>
		<key>mbfpi</key>
		<integer>329</integer>
		<key>mbfpi.sst</key>
		<integer>362</integer>
		<key>mbfpi.st</key>
		<integer>362</integer>
		<key>mbfpsi</key>
		<integer>382</integer>
		<key>mbfpsi.sst</key>
		<integer>412</integer>
		<key>mbfpsi.st</key>
		<integer>412</integer>
		<key>mbfrho</key>
		<integer>268</integer>
		<key>mbfrho.sst</key>
		<integer>298</integer>
		<key>mbfrho.st</key>
		<integer>298</integer>
		<key>mbfscrA</key>
		<integer>736</integer>
		<key>mbfscrB</key>
		<integer>706</integer>
		<key>mbfscrC</key>
		<integer>628</integer>
		<key>mbfscrD</key>
		<integer>560</integer>
		<key>mbfscrE</key>
		<integer>482</integer>
		<key>mbfscrF</key>
		<integer>638</integer>
		<key>mbfscrG</key>
		<integer>532</integer>
		<key>mbfscrH</key>
		<integer>678</integer>
		<key>mbfscrI</key>
		<integer>790</integer>
		<key>mbfscrJ</key>
		<integer>682</integer>
		<key>mbfscrK</key>
		<integer>734</integer>
		<key>mbfscrL</key>
		<integer>732</integer>
		<key>mbfscrM</key>
		<integer>838</integer>
		<key>mbfscrN</key>
		<integer>850</integer>
		<key>mbfscrO</key>
		<integer>604</integer>
		<key>mbfscrP</key>
		<integer>724</integer>
		<key>mbfscrQ</key>
		<integer>554</integer>
		<key>mbfscrR</key>
		<integer>720</integer>
		<key>mbfscrS</key>
		<integer>774</integer>
		<key>mbfscrT</key>
		<integer>674</integer>
		<key>mbfscrU</key>
		<integer>572</integer>
		<key>mbfscrV</key>
		<integer>622</integer>
		<key>mbfscrW</key>
		<integer>788</integer>
		<key>mbfscrX</key>
		<integer>650</integer>
		<key>mbfscrY</key>
		<integer>608</integer>
		<key>mbfscrZ</key>
		<integer>650</integer>
		<key>mbfsigma</key>
		<integer>250</integer>
		<key>mbfsigma.sst</key>
		<integer>280</integer>
		<key>mbfsigma.st</key>
		<integer>280</integer>
		<key>mbftau</key>
		<integer>256</integer>
		<key>mbftau.sst</key>
		<integer>286</integer>
		<key>mbftau.st</key>
		<integer>286</integer>
		<key>mbftheta</key>
		<integer>284</integer>
		<key>mbftheta.sst</key>
		<integer>336</integer>
		<key>mbftheta.st</key>
		<integer>336</integer>
		<key>mbfupsilon</key>
		<integer>285</integer>
		<key>mbfupsilon.sst</key>
		<integer>315</integer>
		<key>mbfupsilon.st</key>
		<integer>315</integer>
		<key>mbfvarepsilon</key>
		<integer>240</integer>
		<key>mbfvarepsilon.sst</key>
		<integer>260</integer>
		<key>mbfvarepsilon.st</key>
		<integer>260</integer>
		<key>mbfvarkappa</key>
		<integer>321</integer>
		<key>mbfvarkappa.sst</key>
		<integer>331</integer>
		<key>mbfvarkappa.st</key>
		<integer>331</integer>
		<key>mbfvarphi</key>
		<integer>340</integer>
		<key>mbfvarphi.sst</key>
		<integer>370</integer>
		<key>mbfvarphi.st</key>
		<integer>370</integer>
		<key>mbfvarpi</key>
		<integer>352</integer>
		<key>mbfvarpi.sst</key>
		<integer>378</integer>
		<key>mbfvarpi.st</key>
		<integer>378</integer>
		<key>mbfvarrho</key>
		<integer>272</integer>
		<key>mbfvarrho.sst</key>
		<integer>302</integer>
		<key>mbfvarrho.st</key>
		<integer>302</integer>
		<key>mbfvarsigma</key>
		<integer>257</integer>
		<key>mbfvarsigma.sst</key>
		<integer>287</integer>
		<key>mbfvarsigma.st</key>
		<integer>287</integer>
		<key>mbfvartheta</key>
		<integer>286</integer>
		<key>mbfvartheta.sst</key>
		<integer>326</integer>
		<key>mbfvartheta.st</key>
		<integer>326</integer>
		<key>mbfxi</key>
		<integer>252</integer>
		<key>mbfxi.sst</key>
		<integer>282</integer>
		<key>mbfxi.st</key>
		<integer>282</integer>
		<key>mbfzeta</key>
		<integer>268</integer>
		<key>mbfzeta.sst</key>
		<integer>298</integer>
		<key>mbfzeta.st</key>
		<integer>298</integer>
		<key>mcalA</key>
		<integer>612</integer>
		<key>mcalB</key>
		<integer>417</integer>
		<key>mcalC</key>
		<integer>328</integer>
		<key>mcalD</key>
		<integer>416</integer>
		<key>mcalE</key>
		<integer>386</integer>
		<key>mcalF</key>
		<integer>510</integer>
		<key>mcalG</key>
		<integer>400</integer>
		<key>mcalH</key>
		<integer>478</integer>
		<key>mcalI</key>
		<integer>414</integer>
		<key>mcalJ</key>
		<integer>530</integer>
		<key>mcalK</key>
		<integer>400</integer>
		<key>mcalL</key>
		<integer>429</integer>
		<key>mcalM</key>
		<integer>678</integer>
		<key>mcalN</key>
		<integer>614</integer>
		<key>mcalO</key>
		<integer>462</integer>
		<key>mcalP</key>
		<integer>422</integer>
		<key>mcalQ</key>
		<integer>448</integer>
		<key>mcalR</key>
		<integer>416</integer>
		<key>mcalS</key>
		<integer>446</integer>
		<key>mcalT</key>
		<integer>453</integer>
		<key>mcalU</key>
		<integer>463</integer>
		<key>mcalV</key>
		<integer>370</integer>
		<key>mcalW</key>
		<integer>593</integer>
		<key>mcalX</key>
		<integer>532</integer>
		<key>mcalY</key>
		<integer>498</integer>
		<key>mcalZ</key>
		<integer>482</integer>
		<key>mitA</key>
		<integer>488</integer>
		<key>mitA.sst</key>
		<integer>480</integer>
		<key>mitA.st</key>
		<integer>480</integer>
		<key>mitAlpha</key>
		<integer>476</integer>
		<key>mitAlpha.sst</key>
		<integer>476</integer>
		<key>mitAlpha.st</key>
		<integer>476</integer>
		<key>mitB</key>
		<integer>340</integer>
		<key>mitB.sst</key>
		<integer>400</integer>
		<key>mitB.st</key>
		<integer>400</integer>
		<key>mitBeta</key>
		<integer>364</integer>
		<key>mitBeta.sst</key>
		<integer>400</integer>
		<key>mitBeta.st</key>
		<integer>400</integer>
		<key>mitC</key>
		<integer>394</integer>
		<key>mitC.sst</key>
		<integer>482</integer>
		<key>mitC.st</key>
		<integer>482</integer>
		<key>mitChi</key>
		<integer>439</integer>
		<key>mitChi.sst</key>
		<integer>449</integer>
		<key>mitChi.st</key>
		<integer>449</integer>
		<key>mitD</key>
		<integer>412</integer>
		<key>mitD.sst</key>
		<integer>462</integer>
		<key>mitD.st</key>
		<integer>462</integer>
		<key>mitDelta</key>
		<integer>458</integer>
		<key>mitDelta.sst</key>
		<integer>458</integer>
		<key>mitDelta.st</key>
		<integer>458</integer>
		<key>mitE</key>
		<integer>370</integer>
		<key>mitE.sst</key>
		<integer>418</integer>
		<key>mitE.st</key>
		<integer>418</integer>
		<key>mitEpsilon</key>
		<integer>414</integer>
		<key>mitEpsilon.sst</key>
		<integer>414</integer>
		<key>mitEpsilon.st</key>
		<integer>414</integer>
		<key>mitEta</key>
		<integer>481</integer>
		<key>mitEta.sst</key>
		<integer>491</integer>
		<key>mitEta.st</key>
		<integer>491</integer>
		<key>mitF</key>
		<integer>361</integer>
		<key>mitF.sst</key>
		<integer>400</integer>
		<key>mitF.st</key>
		<integer>400</integer>
		<key>mitG</key>
		<integer>420</integer>
		<key>mitG.sst</key>
		<integer>478</integer>
		<key>mitG.st</key>
		<integer>478</integer>
		<key>mitGamma</key>
		<integer>366</integer>
		<key>mitGamma.sst</key>
		<integer>410</integer>
		<key>mitGamma.st</key>
		<integer>410</integer>
		<key>mitH</key>
		<integer>480</integer>
		<key>mitH.sst</key>
		<integer>518</integer>
		<key>mitH.st</key>
		<integer>518</integer>
		<key>mitI</key>
		<integer>258</integer>
		<key>mitI.sst</key>
		<integer>308</integer>
		<key>mitI.st</key>
		<integer>308</integer>
		<key>mitIota</key>
		<integer>314</integer>
		<key>mitIota.sst</key>
		<integer>324</integer>
		<key>mitIota.st</key>
		<integer>324</integer>
		<key>mitJ</key>
		<integer>258</integer>
		<key>mitJ.sst</key>
		<integer>302</integer>
		<key>mitJ.st</key>
		<integer>302</integer>
		<key>mitK</key>
		<integer>428</integer>
		<key>mitK.sst</key>
		<integer>510</integer>
		<key>mitK.st</key>
		<integer>510</integer>
		<key>mitKappa</key>
		<integer>461</integer>
		<key>mitKappa.sst</key>
		<integer>481</integer>
		<key>mitKappa.st</key>
		<integer>481</integer>
		<key>mitL</key>
		<integer>382</integer>
		<key>mitL.sst</key>
		<integer>390</integer>
		<key>mitL.st</key>
		<integer>390</integer>
		<key>mitLambda</key>
		<integer>502</integer>
		<key>mitLambda.sst</key>
		<integer>502</integer>
		<key>mitLambda.st</key>
		<integer>502</integer>
		<key>mitM</key>
		<integer>541</integer>
		<key>mitM.sst</key>
		<integer>550</integer>
		<key>mitM.st</key>
		<integer>550</integer>
		<key>mitMu</key>
		<integer>559</integer>
		<key>mitMu.sst</key>
		<integer>559</integer>
		<key>mitMu.st</key>
		<integer>559</integer>
		<key>mitN</key>
		<integer>458</integer>
		<key>mitN.sst</key>
		<integer>500</integer>
		<key>mitN.st</key>
		<integer>500</integer>
		<key>mitNu</key>
		<integer>505</integer>
		<key>mitNu.sst</key>
		<integer>515</integer>
		<key>mitNu.st</key>
		<integer>515</integer>
		<key>mitO</key>
		<integer>428</integer>
		<key>mitO.sst</key>
		<integer>490</integer>
		<key>mitO.st</key>
		<integer>490</integer>
		<key>mitOmega</key>
		<integer>502</integer>
		<key>mitOmega.sst</key>
		<integer>532</integer>
		<key>mitOmega.st</key>
		<integer>532</integer>
		<key>mitOmicron</key>
		<integer>446</integer>
		<key>mitOmicron.sst</key>
		<integer>476</integer>
		<key>mitOmicron.st</key>
		<integer>476</integer>
		<key>mitP</key>
		<integer>368</integer>
		<key>mitP.sst</key>
		<integer>408</integer>
		<key>mitP.st</key>
		<integer>408</integer>
		<key>mitPhi</key>
		<integer>417</integer>
		<key>mitPhi.sst</key>
		<integer>477</integer>
		<key>mitPhi.st</key>
		<integer>477</integer>
		<key>mitPi</key>
		<integer>529</integer>
		<key>mitPi.sst</key>
		<integer>546</integer>
		<key>mitPi.st</key>
		<integer>546</integer>
		<key>mitPsi</key>
		<integer>466</integer>
		<key>mitPsi.sst</key>
		<integer>550</integer>
		<key>mitPsi.st</key>
		<integer>550</integer>
		<key>mitQ</key>
		<integer>432</integer>
		<key>mitQ.sst</key>
		<integer>484</integer>
		<key>mitQ.st</key>
		<integer>484</integer>
		<key>mitR</key>
		<integer>378</integer>
		<key>mitR.sst</key>
		<integer>422</integer>
		<key>mitR.st</key>
		<integer>422</integer>
		<key>mitRho</key>
		<integer>380</integer>
		<key>mitRho.sst</key>
		<integer>400</integer>
		<key>mitRho.st</key>
		<integer>400</integer>
		<key>mitS</key>
		<integer>338</integer>
		<key>mitS.sst</key>
		<integer>360</integer>
		<key>mitS.st</key>
		<integer>360</integer>
		<key>mitSigma</key>
		<integer>437</integer>
		<key>mitSigma.sst</key>
		<integer>467</integer>
		<key>mitSigma.st</key>
		<integer>467</integer>
		<key>mitT</key>
		<integer>312</integer>
		<key>mitT.sst</key>
		<integer>420</integer>
		<key>mitT.st</key>
		<integer>420</integer>
		<key>mitTau</key>
		<integer>366</integer>
		<key>mitTau.sst</key>
		<integer>426</integer>
		<key>mitTau.st</key>
		<integer>426</integer>
		<key>mitTheta</key>
		<integer>458</integer>
		<key>mitTheta.sst</key>
		<integer>498</integer>
		<key>mitTheta.st</key>
		<integer>498</integer>
		<key>mitU</key>
		<integer>404</integer>
		<key>mitU.sst</key>
		<integer>494</integer>
		<key>mitU.st</key>
		<integer>494</integer>
		<key>mitUpsilon</key>
		<integer>425</integer>
		<key>mitUpsilon.sst</key>
		<integer>495</integer>
		<key>mitUpsilon.st</key>
		<integer>495</integer>
		<key>mitV</key>
		<integer>354</integer>
		<key>mitV.sst</key>
		<integer>466</integer>
		<key>mitV.st</key>
		<integer>466</integer>
		<key>mitW</key>
		<integer>486</integer>
		<key>mitW.sst</key>
		<integer>592</integer>
		<key>mitW.st</key>
		<integer>592</integer>
		<key>mitX</key>
		<integer>428</integer>
		<key>mitX.sst</key>
		<integer>456</integer>
		<key>mitX.st</key>
		<integer>456</integer>
		<key>mitXi</key>
		<integer>441</integer>
		<key>mitXi.sst</key>
		<integer>481</integer>
		<key>mitXi.st</key>
		<integer>481</integer>
		<key>mitY</key>
		<integer>336</integer>
		<key>mitY.sst</key>
		<integer>416</integer>
		<key>mitY.st</key>
		<integer>416</integer>
		<key>mitZ</key>
		<integer>387</integer>
		<key>mitZ.sst</key>
		<integer>450</integer>
		<key>mitZ.st</key>
		<integer>450</integer>
		<key>mitZeta</key>
		<integer>417</integer>
		<key>mitZeta.sst</key>
		<integer>447</integer>
		<key>mitZeta.st</key>
		<integer>447</integer>
		<key>mita</key>
		<integer>264</integer>
		<key>mita.sst</key>
		<integer>312</integer>
		<key>mita.st</key>
		<integer>312</integer>
		<key>mitalpha</key>
		<integer>352</integer>
		<key>mitalpha.sst</key>
		<integer>362</integer>
		<key>mitalpha.st</key>
		<integer>362</integer>
		<key>mitb</key>
		<integer>293</integer>
		<key>mitb.sst</key>
		<integer>283</integer>
		<key>mitb.st</key>
		<integer>283</integer>
		<key>mitbeta</key>
		<integer>370</integer>
		<key>mitbeta.sst</key>
		<integer>416</integer>
		<key>mitbeta.st</key>
		<integer>416</integer>
		<key>mitc</key>
		<integer>268</integer>
		<key>mitc.sst</key>
		<integer>296</integer>
		<key>mitc.st</key>
		<integer>296</integer>
		<key>mitchi</key>
		<integer>375</integer>
		<key>mitchi.sst</key>
		<integer>375</integer>
		<key>mitchi.st</key>
		<integer>375</integer>
		<key>mitd</key>
		<integer>338</integer>
		<key>mitd.sst</key>
		<integer>452</integer>
		<key>mitd.st</key>
		<integer>452</integer>
		<key>mitdelta</key>
		<integer>352</integer>
		<key>mitdelta.sst</key>
		<integer>362</integer>
		<key>mitdelta.st</key>
		<integer>362</integer>
		<key>mite</key>
		<integer>264</integer>
		<key>mite.sst</key>
		<integer>304</integer>
		<key>mite.st</key>
		<integer>304</integer>
		<key>mitepsilon</key>
		<integer>306</integer>
		<key>mitepsilon.sst</key>
		<integer>348</integer>
		<key>mitepsilon.st</key>
		<integer>348</integer>
		<key>miteta</key>
		<integer>342</integer>
		<key>miteta.sst</key>
		<integer>362</integer>
		<key>miteta.st</key>
		<integer>362</integer>
		<key>mitf</key>
		<integer>430</integer>
		<key>mitf.sst</key>
		<integer>426</integer>
		<key>mitf.st</key>
		<integer>426</integer>
		<key>mitg</key>
		<integer>267</integer>
		<key>mitg.sst</key>
		<integer>314</integer>
		<key>mitg.st</key>
		<integer>314</integer>
		<key>mitgamma</key>
		<integer>390</integer>
		<key>mitgamma.sst</key>
		<integer>426</integer>
		<key>mitgamma.st</key>
		<integer>426</integer>
		<key>miti</key>
		<integer>204</integer>
		<key>miti.sst</key>
		<integer>232</integer>
		<key>miti.st</key>
		<integer>272</integer>
		<key>mitiota</key>
		<integer>218</integer>
		<key>mitiota.sst</key>
		<integer>228</integer>
		<key>mitiota.st</key>
		<integer>228</integer>
		<key>mitj</key>
		<integer>282</integer>
		<key>mitj.sst</key>
		<integer>291</integer>
		<key>mitj.st</key>
		<integer>333</integer>
		<key>mitk</key>
		<integer>284</integer>
		<key>mitk.sst</key>
		<integer>328</integer>
		<key>mitk.st</key>
		<integer>328</integer>
		<key>mitkappa</key>
		<integer>350</integer>
		<key>mitkappa.sst</key>
		<integer>360</integer>
		<key>mitkappa.st</key>
		<integer>360</integer>
		<key>mitl</key>
		<integer>188</integer>
		<key>mitl.sst</key>
		<integer>186</integer>
		<key>mitl.st</key>
		<integer>184</integer>
		<key>mitlambda</key>
		<integer>355</integer>
		<key>mitlambda.sst</key>
		<integer>365</integer>
		<key>mitlambda.st</key>
		<integer>365</integer>
		<key>mitm</key>
		<integer>456</integer>
		<key>mitm.sst</key>
		<integer>480</integer>
		<key>mitm.st</key>
		<integer>480</integer>
		<key>mitmu</key>
		<integer>376</integer>
		<key>mitmu.sst</key>
		<integer>386</integer>
		<key>mitmu.st</key>
		<integer>386</integer>
		<key>mitn</key>
		<integer>324</integer>
		<key>mitn.sst</key>
		<integer>358</integer>
		<key>mitn.st</key>
		<integer>358</integer>
		<key>mitnabla</key>
		<integer>390</integer>
		<key>mitnabla.sst</key>
		<integer>380</integer>
		<key>mitnabla.st</key>
		<integer>380</integer>
		<key>mitnu</key>
		<integer>342</integer>
		<key>mitnu.sst</key>
		<integer>372</integer>
		<key>mitnu.st</key>
		<integer>372</integer>
		<key>mito</key>
		<integer>280</integer>
		<key>mito.sst</key>
		<integer>310</integer>
		<key>mito.st</key>
		<integer>310</integer>
		<key>mitomega</key>
		<integer>426</integer>
		<key>mitomega.sst</key>
		<integer>436</integer>
		<key>mitomega.st</key>
		<integer>436</integer>
		<key>mitomicron</key>
		<integer>291</integer>
		<key>mitomicron.sst</key>
		<integer>301</integer>
		<key>mitomicron.st</key>
		<integer>301</integer>
		<key>mitp</key>
		<integer>321</integer>
		<key>mitp.sst</key>
		<integer>356</integer>
		<key>mitp.st</key>
		<integer>356</integer>
		<key>mitpartial</key>
		<integer>430</integer>
		<key>mitpartial.sst</key>
		<integer>430</integer>
		<key>mitpartial.st</key>
		<integer>430</integer>
		<key>mitphi</key>
		<integer>406</integer>
		<key>mitphi.sst</key>
		<integer>422</integer>
		<key>mitphi.st</key>
		<integer>422</integer>
		<key>mitpi</key>
		<integer>390</integer>
		<key>mitpi.sst</key>
		<integer>400</integer>
		<key>mitpi.st</key>
		<integer>400</integer>
		<key>mitpsi</key>
		<integer>418</integer>
		<key>mitpsi.sst</key>
		<integer>448</integer>
		<key>mitpsi.st</key>
		<integer>448</integer>
		<key>mitq</key>
		<integer>296</integer>
		<key>mitq.sst</key>
		<integer>340</integer>
		<key>mitq.st</key>
		<integer>340</integer>
		<key>mitr</key>
		<integer>208</integer>
		<key>mitr.sst</key>
		<integer>285</integer>
		<key>mitr.st</key>
		<integer>285</integer>
		<key>mitrho</key>
		<integer>340</integer>
		<key>mitrho.sst</key>
		<integer>350</integer>
		<key>mitrho.st</key>
		<integer>350</integer>
		<key>mits</key>
		<integer>225</integer>
		<key>mits.sst</key>
		<integer>265</integer>
		<key>mits.st</key>
		<integer>265</integer>
		<key>mitsansA</key>
		<integer>400</integer>
		<key>mitsansAlpha</key>
		<integer>400</integer>
		<key>mitsansB</key>
		<integer>366</integer>
		<key>mitsansBeta</key>
		<integer>352</integer>
		<key>mitsansC</key>
		<integer>436</integer>
		<key>mitsansChi</key>
		<integer>370</integer>
		<key>mitsansD</key>
		<integer>380</integer>
		<key>mitsansDelta</key>
		<integer>456</integer>
		<key>mitsansE</key>
		<integer>366</integer>
		<key>mitsansEpsilon</key>
		<integer>370</integer>
		<key>mitsansEta</key>
		<integer>413</integer>
		<key>mitsansF</key>
		<integer>357</integer>
		<key>mitsansG</key>
		<integer>444</integer>
		<key>mitsansGamma</key>
		<integer>426</integer>
		<key>mitsansH</key>
		<integer>409</integer>
		<key>mitsansI</key>
		<integer>201</integer>
		<key>mitsansIota</key>
		<integer>198</integer>
		<key>mitsansJ</key>
		<integer>419</integer>
		<key>mitsansK</key>
		<integer>374</integer>
		<key>mitsansKappa</key>
		<integer>372</integer>
		<key>mitsansL</key>
		<integer>262</integer>
		<key>mitsansLambda</key>
		<integer>518</integer>
		<key>mitsansM</key>
		<integer>441</integer>
		<key>mitsansMu</key>
		<integer>455</integer>
		<key>mitsansN</key>
		<integer>416</integer>
		<key>mitsansNu</key>
		<integer>414</integer>
		<key>mitsansO</key>
		<integer>444</integer>
		<key>mitsansOmega</key>
		<integer>521</integer>
		<key>mitsansOmicron</key>
		<integer>451</integer>
		<key>mitsansP</key>
		<integer>348</integer>
		<key>mitsansPhi</key>
		<integer>489</integer>
		<key>mitsansPi</key>
		<integer>521</integer>
		<key>mitsansPsi</key>
		<integer>548</integer>
		<key>mitsansQ</key>
		<integer>476</integer>
		<key>mitsansR</key>
		<integer>346</integer>
		<key>mitsansRho</key>
		<integer>356</integer>
		<key>mitsansS</key>
		<integer>348</integer>
		<key>mitsansSigma</key>
		<integer>444</integer>
		<key>mitsansT</key>
		<integer>371</integer>
		<key>mitsansTau</key>
		<integer>371</integer>
		<key>mitsansTheta</key>
		<integer>484</integer>
		<key>mitsansU</key>
		<integer>420</integer>
		<key>mitsansUpsilon</key>
		<integer>462</integer>
		<key>mitsansV</key>
		<integer>387</integer>
		<key>mitsansW</key>
		<integer>520</integer>
		<key>mitsansX</key>
		<integer>359</integer>
		<key>mitsansXi</key>
		<integer>458</integer>
		<key>mitsansY</key>
		<integer>352</integer>
		<key>mitsansZ</key>
		<integer>350</integer>
		<key>mitsansZeta</key>
		<integer>342</integer>
		<key>mitsansa</key>
		<integer>325</integer>
		<key>mitsansalpha</key>
		<integer>344</integer>
		<key>mitsansb</key>
		<integer>246</integer>
		<key>mitsansbeta</key>
		<integer>400</integer>
		<key>mitsansc</key>
		<integer>322</integer>
		<key>mitsanschi</key>
		<integer>382</integer>
		<key>mitsansd</key>
		<integer>456</integer>
		<key>mitsansdelta</key>
		<integer>344</integer>
		<key>mitsanse</key>
		<integer>316</integer>
		<key>mitsansepsilon</key>
		<integer>296</integer>
		<key>mitsanseta</key>
		<integer>358</integer>
		<key>mitsansf</key>
		<integer>400</integer>
		<key>mitsansg</key>
		<integer>334</integer>
		<key>mitsansgamma</key>
		<integer>400</integer>
		<key>mitsansh</key>
		<integer>258</integer>
		<key>mitsansi</key>
		<integer>181</integer>
		<key>mitsansiota</key>
		<integer>208</integer>
		<key>mitsansj</key>
		<integer>208</integer>
		<key>mitsansk</key>
		<integer>254</integer>
		<key>mitsanskappa</key>
		<integer>353</integer>
		<key>mitsansl</key>
		<integer>192</integer>
		<key>mitsanslambda</key>
		<integer>362</integer>
		<key>mitsansm</key>
		<integer>472</integer>
		<key>mitsansmu</key>
		<integer>368</integer>
		<key>mitsansn</key>
		<integer>342</integer>
		<key>mitsansnu</key>
		<integer>350</integer>
		<key>mitsanso</key>
		<integer>330</integer>
		<key>mitsansomega</key>
		<integer>422</integer>
		<key>mitsansomicron</key>
		<integer>336</integer>
		<key>mitsansp</key>
		<integer>340</integer>
		<key>mitsansphi</key>
		<integer>458</integer>
		<key>mitsanspi</key>
		<integer>379</integer>
		<key>mitsanspsi</key>
		<integer>452</integer>
		<key>mitsansq</key>
		<integer>338</integer>
		<key>mitsansr</key>
		<integer>259</integer>
		<key>mitsansrho</key>
		<integer>344</integer>
		<key>mitsanss</key>
		<integer>270</integer>
		<key>mitsanssigma</key>
		<integer>316</integer>
		<key>mitsanst</key>
		<integer>208</integer>
		<key>mitsanstau</key>
		<integer>315</integer>
		<key>mitsanstheta</key>
		<integer>393</integer>
		<key>mitsansu</key>
		<integer>330</integer>
		<key>mitsansupsilon</key>
		<integer>348</integer>
		<key>mitsansv</key>
		<integer>299</integer>
		<key>mitsansvarTheta</key>
		<integer>485</integer>
		<key>mitsansvarepsilon</key>
		<integer>336</integer>
		<key>mitsansvarphi</key>
		<integer>428</integer>
		<key>mitsansvarpi</key>
		<integer>422</integer>
		<key>mitsansvarrho</key>
		<integer>337</integer>
		<key>mitsansvarsigma</key>
		<integer>333</integer>
		<key>mitsansvartheta</key>
		<integer>374</integer>
		<key>mitsansw</key>
		<integer>400</integer>
		<key>mitsansx</key>
		<integer>289</integer>
		<key>mitsansxi</key>
		<integer>386</integer>
		<key>mitsansy</key>
		<integer>317</integer>
		<key>mitsansz</key>
		<integer>292</integer>
		<key>mitsanszeta</key>
		<integer>336</integer>
		<key>mitsigma</key>
		<integer>306</integer>
		<key>mitsigma.sst</key>
		<integer>336</integer>
		<key>mitsigma.st</key>
		<integer>336</integer>
		<key>mitt</key>
		<integer>202</integer>
		<key>mitt.sst</key>
		<integer>251</integer>
		<key>mitt.st</key>
		<integer>251</integer>
		<key>mittau</key>
		<integer>311</integer>
		<key>mittau.sst</key>
		<integer>338</integer>
		<key>mittau.st</key>
		<integer>338</integer>
		<key>mittheta</key>
		<integer>385</integer>
		<key>mittheta.sst</key>
		<integer>395</integer>
		<key>mittheta.st</key>
		<integer>395</integer>
		<key>mitu</key>
		<integer>348</integer>
		<key>mitu.sst</key>
		<integer>348</integer>
		<key>mitu.st</key>
		<integer>348</integer>
		<key>mitupsilon</key>
		<integer>352</integer>
		<key>mitupsilon.sst</key>
		<integer>372</integer>
		<key>mitupsilon.st</key>
		<integer>372</integer>
		<key>mitv</key>
		<integer>291</integer>
		<key>mitv.sst</key>
		<integer>332</integer>
		<key>mitv.st</key>
		<integer>332</integer>
		<key>mitvarTheta</key>
		<integer>426</integer>
		<key>mitvarTheta.sst</key>
		<integer>498</integer>
		<key>mitvarTheta.st</key>
		<integer>498</integer>
		<key>mitvarepsilon</key>
		<integer>290</integer>
		<key>mitvarepsilon.sst</key>
		<integer>300</integer>
		<key>mitvarepsilon.st</key>
		<integer>300</integer>
		<key>mitvarkappa</key>
		<integer>441</integer>
		<key>mitvarkappa.sst</key>
		<integer>382</integer>
		<key>mitvarkappa.st</key>
		<integer>382</integer>
		<key>mitvarphi</key>
		<integer>458</integer>
		<key>mitvarphi.sst</key>
		<integer>478</integer>
		<key>mitvarphi.st</key>
		<integer>478</integer>
		<key>mitvarpi</key>
		<integer>416</integer>
		<key>mitvarpi.sst</key>
		<integer>426</integer>
		<key>mitvarpi.st</key>
		<integer>426</integer>
		<key>mitvarrho</key>
		<integer>338</integer>
		<key>mitvarrho.sst</key>
		<integer>354</integer>
		<key>mitvarrho.st</key>
		<integer>354</integer>
		<key>mitvarsigma</key>
		<integer>324</integer>
		<key>mitvarsigma.sst</key>
		<integer>334</integer>
		<key>mitvarsigma.st</key>
		<integer>334</integer>
		<key>mitvartheta</key>
		<integer>374</integer>
		<key>mitvartheta.sst</key>
		<integer>384</integer>
		<key>mitvartheta.st</key>
		<integer>384</integer>
		<key>mitw</key>
		<integer>438</integer>
		<key>mitw.sst</key>
		<integer>455</integer>
		<key>mitw.st</key>
		<integer>455</integer>
		<key>mitx</key>
		<integer>290</integer>
		<key>mitx.sst</key>
		<integer>334</integer>
		<key>mitx.st</key>
		<integer>334</integer>
		<key>mitxi</key>
		<integer>363</integer>
		<key>mitxi.sst</key>
		<integer>378</integer>
		<key>mitxi.st</key>
		<integer>378</integer>
		<key>mity</key>
		<integer>308</integer>
		<key>mity.sst</key>
		<integer>338</integer>
		<key>mity.st</key>
		<integer>338</integer>
		<key>mitz</key>
		<integer>245</integer>
		<key>mitz.sst</key>
		<integer>292</integer>
		<key>mitz.st</key>
		<integer>292</integer>
		<key>mitzeta</key>
		<integer>308</integer>
		<key>mitzeta.sst</key>
		<integer>370</integer>
		<key>mitzeta.st</key>
		<integer>370</integer>
		<key>mscrA</key>
		<integer>734</integer>
		<key>mscrB</key>
		<integer>684</integer>
		<key>mscrC</key>
		<integer>600</integer>
		<key>mscrD</key>
		<integer>564</integer>
		<key>mscrE</key>
		<integer>452</integer>
		<key>mscrF</key>
		<integer>632</integer>
		<key>mscrG</key>
		<integer>526</integer>
		<key>mscrH</key>
		<integer>684</integer>
		<key>mscrI</key>
		<integer>764</integer>
		<key>mscrJ</key>
		<integer>710</integer>
		<key>mscrK</key>
		<integer>752</integer>
		<key>mscrL</key>
		<integer>708</integer>
		<key>mscrM</key>
		<integer>844</integer>
		<key>mscrN</key>
		<integer>805</integer>
		<key>mscrO</key>
		<integer>528</integer>
		<key>mscrP</key>
		<integer>678</integer>
		<key>mscrQ</key>
		<integer>528</integer>
		<key>mscrR</key>
		<integer>710</integer>
		<key>mscrS</key>
		<integer>778</integer>
		<key>mscrT</key>
		<integer>662</integer>
		<key>mscrU</key>
		<integer>594</integer>
		<key>mscrV</key>
		<integer>626</integer>
		<key>mscrW</key>
		<integer>646</integer>
		<key>mscrX</key>
		<integer>672</integer>
		<key>mscrY</key>
		<integer>578</integer>
		<key>mscrZ</key>
		<integer>658</integer>
		<key>mupAlpha.sst</key>
		<integer>356</integer>
		<key>mupDigamma</key>
		<integer>370</integer>
		<key>mupL.sst</key>
		<integer>239</integer>
		<key>mupL.st</key>
		<integer>269</integer>
		<key>mupalpha</key>
		<integer>256</integer>
		<key>mupalpha.sst</key>
		<integer>294</integer>
		<key>mupalpha.st</key>
		<integer>294</integer>
		<key>mupb</key>
		<integer>197</integer>
		<key>mupb.sst</key>
		<integer>197</integer>
		<key>mupb.st</key>
		<integer>197</integer>
		<key>mupbeta</key>
		<integer>226</integer>
		<key>mupbeta.sst</key>
		<integer>268</integer>
		<key>mupbeta.st</key>
		<integer>268</integer>
		<key>mupchi</key>
		<integer>282</integer>
		<key>mupchi.sst</key>
		<integer>312</integer>
		<key>mupchi.st</key>
		<integer>312</integer>
		<key>mupd</key>
		<integer>355</integer>
		<key>mupd.sst</key>
		<integer>355</integer>
		<key>mupd.st</key>
		<integer>355</integer>
		<key>mupdelta</key>
		<integer>217</integer>
		<key>mupdelta.sst</key>
		<integer>261</integer>
		<key>mupdelta.st</key>
		<integer>261</integer>
		<key>mupepsilon</key>
		<integer>226</integer>
		<key>mupepsilon.sst</key>
		<integer>272</integer>
		<key>mupepsilon.st</key>
		<integer>272</integer>
		<key>mupeta</key>
		<integer>258</integer>
		<key>mupeta.sst</key>
		<integer>300</integer>
		<key>mupeta.st</key>
		<integer>300</integer>
		<key>mupf</key>
		<integer>239</integer>
		<key>mupf.sst</key>
		<integer>239</integer>
		<key>mupf.st</key>
		<integer>239</integer>
		<key>mupgamma</key>
		<integer>308</integer>
		<key>mupgamma.sst</key>
		<integer>358</integer>
		<key>mupgamma.st</key>
		<integer>358</integer>
		<key>muph</key>
		<integer>200</integer>
		<key>muph.sst</key>
		<integer>200</integer>
		<key>muph.st</key>
		<integer>200</integer>
		<key>mupi</key>
		<integer>144</integer>
		<key>mupi.sst</key>
		<integer>144</integer>
		<key>mupi.st</key>
		<integer>144</integer>
		<key>mupiota</key>
		<integer>106</integer>
		<key>mupiota.sst</key>
		<integer>142</integer>
		<key>mupiota.st</key>
		<integer>142</integer>
		<key>mupj</key>
		<integer>119</integer>
		<key>mupj.sst</key>
		<integer>119</integer>
		<key>mupj.st</key>
		<integer>119</integer>
		<key>mupk</key>
		<integer>211</integer>
		<key>mupk.sst</key>
		<integer>211</integer>
		<key>mupk.st</key>
		<integer>211</integer>
		<key>mupkappa</key>
		<integer>274</integer>
		<key>mupkappa.sst</key>
		<integer>304</integer>
		<key>mupkappa.st</key>
		<integer>304</integer>
		<key>mupl.sst</key>
		<integer>162</integer>
		<key>mupl.st</key>
		<integer>162</integer>
		<key>muplambda</key>
		<integer>228</integer>
		<key>muplambda.sst</key>
		<integer>258</integer>
		<key>muplambda.st</key>
		<integer>258</integer>
		<key>mupmu</key>
		<integer>268</integer>
		<key>mupmu.sst</key>
		<integer>294</integer>
		<key>mupmu.st</key>
		<integer>294</integer>
		<key>mupnu</key>
		<integer>275</integer>
		<key>mupnu.sst</key>
		<integer>305</integer>
		<key>mupnu.st</key>
		<integer>305</integer>
		<key>mupomega</key>
		<integer>354</integer>
		<key>mupomega.sst</key>
		<integer>374</integer>
		<key>mupomega.st</key>
		<integer>374</integer>
		<key>mupomicron</key>
		<integer>266</integer>
		<key>mupomicron.sst</key>
		<integer>256</integer>
		<key>mupomicron.st</key>
		<integer>256</integer>
		<key>mupphi</key>
		<integer>318</integer>
		<key>mupphi.sst</key>
		<integer>338</integer>
		<key>mupphi.st</key>
		<integer>338</integer>
		<key>muppi</key>
		<integer>345</integer>
		<key>muppi.sst</key>
		<integer>345</integer>
		<key>muppi.st</key>
		<integer>345</integer>
		<key>muppsi</key>
		<integer>332</integer>
		<key>muppsi.sst</key>
		<integer>362</integer>
		<key>muppsi.st</key>
		<integer>362</integer>
		<key>muprho</key>
		<integer>252</integer>
		<key>muprho.sst</key>
		<integer>282</integer>
		<key>muprho.st</key>
		<integer>282</integer>
		<key>mupsigma</key>
		<integer>266</integer>
		<key>mupsigma.sst</key>
		<integer>296</integer>
		<key>mupsigma.st</key>
		<integer>296</integer>
		<key>muptau</key>
		<integer>258</integer>
		<key>muptau.sst</key>
		<integer>268</integer>
		<key>muptau.st</key>
		<integer>268</integer>
		<key>muptheta</key>
		<integer>311</integer>
		<key>muptheta.sst</key>
		<integer>323</integer>
		<key>muptheta.st</key>
		<integer>323</integer>
		<key>mupupsilon</key>
		<integer>256</integer>
		<key>mupupsilon.sst</key>
		<integer>286</integer>
		<key>mupupsilon.st</key>
		<integer>286</integer>
		<key>mupvarTheta</key>
		<integer>389</integer>
		<key>mupvarTheta.sst</key>
		<integer>383</integer>
		<key>mupvarTheta.st</key>
		<integer>383</integer>
		<key>mupvarepsilon</key>
		<integer>205</integer>
		<key>mupvarepsilon.sst</key>
		<integer>233</integer>
		<key>mupvarepsilon.st</key>
		<integer>233</integer>
		<key>mupvarkappa</key>
		<integer>395</integer>
		<key>mupvarkappa.sst</key>
		<integer>376</integer>
		<key>mupvarkappa.st</key>
		<integer>376</integer>
		<key>mupvarphi</key>
		<integer>332</integer>
		<key>mupvarphi.sst</key>
		<integer>354</integer>
		<key>mupvarphi.st</key>
		<integer>354</integer>
		<key>mupvarpi</key>
		<integer>344</integer>
		<key>mupvarpi.sst</key>
		<integer>364</integer>
		<key>mupvarpi.st</key>
		<integer>364</integer>
		<key>mupvarrho</key>
		<integer>241</integer>
		<key>mupvarrho.sst</key>
		<integer>278</integer>
		<key>mupvarrho.st</key>
		<integer>278</integer>
		<key>mupvarsigma</key>
		<integer>232</integer>
		<key>mupvarsigma.sst</key>
		<integer>262</integer>
		<key>mupvarsigma.st</key>
		<integer>262</integer>
		<key>mupvartheta</key>
		<integer>266</integer>
		<key>mupvartheta.sst</key>
		<integer>306</integer>
		<key>mupvartheta.st</key>
		<integer>306</integer>
		<key>mupxi</key>
		<integer>247</integer>
		<key>mupxi.sst</key>
		<integer>277</integer>
		<key>mupxi.st</key>
		<integer>277</integer>
		<key>mupzeta</key>
		<integer>232</integer>
		<key>mupzeta.sst</key>
		<integer>276</integer>
		<key>mupzeta.st</key>
		<integer>276</integer>
		<key>ocirc</key>
		<integer>-220</integer>
		<key>overleftarrow</key>
		<integer>-268</integer>
		<key>overleftharpoon</key>
		<integer>-268</integer>
		<key>overleftrightarrow</key>
		<integer>-335</integer>
		<key>overrightarc</key>
		<integer>-275</integer>
		<key>overrightarrow</key>
		<integer>-268</integer>
		<key>overrightharpoon</key>
		<integer>-268</integer>
		<key>widebreve</key>
		<integer>-220</integer>
		<key>widebreve.h0</key>
		<integer>-120</integer>
		<key>widecheck</key>
		<integer>-220</integer>
		<key>widecheck.h0</key>
		<integer>-120</integer>
		<key>widehat</key>
		<integer>-220</integer>
		<key>widehat.h0</key>
		<integer>-120</integer>
		<key>wideoverbar</key>
		<integer>-240</integer>
		<key>widetilde</key>
		<integer>-220</integer>
		<key>widetilde.h0</key>
		<integer>-120</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>450</integer>
		<key>AxisHeight</key>
		<integer>250</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1300</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1500</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>650</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>686</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>50</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>345</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>677</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>50</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>394</integer>
		<key>FractionRuleThickness</key>
		<integer>50</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>600</integer>
		<key>LowerLimitGapMin</key>
		<integer>167</integer>
		<key>MathLeading</key>
		<integer>160</integer>
		<key>MinConnectorOverlap</key>
		<integer>20</integer>
		<key>OverbarExtraAscender</key>
		<integer>50</integer>
		<key>OverbarRuleThickness</key>
		<integer>50</integer>
		<key>OverbarVerticalGap</key>
		<integer>150</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>60</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>170</integer>
		<key>RadicalExtraAscender</key>
		<integer>50</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-600</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>300</integer>
		<key>RadicalRuleThickness</key>
		<integer>50</integer>
		<key>RadicalVerticalGap</key>
		<integer>62</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>70</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>55</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>350</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>96</integer>
		<key>SpaceAfterScript</key>
		<integer>40</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>686</integer>
		<key>StackBottomShiftDown</key>
		<integer>345</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>350</integer>
		<key>StackGapMin</key>
		<integer>150</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>677</integer>
		<key>StackTopShiftUp</key>
		<integer>444</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>600</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>111</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>167</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>200</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>170</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>100</integer>
		<key>SubscriptShiftDown</key>
		<integer>210</integer>
		<key>SubscriptTopMax</key>
		<integer>368</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>230</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>374</integer>
		<key>SuperscriptBottomMin</key>
		<integer>120</integer>
		<key>SuperscriptShiftUp</key>
		<integer>450</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>320</integer>
		<key>UnderbarExtraDescender</key>
		<integer>50</integer>
		<key>UnderbarRuleThickness</key>
		<integer>50</integer>
		<key>UnderbarVerticalGap</key>
		<integer>150</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>200</integer>
		<key>UpperLimitGapMin</key>
		<integer>111</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>Leftarrow</key>
		<array>
			<string>Leftarrow</string>
			<string>Longleftarrow</string>
		</array>
		<key>Leftrightarrow</key>
		<array>
			<string>Leftrightarrow</string>
			<string>Longleftrightarrow</string>
		</array>
		<key>Mapsfrom</key>
		<array>
			<string>Mapsfrom</string>
			<string>Longmapsfrom</string>
		</array>
		<key>Mapsto</key>
		<array>
			<string>Mapsto</string>
			<string>Longmapsto</string>
		</array>
		<key>Rightarrow</key>
		<array>
			<string>Rightarrow</string>
			<string>Longrightarrow</string>
		</array>
		<key>equal</key>
		<array/>
		<key>equiv</key>
		<array/>
		<key>hookleftarrow</key>
		<array/>
		<key>hookrightarrow</key>
		<array/>
		<key>leftarrow</key>
		<array>
			<string>leftarrow</string>
			<string>longleftarrow</string>
		</array>
		<key>leftarrowtail</key>
		<array/>
		<key>leftharpoondown</key>
		<array/>
		<key>leftharpoonup</key>
		<array/>
		<key>leftrightarrow</key>
		<array>
			<string>leftrightarrow</string>
			<string>longleftrightarrow</string>
		</array>
		<key>leftrightarrows</key>
		<array/>
		<key>leftrightharpoons</key>
		<array/>
		<key>mapsfrom</key>
		<array>
			<string>mapsfrom</string>
			<string>longmapsfrom</string>
		</array>
		<key>mapsto</key>
		<array>
			<string>mapsto</string>
			<string>longmapsto</string>
		</array>
		<key>mathunderbar</key>
		<array/>
		<key>minus</key>
		<array/>
		<key>overbrace</key>
		<array>
			<string>overbrace</string>
			<string>overbrace.h1</string>
			<string>overbrace.h2</string>
			<string>overbrace.h3</string>
			<string>overbrace.h4</string>
		</array>
		<key>overbracket</key>
		<array>
			<string>overbracket</string>
			<string>overbracket.h1</string>
			<string>overbracket.h2</string>
			<string>overbracket.h3</string>
		</array>
		<key>overleftarrow</key>
		<array/>
		<key>overleftharpoon</key>
		<array/>
		<key>overleftrightarrow</key>
		<array>
			<string>overleftrightarrow.h1</string>
		</array>
		<key>overparen</key>
		<array>
			<string>overparen</string>
			<string>overparen.h1</string>
			<string>overparen.h2</string>
			<string>overparen.h3</string>
			<string>overparen.h4</string>
			<string>overparen.h5</string>
			<string>overparen.h6</string>
		</array>
		<key>overrightarc</key>
		<array>
			<string>overrightarc.h1</string>
			<string>overrightarc.h2</string>
			<string>overrightarc.h3</string>
			<string>overrightarc.h4</string>
			<string>overrightarc.h5</string>
			<string>overrightarc.h6</string>
		</array>
		<key>overrightarrow</key>
		<array/>
		<key>overrightharpoon</key>
		<array/>
		<key>rightarrow</key>
		<array>
			<string>rightarrow</string>
			<string>longrightarrow</string>
		</array>
		<key>rightarrowtail</key>
		<array/>
		<key>rightharpoondown</key>
		<array/>
		<key>rightharpoonup</key>
		<array/>
		<key>rightleftarrows</key>
		<array/>
		<key>rightleftharpoons</key>
		<array/>
		<key>twoheadleftarrow</key>
		<array/>
		<key>twoheadrightarrow</key>
		<array/>
		<key>underbrace</key>
		<array>
			<string>underbrace</string>
			<string>underbrace.h1</string>
			<string>underbrace.h2</string>
			<string>underbrace.h3</string>
			<string>underbrace.h4</string>
		</array>
		<key>underbracket</key>
		<array>
			<string>underbracket</string>
			<string>underbracket.h1</string>
			<string>underbracket.h2</string>
			<string>underbracket.h3</string>
		</array>
		<key>underleftarrow</key>
		<array/>
		<key>underleftharpoondown</key>
		<array/>
		<key>underleftrightarrow</key>
		<array>
			<string>underleftrightarrow</string>
			<string>underleftrightarrow.h1</string>
		</array>
		<key>underparen</key>
		<array>
			<string>underparen</string>
			<string>underparen.h1</string>
			<string>underparen.h2</string>
			<string>underparen.h3</string>
			<string>underparen.h4</string>
			<string>underparen.h5</string>
			<string>underparen.h6</string>
		</array>
		<key>underrightarrow</key>
		<array/>
		<key>underrightharpoondown</key>
		<array/>
		<key>widearc</key>
		<array>
			<string>widearc</string>
			<string>widearc.h1</string>
			<string>widearc.h2</string>
			<string>widearc.h3</string>
			<string>widearc.h4</string>
			<string>widearc.h5</string>
			<string>widearc.h6</string>
		</array>
		<key>widebreve</key>
		<array>
			<string>widebreve</string>
			<string>widebreve.h0</string>
			<string>widebreve.h1</string>
			<string>widebreve.h2</string>
			<string>widebreve.h3</string>
			<string>widebreve.h4</string>
			<string>widebreve.h5</string>
			<string>widebreve.h6</string>
		</array>
		<key>widecheck</key>
		<array>
			<string>widecheck</string>
			<string>widecheck.h0</string>
			<string>widecheck.h1</string>
			<string>widecheck.h2</string>
			<string>widecheck.h3</string>
			<string>widecheck.h4</string>
			<string>widecheck.h5</string>
			<string>widecheck.h6</string>
		</array>
		<key>widehat</key>
		<array>
			<string>widehat</string>
			<string>widehat.h0</string>
			<string>widehat.h1</string>
			<string>widehat.h2</string>
			<string>widehat.h3</string>
			<string>widehat.h4</string>
			<string>widehat.h5</string>
			<string>widehat.h6</string>
		</array>
		<key>wideoverbar</key>
		<array/>
		<key>widetilde</key>
		<array>
			<string>widetilde</string>
			<string>widetilde.h0</string>
			<string>widetilde.h1</string>
			<string>widetilde.h2</string>
			<string>widetilde.h3</string>
			<string>widetilde.h4</string>
			<string>widetilde.h5</string>
			<string>widetilde.h6</string>
		</array>
		<key>wideutilde</key>
		<array>
			<string>wideutilde</string>
			<string>wideutilde.h1</string>
			<string>wideutilde.h2</string>
			<string>wideutilde.h3</string>
			<string>wideutilde.h4</string>
			<string>wideutilde.h5</string>
			<string>wideutilde.h6</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>Planckconst</key>
		<integer>34</integer>
		<key>Planckconst.sst</key>
		<integer>34</integer>
		<key>Planckconst.st</key>
		<integer>34</integer>
		<key>awint</key>
		<integer>150</integer>
		<key>awint.v1</key>
		<integer>340</integer>
		<key>ell</key>
		<integer>78</integer>
		<key>fint</key>
		<integer>200</integer>
		<key>fint.v1</key>
		<integer>350</integer>
		<key>hslash</key>
		<integer>34</integer>
		<key>hslash.sst</key>
		<integer>34</integer>
		<key>hslash.st</key>
		<integer>34</integer>
		<key>idotsint</key>
		<integer>160</integer>
		<key>idotsint.cnd</key>
		<integer>200</integer>
		<key>idotsint.v1</key>
		<integer>350</integer>
		<key>idotsint.v1.cnd</key>
		<integer>350</integer>
		<key>iiiint</key>
		<integer>150</integer>
		<key>iiiint.cnd</key>
		<integer>200</integer>
		<key>iiiint.v1</key>
		<integer>350</integer>
		<key>iiiint.v1.cnd</key>
		<integer>350</integer>
		<key>iiint</key>
		<integer>150</integer>
		<key>iiint.cnd</key>
		<integer>200</integer>
		<key>iiint.v1</key>
		<integer>350</integer>
		<key>iiint.v1.cnd</key>
		<integer>350</integer>
		<key>iint</key>
		<integer>150</integer>
		<key>iint.cnd</key>
		<integer>200</integer>
		<key>iint.v1</key>
		<integer>350</integer>
		<key>iint.v1.cnd</key>
		<integer>350</integer>
		<key>imath</key>
		<integer>26</integer>
		<key>int</key>
		<integer>200</integer>
		<key>int.v1</key>
		<integer>350</integer>
		<key>intclockwise</key>
		<integer>150</integer>
		<key>intclockwise.v1</key>
		<integer>340</integer>
		<key>jmath</key>
		<integer>32</integer>
		<key>mbfcalB</key>
		<integer>42</integer>
		<key>mbfcalC</key>
		<integer>42</integer>
		<key>mbfcalD</key>
		<integer>42</integer>
		<key>mbfcalE</key>
		<integer>42</integer>
		<key>mbfcalF</key>
		<integer>42</integer>
		<key>mbfcalG</key>
		<integer>42</integer>
		<key>mbfcalI</key>
		<integer>42</integer>
		<key>mbfcalJ</key>
		<integer>42</integer>
		<key>mbfcalN</key>
		<integer>42</integer>
		<key>mbfcalO</key>
		<integer>42</integer>
		<key>mbfcalP</key>
		<integer>42</integer>
		<key>mbfcalS</key>
		<integer>42</integer>
		<key>mbfcalT</key>
		<integer>42</integer>
		<key>mbfcalU</key>
		<integer>42</integer>
		<key>mbfcalV</key>
		<integer>42</integer>
		<key>mbfcalW</key>
		<integer>42</integer>
		<key>mbfcalX</key>
		<integer>42</integer>
		<key>mbfcalY</key>
		<integer>42</integer>
		<key>mbfcalZ</key>
		<integer>42</integer>
		<key>mbfitA</key>
		<integer>12</integer>
		<key>mbfitA.sst</key>
		<integer>12</integer>
		<key>mbfitA.st</key>
		<integer>12</integer>
		<key>mbfitAlpha</key>
		<integer>42</integer>
		<key>mbfitAlpha.sst</key>
		<integer>42</integer>
		<key>mbfitAlpha.st</key>
		<integer>42</integer>
		<key>mbfitB</key>
		<integer>12</integer>
		<key>mbfitB.sst</key>
		<integer>12</integer>
		<key>mbfitB.st</key>
		<integer>12</integer>
		<key>mbfitBeta</key>
		<integer>32</integer>
		<key>mbfitBeta.sst</key>
		<integer>32</integer>
		<key>mbfitBeta.st</key>
		<integer>32</integer>
		<key>mbfitC</key>
		<integer>62</integer>
		<key>mbfitC.sst</key>
		<integer>62</integer>
		<key>mbfitC.st</key>
		<integer>62</integer>
		<key>mbfitChi</key>
		<integer>60</integer>
		<key>mbfitChi.sst</key>
		<integer>60</integer>
		<key>mbfitChi.st</key>
		<integer>60</integer>
		<key>mbfitD</key>
		<integer>22</integer>
		<key>mbfitD.sst</key>
		<integer>22</integer>
		<key>mbfitD.st</key>
		<integer>22</integer>
		<key>mbfitDelta</key>
		<integer>32</integer>
		<key>mbfitDelta.sst</key>
		<integer>32</integer>
		<key>mbfitDelta.st</key>
		<integer>32</integer>
		<key>mbfitE</key>
		<integer>46</integer>
		<key>mbfitE.sst</key>
		<integer>46</integer>
		<key>mbfitE.st</key>
		<integer>46</integer>
		<key>mbfitEpsilon</key>
		<integer>46</integer>
		<key>mbfitEpsilon.sst</key>
		<integer>46</integer>
		<key>mbfitEpsilon.st</key>
		<integer>46</integer>
		<key>mbfitEta</key>
		<integer>62</integer>
		<key>mbfitEta.sst</key>
		<integer>62</integer>
		<key>mbfitEta.st</key>
		<integer>62</integer>
		<key>mbfitF</key>
		<integer>82</integer>
		<key>mbfitF.sst</key>
		<integer>82</integer>
		<key>mbfitF.st</key>
		<integer>82</integer>
		<key>mbfitG</key>
		<integer>30</integer>
		<key>mbfitG.sst</key>
		<integer>30</integer>
		<key>mbfitG.st</key>
		<integer>30</integer>
		<key>mbfitGamma</key>
		<integer>44</integer>
		<key>mbfitGamma.sst</key>
		<integer>44</integer>
		<key>mbfitGamma.st</key>
		<integer>44</integer>
		<key>mbfitH</key>
		<integer>94</integer>
		<key>mbfitH.sst</key>
		<integer>94</integer>
		<key>mbfitH.st</key>
		<integer>94</integer>
		<key>mbfitI</key>
		<integer>80</integer>
		<key>mbfitI.sst</key>
		<integer>80</integer>
		<key>mbfitI.st</key>
		<integer>80</integer>
		<key>mbfitIota</key>
		<integer>60</integer>
		<key>mbfitIota.sst</key>
		<integer>60</integer>
		<key>mbfitIota.st</key>
		<integer>60</integer>
		<key>mbfitJ</key>
		<integer>88</integer>
		<key>mbfitJ.sst</key>
		<integer>88</integer>
		<key>mbfitJ.st</key>
		<integer>88</integer>
		<key>mbfitK</key>
		<integer>82</integer>
		<key>mbfitK.sst</key>
		<integer>82</integer>
		<key>mbfitK.st</key>
		<integer>82</integer>
		<key>mbfitKappa</key>
		<integer>62</integer>
		<key>mbfitKappa.sst</key>
		<integer>62</integer>
		<key>mbfitKappa.st</key>
		<integer>62</integer>
		<key>mbfitL</key>
		<integer>24</integer>
		<key>mbfitL.sst</key>
		<integer>24</integer>
		<key>mbfitL.st</key>
		<integer>24</integer>
		<key>mbfitLambda</key>
		<integer>52</integer>
		<key>mbfitLambda.sst</key>
		<integer>52</integer>
		<key>mbfitLambda.st</key>
		<integer>52</integer>
		<key>mbfitM</key>
		<integer>84</integer>
		<key>mbfitM.sst</key>
		<integer>84</integer>
		<key>mbfitM.st</key>
		<integer>84</integer>
		<key>mbfitMu</key>
		<integer>64</integer>
		<key>mbfitMu.sst</key>
		<integer>64</integer>
		<key>mbfitMu.st</key>
		<integer>64</integer>
		<key>mbfitN</key>
		<integer>104</integer>
		<key>mbfitN.sst</key>
		<integer>104</integer>
		<key>mbfitN.st</key>
		<integer>104</integer>
		<key>mbfitNu</key>
		<integer>64</integer>
		<key>mbfitNu.sst</key>
		<integer>64</integer>
		<key>mbfitNu.st</key>
		<integer>64</integer>
		<key>mbfitO</key>
		<integer>18</integer>
		<key>mbfitO.sst</key>
		<integer>18</integer>
		<key>mbfitO.st</key>
		<integer>18</integer>
		<key>mbfitOmega</key>
		<integer>40</integer>
		<key>mbfitOmega.sst</key>
		<integer>40</integer>
		<key>mbfitOmega.st</key>
		<integer>40</integer>
		<key>mbfitOmicron</key>
		<integer>18</integer>
		<key>mbfitOmicron.sst</key>
		<integer>18</integer>
		<key>mbfitOmicron.st</key>
		<integer>18</integer>
		<key>mbfitP</key>
		<integer>56</integer>
		<key>mbfitP.sst</key>
		<integer>56</integer>
		<key>mbfitP.st</key>
		<integer>56</integer>
		<key>mbfitPhi</key>
		<integer>38</integer>
		<key>mbfitPhi.sst</key>
		<integer>38</integer>
		<key>mbfitPhi.st</key>
		<integer>38</integer>
		<key>mbfitPi</key>
		<integer>60</integer>
		<key>mbfitPi.sst</key>
		<integer>60</integer>
		<key>mbfitPi.st</key>
		<integer>60</integer>
		<key>mbfitPsi</key>
		<integer>42</integer>
		<key>mbfitPsi.sst</key>
		<integer>42</integer>
		<key>mbfitPsi.st</key>
		<integer>42</integer>
		<key>mbfitQ</key>
		<integer>18</integer>
		<key>mbfitQ.sst</key>
		<integer>18</integer>
		<key>mbfitQ.st</key>
		<integer>18</integer>
		<key>mbfitR</key>
		<integer>14</integer>
		<key>mbfitR.sst</key>
		<integer>14</integer>
		<key>mbfitR.st</key>
		<integer>14</integer>
		<key>mbfitRho</key>
		<integer>34</integer>
		<key>mbfitRho.sst</key>
		<integer>34</integer>
		<key>mbfitRho.st</key>
		<integer>34</integer>
		<key>mbfitS</key>
		<integer>18</integer>
		<key>mbfitS.sst</key>
		<integer>18</integer>
		<key>mbfitS.st</key>
		<integer>18</integer>
		<key>mbfitSigma</key>
		<integer>52</integer>
		<key>mbfitSigma.sst</key>
		<integer>52</integer>
		<key>mbfitSigma.st</key>
		<integer>52</integer>
		<key>mbfitT</key>
		<integer>98</integer>
		<key>mbfitT.sst</key>
		<integer>98</integer>
		<key>mbfitT.st</key>
		<integer>98</integer>
		<key>mbfitTau</key>
		<integer>38</integer>
		<key>mbfitTau.sst</key>
		<integer>38</integer>
		<key>mbfitTau.st</key>
		<integer>38</integer>
		<key>mbfitTheta</key>
		<integer>14</integer>
		<key>mbfitTheta.sst</key>
		<integer>14</integer>
		<key>mbfitTheta.st</key>
		<integer>14</integer>
		<key>mbfitU</key>
		<integer>60</integer>
		<key>mbfitU.sst</key>
		<integer>60</integer>
		<key>mbfitU.st</key>
		<integer>60</integer>
		<key>mbfitUpsilon</key>
		<integer>56</integer>
		<key>mbfitUpsilon.sst</key>
		<integer>56</integer>
		<key>mbfitUpsilon.st</key>
		<integer>56</integer>
		<key>mbfitV</key>
		<integer>126</integer>
		<key>mbfitV.sst</key>
		<integer>126</integer>
		<key>mbfitV.st</key>
		<integer>126</integer>
		<key>mbfitW</key>
		<integer>124</integer>
		<key>mbfitW.sst</key>
		<integer>124</integer>
		<key>mbfitW.st</key>
		<integer>124</integer>
		<key>mbfitX</key>
		<integer>96</integer>
		<key>mbfitX.sst</key>
		<integer>96</integer>
		<key>mbfitX.st</key>
		<integer>96</integer>
		<key>mbfitXi</key>
		<integer>44</integer>
		<key>mbfitXi.sst</key>
		<integer>44</integer>
		<key>mbfitXi.st</key>
		<integer>44</integer>
		<key>mbfitY</key>
		<integer>74</integer>
		<key>mbfitY.sst</key>
		<integer>74</integer>
		<key>mbfitY.st</key>
		<integer>74</integer>
		<key>mbfitZ</key>
		<integer>116</integer>
		<key>mbfitZ.sst</key>
		<integer>116</integer>
		<key>mbfitZ.st</key>
		<integer>116</integer>
		<key>mbfitZeta</key>
		<integer>46</integer>
		<key>mbfitZeta.sst</key>
		<integer>46</integer>
		<key>mbfitZeta.st</key>
		<integer>46</integer>
		<key>mbfita</key>
		<integer>18</integer>
		<key>mbfita.sst</key>
		<integer>18</integer>
		<key>mbfita.st</key>
		<integer>18</integer>
		<key>mbfitalpha</key>
		<integer>22</integer>
		<key>mbfitalpha.sst</key>
		<integer>22</integer>
		<key>mbfitalpha.st</key>
		<integer>22</integer>
		<key>mbfitb</key>
		<integer>12</integer>
		<key>mbfitb.sst</key>
		<integer>12</integer>
		<key>mbfitb.st</key>
		<integer>12</integer>
		<key>mbfitbeta</key>
		<integer>24</integer>
		<key>mbfitbeta.sst</key>
		<integer>24</integer>
		<key>mbfitbeta.st</key>
		<integer>24</integer>
		<key>mbfitc</key>
		<integer>40</integer>
		<key>mbfitc.sst</key>
		<integer>40</integer>
		<key>mbfitc.st</key>
		<integer>40</integer>
		<key>mbfitchi</key>
		<integer>42</integer>
		<key>mbfitchi.sst</key>
		<integer>42</integer>
		<key>mbfitchi.st</key>
		<integer>42</integer>
		<key>mbfitd</key>
		<integer>42</integer>
		<key>mbfitd.sst</key>
		<integer>42</integer>
		<key>mbfitd.st</key>
		<integer>42</integer>
		<key>mbfitdelta</key>
		<integer>20</integer>
		<key>mbfitdelta.sst</key>
		<integer>20</integer>
		<key>mbfitdelta.st</key>
		<integer>20</integer>
		<key>mbfite</key>
		<integer>34</integer>
		<key>mbfite.sst</key>
		<integer>34</integer>
		<key>mbfite.st</key>
		<integer>34</integer>
		<key>mbfitepsilon</key>
		<integer>38</integer>
		<key>mbfitepsilon.sst</key>
		<integer>38</integer>
		<key>mbfitepsilon.st</key>
		<integer>38</integer>
		<key>mbfiteta</key>
		<integer>20</integer>
		<key>mbfiteta.sst</key>
		<integer>20</integer>
		<key>mbfiteta.st</key>
		<integer>20</integer>
		<key>mbfitf</key>
		<integer>80</integer>
		<key>mbfitf.sst</key>
		<integer>80</integer>
		<key>mbfitf.st</key>
		<integer>80</integer>
		<key>mbfitg</key>
		<integer>80</integer>
		<key>mbfitg.sst</key>
		<integer>80</integer>
		<key>mbfitg.st</key>
		<integer>80</integer>
		<key>mbfitgamma</key>
		<integer>48</integer>
		<key>mbfitgamma.sst</key>
		<integer>48</integer>
		<key>mbfitgamma.st</key>
		<integer>48</integer>
		<key>mbfith</key>
		<integer>16</integer>
		<key>mbfith.sst</key>
		<integer>16</integer>
		<key>mbfith.st</key>
		<integer>16</integer>
		<key>mbfiti</key>
		<integer>28</integer>
		<key>mbfiti.sst</key>
		<integer>28</integer>
		<key>mbfiti.st</key>
		<integer>28</integer>
		<key>mbfitiota</key>
		<integer>22</integer>
		<key>mbfitiota.sst</key>
		<integer>22</integer>
		<key>mbfitiota.st</key>
		<integer>22</integer>
		<key>mbfitj</key>
		<integer>96</integer>
		<key>mbfitj.sst</key>
		<integer>96</integer>
		<key>mbfitj.st</key>
		<integer>96</integer>
		<key>mbfitk</key>
		<integer>32</integer>
		<key>mbfitk.sst</key>
		<integer>32</integer>
		<key>mbfitk.st</key>
		<integer>32</integer>
		<key>mbfitkappa</key>
		<integer>26</integer>
		<key>mbfitkappa.sst</key>
		<integer>26</integer>
		<key>mbfitkappa.st</key>
		<integer>26</integer>
		<key>mbfitl</key>
		<integer>40</integer>
		<key>mbfitl.sst</key>
		<integer>40</integer>
		<key>mbfitl.st</key>
		<integer>40</integer>
		<key>mbfitlambda</key>
		<integer>34</integer>
		<key>mbfitlambda.sst</key>
		<integer>34</integer>
		<key>mbfitlambda.st</key>
		<integer>34</integer>
		<key>mbfitm</key>
		<integer>18</integer>
		<key>mbfitm.sst</key>
		<integer>18</integer>
		<key>mbfitm.st</key>
		<integer>18</integer>
		<key>mbfitmu</key>
		<integer>36</integer>
		<key>mbfitmu.sst</key>
		<integer>36</integer>
		<key>mbfitmu.st</key>
		<integer>36</integer>
		<key>mbfitn</key>
		<integer>12</integer>
		<key>mbfitn.sst</key>
		<integer>12</integer>
		<key>mbfitn.st</key>
		<integer>12</integer>
		<key>mbfitnabla</key>
		<integer>38</integer>
		<key>mbfitnu</key>
		<integer>26</integer>
		<key>mbfitnu.sst</key>
		<integer>26</integer>
		<key>mbfitnu.st</key>
		<integer>26</integer>
		<key>mbfito</key>
		<integer>22</integer>
		<key>mbfito.sst</key>
		<integer>22</integer>
		<key>mbfito.st</key>
		<integer>22</integer>
		<key>mbfitomega</key>
		<integer>20</integer>
		<key>mbfitomega.sst</key>
		<integer>20</integer>
		<key>mbfitomega.st</key>
		<integer>20</integer>
		<key>mbfitomicron</key>
		<integer>2</integer>
		<key>mbfitomicron.sst</key>
		<integer>2</integer>
		<key>mbfitomicron.st</key>
		<integer>2</integer>
		<key>mbfitp</key>
		<integer>22</integer>
		<key>mbfitp.sst</key>
		<integer>22</integer>
		<key>mbfitp.st</key>
		<integer>22</integer>
		<key>mbfitpartial</key>
		<integer>30</integer>
		<key>mbfitpartial.sst</key>
		<integer>30</integer>
		<key>mbfitpartial.st</key>
		<integer>30</integer>
		<key>mbfitphi</key>
		<integer>20</integer>
		<key>mbfitphi.sst</key>
		<integer>20</integer>
		<key>mbfitphi.st</key>
		<integer>20</integer>
		<key>mbfitpi</key>
		<integer>34</integer>
		<key>mbfitpi.sst</key>
		<integer>34</integer>
		<key>mbfitpi.st</key>
		<integer>34</integer>
		<key>mbfitpsi</key>
		<integer>30</integer>
		<key>mbfitpsi.sst</key>
		<integer>30</integer>
		<key>mbfitpsi.st</key>
		<integer>30</integer>
		<key>mbfitq</key>
		<integer>24</integer>
		<key>mbfitq.sst</key>
		<integer>24</integer>
		<key>mbfitq.st</key>
		<integer>24</integer>
		<key>mbfitr</key>
		<integer>78</integer>
		<key>mbfitr.sst</key>
		<integer>78</integer>
		<key>mbfitr.st</key>
		<integer>78</integer>
		<key>mbfitrho</key>
		<integer>20</integer>
		<key>mbfitrho.sst</key>
		<integer>20</integer>
		<key>mbfitrho.st</key>
		<integer>20</integer>
		<key>mbfits</key>
		<integer>24</integer>
		<key>mbfits.sst</key>
		<integer>24</integer>
		<key>mbfits.st</key>
		<integer>24</integer>
		<key>mbfitsansA</key>
		<integer>-12</integer>
		<key>mbfitsansAlpha</key>
		<integer>48</integer>
		<key>mbfitsansB</key>
		<integer>16</integer>
		<key>mbfitsansBeta</key>
		<integer>16</integer>
		<key>mbfitsansC</key>
		<integer>46</integer>
		<key>mbfitsansChi</key>
		<integer>88</integer>
		<key>mbfitsansD</key>
		<integer>26</integer>
		<key>mbfitsansDelta</key>
		<integer>20</integer>
		<key>mbfitsansE</key>
		<integer>70</integer>
		<key>mbfitsansEpsilon</key>
		<integer>70</integer>
		<key>mbfitsansEta</key>
		<integer>56</integer>
		<key>mbfitsansF</key>
		<integer>118</integer>
		<key>mbfitsansG</key>
		<integer>46</integer>
		<key>mbfitsansGamma</key>
		<integer>144</integer>
		<key>mbfitsansH</key>
		<integer>56</integer>
		<key>mbfitsansI</key>
		<integer>56</integer>
		<key>mbfitsansIota</key>
		<integer>56</integer>
		<key>mbfitsansJ</key>
		<integer>56</integer>
		<key>mbfitsansK</key>
		<integer>66</integer>
		<key>mbfitsansKappa</key>
		<integer>66</integer>
		<key>mbfitsansL</key>
		<integer>-4</integer>
		<key>mbfitsansLambda</key>
		<integer>172</integer>
		<key>mbfitsansM</key>
		<integer>54</integer>
		<key>mbfitsansMu</key>
		<integer>54</integer>
		<key>mbfitsansN</key>
		<integer>58</integer>
		<key>mbfitsansNu</key>
		<integer>58</integer>
		<key>mbfitsansO</key>
		<integer>24</integer>
		<key>mbfitsansOmega</key>
		<integer>180</integer>
		<key>mbfitsansOmicron</key>
		<integer>24</integer>
		<key>mbfitsansP</key>
		<integer>46</integer>
		<key>mbfitsansPhi</key>
		<integer>294</integer>
		<key>mbfitsansPi</key>
		<integer>204</integer>
		<key>mbfitsansPsi</key>
		<integer>288</integer>
		<key>mbfitsansQ</key>
		<integer>44</integer>
		<key>mbfitsansR</key>
		<integer>10</integer>
		<key>mbfitsansRho</key>
		<integer>46</integer>
		<key>mbfitsansS</key>
		<integer>36</integer>
		<key>mbfitsansSigma</key>
		<integer>58</integer>
		<key>mbfitsansT</key>
		<integer>110</integer>
		<key>mbfitsansTau</key>
		<integer>110</integer>
		<key>mbfitsansTheta</key>
		<integer>90</integer>
		<key>mbfitsansU</key>
		<integer>56</integer>
		<key>mbfitsansUpsilon</key>
		<integer>84</integer>
		<key>mbfitsansV</key>
		<integer>110</integer>
		<key>mbfitsansW</key>
		<integer>110</integer>
		<key>mbfitsansX</key>
		<integer>88</integer>
		<key>mbfitsansXi</key>
		<integer>114</integer>
		<key>mbfitsansY</key>
		<integer>106</integer>
		<key>mbfitsansZ</key>
		<integer>88</integer>
		<key>mbfitsansZeta</key>
		<integer>88</integer>
		<key>mbfitsansa</key>
		<integer>2</integer>
		<key>mbfitsansalpha</key>
		<integer>32</integer>
		<key>mbfitsansb</key>
		<integer>12</integer>
		<key>mbfitsansbeta</key>
		<integer>34</integer>
		<key>mbfitsansc</key>
		<integer>38</integer>
		<key>mbfitsanschi</key>
		<integer>28</integer>
		<key>mbfitsansd</key>
		<integer>46</integer>
		<key>mbfitsansdelta</key>
		<integer>30</integer>
		<key>mbfitsanse</key>
		<integer>14</integer>
		<key>mbfitsansepsilon</key>
		<integer>36</integer>
		<key>mbfitsanseta</key>
		<integer>30</integer>
		<key>mbfitsansf</key>
		<integer>106</integer>
		<key>mbfitsansg</key>
		<integer>110</integer>
		<key>mbfitsansgamma</key>
		<integer>78</integer>
		<key>mbfitsansh</key>
		<integer>-16</integer>
		<key>mbfitsansi</key>
		<integer>42</integer>
		<key>mbfitsansiota</key>
		<integer>32</integer>
		<key>mbfitsansj</key>
		<integer>70</integer>
		<key>mbfitsansk</key>
		<integer>52</integer>
		<key>mbfitsanskappa</key>
		<integer>36</integer>
		<key>mbfitsansl</key>
		<integer>14</integer>
		<key>mbfitsanslambda</key>
		<integer>34</integer>
		<key>mbfitsansm</key>
		<integer>8</integer>
		<key>mbfitsansmu</key>
		<integer>36</integer>
		<key>mbfitsansn</key>
		<integer>4</integer>
		<key>mbfitsansnabla</key>
		<integer>32</integer>
		<key>mbfitsansnu</key>
		<integer>54</integer>
		<key>mbfitsanso</key>
		<integer>34</integer>
		<key>mbfitsansomega</key>
		<integer>30</integer>
		<key>mbfitsansomicron</key>
		<integer>34</integer>
		<key>mbfitsansp</key>
		<integer>46</integer>
		<key>mbfitsanspartial</key>
		<integer>30</integer>
		<key>mbfitsansphi</key>
		<integer>30</integer>
		<key>mbfitsanspi</key>
		<integer>34</integer>
		<key>mbfitsanspsi</key>
		<integer>50</integer>
		<key>mbfitsansq</key>
		<integer>66</integer>
		<key>mbfitsansr</key>
		<integer>90</integer>
		<key>mbfitsansrho</key>
		<integer>30</integer>
		<key>mbfitsanss</key>
		<integer>32</integer>
		<key>mbfitsanssigma</key>
		<integer>76</integer>
		<key>mbfitsanst</key>
		<integer>60</integer>
		<key>mbfitsanstau</key>
		<integer>50</integer>
		<key>mbfitsanstheta</key>
		<integer>30</integer>
		<key>mbfitsansu</key>
		<integer>38</integer>
		<key>mbfitsansupsilon</key>
		<integer>28</integer>
		<key>mbfitsansv</key>
		<integer>84</integer>
		<key>mbfitsansvarepsilon</key>
		<integer>36</integer>
		<key>mbfitsansvarphi</key>
		<integer>50</integer>
		<key>mbfitsansvarpi</key>
		<integer>32</integer>
		<key>mbfitsansvarrho</key>
		<integer>30</integer>
		<key>mbfitsansvarsigma</key>
		<integer>32</integer>
		<key>mbfitsansvartheta</key>
		<integer>48</integer>
		<key>mbfitsansw</key>
		<integer>88</integer>
		<key>mbfitsansx</key>
		<integer>84</integer>
		<key>mbfitsansxi</key>
		<integer>34</integer>
		<key>mbfitsansy</key>
		<integer>106</integer>
		<key>mbfitsansz</key>
		<integer>62</integer>
		<key>mbfitsanszeta</key>
		<integer>40</integer>
		<key>mbfitsigma</key>
		<integer>46</integer>
		<key>mbfitsigma.sst</key>
		<integer>46</integer>
		<key>mbfitsigma.st</key>
		<integer>46</integer>
		<key>mbfitt</key>
		<integer>66</integer>
		<key>mbfitt.sst</key>
		<integer>66</integer>
		<key>mbfitt.st</key>
		<integer>66</integer>
		<key>mbfittau</key>
		<integer>26</integer>
		<key>mbfittau.sst</key>
		<integer>26</integer>
		<key>mbfittau.st</key>
		<integer>26</integer>
		<key>mbfittheta</key>
		<integer>20</integer>
		<key>mbfittheta.sst</key>
		<integer>20</integer>
		<key>mbfittheta.st</key>
		<integer>20</integer>
		<key>mbfitu</key>
		<integer>16</integer>
		<key>mbfitu.sst</key>
		<integer>16</integer>
		<key>mbfitu.st</key>
		<integer>16</integer>
		<key>mbfitupsilon</key>
		<integer>18</integer>
		<key>mbfitupsilon.sst</key>
		<integer>18</integer>
		<key>mbfitupsilon.st</key>
		<integer>18</integer>
		<key>mbfitv</key>
		<integer>36</integer>
		<key>mbfitv.sst</key>
		<integer>36</integer>
		<key>mbfitv.st</key>
		<integer>36</integer>
		<key>mbfitvarTheta</key>
		<integer>18</integer>
		<key>mbfitvarTheta.sst</key>
		<integer>18</integer>
		<key>mbfitvarTheta.st</key>
		<integer>18</integer>
		<key>mbfitvarepsilon</key>
		<integer>4</integer>
		<key>mbfitvarepsilon.sst</key>
		<integer>4</integer>
		<key>mbfitvarepsilon.st</key>
		<integer>4</integer>
		<key>mbfitvarkappa</key>
		<integer>56</integer>
		<key>mbfitvarkappa.sst</key>
		<integer>56</integer>
		<key>mbfitvarkappa.st</key>
		<integer>56</integer>
		<key>mbfitvarphi</key>
		<integer>30</integer>
		<key>mbfitvarphi.sst</key>
		<integer>30</integer>
		<key>mbfitvarphi.st</key>
		<integer>30</integer>
		<key>mbfitvarpi</key>
		<integer>22</integer>
		<key>mbfitvarpi.sst</key>
		<integer>22</integer>
		<key>mbfitvarpi.st</key>
		<integer>22</integer>
		<key>mbfitvarrho</key>
		<integer>20</integer>
		<key>mbfitvarrho.sst</key>
		<integer>20</integer>
		<key>mbfitvarrho.st</key>
		<integer>20</integer>
		<key>mbfitvarsigma</key>
		<integer>32</integer>
		<key>mbfitvarsigma.sst</key>
		<integer>32</integer>
		<key>mbfitvarsigma.st</key>
		<integer>32</integer>
		<key>mbfitvartheta</key>
		<integer>28</integer>
		<key>mbfitvartheta.sst</key>
		<integer>28</integer>
		<key>mbfitvartheta.st</key>
		<integer>28</integer>
		<key>mbfitw</key>
		<integer>38</integer>
		<key>mbfitw.sst</key>
		<integer>38</integer>
		<key>mbfitw.st</key>
		<integer>38</integer>
		<key>mbfitx</key>
		<integer>52</integer>
		<key>mbfitx.sst</key>
		<integer>52</integer>
		<key>mbfitx.st</key>
		<integer>52</integer>
		<key>mbfitxi</key>
		<integer>20</integer>
		<key>mbfitxi.sst</key>
		<integer>20</integer>
		<key>mbfitxi.st</key>
		<integer>20</integer>
		<key>mbfity</key>
		<integer>60</integer>
		<key>mbfity.sst</key>
		<integer>60</integer>
		<key>mbfity.st</key>
		<integer>60</integer>
		<key>mbfitz</key>
		<integer>50</integer>
		<key>mbfitz.sst</key>
		<integer>50</integer>
		<key>mbfitz.st</key>
		<integer>50</integer>
		<key>mbfitzeta</key>
		<integer>30</integer>
		<key>mbfitzeta.sst</key>
		<integer>30</integer>
		<key>mbfitzeta.st</key>
		<integer>30</integer>
		<key>mbfscrA</key>
		<integer>42</integer>
		<key>mbfscrB</key>
		<integer>42</integer>
		<key>mbfscrC</key>
		<integer>42</integer>
		<key>mbfscrD</key>
		<integer>42</integer>
		<key>mbfscrE</key>
		<integer>42</integer>
		<key>mbfscrF</key>
		<integer>42</integer>
		<key>mbfscrG</key>
		<integer>42</integer>
		<key>mbfscrH</key>
		<integer>42</integer>
		<key>mbfscrI</key>
		<integer>42</integer>
		<key>mbfscrJ</key>
		<integer>42</integer>
		<key>mbfscrK</key>
		<integer>42</integer>
		<key>mbfscrL</key>
		<integer>42</integer>
		<key>mbfscrM</key>
		<integer>42</integer>
		<key>mbfscrN</key>
		<integer>42</integer>
		<key>mbfscrO</key>
		<integer>42</integer>
		<key>mbfscrP</key>
		<integer>42</integer>
		<key>mbfscrQ</key>
		<integer>42</integer>
		<key>mbfscrR</key>
		<integer>42</integer>
		<key>mbfscrS</key>
		<integer>42</integer>
		<key>mbfscrT</key>
		<integer>42</integer>
		<key>mbfscrU</key>
		<integer>42</integer>
		<key>mbfscrV</key>
		<integer>42</integer>
		<key>mbfscrW</key>
		<integer>42</integer>
		<key>mbfscrX</key>
		<integer>42</integer>
		<key>mbfscrY</key>
		<integer>42</integer>
		<key>mbfscrZ</key>
		<integer>42</integer>
		<key>mcalB</key>
		<integer>42</integer>
		<key>mcalC</key>
		<integer>42</integer>
		<key>mcalD</key>
		<integer>42</integer>
		<key>mcalE</key>
		<integer>42</integer>
		<key>mcalF</key>
		<integer>42</integer>
		<key>mcalG</key>
		<integer>42</integer>
		<key>mcalI</key>
		<integer>42</integer>
		<key>mcalJ</key>
		<integer>42</integer>
		<key>mcalN</key>
		<integer>42</integer>
		<key>mcalO</key>
		<integer>42</integer>
		<key>mcalP</key>
		<integer>42</integer>
		<key>mcalS</key>
		<integer>42</integer>
		<key>mcalT</key>
		<integer>42</integer>
		<key>mcalU</key>
		<integer>42</integer>
		<key>mcalV</key>
		<integer>42</integer>
		<key>mcalW</key>
		<integer>42</integer>
		<key>mcalX</key>
		<integer>42</integer>
		<key>mcalY</key>
		<integer>42</integer>
		<key>mcalZ</key>
		<integer>42</integer>
		<key>mitA</key>
		<integer>26</integer>
		<key>mitA.sst</key>
		<integer>26</integer>
		<key>mitA.st</key>
		<integer>26</integer>
		<key>mitAlpha</key>
		<integer>26</integer>
		<key>mitAlpha.sst</key>
		<integer>26</integer>
		<key>mitAlpha.st</key>
		<integer>26</integer>
		<key>mitB</key>
		<integer>26</integer>
		<key>mitB.sst</key>
		<integer>26</integer>
		<key>mitB.st</key>
		<integer>26</integer>
		<key>mitBeta</key>
		<integer>26</integer>
		<key>mitBeta.sst</key>
		<integer>26</integer>
		<key>mitBeta.st</key>
		<integer>26</integer>
		<key>mitC</key>
		<integer>38</integer>
		<key>mitC.sst</key>
		<integer>38</integer>
		<key>mitC.st</key>
		<integer>38</integer>
		<key>mitChi</key>
		<integer>54</integer>
		<key>mitChi.sst</key>
		<integer>54</integer>
		<key>mitChi.st</key>
		<integer>54</integer>
		<key>mitD</key>
		<integer>30</integer>
		<key>mitD.sst</key>
		<integer>30</integer>
		<key>mitD.st</key>
		<integer>30</integer>
		<key>mitDelta</key>
		<integer>28</integer>
		<key>mitDelta.sst</key>
		<integer>28</integer>
		<key>mitDelta.st</key>
		<integer>28</integer>
		<key>mitE</key>
		<integer>36</integer>
		<key>mitE.sst</key>
		<integer>36</integer>
		<key>mitE.st</key>
		<integer>36</integer>
		<key>mitEpsilon</key>
		<integer>46</integer>
		<key>mitEpsilon.sst</key>
		<integer>46</integer>
		<key>mitEpsilon.st</key>
		<integer>46</integer>
		<key>mitEta</key>
		<integer>50</integer>
		<key>mitEta.sst</key>
		<integer>50</integer>
		<key>mitEta.st</key>
		<integer>50</integer>
		<key>mitF</key>
		<integer>62</integer>
		<key>mitF.sst</key>
		<integer>62</integer>
		<key>mitF.st</key>
		<integer>62</integer>
		<key>mitG</key>
		<integer>48</integer>
		<key>mitG.sst</key>
		<integer>48</integer>
		<key>mitG.st</key>
		<integer>48</integer>
		<key>mitGamma</key>
		<integer>66</integer>
		<key>mitGamma.sst</key>
		<integer>66</integer>
		<key>mitGamma.st</key>
		<integer>66</integer>
		<key>mitH</key>
		<integer>72</integer>
		<key>mitH.sst</key>
		<integer>72</integer>
		<key>mitH.st</key>
		<integer>72</integer>
		<key>mitI</key>
		<integer>66</integer>
		<key>mitI.sst</key>
		<integer>66</integer>
		<key>mitI.st</key>
		<integer>66</integer>
		<key>mitIota</key>
		<integer>58</integer>
		<key>mitIota.sst</key>
		<integer>58</integer>
		<key>mitIota.st</key>
		<integer>58</integer>
		<key>mitJ</key>
		<integer>64</integer>
		<key>mitJ.sst</key>
		<integer>64</integer>
		<key>mitJ.st</key>
		<integer>64</integer>
		<key>mitK</key>
		<integer>68</integer>
		<key>mitK.sst</key>
		<integer>68</integer>
		<key>mitK.st</key>
		<integer>68</integer>
		<key>mitKappa</key>
		<integer>56</integer>
		<key>mitKappa.sst</key>
		<integer>56</integer>
		<key>mitKappa.st</key>
		<integer>56</integer>
		<key>mitL</key>
		<integer>28</integer>
		<key>mitL.sst</key>
		<integer>28</integer>
		<key>mitL.st</key>
		<integer>28</integer>
		<key>mitLambda</key>
		<integer>42</integer>
		<key>mitLambda.sst</key>
		<integer>42</integer>
		<key>mitLambda.st</key>
		<integer>42</integer>
		<key>mitM</key>
		<integer>78</integer>
		<key>mitM.sst</key>
		<integer>78</integer>
		<key>mitM.st</key>
		<integer>78</integer>
		<key>mitMu</key>
		<integer>58</integer>
		<key>mitMu.sst</key>
		<integer>58</integer>
		<key>mitMu.st</key>
		<integer>58</integer>
		<key>mitN</key>
		<integer>70</integer>
		<key>mitN.sst</key>
		<integer>70</integer>
		<key>mitN.st</key>
		<integer>70</integer>
		<key>mitNu</key>
		<integer>52</integer>
		<key>mitNu.sst</key>
		<integer>52</integer>
		<key>mitNu.st</key>
		<integer>52</integer>
		<key>mitO</key>
		<integer>42</integer>
		<key>mitO.sst</key>
		<integer>42</integer>
		<key>mitO.st</key>
		<integer>42</integer>
		<key>mitOmega</key>
		<integer>40</integer>
		<key>mitOmega.sst</key>
		<integer>40</integer>
		<key>mitOmega.st</key>
		<integer>40</integer>
		<key>mitOmicron</key>
		<integer>22</integer>
		<key>mitOmicron.sst</key>
		<integer>22</integer>
		<key>mitOmicron.st</key>
		<integer>22</integer>
		<key>mitP</key>
		<integer>64</integer>
		<key>mitP.sst</key>
		<integer>64</integer>
		<key>mitP.st</key>
		<integer>64</integer>
		<key>mitPhi</key>
		<integer>30</integer>
		<key>mitPhi.sst</key>
		<integer>30</integer>
		<key>mitPhi.st</key>
		<integer>30</integer>
		<key>mitPi</key>
		<integer>50</integer>
		<key>mitPi.sst</key>
		<integer>50</integer>
		<key>mitPi.st</key>
		<integer>50</integer>
		<key>mitPsi</key>
		<integer>52</integer>
		<key>mitPsi.sst</key>
		<integer>52</integer>
		<key>mitPsi.st</key>
		<integer>52</integer>
		<key>mitQ</key>
		<integer>22</integer>
		<key>mitQ.sst</key>
		<integer>22</integer>
		<key>mitQ.st</key>
		<integer>22</integer>
		<key>mitR</key>
		<integer>44</integer>
		<key>mitR.sst</key>
		<integer>44</integer>
		<key>mitR.st</key>
		<integer>44</integer>
		<key>mitRho</key>
		<integer>64</integer>
		<key>mitRho.sst</key>
		<integer>64</integer>
		<key>mitRho.st</key>
		<integer>64</integer>
		<key>mitS</key>
		<integer>32</integer>
		<key>mitS.sst</key>
		<integer>32</integer>
		<key>mitS.st</key>
		<integer>32</integer>
		<key>mitSigma</key>
		<integer>44</integer>
		<key>mitSigma.sst</key>
		<integer>44</integer>
		<key>mitSigma.st</key>
		<integer>44</integer>
		<key>mitT</key>
		<integer>62</integer>
		<key>mitT.sst</key>
		<integer>62</integer>
		<key>mitT.st</key>
		<integer>62</integer>
		<key>mitTau</key>
		<integer>44</integer>
		<key>mitTau.sst</key>
		<integer>44</integer>
		<key>mitTau.st</key>
		<integer>44</integer>
		<key>mitTheta</key>
		<integer>18</integer>
		<key>mitTheta.sst</key>
		<integer>18</integer>
		<key>mitTheta.st</key>
		<integer>18</integer>
		<key>mitU</key>
		<integer>40</integer>
		<key>mitU.sst</key>
		<integer>40</integer>
		<key>mitU.st</key>
		<integer>40</integer>
		<key>mitUpsilon</key>
		<integer>52</integer>
		<key>mitUpsilon.sst</key>
		<integer>52</integer>
		<key>mitUpsilon.st</key>
		<integer>52</integer>
		<key>mitV</key>
		<integer>64</integer>
		<key>mitV.sst</key>
		<integer>64</integer>
		<key>mitV.st</key>
		<integer>64</integer>
		<key>mitW</key>
		<integer>58</integer>
		<key>mitW.sst</key>
		<integer>58</integer>
		<key>mitW.st</key>
		<integer>58</integer>
		<key>mitX</key>
		<integer>62</integer>
		<key>mitX.sst</key>
		<integer>62</integer>
		<key>mitX.st</key>
		<integer>62</integer>
		<key>mitXi</key>
		<integer>48</integer>
		<key>mitXi.sst</key>
		<integer>48</integer>
		<key>mitXi.st</key>
		<integer>48</integer>
		<key>mitY</key>
		<integer>56</integer>
		<key>mitY.sst</key>
		<integer>56</integer>
		<key>mitY.st</key>
		<integer>56</integer>
		<key>mitZ</key>
		<integer>58</integer>
		<key>mitZ.sst</key>
		<integer>58</integer>
		<key>mitZ.st</key>
		<integer>58</integer>
		<key>mitZeta</key>
		<integer>54</integer>
		<key>mitZeta.sst</key>
		<integer>54</integer>
		<key>mitZeta.st</key>
		<integer>54</integer>
		<key>mita</key>
		<integer>36</integer>
		<key>mita.sst</key>
		<integer>36</integer>
		<key>mita.st</key>
		<integer>36</integer>
		<key>mitalpha</key>
		<integer>26</integer>
		<key>mitalpha.sst</key>
		<integer>26</integer>
		<key>mitalpha.st</key>
		<integer>26</integer>
		<key>mitb</key>
		<integer>32</integer>
		<key>mitb.sst</key>
		<integer>32</integer>
		<key>mitb.st</key>
		<integer>32</integer>
		<key>mitbeta</key>
		<integer>30</integer>
		<key>mitbeta.sst</key>
		<integer>30</integer>
		<key>mitbeta.st</key>
		<integer>30</integer>
		<key>mitc</key>
		<integer>48</integer>
		<key>mitc.sst</key>
		<integer>48</integer>
		<key>mitc.st</key>
		<integer>48</integer>
		<key>mitchi</key>
		<integer>38</integer>
		<key>mitchi.sst</key>
		<integer>38</integer>
		<key>mitchi.st</key>
		<integer>38</integer>
		<key>mitd</key>
		<integer>48</integer>
		<key>mitd.sst</key>
		<integer>48</integer>
		<key>mitd.st</key>
		<integer>48</integer>
		<key>mitdelta</key>
		<integer>28</integer>
		<key>mitdelta.sst</key>
		<integer>28</integer>
		<key>mitdelta.st</key>
		<integer>28</integer>
		<key>mite</key>
		<integer>48</integer>
		<key>mite.sst</key>
		<integer>48</integer>
		<key>mite.st</key>
		<integer>48</integer>
		<key>mitepsilon</key>
		<integer>30</integer>
		<key>mitepsilon.sst</key>
		<integer>30</integer>
		<key>mitepsilon.st</key>
		<integer>30</integer>
		<key>miteta</key>
		<integer>20</integer>
		<key>miteta.sst</key>
		<integer>20</integer>
		<key>miteta.st</key>
		<integer>20</integer>
		<key>mitf</key>
		<integer>80</integer>
		<key>mitf.sst</key>
		<integer>80</integer>
		<key>mitf.st</key>
		<integer>80</integer>
		<key>mitg</key>
		<integer>108</integer>
		<key>mitg.sst</key>
		<integer>108</integer>
		<key>mitg.st</key>
		<integer>108</integer>
		<key>mitgamma</key>
		<integer>46</integer>
		<key>mitgamma.sst</key>
		<integer>46</integer>
		<key>mitgamma.st</key>
		<integer>46</integer>
		<key>miti</key>
		<integer>34</integer>
		<key>miti.sst</key>
		<integer>34</integer>
		<key>miti.st</key>
		<integer>34</integer>
		<key>mitiota</key>
		<integer>28</integer>
		<key>mitiota.sst</key>
		<integer>28</integer>
		<key>mitiota.st</key>
		<integer>28</integer>
		<key>mitj</key>
		<integer>72</integer>
		<key>mitj.sst</key>
		<integer>72</integer>
		<key>mitj.st</key>
		<integer>72</integer>
		<key>mitk</key>
		<integer>50</integer>
		<key>mitk.sst</key>
		<integer>50</integer>
		<key>mitk.st</key>
		<integer>50</integer>
		<key>mitkappa</key>
		<integer>32</integer>
		<key>mitkappa.sst</key>
		<integer>32</integer>
		<key>mitkappa.st</key>
		<integer>32</integer>
		<key>mitl</key>
		<integer>44</integer>
		<key>mitl.sst</key>
		<integer>44</integer>
		<key>mitl.st</key>
		<integer>44</integer>
		<key>mitlambda</key>
		<integer>30</integer>
		<key>mitlambda.sst</key>
		<integer>30</integer>
		<key>mitlambda.st</key>
		<integer>30</integer>
		<key>mitm</key>
		<integer>28</integer>
		<key>mitm.sst</key>
		<integer>28</integer>
		<key>mitm.st</key>
		<integer>28</integer>
		<key>mitmu</key>
		<integer>32</integer>
		<key>mitmu.sst</key>
		<integer>32</integer>
		<key>mitmu.st</key>
		<integer>32</integer>
		<key>mitn</key>
		<integer>28</integer>
		<key>mitn.sst</key>
		<integer>28</integer>
		<key>mitn.st</key>
		<integer>28</integer>
		<key>mitnabla</key>
		<integer>20</integer>
		<key>mitnu</key>
		<integer>30</integer>
		<key>mitnu.sst</key>
		<integer>30</integer>
		<key>mitnu.st</key>
		<integer>30</integer>
		<key>mito</key>
		<integer>32</integer>
		<key>mito.sst</key>
		<integer>32</integer>
		<key>mito.st</key>
		<integer>32</integer>
		<key>mitomega</key>
		<integer>28</integer>
		<key>mitomega.sst</key>
		<integer>28</integer>
		<key>mitomega.st</key>
		<integer>28</integer>
		<key>mitomicron</key>
		<integer>22</integer>
		<key>mitomicron.sst</key>
		<integer>22</integer>
		<key>mitomicron.st</key>
		<integer>22</integer>
		<key>mitp</key>
		<integer>42</integer>
		<key>mitp.sst</key>
		<integer>42</integer>
		<key>mitp.st</key>
		<integer>42</integer>
		<key>mitpartial</key>
		<integer>38</integer>
		<key>mitpartial.sst</key>
		<integer>38</integer>
		<key>mitpartial.st</key>
		<integer>38</integer>
		<key>mitphi</key>
		<integer>22</integer>
		<key>mitphi.sst</key>
		<integer>22</integer>
		<key>mitphi.st</key>
		<integer>22</integer>
		<key>mitpi</key>
		<integer>26</integer>
		<key>mitpi.sst</key>
		<integer>26</integer>
		<key>mitpi.st</key>
		<integer>26</integer>
		<key>mitpsi</key>
		<integer>26</integer>
		<key>mitpsi.sst</key>
		<integer>26</integer>
		<key>mitpsi.st</key>
		<integer>26</integer>
		<key>mitq</key>
		<integer>30</integer>
		<key>mitq.sst</key>
		<integer>30</integer>
		<key>mitq.st</key>
		<integer>30</integer>
		<key>mitr</key>
		<integer>62</integer>
		<key>mitr.sst</key>
		<integer>62</integer>
		<key>mitr.st</key>
		<integer>62</integer>
		<key>mitrho</key>
		<integer>28</integer>
		<key>mitrho.sst</key>
		<integer>28</integer>
		<key>mitrho.st</key>
		<integer>28</integer>
		<key>mits</key>
		<integer>38</integer>
		<key>mits.sst</key>
		<integer>38</integer>
		<key>mits.st</key>
		<integer>38</integer>
		<key>mitsansA</key>
		<integer>-14</integer>
		<key>mitsansB</key>
		<integer>24</integer>
		<key>mitsansC</key>
		<integer>68</integer>
		<key>mitsansD</key>
		<integer>40</integer>
		<key>mitsansE</key>
		<integer>70</integer>
		<key>mitsansF</key>
		<integer>116</integer>
		<key>mitsansG</key>
		<integer>66</integer>
		<key>mitsansH</key>
		<integer>54</integer>
		<key>mitsansI</key>
		<integer>54</integer>
		<key>mitsansJ</key>
		<integer>56</integer>
		<key>mitsansK</key>
		<integer>76</integer>
		<key>mitsansL</key>
		<integer>-6</integer>
		<key>mitsansM</key>
		<integer>54</integer>
		<key>mitsansN</key>
		<integer>54</integer>
		<key>mitsansO</key>
		<integer>40</integer>
		<key>mitsansP</key>
		<integer>70</integer>
		<key>mitsansQ</key>
		<integer>58</integer>
		<key>mitsansR</key>
		<integer>30</integer>
		<key>mitsansS</key>
		<integer>40</integer>
		<key>mitsansT</key>
		<integer>112</integer>
		<key>mitsansU</key>
		<integer>56</integer>
		<key>mitsansV</key>
		<integer>112</integer>
		<key>mitsansW</key>
		<integer>112</integer>
		<key>mitsansX</key>
		<integer>86</integer>
		<key>mitsansY</key>
		<integer>104</integer>
		<key>mitsansZ</key>
		<integer>92</integer>
		<key>mitsansa</key>
		<integer>2</integer>
		<key>mitsansb</key>
		<integer>32</integer>
		<key>mitsansc</key>
		<integer>54</integer>
		<key>mitsansd</key>
		<integer>72</integer>
		<key>mitsanse</key>
		<integer>28</integer>
		<key>mitsansf</key>
		<integer>212</integer>
		<key>mitsansg</key>
		<integer>110</integer>
		<key>mitsansh</key>
		<integer>-12</integer>
		<key>mitsansi</key>
		<integer>46</integer>
		<key>mitsansj</key>
		<integer>58</integer>
		<key>mitsansk</key>
		<integer>48</integer>
		<key>mitsansl</key>
		<integer>8</integer>
		<key>mitsansm</key>
		<integer>10</integer>
		<key>mitsansn</key>
		<integer>8</integer>
		<key>mitsanso</key>
		<integer>44</integer>
		<key>mitsansp</key>
		<integer>62</integer>
		<key>mitsansq</key>
		<integer>74</integer>
		<key>mitsansr</key>
		<integer>86</integer>
		<key>mitsanss</key>
		<integer>28</integer>
		<key>mitsanst</key>
		<integer>52</integer>
		<key>mitsansu</key>
		<integer>28</integer>
		<key>mitsansv</key>
		<integer>82</integer>
		<key>mitsansvarTheta</key>
		<integer>68</integer>
		<key>mitsansw</key>
		<integer>84</integer>
		<key>mitsansx</key>
		<integer>78</integer>
		<key>mitsansy</key>
		<integer>100</integer>
		<key>mitsansz</key>
		<integer>62</integer>
		<key>mitsigma</key>
		<integer>46</integer>
		<key>mitsigma.sst</key>
		<integer>46</integer>
		<key>mitsigma.st</key>
		<integer>46</integer>
		<key>mitt</key>
		<integer>34</integer>
		<key>mitt.sst</key>
		<integer>34</integer>
		<key>mitt.st</key>
		<integer>34</integer>
		<key>mittau</key>
		<integer>28</integer>
		<key>mittau.sst</key>
		<integer>28</integer>
		<key>mittau.st</key>
		<integer>28</integer>
		<key>mittheta</key>
		<integer>28</integer>
		<key>mittheta.sst</key>
		<integer>28</integer>
		<key>mittheta.st</key>
		<integer>28</integer>
		<key>mitu</key>
		<integer>28</integer>
		<key>mitu.sst</key>
		<integer>28</integer>
		<key>mitu.st</key>
		<integer>28</integer>
		<key>mitupsilon</key>
		<integer>28</integer>
		<key>mitupsilon.sst</key>
		<integer>28</integer>
		<key>mitupsilon.st</key>
		<integer>28</integer>
		<key>mitv</key>
		<integer>52</integer>
		<key>mitv.sst</key>
		<integer>52</integer>
		<key>mitv.st</key>
		<integer>52</integer>
		<key>mitvarTheta</key>
		<integer>26</integer>
		<key>mitvarTheta.sst</key>
		<integer>26</integer>
		<key>mitvarTheta.st</key>
		<integer>26</integer>
		<key>mitvarepsilon</key>
		<integer>30</integer>
		<key>mitvarepsilon.sst</key>
		<integer>30</integer>
		<key>mitvarepsilon.st</key>
		<integer>30</integer>
		<key>mitvarkappa</key>
		<integer>14</integer>
		<key>mitvarkappa.sst</key>
		<integer>14</integer>
		<key>mitvarkappa.st</key>
		<integer>14</integer>
		<key>mitvarphi</key>
		<integer>30</integer>
		<key>mitvarphi.sst</key>
		<integer>30</integer>
		<key>mitvarphi.st</key>
		<integer>30</integer>
		<key>mitvarpi</key>
		<integer>30</integer>
		<key>mitvarpi.sst</key>
		<integer>30</integer>
		<key>mitvarpi.st</key>
		<integer>30</integer>
		<key>mitvarrho</key>
		<integer>20</integer>
		<key>mitvarrho.sst</key>
		<integer>20</integer>
		<key>mitvarrho.st</key>
		<integer>20</integer>
		<key>mitvarsigma</key>
		<integer>28</integer>
		<key>mitvarsigma.sst</key>
		<integer>28</integer>
		<key>mitvarsigma.st</key>
		<integer>28</integer>
		<key>mitvartheta</key>
		<integer>20</integer>
		<key>mitvartheta.sst</key>
		<integer>20</integer>
		<key>mitvartheta.st</key>
		<integer>20</integer>
		<key>mitw</key>
		<integer>40</integer>
		<key>mitw.sst</key>
		<integer>40</integer>
		<key>mitw.st</key>
		<integer>40</integer>
		<key>mitx</key>
		<integer>48</integer>
		<key>mitx.sst</key>
		<integer>48</integer>
		<key>mitx.st</key>
		<integer>48</integer>
		<key>mitxi</key>
		<integer>22</integer>
		<key>mitxi.sst</key>
		<integer>22</integer>
		<key>mitxi.st</key>
		<integer>22</integer>
		<key>mity</key>
		<integer>52</integer>
		<key>mity.sst</key>
		<integer>52</integer>
		<key>mity.st</key>
		<integer>52</integer>
		<key>mitz</key>
		<integer>54</integer>
		<key>mitz.sst</key>
		<integer>54</integer>
		<key>mitz.st</key>
		<integer>54</integer>
		<key>mitzeta</key>
		<integer>32</integer>
		<key>mitzeta.sst</key>
		<integer>32</integer>
		<key>mitzeta.st</key>
		<integer>32</integer>
		<key>mscrA</key>
		<integer>42</integer>
		<key>mscrB</key>
		<integer>42</integer>
		<key>mscrC</key>
		<integer>42</integer>
		<key>mscrD</key>
		<integer>42</integer>
		<key>mscrE</key>
		<integer>42</integer>
		<key>mscrF</key>
		<integer>42</integer>
		<key>mscrG</key>
		<integer>42</integer>
		<key>mscrH</key>
		<integer>42</integer>
		<key>mscrI</key>
		<integer>42</integer>
		<key>mscrJ</key>
		<integer>42</integer>
		<key>mscrK</key>
		<integer>42</integer>
		<key>mscrL</key>
		<integer>42</integer>
		<key>mscrM</key>
		<integer>42</integer>
		<key>mscrN</key>
		<integer>42</integer>
		<key>mscrO</key>
		<integer>42</integer>
		<key>mscrP</key>
		<integer>42</integer>
		<key>mscrQ</key>
		<integer>42</integer>
		<key>mscrR</key>
		<integer>42</integer>
		<key>mscrS</key>
		<integer>42</integer>
		<key>mscrT</key>
		<integer>42</integer>
		<key>mscrU</key>
		<integer>42</integer>
		<key>mscrV</key>
		<integer>42</integer>
		<key>mscrW</key>
		<integer>42</integer>
		<key>mscrX</key>
		<integer>42</integer>
		<key>mscrY</key>
		<integer>42</integer>
		<key>mscrZ</key>
		<integer>42</integer>
		<key>oiiint</key>
		<integer>100</integer>
		<key>oiiint.cnd</key>
		<integer>100</integer>
		<key>oiiint.v1</key>
		<integer>340</integer>
		<key>oiiint.v1.cnd</key>
		<integer>340</integer>
		<key>oiiintacw</key>
		<integer>100</integer>
		<key>oiiintacw.cnd</key>
		<integer>100</integer>
		<key>oiiintacw.v1</key>
		<integer>350</integer>
		<key>oiiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>oiiintcw</key>
		<integer>100</integer>
		<key>oiiintcw.cnd</key>
		<integer>100</integer>
		<key>oiiintcw.v1</key>
		<integer>350</integer>
		<key>oiiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>oiint</key>
		<integer>100</integer>
		<key>oiint.cnd</key>
		<integer>100</integer>
		<key>oiint.v1</key>
		<integer>340</integer>
		<key>oiint.v1.cnd</key>
		<integer>340</integer>
		<key>oiintacw</key>
		<integer>100</integer>
		<key>oiintacw.cnd</key>
		<integer>100</integer>
		<key>oiintacw.v1</key>
		<integer>350</integer>
		<key>oiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>oiintcw</key>
		<integer>100</integer>
		<key>oiintcw.cnd</key>
		<integer>100</integer>
		<key>oiintcw.v1</key>
		<integer>350</integer>
		<key>oiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>oint</key>
		<integer>100</integer>
		<key>oint.v1</key>
		<integer>340</integer>
		<key>ointacw</key>
		<integer>150</integer>
		<key>ointacw.v1</key>
		<integer>310</integer>
		<key>ointcw</key>
		<integer>100</integer>
		<key>ointcw.v1</key>
		<integer>350</integer>
		<key>sqiiint</key>
		<integer>100</integer>
		<key>sqiiint.cnd</key>
		<integer>100</integer>
		<key>sqiiint.v1</key>
		<integer>350</integer>
		<key>sqiiint.v1.cnd</key>
		<integer>340</integer>
		<key>sqiint</key>
		<integer>100</integer>
		<key>sqiint.cnd</key>
		<integer>100</integer>
		<key>sqiint.v1</key>
		<integer>350</integer>
		<key>sqiint.v1.cnd</key>
		<integer>340</integer>
		<key>sqint.v1</key>
		<integer>340</integer>
		<key>variint</key>
		<integer>100</integer>
		<key>varint</key>
		<integer>200</integer>
		<key>varint.v1</key>
		<integer>350</integer>
		<key>varoiiintacw</key>
		<integer>100</integer>
		<key>varoiiintacw.cnd</key>
		<integer>100</integer>
		<key>varoiiintacw.v1</key>
		<integer>350</integer>
		<key>varoiiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>varoiiintcw</key>
		<integer>100</integer>
		<key>varoiiintcw.cnd</key>
		<integer>100</integer>
		<key>varoiiintcw.v1</key>
		<integer>350</integer>
		<key>varoiiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>varoiintacw</key>
		<integer>100</integer>
		<key>varoiintacw.cnd</key>
		<integer>100</integer>
		<key>varoiintacw.v1</key>
		<integer>350</integer>
		<key>varoiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>varoiintcw</key>
		<integer>100</integer>
		<key>varoiintcw.cnd</key>
		<integer>100</integer>
		<key>varoiintcw.v1</key>
		<integer>350</integer>
		<key>varoiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>varointacw</key>
		<integer>100</integer>
		<key>varointcw</key>
		<integer>150</integer>
		<key>varointcw.v1</key>
		<integer>310</integer>
		<key>wp</key>
		<integer>22</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>Downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>514</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown.bt</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>362</integer>
					<key>endConnector</key>
					<integer>121</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblvert.ex</string>
					<key>startConnector</key>
					<integer>121</integer>
				</dict>
			</array>
		</dict>
		<key>Uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>362</integer>
					<key>endConnector</key>
					<integer>121</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblvert.ex</string>
					<key>startConnector</key>
					<integer>121</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>Updownarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>514</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown.bt</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>362</integer>
					<key>endConnector</key>
					<integer>121</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblvert.ex</string>
					<key>startConnector</key>
					<integer>121</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>Vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vert.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vert.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>Vvert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vvert.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vvert.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>504</integer>
					<key>endConnector</key>
					<integer>168</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown.bt</string>
					<key>startConnector</key>
					<integer>168</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>342</integer>
					<key>endConnector</key>
					<integer>114</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowvert.ex</string>
					<key>startConnector</key>
					<integer>114</integer>
				</dict>
			</array>
		</dict>
		<key>int</key>
		<dict>
			<key>italic</key>
			<integer>400</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1152</integer>
					<key>endConnector</key>
					<integer>384</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>integralbt</string>
					<key>startConnector</key>
					<integer>384</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>350</integer>
					<key>endConnector</key>
					<integer>117</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>integralex</string>
					<key>startConnector</key>
					<integer>117</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1152</integer>
					<key>endConnector</key>
					<integer>384</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>integraltp</string>
					<key>startConnector</key>
					<integer>384</integer>
				</dict>
			</array>
		</dict>
		<key>lBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lBrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.bt</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1500</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbracemid</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.tp</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
			</array>
		</dict>
		<key>lbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lbrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lceil.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lfloor.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lparen.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>mid</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>mid</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>mid</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
			</array>
		</dict>
		<key>parallel</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>parallel</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>parallel</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
			</array>
		</dict>
		<key>rBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rBrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.bt</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1500</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbracemid</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>70</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.tp</string>
					<key>startConnector</key>
					<integer>70</integer>
				</dict>
			</array>
		</dict>
		<key>rbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rbrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rceil.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rfloor.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rparen.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>sqrt</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3127</integer>
					<key>endConnector</key>
					<integer>1042</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrtbottom</string>
					<key>startConnector</key>
					<integer>1042</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>316</integer>
					<key>endConnector</key>
					<integer>105</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>sqrt.ex</string>
					<key>startConnector</key>
					<integer>105</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>307</integer>
					<key>endConnector</key>
					<integer>102</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrt.tp</string>
					<key>startConnector</key>
					<integer>102</integer>
				</dict>
			</array>
		</dict>
		<key>uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>342</integer>
					<key>endConnector</key>
					<integer>114</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowvert.ex</string>
					<key>startConnector</key>
					<integer>114</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>updownarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>504</integer>
					<key>endConnector</key>
					<integer>168</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown.bt</string>
					<key>startConnector</key>
					<integer>168</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>342</integer>
					<key>endConnector</key>
					<integer>114</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowvert.ex</string>
					<key>startConnector</key>
					<integer>114</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>vert.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vert.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>Bbbsum</key>
		<array>
			<string>Bbbsum</string>
			<string>Bbbsum.v1</string>
		</array>
		<key>Downarrow</key>
		<array>
			<string>Downarrow</string>
		</array>
		<key>Uparrow</key>
		<array>
			<string>Uparrow</string>
		</array>
		<key>Updownarrow</key>
		<array/>
		<key>Vert</key>
		<array>
			<string>Vert</string>
			<string>Vert.v1</string>
			<string>Vert.v2</string>
			<string>Vert.v3</string>
			<string>Vert.v4</string>
		</array>
		<key>Vvert</key>
		<array>
			<string>Vvert</string>
			<string>Vvert.v1</string>
			<string>Vvert.v2</string>
			<string>Vvert.v3</string>
			<string>Vvert.v4</string>
		</array>
		<key>awint</key>
		<array>
			<string>awint</string>
			<string>awint.v1</string>
		</array>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.v1</string>
			<string>backslash.v2</string>
			<string>backslash.v3</string>
			<string>backslash.v4</string>
		</array>
		<key>bigcap</key>
		<array>
			<string>bigcap</string>
			<string>bigcap.v1</string>
		</array>
		<key>bigcapplus</key>
		<array>
			<string>bigcapplus</string>
			<string>bigcapplus.v1</string>
		</array>
		<key>bigcup</key>
		<array>
			<string>bigcup</string>
			<string>bigcup.v1</string>
		</array>
		<key>bigcupdot</key>
		<array>
			<string>bigcupdot</string>
			<string>bigcupdot.v1</string>
		</array>
		<key>bigodot</key>
		<array>
			<string>bigodot</string>
			<string>bigodot.v1</string>
		</array>
		<key>bigoplus</key>
		<array>
			<string>bigoplus</string>
			<string>bigoplus.v1</string>
		</array>
		<key>bigotimes</key>
		<array>
			<string>bigotimes</string>
			<string>bigotimes.v1</string>
		</array>
		<key>bigsqcap</key>
		<array>
			<string>bigsqcap</string>
			<string>bigsqcap.v1</string>
		</array>
		<key>bigsqcapplus</key>
		<array>
			<string>bigsqcapplus</string>
			<string>bigsqcapplus.v1</string>
		</array>
		<key>bigsqcup</key>
		<array>
			<string>bigsqcup</string>
			<string>bigsqcup.v1</string>
		</array>
		<key>bigsqcupplus</key>
		<array>
			<string>bigsqcupplus</string>
			<string>bigsqcupplus.v1</string>
		</array>
		<key>bigtimes</key>
		<array>
			<string>bigtimes</string>
			<string>bigtimes.v1</string>
		</array>
		<key>biguplus</key>
		<array>
			<string>biguplus</string>
			<string>biguplus.v1</string>
		</array>
		<key>bigvee</key>
		<array>
			<string>bigvee</string>
			<string>bigvee.v1</string>
		</array>
		<key>bigwedge</key>
		<array>
			<string>bigwedge</string>
			<string>bigwedge.v1</string>
		</array>
		<key>coprod</key>
		<array>
			<string>coprod</string>
			<string>coprod.v1</string>
		</array>
		<key>coprodsans</key>
		<array>
			<string>coprodsans</string>
			<string>coprodsans.v1</string>
		</array>
		<key>downarrow</key>
		<array>
			<string>downarrow</string>
		</array>
		<key>fint</key>
		<array>
			<string>fint</string>
			<string>fint.v1</string>
		</array>
		<key>idotsint</key>
		<array>
			<string>idotsint</string>
			<string>idotsint.v1</string>
		</array>
		<key>idotsint.cnd</key>
		<array>
			<string>idotsint.cnd</string>
			<string>idotsint.v1.cnd</string>
		</array>
		<key>iiiint</key>
		<array>
			<string>iiiint</string>
			<string>iiiint.v1</string>
		</array>
		<key>iiiint.cnd</key>
		<array>
			<string>iiiint.cnd</string>
			<string>iiiint.v1.cnd</string>
		</array>
		<key>iiint</key>
		<array>
			<string>iiint</string>
			<string>iiint.v1</string>
		</array>
		<key>iiint.cnd</key>
		<array>
			<string>iiint.cnd</string>
			<string>iiint.v1.cnd</string>
		</array>
		<key>iint</key>
		<array>
			<string>iint</string>
			<string>iint.v1</string>
		</array>
		<key>iint.cnd</key>
		<array>
			<string>iint.cnd</string>
			<string>iint.v1.cnd</string>
		</array>
		<key>int</key>
		<array>
			<string>int</string>
			<string>int.v1</string>
		</array>
		<key>intclockwise</key>
		<array>
			<string>intclockwise</string>
			<string>intclockwise.v1</string>
		</array>
		<key>lAngle</key>
		<array>
			<string>lAngle</string>
			<string>lAngle.v1</string>
			<string>lAngle.v2</string>
			<string>lAngle.v3</string>
			<string>lAngle.v4</string>
			<string>lAngle.v5</string>
			<string>lAngle.v6</string>
			<string>lAngle.v7</string>
		</array>
		<key>lBrack</key>
		<array>
			<string>lBrack</string>
			<string>lBrack.v1</string>
			<string>lBrack.v2</string>
			<string>lBrack.v3</string>
			<string>lBrack.v4</string>
		</array>
		<key>langle</key>
		<array>
			<string>langle</string>
			<string>langle.v1</string>
			<string>langle.v2</string>
			<string>langle.v3</string>
			<string>langle.v4</string>
			<string>langle.v5</string>
			<string>langle.v6</string>
			<string>langle.v7</string>
		</array>
		<key>lbag</key>
		<array>
			<string>lbag</string>
			<string>lbag.v1</string>
			<string>lbag.v2</string>
			<string>lbag.v3</string>
			<string>lbag.v4</string>
			<string>lbag.v5</string>
			<string>lbag.v6</string>
			<string>lbag.v7</string>
		</array>
		<key>lbrace</key>
		<array>
			<string>lbrace</string>
			<string>lbrace.v1</string>
			<string>lbrace.v2</string>
			<string>lbrace.v3</string>
			<string>lbrace.v4</string>
		</array>
		<key>lbrack</key>
		<array>
			<string>lbrack</string>
			<string>lbrack.v1</string>
			<string>lbrack.v2</string>
			<string>lbrack.v3</string>
			<string>lbrack.v4</string>
		</array>
		<key>lceil</key>
		<array>
			<string>lceil</string>
			<string>lceil.v1</string>
			<string>lceil.v2</string>
			<string>lceil.v3</string>
			<string>lceil.v4</string>
		</array>
		<key>lfloor</key>
		<array>
			<string>lfloor</string>
			<string>lfloor.v1</string>
			<string>lfloor.v2</string>
			<string>lfloor.v3</string>
			<string>lfloor.v4</string>
		</array>
		<key>lparen</key>
		<array>
			<string>lparen</string>
			<string>lparen.v1</string>
			<string>lparen.v2</string>
			<string>lparen.v3</string>
			<string>lparen.v4</string>
		</array>
		<key>mathslash</key>
		<array>
			<string>mathslash</string>
			<string>slash.v1</string>
			<string>slash.v2</string>
			<string>slash.v3</string>
			<string>slash.v4</string>
		</array>
		<key>mid</key>
		<array>
			<string>mid</string>
		</array>
		<key>oiiint</key>
		<array>
			<string>oiiint</string>
			<string>oiiint.v1</string>
		</array>
		<key>oiiint.cnd</key>
		<array>
			<string>oiiint.cnd</string>
			<string>oiiint.v1.cnd</string>
		</array>
		<key>oiiintacw</key>
		<array>
			<string>oiiintacw</string>
			<string>oiiintacw.v1</string>
		</array>
		<key>oiiintacw.cnd</key>
		<array>
			<string>oiiintacw.cnd</string>
			<string>oiiintacw.v1.cnd</string>
		</array>
		<key>oiiintcw</key>
		<array>
			<string>oiiintcw</string>
			<string>oiiintcw.v1</string>
		</array>
		<key>oiiintcw.cnd</key>
		<array>
			<string>oiiintcw.cnd</string>
			<string>oiiintcw.v1.cnd</string>
		</array>
		<key>oiint</key>
		<array>
			<string>oiint</string>
			<string>oiint.v1</string>
		</array>
		<key>oiint.cnd</key>
		<array>
			<string>oiint.cnd</string>
			<string>oiint.v1.cnd</string>
		</array>
		<key>oiintacw</key>
		<array>
			<string>oiintacw</string>
			<string>oiintacw.v1</string>
		</array>
		<key>oiintacw.cnd</key>
		<array>
			<string>oiintacw.cnd</string>
			<string>oiintacw.v1.cnd</string>
		</array>
		<key>oiintcw</key>
		<array>
			<string>oiintcw</string>
			<string>oiintcw.v1</string>
		</array>
		<key>oiintcw.cnd</key>
		<array>
			<string>oiintcw.cnd</string>
			<string>oiintcw.v1.cnd</string>
		</array>
		<key>oint</key>
		<array>
			<string>oint</string>
			<string>oint.v1</string>
		</array>
		<key>ointacw</key>
		<array>
			<string>ointacw</string>
			<string>ointacw.v1</string>
		</array>
		<key>ointcw</key>
		<array>
			<string>ointcw</string>
			<string>ointcw.v1</string>
		</array>
		<key>parallel</key>
		<array>
			<string>parallel</string>
		</array>
		<key>prod</key>
		<array>
			<string>prod</string>
			<string>prod.v1</string>
		</array>
		<key>prodsans</key>
		<array>
			<string>prodsans</string>
			<string>prodsans.v1</string>
		</array>
		<key>rAngle</key>
		<array>
			<string>rAngle</string>
			<string>rAngle.v1</string>
			<string>rAngle.v2</string>
			<string>rAngle.v3</string>
			<string>rAngle.v4</string>
			<string>rAngle.v5</string>
			<string>rAngle.v6</string>
			<string>rAngle.v7</string>
		</array>
		<key>rBrack</key>
		<array>
			<string>rBrack</string>
			<string>rBrack.v1</string>
			<string>rBrack.v2</string>
			<string>rBrack.v3</string>
			<string>rBrack.v4</string>
		</array>
		<key>rangle</key>
		<array>
			<string>rangle</string>
			<string>rangle.v1</string>
			<string>rangle.v2</string>
			<string>rangle.v3</string>
			<string>rangle.v4</string>
			<string>rangle.v5</string>
			<string>rangle.v6</string>
			<string>rangle.v7</string>
		</array>
		<key>rbag</key>
		<array>
			<string>rbag</string>
			<string>rbag.v1</string>
			<string>rbag.v2</string>
			<string>rbag.v3</string>
			<string>rbag.v4</string>
			<string>rbag.v5</string>
			<string>rbag.v6</string>
			<string>rbag.v7</string>
		</array>
		<key>rbrace</key>
		<array>
			<string>rbrace</string>
			<string>rbrace.v1</string>
			<string>rbrace.v2</string>
			<string>rbrace.v3</string>
			<string>rbrace.v4</string>
		</array>
		<key>rbrack</key>
		<array>
			<string>rbrack</string>
			<string>rbrack.v1</string>
			<string>rbrack.v2</string>
			<string>rbrack.v3</string>
			<string>rbrack.v4</string>
		</array>
		<key>rceil</key>
		<array>
			<string>rceil</string>
			<string>rceil.v1</string>
			<string>rceil.v2</string>
			<string>rceil.v3</string>
			<string>rceil.v4</string>
		</array>
		<key>rfloor</key>
		<array>
			<string>rfloor</string>
			<string>rfloor.v1</string>
			<string>rfloor.v2</string>
			<string>rfloor.v3</string>
			<string>rfloor.v4</string>
		</array>
		<key>rparen</key>
		<array>
			<string>rparen</string>
			<string>rparen.v1</string>
			<string>rparen.v2</string>
			<string>rparen.v3</string>
			<string>rparen.v4</string>
		</array>
		<key>sqiiint</key>
		<array>
			<string>sqiiint</string>
			<string>sqiiint.v1</string>
		</array>
		<key>sqiiint.cnd</key>
		<array>
			<string>sqiiint.cnd</string>
			<string>sqiiint.v1.cnd</string>
		</array>
		<key>sqiint</key>
		<array>
			<string>sqiint</string>
			<string>sqiint.v1</string>
		</array>
		<key>sqiint.cnd</key>
		<array>
			<string>sqiint.cnd</string>
			<string>sqiint.v1.cnd</string>
		</array>
		<key>sqint</key>
		<array>
			<string>sqint</string>
			<string>sqint.v1</string>
		</array>
		<key>sqrt</key>
		<array>
			<string>sqrt</string>
			<string>sqrt.v1</string>
			<string>sqrt.v2</string>
			<string>sqrt.v3</string>
			<string>sqrt.v4</string>
		</array>
		<key>sum</key>
		<array>
			<string>sum</string>
			<string>sum.v1</string>
		</array>
		<key>sumsans</key>
		<array>
			<string>sumsans</string>
			<string>sumsans.v1</string>
		</array>
		<key>uparrow</key>
		<array>
			<string>uparrow</string>
		</array>
		<key>updownarrow</key>
		<array/>
		<key>varidotsint</key>
		<array>
			<string>varidotsint</string>
			<string>varidotsint.v1</string>
		</array>
		<key>varidotsint.cnd</key>
		<array>
			<string>varidotsint.cnd</string>
			<string>varidotsint.v1.cnd</string>
		</array>
		<key>variiiint</key>
		<array>
			<string>variiiint</string>
			<string>variiiint.v1</string>
		</array>
		<key>variiiint.cnd</key>
		<array>
			<string>variiiint.cnd</string>
			<string>variiiint.v1.cnd</string>
		</array>
		<key>variiint</key>
		<array>
			<string>variiint</string>
			<string>variiint.v1</string>
		</array>
		<key>variiint.cnd</key>
		<array>
			<string>variiint.cnd</string>
			<string>variiint.v1.cnd</string>
		</array>
		<key>variint</key>
		<array>
			<string>variint</string>
			<string>variint.v1</string>
		</array>
		<key>variint.cnd</key>
		<array>
			<string>variint.cnd</string>
			<string>variint.v1.cnd</string>
		</array>
		<key>varint</key>
		<array>
			<string>varint</string>
			<string>varint.v1</string>
		</array>
		<key>varoiiintacw</key>
		<array>
			<string>varoiiintacw</string>
			<string>varoiiintacw.v1</string>
		</array>
		<key>varoiiintacw.cnd</key>
		<array>
			<string>varoiiintacw.cnd</string>
			<string>varoiiintacw.v1.cnd</string>
		</array>
		<key>varoiiintcw</key>
		<array>
			<string>varoiiintcw</string>
			<string>varoiiintcw.v1</string>
		</array>
		<key>varoiiintcw.cnd</key>
		<array>
			<string>varoiiintcw.cnd</string>
			<string>varoiiintcw.v1.cnd</string>
		</array>
		<key>varoiintacw</key>
		<array>
			<string>varoiintacw</string>
			<string>varoiintacw.v1</string>
		</array>
		<key>varoiintacw.cnd</key>
		<array>
			<string>varoiintacw.cnd</string>
			<string>varoiintacw.v1.cnd</string>
		</array>
		<key>varoiintcw</key>
		<array>
			<string>varoiintcw</string>
			<string>varoiintcw.v1</string>
		</array>
		<key>varoiintcw.cnd</key>
		<array>
			<string>varoiintcw.cnd</string>
			<string>varoiintcw.v1.cnd</string>
		</array>
		<key>varointacw</key>
		<array>
			<string>varointacw</string>
			<string>varointacw.v1</string>
		</array>
		<key>varointcw</key>
		<array>
			<string>varointcw</string>
			<string>varointcw.v1</string>
		</array>
		<key>vert</key>
		<array>
			<string>vert</string>
			<string>vert.v1</string>
			<string>vert.v2</string>
			<string>vert.v3</string>
			<string>vert.v4</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
