<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>A</key>
		<integer>361</integer>
		<key>B</key>
		<integer>305</integer>
		<key>C</key>
		<integer>360</integer>
		<key>D</key>
		<integer>350</integer>
		<key>E</key>
		<integer>304</integer>
		<key>F</key>
		<integer>294</integer>
		<key>G</key>
		<integer>376</integer>
		<key>H</key>
		<integer>360</integer>
		<key>I</key>
		<integer>166</integer>
		<key>J</key>
		<integer>214</integer>
		<key>K</key>
		<integer>368</integer>
		<key>L</key>
		<integer>151</integer>
		<key>M</key>
		<integer>438</integer>
		<key>N</key>
		<integer>360</integer>
		<key>O</key>
		<integer>361</integer>
		<key>P</key>
		<integer>279</integer>
		<key>Q</key>
		<integer>358</integer>
		<key>R</key>
		<integer>272</integer>
		<key>S</key>
		<integer>267</integer>
		<key>T</key>
		<integer>305</integer>
		<key>U</key>
		<integer>390</integer>
		<key>V</key>
		<integer>388</integer>
		<key>W</key>
		<integer>456</integer>
		<key>X</key>
		<integer>397</integer>
		<key>Y</key>
		<integer>396</integer>
		<key>Z</key>
		<integer>304</integer>
		<key>a</key>
		<integer>222</integer>
		<key>b</key>
		<integer>112</integer>
		<key>breve</key>
		<integer>168</integer>
		<key>c</key>
		<integer>244</integer>
		<key>caron</key>
		<integer>166</integer>
		<key>circumflex</key>
		<integer>166</integer>
		<key>d</key>
		<integer>367</integer>
		<key>dotaccent</key>
		<integer>168</integer>
		<key>dotlessi</key>
		<integer>134</integer>
		<key>e</key>
		<integer>224</integer>
		<key>f</key>
		<integer>264</integer>
		<key>g</key>
		<integer>233</integer>
		<key>h</key>
		<integer>116</integer>
		<key>hungarumlaut</key>
		<integer>186</integer>
		<key>i</key>
		<integer>132</integer>
		<key>j</key>
		<integer>144</integer>
		<key>k</key>
		<integer>116</integer>
		<key>l</key>
		<integer>138</integer>
		<key>m</key>
		<integer>396</integer>
		<key>n</key>
		<integer>250</integer>
		<key>o</key>
		<integer>250</integer>
		<key>p</key>
		<integer>238</integer>
		<key>periodcentered</key>
		<integer>126</integer>
		<key>q</key>
		<integer>256</integer>
		<key>r</key>
		<integer>170</integer>
		<key>ring</key>
		<integer>166</integer>
		<key>s</key>
		<integer>200</integer>
		<key>t</key>
		<integer>116</integer>
		<key>tilde</key>
		<integer>166</integer>
		<key>u</key>
		<integer>234</integer>
		<key>u1D400</key>
		<integer>345</integer>
		<key>u1D401</key>
		<integer>318</integer>
		<key>u1D402</key>
		<integer>368</integer>
		<key>u1D403</key>
		<integer>352</integer>
		<key>u1D404</key>
		<integer>328</integer>
		<key>u1D405</key>
		<integer>300</integer>
		<key>u1D406</key>
		<integer>396</integer>
		<key>u1D407</key>
		<integer>390</integer>
		<key>u1D408</key>
		<integer>195</integer>
		<key>u1D409</key>
		<integer>310</integer>
		<key>u1D40A</key>
		<integer>410</integer>
		<key>u1D40B</key>
		<integer>192</integer>
		<key>u1D40C</key>
		<integer>468</integer>
		<key>u1D40D</key>
		<integer>358</integer>
		<key>u1D40E</key>
		<integer>389</integer>
		<key>u1D40F</key>
		<integer>308</integer>
		<key>u1D410</key>
		<integer>389</integer>
		<key>u1D411</key>
		<integer>323</integer>
		<key>u1D412</key>
		<integer>274</integer>
		<key>u1D413</key>
		<integer>334</integer>
		<key>u1D414</key>
		<integer>420</integer>
		<key>u1D415</key>
		<integer>424</integer>
		<key>u1D416</key>
		<integer>540</integer>
		<key>u1D417</key>
		<integer>402</integer>
		<key>u1D418</key>
		<integer>415</integer>
		<key>u1D419</key>
		<integer>331</integer>
		<key>u1D41A</key>
		<integer>256</integer>
		<key>u1D41B</key>
		<integer>139</integer>
		<key>u1D41C</key>
		<integer>261</integer>
		<key>u1D41D</key>
		<integer>400</integer>
		<key>u1D41E</key>
		<integer>235</integer>
		<key>u1D41F</key>
		<integer>246</integer>
		<key>u1D420</key>
		<integer>240</integer>
		<key>u1D421</key>
		<integer>130</integer>
		<key>u1D422</key>
		<integer>136</integer>
		<key>u1D422.dtls</key>
		<integer>139</integer>
		<key>u1D423</key>
		<integer>189</integer>
		<key>u1D423.dtls</key>
		<integer>190</integer>
		<key>u1D424</key>
		<integer>140</integer>
		<key>u1D425</key>
		<integer>136</integer>
		<key>u1D426</key>
		<integer>414</integer>
		<key>u1D427</key>
		<integer>280</integer>
		<key>u1D428</key>
		<integer>250</integer>
		<key>u1D429</key>
		<integer>272</integer>
		<key>u1D42A</key>
		<integer>285</integer>
		<key>u1D42B</key>
		<integer>231</integer>
		<key>u1D42C</key>
		<integer>193</integer>
		<key>u1D42D</key>
		<integer>176</integer>
		<key>u1D42E</key>
		<integer>249</integer>
		<key>u1D42F</key>
		<integer>288</integer>
		<key>u1D430</key>
		<integer>407</integer>
		<key>u1D431</key>
		<integer>262</integer>
		<key>u1D432</key>
		<integer>265</integer>
		<key>u1D433</key>
		<integer>220</integer>
		<key>u1D434</key>
		<integer>510</integer>
		<key>u1D435</key>
		<integer>476</integer>
		<key>u1D436</key>
		<integer>492</integer>
		<key>u1D437</key>
		<integer>494</integer>
		<key>u1D438</key>
		<integer>490</integer>
		<key>u1D439</key>
		<integer>490</integer>
		<key>u1D43A</key>
		<integer>488</integer>
		<key>u1D43B</key>
		<integer>556</integer>
		<key>u1D43C</key>
		<integer>388</integer>
		<key>u1D43D</key>
		<integer>478</integer>
		<key>u1D43E</key>
		<integer>536</integer>
		<key>u1D43F</key>
		<integer>387</integer>
		<key>u1D440</key>
		<integer>624</integer>
		<key>u1D441</key>
		<integer>536</integer>
		<key>u1D442</key>
		<integer>475</integer>
		<key>u1D443</key>
		<integer>477</integer>
		<key>u1D444</key>
		<integer>490</integer>
		<key>u1D445</key>
		<integer>474</integer>
		<key>u1D446</key>
		<integer>469</integer>
		<key>u1D447</key>
		<integer>382</integer>
		<key>u1D448</key>
		<integer>440</integer>
		<key>u1D449</key>
		<integer>438</integer>
		<key>u1D44A</key>
		<integer>574</integer>
		<key>u1D44B</key>
		<integer>530</integer>
		<key>u1D44C</key>
		<integer>391</integer>
		<key>u1D44D</key>
		<integer>521</integer>
		<key>u1D44E</key>
		<integer>324</integer>
		<key>u1D44F</key>
		<integer>240</integer>
		<key>u1D450</key>
		<integer>304</integer>
		<key>u1D451</key>
		<integer>470</integer>
		<key>u1D452</key>
		<integer>299</integer>
		<key>u1D453</key>
		<integer>528</integer>
		<key>u1D454</key>
		<integer>324</integer>
		<key>u1D456</key>
		<integer>208</integer>
		<key>u1D457</key>
		<integer>322</integer>
		<key>u1D458</key>
		<integer>234</integer>
		<key>u1D459</key>
		<integer>222</integer>
		<key>u1D45A</key>
		<integer>399</integer>
		<key>u1D45B</key>
		<integer>306</integer>
		<key>u1D45C</key>
		<integer>293</integer>
		<key>u1D45D</key>
		<integer>320</integer>
		<key>u1D45E</key>
		<integer>340</integer>
		<key>u1D45F</key>
		<integer>244</integer>
		<key>u1D460</key>
		<integer>268</integer>
		<key>u1D461</key>
		<integer>214</integer>
		<key>u1D462</key>
		<integer>277</integer>
		<key>u1D463</key>
		<integer>319</integer>
		<key>u1D464</key>
		<integer>481</integer>
		<key>u1D465</key>
		<integer>304</integer>
		<key>u1D466</key>
		<integer>319</integer>
		<key>u1D467</key>
		<integer>300</integer>
		<key>u1D468</key>
		<integer>518</integer>
		<key>u1D469</key>
		<integer>486</integer>
		<key>u1D46A</key>
		<integer>494</integer>
		<key>u1D46B</key>
		<integer>478</integer>
		<key>u1D46C</key>
		<integer>492</integer>
		<key>u1D46D</key>
		<integer>491</integer>
		<key>u1D46E</key>
		<integer>510</integer>
		<key>u1D46F</key>
		<integer>564</integer>
		<key>u1D470</key>
		<integer>404</integer>
		<key>u1D471</key>
		<integer>490</integer>
		<key>u1D472</key>
		<integer>554</integer>
		<key>u1D473</key>
		<integer>399</integer>
		<key>u1D474</key>
		<integer>630</integer>
		<key>u1D475</key>
		<integer>537</integer>
		<key>u1D476</key>
		<integer>493</integer>
		<key>u1D477</key>
		<integer>460</integer>
		<key>u1D478</key>
		<integer>483</integer>
		<key>u1D479</key>
		<integer>455</integer>
		<key>u1D47A</key>
		<integer>474</integer>
		<key>u1D47B</key>
		<integer>404</integer>
		<key>u1D47C</key>
		<integer>473</integer>
		<key>u1D47D</key>
		<integer>462</integer>
		<key>u1D47E</key>
		<integer>598</integer>
		<key>u1D47F</key>
		<integer>529</integer>
		<key>u1D480</key>
		<integer>410</integer>
		<key>u1D481</key>
		<integer>512</integer>
		<key>u1D482</key>
		<integer>330</integer>
		<key>u1D483</key>
		<integer>244</integer>
		<key>u1D484</key>
		<integer>328</integer>
		<key>u1D485</key>
		<integer>502</integer>
		<key>u1D486</key>
		<integer>338</integer>
		<key>u1D487</key>
		<integer>528</integer>
		<key>u1D488</key>
		<integer>338</integer>
		<key>u1D489</key>
		<integer>246</integer>
		<key>u1D48A</key>
		<integer>234</integer>
		<key>u1D48A.dtls</key>
		<integer>178</integer>
		<key>u1D48B</key>
		<integer>348</integer>
		<key>u1D48B.dtls</key>
		<integer>345</integer>
		<key>u1D48C</key>
		<integer>247</integer>
		<key>u1D48D</key>
		<integer>238</integer>
		<key>u1D48E</key>
		<integer>380</integer>
		<key>u1D48F</key>
		<integer>318</integer>
		<key>u1D490</key>
		<integer>340</integer>
		<key>u1D491</key>
		<integer>354</integer>
		<key>u1D492</key>
		<integer>368</integer>
		<key>u1D493</key>
		<integer>264</integer>
		<key>u1D494</key>
		<integer>289</integer>
		<key>u1D495</key>
		<integer>243</integer>
		<key>u1D496</key>
		<integer>298</integer>
		<key>u1D497</key>
		<integer>342</integer>
		<key>u1D498</key>
		<integer>486</integer>
		<key>u1D499</key>
		<integer>332</integer>
		<key>u1D49A</key>
		<integer>335</integer>
		<key>u1D49B</key>
		<integer>308</integer>
		<key>u1D49C</key>
		<integer>800</integer>
		<key>u1D49C.cal</key>
		<integer>642</integer>
		<key>u1D49E</key>
		<integer>697</integer>
		<key>u1D49E.cal</key>
		<integer>444</integer>
		<key>u1D49F</key>
		<integer>725</integer>
		<key>u1D49F.cal</key>
		<integer>500</integer>
		<key>u1D4A2</key>
		<integer>642</integer>
		<key>u1D4A2.cal</key>
		<integer>452</integer>
		<key>u1D4A5</key>
		<integer>662</integer>
		<key>u1D4A5.cal</key>
		<integer>612</integer>
		<key>u1D4A6</key>
		<integer>772</integer>
		<key>u1D4A6.cal</key>
		<integer>500</integer>
		<key>u1D4A9</key>
		<integer>740</integer>
		<key>u1D4A9.cal</key>
		<integer>636</integer>
		<key>u1D4AA</key>
		<integer>519</integer>
		<key>u1D4AA.cal</key>
		<integer>466</integer>
		<key>u1D4AB</key>
		<integer>710</integer>
		<key>u1D4AB.cal</key>
		<integer>391</integer>
		<key>u1D4AC</key>
		<integer>520</integer>
		<key>u1D4AC.cal</key>
		<integer>456</integer>
		<key>u1D4AE</key>
		<integer>562</integer>
		<key>u1D4AE.cal</key>
		<integer>477</integer>
		<key>u1D4AF</key>
		<integer>605</integer>
		<key>u1D4AF.cal</key>
		<integer>396</integer>
		<key>u1D4B0</key>
		<integer>552</integer>
		<key>u1D4B0.cal</key>
		<integer>536</integer>
		<key>u1D4B1</key>
		<integer>660</integer>
		<key>u1D4B1.cal</key>
		<integer>433</integer>
		<key>u1D4B2</key>
		<integer>756</integer>
		<key>u1D4B2.cal</key>
		<integer>574</integer>
		<key>u1D4B3</key>
		<integer>602</integer>
		<key>u1D4B3.cal</key>
		<integer>510</integer>
		<key>u1D4B4</key>
		<integer>648</integer>
		<key>u1D4B4.cal</key>
		<integer>495</integer>
		<key>u1D4B5</key>
		<integer>652</integer>
		<key>u1D4B5.cal</key>
		<integer>496</integer>
		<key>u1D4B6</key>
		<integer>499</integer>
		<key>u1D4B7</key>
		<integer>497</integer>
		<key>u1D4B8</key>
		<integer>487</integer>
		<key>u1D4B9</key>
		<integer>782</integer>
		<key>u1D4BB</key>
		<integer>600</integer>
		<key>u1D4BD</key>
		<integer>511</integer>
		<key>u1D4BE</key>
		<integer>443</integer>
		<key>u1D4BE.dtls</key>
		<integer>317</integer>
		<key>u1D4BF</key>
		<integer>671</integer>
		<key>u1D4BF.dtls</key>
		<integer>554</integer>
		<key>u1D4C0</key>
		<integer>513</integer>
		<key>u1D4C1</key>
		<integer>511</integer>
		<key>u1D4C2</key>
		<integer>603</integer>
		<key>u1D4C3</key>
		<integer>455</integer>
		<key>u1D4C5</key>
		<integer>545</integer>
		<key>u1D4C6</key>
		<integer>492</integer>
		<key>u1D4C7</key>
		<integer>404</integer>
		<key>u1D4C8</key>
		<integer>376</integer>
		<key>u1D4C9</key>
		<integer>422</integer>
		<key>u1D4CA</key>
		<integer>433</integer>
		<key>u1D4CB</key>
		<integer>393</integer>
		<key>u1D4CC</key>
		<integer>525</integer>
		<key>u1D4CD</key>
		<integer>454</integer>
		<key>u1D4CE</key>
		<integer>434</integer>
		<key>u1D4CF</key>
		<integer>399</integer>
		<key>u1D4D0</key>
		<integer>900</integer>
		<key>u1D4D0.cal</key>
		<integer>651</integer>
		<key>u1D4D1</key>
		<integer>856</integer>
		<key>u1D4D1.cal</key>
		<integer>553</integer>
		<key>u1D4D2</key>
		<integer>744</integer>
		<key>u1D4D2.cal</key>
		<integer>519</integer>
		<key>u1D4D3</key>
		<integer>799</integer>
		<key>u1D4D3.cal</key>
		<integer>511</integer>
		<key>u1D4D4</key>
		<integer>657</integer>
		<key>u1D4D4.cal</key>
		<integer>460</integer>
		<key>u1D4D5</key>
		<integer>719</integer>
		<key>u1D4D5.cal</key>
		<integer>474</integer>
		<key>u1D4D6</key>
		<integer>667</integer>
		<key>u1D4D6.cal</key>
		<integer>481</integer>
		<key>u1D4D7</key>
		<integer>787</integer>
		<key>u1D4D7.cal</key>
		<integer>502</integer>
		<key>u1D4D8</key>
		<integer>726</integer>
		<key>u1D4D8.cal</key>
		<integer>418</integer>
		<key>u1D4D9</key>
		<integer>631</integer>
		<key>u1D4D9.cal</key>
		<integer>622</integer>
		<key>u1D4DA</key>
		<integer>843</integer>
		<key>u1D4DA.cal</key>
		<integer>516</integer>
		<key>u1D4DB</key>
		<integer>906</integer>
		<key>u1D4DB.cal</key>
		<integer>493</integer>
		<key>u1D4DC</key>
		<integer>1006</integer>
		<key>u1D4DC.cal</key>
		<integer>707</integer>
		<key>u1D4DD</key>
		<integer>827</integer>
		<key>u1D4DD.cal</key>
		<integer>654</integer>
		<key>u1D4DE</key>
		<integer>562</integer>
		<key>u1D4DE.cal</key>
		<integer>482</integer>
		<key>u1D4DF</key>
		<integer>816</integer>
		<key>u1D4DF.cal</key>
		<integer>462</integer>
		<key>u1D4E0</key>
		<integer>562</integer>
		<key>u1D4E0.cal</key>
		<integer>473</integer>
		<key>u1D4E1</key>
		<integer>816</integer>
		<key>u1D4E1.cal</key>
		<integer>474</integer>
		<key>u1D4E2</key>
		<integer>606</integer>
		<key>u1D4E2.cal</key>
		<integer>487</integer>
		<key>u1D4E3</key>
		<integer>715</integer>
		<key>u1D4E3.cal</key>
		<integer>411</integer>
		<key>u1D4E4</key>
		<integer>624</integer>
		<key>u1D4E4.cal</key>
		<integer>549</integer>
		<key>u1D4E5</key>
		<integer>722</integer>
		<key>u1D4E5.cal</key>
		<integer>438</integer>
		<key>u1D4E6</key>
		<integer>936</integer>
		<key>u1D4E6.cal</key>
		<integer>580</integer>
		<key>u1D4E7</key>
		<integer>666</integer>
		<key>u1D4E7.cal</key>
		<integer>534</integer>
		<key>u1D4E8</key>
		<integer>634</integer>
		<key>u1D4E8.cal</key>
		<integer>522</integer>
		<key>u1D4E9</key>
		<integer>676</integer>
		<key>u1D4E9.cal</key>
		<integer>513</integer>
		<key>u1D4EA</key>
		<integer>585</integer>
		<key>u1D4EB</key>
		<integer>525</integer>
		<key>u1D4EC</key>
		<integer>560</integer>
		<key>u1D4ED</key>
		<integer>848</integer>
		<key>u1D4EE</key>
		<integer>523</integer>
		<key>u1D4EF</key>
		<integer>642</integer>
		<key>u1D4F0</key>
		<integer>557</integer>
		<key>u1D4F1</key>
		<integer>557</integer>
		<key>u1D4F2</key>
		<integer>469</integer>
		<key>u1D4F2.dtls</key>
		<integer>354</integer>
		<key>u1D4F3</key>
		<integer>758</integer>
		<key>u1D4F3.dtls</key>
		<integer>651</integer>
		<key>u1D4F4</key>
		<integer>555</integer>
		<key>u1D4F5</key>
		<integer>525</integer>
		<key>u1D4F6</key>
		<integer>648</integer>
		<key>u1D4F7</key>
		<integer>481</integer>
		<key>u1D4F8</key>
		<integer>576</integer>
		<key>u1D4F9</key>
		<integer>636</integer>
		<key>u1D4FA</key>
		<integer>562</integer>
		<key>u1D4FB</key>
		<integer>452</integer>
		<key>u1D4FC</key>
		<integer>375</integer>
		<key>u1D4FD</key>
		<integer>475</integer>
		<key>u1D4FE</key>
		<integer>475</integer>
		<key>u1D4FF</key>
		<integer>464</integer>
		<key>u1D500</key>
		<integer>622</integer>
		<key>u1D501</key>
		<integer>488</integer>
		<key>u1D502</key>
		<integer>500</integer>
		<key>u1D503</key>
		<integer>391</integer>
		<key>u1D526.dtls</key>
		<integer>177</integer>
		<key>u1D527.dtls</key>
		<integer>199</integer>
		<key>u1D538.bi</key>
		<integer>467</integer>
		<key>u1D538.it</key>
		<integer>434</integer>
		<key>u1D539.bi</key>
		<integer>496</integer>
		<key>u1D539.it</key>
		<integer>476</integer>
		<key>u1D53B.bi</key>
		<integer>484</integer>
		<key>u1D53B.it</key>
		<integer>460</integer>
		<key>u1D53C.bi</key>
		<integer>490</integer>
		<key>u1D53C.it</key>
		<integer>470</integer>
		<key>u1D53D.bi</key>
		<integer>490</integer>
		<key>u1D53D.it</key>
		<integer>471</integer>
		<key>u1D53E.bi</key>
		<integer>501</integer>
		<key>u1D53E.it</key>
		<integer>498</integer>
		<key>u1D540.bi</key>
		<integer>346</integer>
		<key>u1D540.it</key>
		<integer>315</integer>
		<key>u1D541.bi</key>
		<integer>461</integer>
		<key>u1D541.it</key>
		<integer>435</integer>
		<key>u1D542.bi</key>
		<integer>522</integer>
		<key>u1D542.it</key>
		<integer>503</integer>
		<key>u1D543.bi</key>
		<integer>423</integer>
		<key>u1D543.it</key>
		<integer>401</integer>
		<key>u1D544.bi</key>
		<integer>614</integer>
		<key>u1D544.it</key>
		<integer>588</integer>
		<key>u1D546.bi</key>
		<integer>514</integer>
		<key>u1D546.it</key>
		<integer>504</integer>
		<key>u1D54A.bi</key>
		<integer>500</integer>
		<key>u1D54A.it</key>
		<integer>440</integer>
		<key>u1D54B.bi</key>
		<integer>344</integer>
		<key>u1D54B.it</key>
		<integer>373</integer>
		<key>u1D54C.bi</key>
		<integer>525</integer>
		<key>u1D54C.it</key>
		<integer>504</integer>
		<key>u1D54D.bi</key>
		<integer>409</integer>
		<key>u1D54D.it</key>
		<integer>397</integer>
		<key>u1D54E.bi</key>
		<integer>598</integer>
		<key>u1D54E.it</key>
		<integer>581</integer>
		<key>u1D54F.bi</key>
		<integer>549</integer>
		<key>u1D54F.it</key>
		<integer>503</integer>
		<key>u1D550.bi</key>
		<integer>410</integer>
		<key>u1D550.it</key>
		<integer>387</integer>
		<key>u1D552.bi</key>
		<integer>417</integer>
		<key>u1D552.it</key>
		<integer>380</integer>
		<key>u1D553.bi</key>
		<integer>426</integer>
		<key>u1D553.it</key>
		<integer>392</integer>
		<key>u1D554.bi</key>
		<integer>395</integer>
		<key>u1D554.it</key>
		<integer>360</integer>
		<key>u1D555.it</key>
		<integer>437</integer>
		<key>u1D556.bi</key>
		<integer>392</integer>
		<key>u1D556.it</key>
		<integer>362</integer>
		<key>u1D557.bi</key>
		<integer>431</integer>
		<key>u1D557.it</key>
		<integer>373</integer>
		<key>u1D558.bi</key>
		<integer>435</integer>
		<key>u1D558.it</key>
		<integer>390</integer>
		<key>u1D559.bi</key>
		<integer>425</integer>
		<key>u1D559.it</key>
		<integer>385</integer>
		<key>u1D55A.bi</key>
		<integer>302</integer>
		<key>u1D55A.dtls</key>
		<integer>129</integer>
		<key>u1D55A.it</key>
		<integer>286</integer>
		<key>u1D55B.bi</key>
		<integer>341</integer>
		<key>u1D55B.dtls</key>
		<integer>182</integer>
		<key>u1D55B.it</key>
		<integer>313</integer>
		<key>u1D55C.bi</key>
		<integer>425</integer>
		<key>u1D55C.it</key>
		<integer>377</integer>
		<key>u1D55D.bi</key>
		<integer>323</integer>
		<key>u1D55D.it</key>
		<integer>291</integer>
		<key>u1D55E.bi</key>
		<integer>566</integer>
		<key>u1D55E.it</key>
		<integer>519</integer>
		<key>u1D55F.bi</key>
		<integer>425</integer>
		<key>u1D55F.it</key>
		<integer>385</integer>
		<key>u1D560.bi</key>
		<integer>354</integer>
		<key>u1D560.it</key>
		<integer>314</integer>
		<key>u1D561.bi</key>
		<integer>424</integer>
		<key>u1D561.it</key>
		<integer>396</integer>
		<key>u1D562.bi</key>
		<integer>437</integer>
		<key>u1D562.it</key>
		<integer>407</integer>
		<key>u1D563.bi</key>
		<integer>385</integer>
		<key>u1D563.it</key>
		<integer>347</integer>
		<key>u1D564.bi</key>
		<integer>376</integer>
		<key>u1D564.it</key>
		<integer>351</integer>
		<key>u1D565.bi</key>
		<integer>322</integer>
		<key>u1D565.it</key>
		<integer>274</integer>
		<key>u1D566.bi</key>
		<integer>436</integer>
		<key>u1D566.it</key>
		<integer>343</integer>
		<key>u1D567.bi</key>
		<integer>319</integer>
		<key>u1D567.it</key>
		<integer>300</integer>
		<key>u1D568.bi</key>
		<integer>446</integer>
		<key>u1D568.it</key>
		<integer>403</integer>
		<key>u1D569.bi</key>
		<integer>356</integer>
		<key>u1D569.it</key>
		<integer>348</integer>
		<key>u1D56A.bi</key>
		<integer>417</integer>
		<key>u1D56A.it</key>
		<integer>343</integer>
		<key>u1D56B.bi</key>
		<integer>414</integer>
		<key>u1D56B.it</key>
		<integer>375</integer>
		<key>u1D58E.dtls</key>
		<integer>199</integer>
		<key>u1D58F.dtls</key>
		<integer>206</integer>
		<key>u1D5A0</key>
		<integer>333</integer>
		<key>u1D5A1</key>
		<integer>250</integer>
		<key>u1D5A2</key>
		<integer>364</integer>
		<key>u1D5A3</key>
		<integer>365</integer>
		<key>u1D5A4</key>
		<integer>307</integer>
		<key>u1D5A5</key>
		<integer>298</integer>
		<key>u1D5A6</key>
		<integer>371</integer>
		<key>u1D5A7</key>
		<integer>329</integer>
		<key>u1D5A8</key>
		<integer>200</integer>
		<key>u1D5A9</key>
		<integer>264</integer>
		<key>u1D5AA</key>
		<integer>280</integer>
		<key>u1D5AB</key>
		<integer>318</integer>
		<key>u1D5AC</key>
		<integer>421</integer>
		<key>u1D5AD</key>
		<integer>337</integer>
		<key>u1D5AE</key>
		<integer>357</integer>
		<key>u1D5AF</key>
		<integer>261</integer>
		<key>u1D5B0</key>
		<integer>360</integer>
		<key>u1D5B1</key>
		<integer>261</integer>
		<key>u1D5B2</key>
		<integer>256</integer>
		<key>u1D5B3</key>
		<integer>304</integer>
		<key>u1D5B4</key>
		<integer>330</integer>
		<key>u1D5B5</key>
		<integer>327</integer>
		<key>u1D5B6</key>
		<integer>460</integer>
		<key>u1D5B7</key>
		<integer>350</integer>
		<key>u1D5B8</key>
		<integer>315</integer>
		<key>u1D5B9</key>
		<integer>315</integer>
		<key>u1D5BA</key>
		<integer>213</integer>
		<key>u1D5BB</key>
		<integer>264</integer>
		<key>u1D5BC</key>
		<integer>227</integer>
		<key>u1D5BD</key>
		<integer>232</integer>
		<key>u1D5BE</key>
		<integer>225</integer>
		<key>u1D5BF</key>
		<integer>244</integer>
		<key>u1D5C0</key>
		<integer>244</integer>
		<key>u1D5C1</key>
		<integer>243</integer>
		<key>u1D5C2</key>
		<integer>110</integer>
		<key>u1D5C2.dtls</key>
		<integer>110</integer>
		<key>u1D5C3</key>
		<integer>141</integer>
		<key>u1D5C3.dtls</key>
		<integer>139</integer>
		<key>u1D5C4</key>
		<integer>257</integer>
		<key>u1D5C5</key>
		<integer>102</integer>
		<key>u1D5C6</key>
		<integer>378</integer>
		<key>u1D5C7</key>
		<integer>243</integer>
		<key>u1D5C8</key>
		<integer>249</integer>
		<key>u1D5C9</key>
		<integer>268</integer>
		<key>u1D5CA</key>
		<integer>231</integer>
		<key>u1D5CB</key>
		<integer>195</integer>
		<key>u1D5CC</key>
		<integer>199</integer>
		<key>u1D5CD</key>
		<integer>124</integer>
		<key>u1D5CE</key>
		<integer>246</integer>
		<key>u1D5CF</key>
		<integer>237</integer>
		<key>u1D5D0</key>
		<integer>351</integer>
		<key>u1D5D1</key>
		<integer>241</integer>
		<key>u1D5D2</key>
		<integer>260</integer>
		<key>u1D5D3</key>
		<integer>221</integer>
		<key>u1D5D4</key>
		<integer>345</integer>
		<key>u1D5D5</key>
		<integer>273</integer>
		<key>u1D5D6</key>
		<integer>368</integer>
		<key>u1D5D7</key>
		<integer>377</integer>
		<key>u1D5D8</key>
		<integer>338</integer>
		<key>u1D5D9</key>
		<integer>325</integer>
		<key>u1D5DA</key>
		<integer>392</integer>
		<key>u1D5DB</key>
		<integer>357</integer>
		<key>u1D5DC</key>
		<integer>220</integer>
		<key>u1D5DD</key>
		<integer>344</integer>
		<key>u1D5DE</key>
		<integer>333</integer>
		<key>u1D5DF</key>
		<integer>333</integer>
		<key>u1D5E0</key>
		<integer>456</integer>
		<key>u1D5E1</key>
		<integer>362</integer>
		<key>u1D5E2</key>
		<integer>389</integer>
		<key>u1D5E3</key>
		<integer>290</integer>
		<key>u1D5E4</key>
		<integer>394</integer>
		<key>u1D5E5</key>
		<integer>298</integer>
		<key>u1D5E6</key>
		<integer>273</integer>
		<key>u1D5E7</key>
		<integer>320</integer>
		<key>u1D5E8</key>
		<integer>349</integer>
		<key>u1D5E9</key>
		<integer>345</integer>
		<key>u1D5EA</key>
		<integer>498</integer>
		<key>u1D5EB</key>
		<integer>370</integer>
		<key>u1D5EC</key>
		<integer>347</integer>
		<key>u1D5ED</key>
		<integer>324</integer>
		<key>u1D5EE</key>
		<integer>225</integer>
		<key>u1D5EF</key>
		<integer>271</integer>
		<key>u1D5F0</key>
		<integer>233</integer>
		<key>u1D5F1</key>
		<integer>244</integer>
		<key>u1D5F2</key>
		<integer>229</integer>
		<key>u1D5F3</key>
		<integer>242</integer>
		<key>u1D5F4</key>
		<integer>259</integer>
		<key>u1D5F5</key>
		<integer>255</integer>
		<key>u1D5F6</key>
		<integer>122</integer>
		<key>u1D5F6.dtls</key>
		<integer>122</integer>
		<key>u1D5F7</key>
		<integer>106</integer>
		<key>u1D5F7.dtls</key>
		<integer>195</integer>
		<key>u1D5F8</key>
		<integer>280</integer>
		<key>u1D5F9</key>
		<integer>117</integer>
		<key>u1D5FA</key>
		<integer>388</integer>
		<key>u1D5FB</key>
		<integer>255</integer>
		<key>u1D5FC</key>
		<integer>250</integer>
		<key>u1D5FD</key>
		<integer>271</integer>
		<key>u1D5FE</key>
		<integer>241</integer>
		<key>u1D5FF</key>
		<integer>230</integer>
		<key>u1D600</key>
		<integer>191</integer>
		<key>u1D601</key>
		<integer>189</integer>
		<key>u1D602</key>
		<integer>259</integer>
		<key>u1D603</key>
		<integer>231</integer>
		<key>u1D604</key>
		<integer>350</integer>
		<key>u1D605</key>
		<integer>253</integer>
		<key>u1D606</key>
		<integer>236</integer>
		<key>u1D607</key>
		<integer>219</integer>
		<key>u1D608</key>
		<integer>499</integer>
		<key>u1D609</key>
		<integer>436</integer>
		<key>u1D60A</key>
		<integer>536</integer>
		<key>u1D60B</key>
		<integer>412</integer>
		<key>u1D60C</key>
		<integer>470</integer>
		<key>u1D60D</key>
		<integer>472</integer>
		<key>u1D60E</key>
		<integer>544</integer>
		<key>u1D60F</key>
		<integer>472</integer>
		<key>u1D610</key>
		<integer>352</integer>
		<key>u1D611</key>
		<integer>424</integer>
		<key>u1D612</key>
		<integer>468</integer>
		<key>u1D613</key>
		<integer>430</integer>
		<key>u1D614</key>
		<integer>584</integer>
		<key>u1D615</key>
		<integer>526</integer>
		<key>u1D616</key>
		<integer>527</integer>
		<key>u1D617</key>
		<integer>436</integer>
		<key>u1D618</key>
		<integer>525</integer>
		<key>u1D619</key>
		<integer>420</integer>
		<key>u1D61A</key>
		<integer>426</integer>
		<key>u1D61B</key>
		<integer>454</integer>
		<key>u1D61C</key>
		<integer>489</integer>
		<key>u1D61D</key>
		<integer>492</integer>
		<key>u1D61E</key>
		<integer>626</integer>
		<key>u1D61F</key>
		<integer>510</integer>
		<key>u1D620</key>
		<integer>480</integer>
		<key>u1D621</key>
		<integer>486</integer>
		<key>u1D622</key>
		<integer>333</integer>
		<key>u1D623</key>
		<integer>304</integer>
		<key>u1D624</key>
		<integer>355</integer>
		<key>u1D625</key>
		<integer>458</integer>
		<key>u1D626</key>
		<integer>350</integer>
		<key>u1D627</key>
		<integer>414</integer>
		<key>u1D628</key>
		<integer>352</integer>
		<key>u1D629</key>
		<integer>286</integer>
		<key>u1D62A</key>
		<integer>268</integer>
		<key>u1D62A.dtls</key>
		<integer>219</integer>
		<key>u1D62B</key>
		<integer>303</integer>
		<key>u1D62B.dtls</key>
		<integer>244</integer>
		<key>u1D62C</key>
		<integer>309</integer>
		<key>u1D62D</key>
		<integer>268</integer>
		<key>u1D62E</key>
		<integer>512</integer>
		<key>u1D62F</key>
		<integer>360</integer>
		<key>u1D630</key>
		<integer>360</integer>
		<key>u1D631</key>
		<integer>396</integer>
		<key>u1D632</key>
		<integer>360</integer>
		<key>u1D633</key>
		<integer>335</integer>
		<key>u1D634</key>
		<integer>310</integer>
		<key>u1D635</key>
		<integer>278</integer>
		<key>u1D636</key>
		<integer>346</integer>
		<key>u1D637</key>
		<integer>349</integer>
		<key>u1D638</key>
		<integer>439</integer>
		<key>u1D639</key>
		<integer>343</integer>
		<key>u1D63A</key>
		<integer>355</integer>
		<key>u1D63B</key>
		<integer>351</integer>
		<key>u1D63C</key>
		<integer>517</integer>
		<key>u1D63D</key>
		<integer>468</integer>
		<key>u1D63E</key>
		<integer>570</integer>
		<key>u1D63F</key>
		<integer>494</integer>
		<key>u1D640</key>
		<integer>496</integer>
		<key>u1D641</key>
		<integer>504</integer>
		<key>u1D642</key>
		<integer>564</integer>
		<key>u1D643</key>
		<integer>524</integer>
		<key>u1D644</key>
		<integer>408</integer>
		<key>u1D645</key>
		<integer>506</integer>
		<key>u1D646</key>
		<integer>512</integer>
		<key>u1D647</key>
		<integer>440</integer>
		<key>u1D648</key>
		<integer>620</integer>
		<key>u1D649</key>
		<integer>548</integer>
		<key>u1D64A</key>
		<integer>561</integer>
		<key>u1D64B</key>
		<integer>458</integer>
		<key>u1D64C</key>
		<integer>562</integer>
		<key>u1D64D</key>
		<integer>467</integer>
		<key>u1D64E</key>
		<integer>444</integer>
		<key>u1D64F</key>
		<integer>507</integer>
		<key>u1D650</key>
		<integer>522</integer>
		<key>u1D651</key>
		<integer>513</integer>
		<key>u1D652</key>
		<integer>666</integer>
		<key>u1D653</key>
		<integer>536</integer>
		<key>u1D654</key>
		<integer>515</integer>
		<key>u1D655</key>
		<integer>531</integer>
		<key>u1D656</key>
		<integer>353</integer>
		<key>u1D657</key>
		<integer>304</integer>
		<key>u1D658</key>
		<integer>371</integer>
		<key>u1D659</key>
		<integer>443</integer>
		<key>u1D65A</key>
		<integer>355</integer>
		<key>u1D65B</key>
		<integer>412</integer>
		<key>u1D65C</key>
		<integer>372</integer>
		<key>u1D65D</key>
		<integer>298</integer>
		<key>u1D65E</key>
		<integer>284</integer>
		<key>u1D65E.dtls</key>
		<integer>233</integer>
		<key>u1D65F</key>
		<integer>351</integer>
		<key>u1D65F.dtls</key>
		<integer>306</integer>
		<key>u1D660</key>
		<integer>327</integer>
		<key>u1D661</key>
		<integer>263</integer>
		<key>u1D662</key>
		<integer>534</integer>
		<key>u1D663</key>
		<integer>362</integer>
		<key>u1D664</key>
		<integer>371</integer>
		<key>u1D665</key>
		<integer>367</integer>
		<key>u1D666</key>
		<integer>359</integer>
		<key>u1D667</key>
		<integer>373</integer>
		<key>u1D668</key>
		<integer>311</integer>
		<key>u1D669</key>
		<integer>332</integer>
		<key>u1D66A</key>
		<integer>367</integer>
		<key>u1D66B</key>
		<integer>345</integer>
		<key>u1D66C</key>
		<integer>464</integer>
		<key>u1D66D</key>
		<integer>361</integer>
		<key>u1D66E</key>
		<integer>349</integer>
		<key>u1D66F</key>
		<integer>347</integer>
		<key>u1D670</key>
		<integer>262</integer>
		<key>u1D671</key>
		<integer>262</integer>
		<key>u1D672</key>
		<integer>262</integer>
		<key>u1D673</key>
		<integer>262</integer>
		<key>u1D674</key>
		<integer>262</integer>
		<key>u1D675</key>
		<integer>262</integer>
		<key>u1D676</key>
		<integer>262</integer>
		<key>u1D677</key>
		<integer>262</integer>
		<key>u1D678</key>
		<integer>262</integer>
		<key>u1D679</key>
		<integer>360</integer>
		<key>u1D67A</key>
		<integer>262</integer>
		<key>u1D67B</key>
		<integer>262</integer>
		<key>u1D67C</key>
		<integer>262</integer>
		<key>u1D67D</key>
		<integer>262</integer>
		<key>u1D67E</key>
		<integer>262</integer>
		<key>u1D67F</key>
		<integer>262</integer>
		<key>u1D680</key>
		<integer>262</integer>
		<key>u1D681</key>
		<integer>262</integer>
		<key>u1D682</key>
		<integer>262</integer>
		<key>u1D683</key>
		<integer>262</integer>
		<key>u1D684</key>
		<integer>262</integer>
		<key>u1D685</key>
		<integer>262</integer>
		<key>u1D686</key>
		<integer>262</integer>
		<key>u1D687</key>
		<integer>262</integer>
		<key>u1D688</key>
		<integer>262</integer>
		<key>u1D689</key>
		<integer>262</integer>
		<key>u1D68A</key>
		<integer>262</integer>
		<key>u1D68B</key>
		<integer>262</integer>
		<key>u1D68C</key>
		<integer>308</integer>
		<key>u1D68D</key>
		<integer>262</integer>
		<key>u1D68E</key>
		<integer>262</integer>
		<key>u1D68F</key>
		<integer>262</integer>
		<key>u1D690</key>
		<integer>262</integer>
		<key>u1D691</key>
		<integer>262</integer>
		<key>u1D692</key>
		<integer>262</integer>
		<key>u1D692.dtls</key>
		<integer>206</integer>
		<key>u1D693</key>
		<integer>262</integer>
		<key>u1D693.dtls</key>
		<integer>253</integer>
		<key>u1D694</key>
		<integer>262</integer>
		<key>u1D695</key>
		<integer>262</integer>
		<key>u1D696</key>
		<integer>262</integer>
		<key>u1D697</key>
		<integer>262</integer>
		<key>u1D698</key>
		<integer>262</integer>
		<key>u1D699</key>
		<integer>262</integer>
		<key>u1D69A</key>
		<integer>262</integer>
		<key>u1D69B</key>
		<integer>262</integer>
		<key>u1D69C</key>
		<integer>262</integer>
		<key>u1D69D</key>
		<integer>262</integer>
		<key>u1D69E</key>
		<integer>262</integer>
		<key>u1D69F</key>
		<integer>262</integer>
		<key>u1D6A0</key>
		<integer>262</integer>
		<key>u1D6A1</key>
		<integer>262</integer>
		<key>u1D6A2</key>
		<integer>262</integer>
		<key>u1D6A3</key>
		<integer>262</integer>
		<key>u1D6A4</key>
		<integer>179</integer>
		<key>u1D6A5</key>
		<integer>193</integer>
		<key>u1D6A8</key>
		<integer>343</integer>
		<key>u1D6A9</key>
		<integer>318</integer>
		<key>u1D6AA</key>
		<integer>304</integer>
		<key>u1D6AB</key>
		<integer>326</integer>
		<key>u1D6AC</key>
		<integer>328</integer>
		<key>u1D6AD</key>
		<integer>331</integer>
		<key>u1D6AE</key>
		<integer>390</integer>
		<key>u1D6AF</key>
		<integer>389</integer>
		<key>u1D6B0</key>
		<integer>195</integer>
		<key>u1D6B1</key>
		<integer>410</integer>
		<key>u1D6B2</key>
		<integer>336</integer>
		<key>u1D6B3</key>
		<integer>468</integer>
		<key>u1D6B4</key>
		<integer>358</integer>
		<key>u1D6B5</key>
		<integer>312</integer>
		<key>u1D6B6</key>
		<integer>389</integer>
		<key>u1D6B7</key>
		<integer>390</integer>
		<key>u1D6B8</key>
		<integer>308</integer>
		<key>u1D6B9</key>
		<integer>389</integer>
		<key>u1D6BA</key>
		<integer>320</integer>
		<key>u1D6BB</key>
		<integer>334</integer>
		<key>u1D6BC</key>
		<integer>403</integer>
		<key>u1D6BD</key>
		<integer>418</integer>
		<key>u1D6BE</key>
		<integer>400</integer>
		<key>u1D6BF</key>
		<integer>394</integer>
		<key>u1D6C0</key>
		<integer>379</integer>
		<key>u1D6C1</key>
		<integer>336</integer>
		<key>u1D6C2</key>
		<integer>256</integer>
		<key>u1D6C3</key>
		<integer>284</integer>
		<key>u1D6C4</key>
		<integer>256</integer>
		<key>u1D6C5</key>
		<integer>252</integer>
		<key>u1D6C6</key>
		<integer>228</integer>
		<key>u1D6C7</key>
		<integer>230</integer>
		<key>u1D6C8</key>
		<integer>278</integer>
		<key>u1D6C9</key>
		<integer>250</integer>
		<key>u1D6CA</key>
		<integer>136</integer>
		<key>u1D6CB</key>
		<integer>290</integer>
		<key>u1D6CC</key>
		<integer>141</integer>
		<key>u1D6CD</key>
		<integer>282</integer>
		<key>u1D6CE</key>
		<integer>255</integer>
		<key>u1D6CF</key>
		<integer>231</integer>
		<key>u1D6D0</key>
		<integer>250</integer>
		<key>u1D6D1</key>
		<integer>314</integer>
		<key>u1D6D2</key>
		<integer>280</integer>
		<key>u1D6D3</key>
		<integer>234</integer>
		<key>u1D6D4</key>
		<integer>277</integer>
		<key>u1D6D5</key>
		<integer>238</integer>
		<key>u1D6D6</key>
		<integer>282</integer>
		<key>u1D6D7</key>
		<integer>326</integer>
		<key>u1D6D8</key>
		<integer>304</integer>
		<key>u1D6D9</key>
		<integer>382</integer>
		<key>u1D6DA</key>
		<integer>367</integer>
		<key>u1D6DB</key>
		<integer>258</integer>
		<key>u1D6DC</key>
		<integer>228</integer>
		<key>u1D6DD</key>
		<integer>316</integer>
		<key>u1D6DE</key>
		<integer>279</integer>
		<key>u1D6DF</key>
		<integer>326</integer>
		<key>u1D6E0</key>
		<integer>256</integer>
		<key>u1D6E1</key>
		<integer>476</integer>
		<key>u1D6E2</key>
		<integer>508</integer>
		<key>u1D6E2.ss</key>
		<integer>499</integer>
		<key>u1D6E3</key>
		<integer>464</integer>
		<key>u1D6E3.ss</key>
		<integer>436</integer>
		<key>u1D6E4</key>
		<integer>492</integer>
		<key>u1D6E4.ss</key>
		<integer>443</integer>
		<key>u1D6E5</key>
		<integer>453</integer>
		<key>u1D6E5.ss</key>
		<integer>498</integer>
		<key>u1D6E6</key>
		<integer>492</integer>
		<key>u1D6E6.ss</key>
		<integer>470</integer>
		<key>u1D6E7</key>
		<integer>503</integer>
		<key>u1D6E7.ss</key>
		<integer>486</integer>
		<key>u1D6E8</key>
		<integer>560</integer>
		<key>u1D6E8.ss</key>
		<integer>472</integer>
		<key>u1D6E9</key>
		<integer>463</integer>
		<key>u1D6E9.ss</key>
		<integer>490</integer>
		<key>u1D6EA</key>
		<integer>360</integer>
		<key>u1D6EA.ss</key>
		<integer>352</integer>
		<key>u1D6EB</key>
		<integer>540</integer>
		<key>u1D6EB.ss</key>
		<integer>468</integer>
		<key>u1D6EC</key>
		<integer>510</integer>
		<key>u1D6EC.ss</key>
		<integer>492</integer>
		<key>u1D6ED</key>
		<integer>614</integer>
		<key>u1D6ED.ss</key>
		<integer>584</integer>
		<key>u1D6EE</key>
		<integer>538</integer>
		<key>u1D6EE.ss</key>
		<integer>526</integer>
		<key>u1D6EF</key>
		<integer>476</integer>
		<key>u1D6EF.ss</key>
		<integer>495</integer>
		<key>u1D6F0</key>
		<integer>465</integer>
		<key>u1D6F0.ss</key>
		<integer>490</integer>
		<key>u1D6F1</key>
		<integer>554</integer>
		<key>u1D6F1.ss</key>
		<integer>504</integer>
		<key>u1D6F2</key>
		<integer>477</integer>
		<key>u1D6F2.ss</key>
		<integer>436</integer>
		<key>u1D6F3</key>
		<integer>465</integer>
		<key>u1D6F3.ss</key>
		<integer>490</integer>
		<key>u1D6F4</key>
		<integer>509</integer>
		<key>u1D6F4.ss</key>
		<integer>463</integer>
		<key>u1D6F5</key>
		<integer>348</integer>
		<key>u1D6F5.ss</key>
		<integer>392</integer>
		<key>u1D6F6</key>
		<integer>358</integer>
		<key>u1D6F6.ss</key>
		<integer>396</integer>
		<key>u1D6F7</key>
		<integer>464</integer>
		<key>u1D6F7.ss</key>
		<integer>486</integer>
		<key>u1D6F8</key>
		<integer>530</integer>
		<key>u1D6F8.ss</key>
		<integer>510</integer>
		<key>u1D6F9</key>
		<integer>386</integer>
		<key>u1D6F9.ss</key>
		<integer>456</integer>
		<key>u1D6FA</key>
		<integer>546</integer>
		<key>u1D6FA.ss</key>
		<integer>539</integer>
		<key>u1D6FB</key>
		<integer>321</integer>
		<key>u1D6FC</key>
		<integer>284</integer>
		<key>u1D6FC.ss</key>
		<integer>393</integer>
		<key>u1D6FD</key>
		<integer>424</integer>
		<key>u1D6FD.ss</key>
		<integer>427</integer>
		<key>u1D6FE</key>
		<integer>246</integer>
		<key>u1D6FE.ss</key>
		<integer>351</integer>
		<key>u1D6FF</key>
		<integer>342</integer>
		<key>u1D6FF.ss</key>
		<integer>376</integer>
		<key>u1D700</key>
		<integer>291</integer>
		<key>u1D700.ss</key>
		<integer>347</integer>
		<key>u1D701</key>
		<integer>334</integer>
		<key>u1D701.ss</key>
		<integer>336</integer>
		<key>u1D702</key>
		<integer>288</integer>
		<key>u1D702.ss</key>
		<integer>346</integer>
		<key>u1D703</key>
		<integer>323</integer>
		<key>u1D703.ss</key>
		<integer>359</integer>
		<key>u1D704</key>
		<integer>164</integer>
		<key>u1D704.ss</key>
		<integer>248</integer>
		<key>u1D705</key>
		<integer>300</integer>
		<key>u1D705.ss</key>
		<integer>336</integer>
		<key>u1D706</key>
		<integer>348</integer>
		<key>u1D706.ss</key>
		<integer>368</integer>
		<key>u1D707</key>
		<integer>374</integer>
		<key>u1D707.ss</key>
		<integer>380</integer>
		<key>u1D708</key>
		<integer>284</integer>
		<key>u1D708.ss</key>
		<integer>288</integer>
		<key>u1D709</key>
		<integer>308</integer>
		<key>u1D709.ss</key>
		<integer>349</integer>
		<key>u1D70A</key>
		<integer>293</integer>
		<key>u1D70A.ss</key>
		<integer>366</integer>
		<key>u1D70B</key>
		<integer>370</integer>
		<key>u1D70B.ss</key>
		<integer>382</integer>
		<key>u1D70C</key>
		<integer>333</integer>
		<key>u1D70C.ss</key>
		<integer>367</integer>
		<key>u1D70D</key>
		<integer>250</integer>
		<key>u1D70D.ss</key>
		<integer>318</integer>
		<key>u1D70E</key>
		<integer>294</integer>
		<key>u1D70E.ss</key>
		<integer>403</integer>
		<key>u1D70F</key>
		<integer>295</integer>
		<key>u1D70F.ss</key>
		<integer>334</integer>
		<key>u1D710</key>
		<integer>238</integer>
		<key>u1D710.ss</key>
		<integer>357</integer>
		<key>u1D711</key>
		<integer>340</integer>
		<key>u1D711.ss</key>
		<integer>432</integer>
		<key>u1D712</key>
		<integer>438</integer>
		<key>u1D712.ss</key>
		<integer>357</integer>
		<key>u1D713</key>
		<integer>370</integer>
		<key>u1D713.ss</key>
		<integer>427</integer>
		<key>u1D714</key>
		<integer>392</integer>
		<key>u1D714.ss</key>
		<integer>415</integer>
		<key>u1D715</key>
		<integer>314</integer>
		<key>u1D715.ss</key>
		<integer>368</integer>
		<key>u1D716</key>
		<integer>309</integer>
		<key>u1D716.ss</key>
		<integer>331</integer>
		<key>u1D717</key>
		<integer>348</integer>
		<key>u1D717.ss</key>
		<integer>367</integer>
		<key>u1D718</key>
		<integer>300</integer>
		<key>u1D719</key>
		<integer>442</integer>
		<key>u1D719.ss</key>
		<integer>458</integer>
		<key>u1D71A</key>
		<integer>350</integer>
		<key>u1D71A.ss</key>
		<integer>368</integer>
		<key>u1D71B</key>
		<integer>504</integer>
		<key>u1D71B.ss</key>
		<integer>497</integer>
		<key>u1D71C</key>
		<integer>518</integer>
		<key>u1D71D</key>
		<integer>496</integer>
		<key>u1D71E</key>
		<integer>532</integer>
		<key>u1D71F</key>
		<integer>464</integer>
		<key>u1D720</key>
		<integer>512</integer>
		<key>u1D721</key>
		<integer>518</integer>
		<key>u1D722</key>
		<integer>560</integer>
		<key>u1D723</key>
		<integer>485</integer>
		<key>u1D724</key>
		<integer>366</integer>
		<key>u1D725</key>
		<integer>558</integer>
		<key>u1D726</key>
		<integer>520</integer>
		<key>u1D727</key>
		<integer>632</integer>
		<key>u1D728</key>
		<integer>523</integer>
		<key>u1D729</key>
		<integer>495</integer>
		<key>u1D72A</key>
		<integer>483</integer>
		<key>u1D72B</key>
		<integer>558</integer>
		<key>u1D72C</key>
		<integer>526</integer>
		<key>u1D72D</key>
		<integer>485</integer>
		<key>u1D72E</key>
		<integer>526</integer>
		<key>u1D72F</key>
		<integer>364</integer>
		<key>u1D730</key>
		<integer>376</integer>
		<key>u1D731</key>
		<integer>482</integer>
		<key>u1D732</key>
		<integer>535</integer>
		<key>u1D733</key>
		<integer>406</integer>
		<key>u1D734</key>
		<integer>548</integer>
		<key>u1D735</key>
		<integer>322</integer>
		<key>u1D736</key>
		<integer>337</integer>
		<key>u1D737</key>
		<integer>462</integer>
		<key>u1D738</key>
		<integer>274</integer>
		<key>u1D739</key>
		<integer>369</integer>
		<key>u1D73A</key>
		<integer>280</integer>
		<key>u1D73B</key>
		<integer>369</integer>
		<key>u1D73C</key>
		<integer>336</integer>
		<key>u1D73D</key>
		<integer>378</integer>
		<key>u1D73E</key>
		<integer>164</integer>
		<key>u1D73F</key>
		<integer>316</integer>
		<key>u1D740</key>
		<integer>346</integer>
		<key>u1D741</key>
		<integer>404</integer>
		<key>u1D742</key>
		<integer>372</integer>
		<key>u1D743</key>
		<integer>348</integer>
		<key>u1D744</key>
		<integer>340</integer>
		<key>u1D745</key>
		<integer>384</integer>
		<key>u1D746</key>
		<integer>398</integer>
		<key>u1D747</key>
		<integer>404</integer>
		<key>u1D748</key>
		<integer>324</integer>
		<key>u1D749</key>
		<integer>308</integer>
		<key>u1D74A</key>
		<integer>284</integer>
		<key>u1D74B</key>
		<integer>429</integer>
		<key>u1D74C</key>
		<integer>444</integer>
		<key>u1D74D</key>
		<integer>394</integer>
		<key>u1D74E</key>
		<integer>398</integer>
		<key>u1D74F</key>
		<integer>404</integer>
		<key>u1D750</key>
		<integer>326</integer>
		<key>u1D751</key>
		<integer>398</integer>
		<key>u1D752</key>
		<integer>309</integer>
		<key>u1D753</key>
		<integer>454</integer>
		<key>u1D754</key>
		<integer>370</integer>
		<key>u1D755</key>
		<integer>506</integer>
		<key>u1D790</key>
		<integer>517</integer>
		<key>u1D791</key>
		<integer>468</integer>
		<key>u1D792</key>
		<integer>501</integer>
		<key>u1D793</key>
		<integer>534</integer>
		<key>u1D794</key>
		<integer>496</integer>
		<key>u1D795</key>
		<integer>531</integer>
		<key>u1D796</key>
		<integer>524</integer>
		<key>u1D797</key>
		<integer>559</integer>
		<key>u1D798</key>
		<integer>417</integer>
		<key>u1D799</key>
		<integer>512</integer>
		<key>u1D79A</key>
		<integer>507</integer>
		<key>u1D79B</key>
		<integer>620</integer>
		<key>u1D79C</key>
		<integer>548</integer>
		<key>u1D79D</key>
		<integer>513</integer>
		<key>u1D79E</key>
		<integer>561</integer>
		<key>u1D79F</key>
		<integer>532</integer>
		<key>u1D7A0</key>
		<integer>458</integer>
		<key>u1D7A1</key>
		<integer>559</integer>
		<key>u1D7A2</key>
		<integer>504</integer>
		<key>u1D7A3</key>
		<integer>520</integer>
		<key>u1D7A4</key>
		<integer>465</integer>
		<key>u1D7A5</key>
		<integer>561</integer>
		<key>u1D7A6</key>
		<integer>536</integer>
		<key>u1D7A7</key>
		<integer>535</integer>
		<key>u1D7A8</key>
		<integer>548</integer>
		<key>u1D7A9</key>
		<integer>454</integer>
		<key>u1D7AA</key>
		<integer>439</integer>
		<key>u1D7AB</key>
		<integer>436</integer>
		<key>u1D7AC</key>
		<integer>362</integer>
		<key>u1D7AD</key>
		<integer>391</integer>
		<key>u1D7AE</key>
		<integer>352</integer>
		<key>u1D7AF</key>
		<integer>402</integer>
		<key>u1D7B0</key>
		<integer>366</integer>
		<key>u1D7B1</key>
		<integer>380</integer>
		<key>u1D7B2</key>
		<integer>262</integer>
		<key>u1D7B3</key>
		<integer>368</integer>
		<key>u1D7B4</key>
		<integer>387</integer>
		<key>u1D7B5</key>
		<integer>397</integer>
		<key>u1D7B6</key>
		<integer>362</integer>
		<key>u1D7B7</key>
		<integer>392</integer>
		<key>u1D7B8</key>
		<integer>371</integer>
		<key>u1D7B9</key>
		<integer>416</integer>
		<key>u1D7BA</key>
		<integer>380</integer>
		<key>u1D7BB</key>
		<integer>358</integer>
		<key>u1D7BC</key>
		<integer>407</integer>
		<key>u1D7BD</key>
		<integer>361</integer>
		<key>u1D7BE</key>
		<integer>375</integer>
		<key>u1D7BF</key>
		<integer>441</integer>
		<key>u1D7C0</key>
		<integer>387</integer>
		<key>u1D7C1</key>
		<integer>451</integer>
		<key>u1D7C2</key>
		<integer>478</integer>
		<key>u1D7C3</key>
		<integer>374</integer>
		<key>u1D7C4</key>
		<integer>334</integer>
		<key>u1D7C5</key>
		<integer>389</integer>
		<key>u1D7C6</key>
		<integer>423</integer>
		<key>u1D7C7</key>
		<integer>489</integer>
		<key>u1D7C8</key>
		<integer>372</integer>
		<key>u1D7C9</key>
		<integer>544</integer>
		<key>uni0237</key>
		<integer>150</integer>
		<key>uni02C8</key>
		<integer>139</integer>
		<key>uni02C9</key>
		<integer>166</integer>
		<key>uni02CA</key>
		<integer>206</integer>
		<key>uni02CB</key>
		<integer>136</integer>
		<key>uni02CC</key>
		<integer>139</integer>
		<key>uni02CD</key>
		<integer>167</integer>
		<key>uni02CE</key>
		<integer>137</integer>
		<key>uni02CF</key>
		<integer>196</integer>
		<key>uni02F3</key>
		<integer>166</integer>
		<key>uni02F7</key>
		<integer>166</integer>
		<key>uni0300</key>
		<integer>-259</integer>
		<key>uni0301</key>
		<integer>-259</integer>
		<key>uni0302</key>
		<integer>-230</integer>
		<key>uni0302.size1</key>
		<integer>280</integer>
		<key>uni0302.size2</key>
		<integer>489</integer>
		<key>uni0302.size3</key>
		<integer>730</integer>
		<key>uni0302.size4</key>
		<integer>943</integer>
		<key>uni0302.size5</key>
		<integer>1164</integer>
		<key>uni0303</key>
		<integer>-230</integer>
		<key>uni0303.size1</key>
		<integer>280</integer>
		<key>uni0303.size2</key>
		<integer>489</integer>
		<key>uni0303.size3</key>
		<integer>730</integer>
		<key>uni0303.size4</key>
		<integer>943</integer>
		<key>uni0303.size5</key>
		<integer>1164</integer>
		<key>uni0304</key>
		<integer>-230</integer>
		<key>uni0305</key>
		<integer>-230</integer>
		<key>uni0305.size1</key>
		<integer>500</integer>
		<key>uni0305.size2</key>
		<integer>750</integer>
		<key>uni0305.size3</key>
		<integer>1000</integer>
		<key>uni0305.size4</key>
		<integer>1250</integer>
		<key>uni0305.size5</key>
		<integer>1500</integer>
		<key>uni0306</key>
		<integer>-232</integer>
		<key>uni0307</key>
		<integer>-230</integer>
		<key>uni0308</key>
		<integer>-230</integer>
		<key>uni0309</key>
		<integer>-212</integer>
		<key>uni030A</key>
		<integer>-230</integer>
		<key>uni030B</key>
		<integer>-212</integer>
		<key>uni030C</key>
		<integer>-230</integer>
		<key>uni030C.size1</key>
		<integer>280</integer>
		<key>uni030C.size2</key>
		<integer>489</integer>
		<key>uni030C.size3</key>
		<integer>730</integer>
		<key>uni030C.size4</key>
		<integer>943</integer>
		<key>uni030C.size5</key>
		<integer>1164</integer>
		<key>uni030D</key>
		<integer>-222</integer>
		<key>uni030E</key>
		<integer>-230</integer>
		<key>uni030F</key>
		<integer>-212</integer>
		<key>uni0310</key>
		<integer>-232</integer>
		<key>uni0311</key>
		<integer>-232</integer>
		<key>uni0312</key>
		<integer>-230</integer>
		<key>uni0313</key>
		<integer>-230</integer>
		<key>uni0314</key>
		<integer>-230</integer>
		<key>uni0315</key>
		<integer>-16</integer>
		<key>uni0316</key>
		<integer>-239</integer>
		<key>uni0317</key>
		<integer>-259</integer>
		<key>uni0318</key>
		<integer>-304</integer>
		<key>uni0319</key>
		<integer>-174</integer>
		<key>uni031A</key>
		<integer>-230</integer>
		<key>uni031B</key>
		<integer>4</integer>
		<key>uni031C</key>
		<integer>-296</integer>
		<key>uni031D</key>
		<integer>-230</integer>
		<key>uni031E</key>
		<integer>-230</integer>
		<key>uni031F</key>
		<integer>-230</integer>
		<key>uni0320</key>
		<integer>-230</integer>
		<key>uni0321</key>
		<integer>-117</integer>
		<key>uni0322</key>
		<integer>64</integer>
		<key>uni0323</key>
		<integer>-230</integer>
		<key>uni0324</key>
		<integer>-230</integer>
		<key>uni0325</key>
		<integer>-230</integer>
		<key>uni0326</key>
		<integer>-230</integer>
		<key>uni0327</key>
		<integer>-230</integer>
		<key>uni0328</key>
		<integer>-230</integer>
		<key>uni0329</key>
		<integer>-230</integer>
		<key>uni032A</key>
		<integer>-229</integer>
		<key>uni032B</key>
		<integer>-228</integer>
		<key>uni032C</key>
		<integer>-230</integer>
		<key>uni032D</key>
		<integer>-230</integer>
		<key>uni032E</key>
		<integer>-230</integer>
		<key>uni032F</key>
		<integer>-230</integer>
		<key>uni0330</key>
		<integer>-230</integer>
		<key>uni0330.size1</key>
		<integer>280</integer>
		<key>uni0330.size2</key>
		<integer>489</integer>
		<key>uni0330.size3</key>
		<integer>730</integer>
		<key>uni0330.size4</key>
		<integer>943</integer>
		<key>uni0330.size5</key>
		<integer>1164</integer>
		<key>uni0331</key>
		<integer>-230</integer>
		<key>uni0332</key>
		<integer>-230</integer>
		<key>uni0332.size1</key>
		<integer>500</integer>
		<key>uni0332.size2</key>
		<integer>750</integer>
		<key>uni0332.size3</key>
		<integer>1000</integer>
		<key>uni0332.size4</key>
		<integer>1250</integer>
		<key>uni0332.size5</key>
		<integer>1500</integer>
		<key>uni0333</key>
		<integer>-230</integer>
		<key>uni0334</key>
		<integer>-236</integer>
		<key>uni0335</key>
		<integer>-231</integer>
		<key>uni0336</key>
		<integer>-230</integer>
		<key>uni0337</key>
		<integer>-210</integer>
		<key>uni0338</key>
		<integer>-174</integer>
		<key>uni0338.size1</key>
		<integer>-339</integer>
		<key>uni0338.size2</key>
		<integer>-339</integer>
		<key>uni0338.size3</key>
		<integer>-340</integer>
		<key>uni0338.size4</key>
		<integer>-330</integer>
		<key>uni0338.size5</key>
		<integer>-343</integer>
		<key>uni0338.size6</key>
		<integer>-486</integer>
		<key>uni0339</key>
		<integer>-216</integer>
		<key>uni033A</key>
		<integer>-229</integer>
		<key>uni033B</key>
		<integer>-230</integer>
		<key>uni033C</key>
		<integer>-230</integer>
		<key>uni033D</key>
		<integer>-230</integer>
		<key>uni033E</key>
		<integer>-230</integer>
		<key>uni033F</key>
		<integer>-230</integer>
		<key>uni0346</key>
		<integer>-209</integer>
		<key>uni034C</key>
		<integer>-221</integer>
		<key>uni034D</key>
		<integer>239</integer>
		<key>uni0359</key>
		<integer>-222</integer>
		<key>uni035C</key>
		<integer>-39</integer>
		<key>uni0360</key>
		<integer>-15</integer>
		<key>uni0361</key>
		<integer>-39</integer>
		<key>uni0362</key>
		<integer>-20</integer>
		<key>uni0391</key>
		<integer>361</integer>
		<key>uni0392</key>
		<integer>305</integer>
		<key>uni0393</key>
		<integer>294</integer>
		<key>uni0394</key>
		<integer>362</integer>
		<key>uni0395</key>
		<integer>304</integer>
		<key>uni0396</key>
		<integer>304</integer>
		<key>uni0397</key>
		<integer>360</integer>
		<key>uni0398</key>
		<integer>361</integer>
		<key>uni0399</key>
		<integer>166</integer>
		<key>uni039A</key>
		<integer>370</integer>
		<key>uni039B</key>
		<integer>349</integer>
		<key>uni039C</key>
		<integer>438</integer>
		<key>uni039D</key>
		<integer>360</integer>
		<key>uni039E</key>
		<integer>322</integer>
		<key>uni039F</key>
		<integer>361</integer>
		<key>uni03A0</key>
		<integer>360</integer>
		<key>uni03A1</key>
		<integer>279</integer>
		<key>uni03A3</key>
		<integer>315</integer>
		<key>uni03A4</key>
		<integer>305</integer>
		<key>uni03A5</key>
		<integer>390</integer>
		<key>uni03A6</key>
		<integer>382</integer>
		<key>uni03A7</key>
		<integer>401</integer>
		<key>uni03A8</key>
		<integer>373</integer>
		<key>uni03A9</key>
		<integer>372</integer>
		<key>uni03B1</key>
		<integer>279</integer>
		<key>uni03B2</key>
		<integer>260</integer>
		<key>uni03B3</key>
		<integer>227</integer>
		<key>uni03B4</key>
		<integer>250</integer>
		<key>uni03B5</key>
		<integer>216</integer>
		<key>uni03B6</key>
		<integer>221</integer>
		<key>uni03B7</key>
		<integer>231</integer>
		<key>uni03B8</key>
		<integer>248</integer>
		<key>uni03B9</key>
		<integer>144</integer>
		<key>uni03BA</key>
		<integer>255</integer>
		<key>uni03BB</key>
		<integer>136</integer>
		<key>uni03BC</key>
		<integer>254</integer>
		<key>uni03BD</key>
		<integer>232</integer>
		<key>uni03BE</key>
		<integer>221</integer>
		<key>uni03BF</key>
		<integer>254</integer>
		<key>uni03C0</key>
		<integer>246</integer>
		<key>uni03C1</key>
		<integer>260</integer>
		<key>uni03C2</key>
		<integer>234</integer>
		<key>uni03C3</key>
		<integer>274</integer>
		<key>uni03C4</key>
		<integer>240</integer>
		<key>uni03C5</key>
		<integer>255</integer>
		<key>uni03C6</key>
		<integer>311</integer>
		<key>uni03C7</key>
		<integer>272</integer>
		<key>uni03C8</key>
		<integer>352</integer>
		<key>uni03C9</key>
		<integer>312</integer>
		<key>uni03D0</key>
		<integer>232</integer>
		<key>uni03D1</key>
		<integer>242</integer>
		<key>uni03D2</key>
		<integer>398</integer>
		<key>uni03D5</key>
		<integer>311</integer>
		<key>uni03D6</key>
		<integer>406</integer>
		<key>uni03D8</key>
		<integer>361</integer>
		<key>uni03D9</key>
		<integer>250</integer>
		<key>uni03DA</key>
		<integer>441</integer>
		<key>uni03DB</key>
		<integer>236</integer>
		<key>uni03DC</key>
		<integer>278</integer>
		<key>uni03DD</key>
		<integer>258</integer>
		<key>uni03F0</key>
		<integer>278</integer>
		<key>uni03F1</key>
		<integer>250</integer>
		<key>uni03F4</key>
		<integer>361</integer>
		<key>uni03F5</key>
		<integer>216</integer>
		<key>uni03F6</key>
		<integer>223</integer>
		<key>uni203E</key>
		<integer>250</integer>
		<key>uni203E.size1</key>
		<integer>500</integer>
		<key>uni203E.size2</key>
		<integer>750</integer>
		<key>uni203E.size3</key>
		<integer>1000</integer>
		<key>uni203E.size4</key>
		<integer>1250</integer>
		<key>uni203E.size5</key>
		<integer>1500</integer>
		<key>uni20D0</key>
		<integer>218</integer>
		<key>uni20D1</key>
		<integer>218</integer>
		<key>uni20D2</key>
		<integer>-217</integer>
		<key>uni20D2.size1</key>
		<integer>-217</integer>
		<key>uni20D2.size2</key>
		<integer>-217</integer>
		<key>uni20D2.size3</key>
		<integer>-217</integer>
		<key>uni20D2.size4</key>
		<integer>-217</integer>
		<key>uni20D2.size5</key>
		<integer>-217</integer>
		<key>uni20D2.size6</key>
		<integer>-217</integer>
		<key>uni20D6</key>
		<integer>218</integer>
		<key>uni20D7</key>
		<integer>218</integer>
		<key>uni20DB</key>
		<integer>-214</integer>
		<key>uni20DC</key>
		<integer>-252</integer>
		<key>uni20DD</key>
		<integer>-250</integer>
		<key>uni20DE</key>
		<integer>-250</integer>
		<key>uni20DF</key>
		<integer>-248</integer>
		<key>uni20E1</key>
		<integer>239</integer>
		<key>uni20E4</key>
		<integer>-249</integer>
		<key>uni20E5</key>
		<integer>-235</integer>
		<key>uni20E6</key>
		<integer>-218</integer>
		<key>uni20E7</key>
		<integer>-242</integer>
		<key>uni20E8</key>
		<integer>-214</integer>
		<key>uni20E9</key>
		<integer>-214</integer>
		<key>uni20EA</key>
		<integer>-270</integer>
		<key>uni20EB</key>
		<integer>-128</integer>
		<key>uni20EC</key>
		<integer>218</integer>
		<key>uni20ED</key>
		<integer>218</integer>
		<key>uni20EE</key>
		<integer>218</integer>
		<key>uni20EF</key>
		<integer>218</integer>
		<key>uni20F0</key>
		<integer>-222</integer>
		<key>uni2102.bi</key>
		<integer>458</integer>
		<key>uni2102.it</key>
		<integer>468</integer>
		<key>uni210A</key>
		<integer>491</integer>
		<key>uni210B</key>
		<integer>700</integer>
		<key>uni210B.cal</key>
		<integer>509</integer>
		<key>uni210D.bi</key>
		<integer>502</integer>
		<key>uni210D.it</key>
		<integer>492</integer>
		<key>uni210E</key>
		<integer>246</integer>
		<key>uni210F</key>
		<integer>246</integer>
		<key>uni2110</key>
		<integer>647</integer>
		<key>uni2110.cal</key>
		<integer>411</integer>
		<key>uni2112</key>
		<integer>850</integer>
		<key>uni2112.cal</key>
		<integer>476</integer>
		<key>uni2113</key>
		<integer>506</integer>
		<key>uni2115.bi</key>
		<integer>497</integer>
		<key>uni2115.it</key>
		<integer>490</integer>
		<key>uni2119.bi</key>
		<integer>454</integer>
		<key>uni2119.it</key>
		<integer>451</integer>
		<key>uni211A.bi</key>
		<integer>458</integer>
		<key>uni211A.it</key>
		<integer>474</integer>
		<key>uni211B</key>
		<integer>770</integer>
		<key>uni211B.cal</key>
		<integer>427</integer>
		<key>uni211D.bi</key>
		<integer>458</integer>
		<key>uni211D.it</key>
		<integer>451</integer>
		<key>uni2124.bi</key>
		<integer>554</integer>
		<key>uni2124.it</key>
		<integer>478</integer>
		<key>uni212C</key>
		<integer>808</integer>
		<key>uni212C.cal</key>
		<integer>512</integer>
		<key>uni212F</key>
		<integer>463</integer>
		<key>uni2130</key>
		<integer>633</integer>
		<key>uni2130.cal</key>
		<integer>446</integer>
		<key>uni2131</key>
		<integer>658</integer>
		<key>uni2131.cal</key>
		<integer>410</integer>
		<key>uni2133</key>
		<integer>867</integer>
		<key>uni2133.cal</key>
		<integer>713</integer>
		<key>uni2145</key>
		<integer>460</integer>
		<key>uni2146</key>
		<integer>437</integer>
		<key>uni2147</key>
		<integer>362</integer>
		<key>uni2148</key>
		<integer>286</integer>
		<key>uni2148.dtls</key>
		<integer>206</integer>
		<key>uni2149</key>
		<integer>313</integer>
		<key>uni2149.dtls</key>
		<integer>248</integer>
		<key>uni2202</key>
		<integer>232</integer>
		<key>uni2202.rtlm</key>
		<integer>201</integer>
		<key>uni2202.ss</key>
		<integer>349</integer>
		<key>uni220F</key>
		<integer>500</integer>
		<key>uni220F.display</key>
		<integer>678</integer>
		<key>uni2210</key>
		<integer>500</integer>
		<key>uni2210.display</key>
		<integer>678</integer>
		<key>uni2211</key>
		<integer>457</integer>
		<key>uni2211.display</key>
		<integer>646</integer>
		<key>uni22C5</key>
		<integer>143</integer>
		<key>uni23B4.size1</key>
		<integer>532</integer>
		<key>uni23B4.size2</key>
		<integer>803</integer>
		<key>uni23B4.size3</key>
		<integer>1074</integer>
		<key>uni23B4.size4</key>
		<integer>1346</integer>
		<key>uni23B4.size5</key>
		<integer>1618</integer>
		<key>uni23B5.size1</key>
		<integer>530</integer>
		<key>uni23B5.size2</key>
		<integer>803</integer>
		<key>uni23B5.size3</key>
		<integer>1074</integer>
		<key>uni23B5.size4</key>
		<integer>1346</integer>
		<key>uni23B5.size5</key>
		<integer>1618</integer>
		<key>uni23DC.size1</key>
		<integer>463</integer>
		<key>uni23DC.size2</key>
		<integer>730</integer>
		<key>uni23DC.size3</key>
		<integer>943</integer>
		<key>uni23DC.size4</key>
		<integer>1164</integer>
		<key>uni23DC.size5</key>
		<integer>1618</integer>
		<key>uni23DD.size1</key>
		<integer>463</integer>
		<key>uni23DD.size2</key>
		<integer>730</integer>
		<key>uni23DD.size3</key>
		<integer>943</integer>
		<key>uni23DD.size4</key>
		<integer>1164</integer>
		<key>uni23DD.size5</key>
		<integer>1618</integer>
		<key>uni23DE.size1</key>
		<integer>462</integer>
		<key>uni23DE.size2</key>
		<integer>730</integer>
		<key>uni23DE.size3</key>
		<integer>943</integer>
		<key>uni23DE.size4</key>
		<integer>1164</integer>
		<key>uni23DE.size5</key>
		<integer>1619</integer>
		<key>uni23DF.size1</key>
		<integer>462</integer>
		<key>uni23DF.size2</key>
		<integer>730</integer>
		<key>uni23DF.size3</key>
		<integer>943</integer>
		<key>uni23DF.size4</key>
		<integer>1164</integer>
		<key>uni23DF.size5</key>
		<integer>1619</integer>
		<key>uni23E0.size1</key>
		<integer>730</integer>
		<key>uni23E0.size2</key>
		<integer>943</integer>
		<key>uni23E0.size3</key>
		<integer>1156</integer>
		<key>uni23E0.size4</key>
		<integer>1369</integer>
		<key>uni23E0.size5</key>
		<integer>1582</integer>
		<key>uni23E1.size1</key>
		<integer>730</integer>
		<key>uni23E1.size2</key>
		<integer>943</integer>
		<key>uni23E1.size3</key>
		<integer>1156</integer>
		<key>uni23E1.size4</key>
		<integer>1369</integer>
		<key>uni23E1.size5</key>
		<integer>1582</integer>
		<key>v</key>
		<integer>276</integer>
		<key>w</key>
		<integer>358</integer>
		<key>x</key>
		<integer>254</integer>
		<key>y</key>
		<integer>284</integer>
		<key>z</key>
		<integer>246</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>450</integer>
		<key>AxisHeight</key>
		<integer>250</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1500</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1450</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>662</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>198</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>700</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>66</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>480</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>198</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>580</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>66</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>480</integer>
		<key>FractionRuleThickness</key>
		<integer>66</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>600</integer>
		<key>LowerLimitGapMin</key>
		<integer>150</integer>
		<key>MathLeading</key>
		<integer>150</integer>
		<key>MinConnectorOverlap</key>
		<integer>50</integer>
		<key>OverbarExtraAscender</key>
		<integer>66</integer>
		<key>OverbarRuleThickness</key>
		<integer>66</integer>
		<key>OverbarVerticalGap</key>
		<integer>198</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>70</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>186</integer>
		<key>RadicalExtraAscender</key>
		<integer>66</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-555</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>277</integer>
		<key>RadicalRuleThickness</key>
		<integer>66</integer>
		<key>RadicalVerticalGap</key>
		<integer>82</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>75</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>60</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>300</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>66</integer>
		<key>SpaceAfterScript</key>
		<integer>41</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>900</integer>
		<key>StackBottomShiftDown</key>
		<integer>800</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>462</integer>
		<key>StackGapMin</key>
		<integer>198</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>580</integer>
		<key>StackTopShiftUp</key>
		<integer>480</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>600</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>150</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>150</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>300</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>264</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>50</integer>
		<key>SubscriptShiftDown</key>
		<integer>250</integer>
		<key>SubscriptTopMax</key>
		<integer>400</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>375</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>400</integer>
		<key>SuperscriptBottomMin</key>
		<integer>125</integer>
		<key>SuperscriptShiftUp</key>
		<integer>400</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>275</integer>
		<key>UnderbarExtraDescender</key>
		<integer>66</integer>
		<key>UnderbarRuleThickness</key>
		<integer>66</integer>
		<key>UnderbarVerticalGap</key>
		<integer>198</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>300</integer>
		<key>UpperLimitGapMin</key>
		<integer>150</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>uni0302</key>
		<array>
			<string>uni0302</string>
			<string>uni0302.size1</string>
			<string>uni0302.size2</string>
			<string>uni0302.size3</string>
			<string>uni0302.size4</string>
			<string>uni0302.size5</string>
		</array>
		<key>uni0303</key>
		<array>
			<string>uni0303</string>
			<string>uni0303.size1</string>
			<string>uni0303.size2</string>
			<string>uni0303.size3</string>
			<string>uni0303.size4</string>
			<string>uni0303.size5</string>
		</array>
		<key>uni0305</key>
		<array>
			<string>uni0305</string>
			<string>uni0305.size1</string>
			<string>uni0305.size2</string>
			<string>uni0305.size3</string>
			<string>uni0305.size4</string>
			<string>uni0305.size5</string>
		</array>
		<key>uni030C</key>
		<array>
			<string>uni030C</string>
			<string>uni030C.size1</string>
			<string>uni030C.size2</string>
			<string>uni030C.size3</string>
			<string>uni030C.size4</string>
			<string>uni030C.size5</string>
		</array>
		<key>uni0330</key>
		<array>
			<string>uni0330</string>
			<string>uni0330.size1</string>
			<string>uni0330.size2</string>
			<string>uni0330.size3</string>
			<string>uni0330.size4</string>
			<string>uni0330.size5</string>
		</array>
		<key>uni0332</key>
		<array>
			<string>uni0332</string>
			<string>uni0332.size1</string>
			<string>uni0332.size2</string>
			<string>uni0332.size3</string>
			<string>uni0332.size4</string>
			<string>uni0332.size5</string>
		</array>
		<key>uni034D</key>
		<array>
			<string>uni034D</string>
		</array>
		<key>uni203E</key>
		<array>
			<string>uni203E</string>
			<string>uni203E.size1</string>
			<string>uni203E.size2</string>
			<string>uni203E.size3</string>
			<string>uni203E.size4</string>
			<string>uni203E.size5</string>
		</array>
		<key>uni20D0</key>
		<array>
			<string>uni20D0</string>
		</array>
		<key>uni20D1</key>
		<array>
			<string>uni20D1</string>
		</array>
		<key>uni20D6</key>
		<array>
			<string>uni20D6</string>
		</array>
		<key>uni20D7</key>
		<array>
			<string>uni20D7</string>
		</array>
		<key>uni20E1</key>
		<array>
			<string>uni20E1</string>
		</array>
		<key>uni20E9</key>
		<array>
			<string>uni20E9</string>
		</array>
		<key>uni20EC</key>
		<array>
			<string>uni20EC</string>
		</array>
		<key>uni20ED</key>
		<array>
			<string>uni20ED</string>
		</array>
		<key>uni20EE</key>
		<array>
			<string>uni20EE</string>
		</array>
		<key>uni20EF</key>
		<array>
			<string>uni20EF</string>
		</array>
		<key>uni2190</key>
		<array/>
		<key>uni2192</key>
		<array/>
		<key>uni2194</key>
		<array/>
		<key>uni21A4</key>
		<array/>
		<key>uni21A6</key>
		<array/>
		<key>uni21BC</key>
		<array/>
		<key>uni21BD</key>
		<array/>
		<key>uni21C0</key>
		<array/>
		<key>uni21D0</key>
		<array/>
		<key>uni21D2</key>
		<array/>
		<key>uni21D4</key>
		<array/>
		<key>uni21DA</key>
		<array/>
		<key>uni21DB</key>
		<array/>
		<key>uni23B4</key>
		<array>
			<string>uni23B4</string>
			<string>uni23B4.size1</string>
			<string>uni23B4.size2</string>
			<string>uni23B4.size3</string>
			<string>uni23B4.size4</string>
			<string>uni23B4.size5</string>
		</array>
		<key>uni23B5</key>
		<array>
			<string>uni23B5</string>
			<string>uni23B5.size1</string>
			<string>uni23B5.size2</string>
			<string>uni23B5.size3</string>
			<string>uni23B5.size4</string>
			<string>uni23B5.size5</string>
		</array>
		<key>uni23DC</key>
		<array>
			<string>uni23DC</string>
			<string>uni23DC.size1</string>
			<string>uni23DC.size2</string>
			<string>uni23DC.size3</string>
			<string>uni23DC.size4</string>
			<string>uni23DC.size5</string>
		</array>
		<key>uni23DD</key>
		<array>
			<string>uni23DD</string>
			<string>uni23DD.size1</string>
			<string>uni23DD.size2</string>
			<string>uni23DD.size3</string>
			<string>uni23DD.size4</string>
			<string>uni23DD.size5</string>
		</array>
		<key>uni23DE</key>
		<array>
			<string>uni23DE</string>
			<string>uni23DE.size1</string>
			<string>uni23DE.size2</string>
			<string>uni23DE.size3</string>
			<string>uni23DE.size4</string>
			<string>uni23DE.size5</string>
		</array>
		<key>uni23DF</key>
		<array>
			<string>uni23DF</string>
			<string>uni23DF.size1</string>
			<string>uni23DF.size2</string>
			<string>uni23DF.size3</string>
			<string>uni23DF.size4</string>
			<string>uni23DF.size5</string>
		</array>
		<key>uni23E0</key>
		<array>
			<string>uni23E0</string>
			<string>uni23E0.size1</string>
			<string>uni23E0.size2</string>
			<string>uni23E0.size3</string>
			<string>uni23E0.size4</string>
			<string>uni23E0.size5</string>
		</array>
		<key>uni23E1</key>
		<array>
			<string>uni23E1</string>
			<string>uni23E1.size1</string>
			<string>uni23E1.size2</string>
			<string>uni23E1.size3</string>
			<string>uni23E1.size4</string>
			<string>uni23E1.size5</string>
		</array>
		<key>uni2B45</key>
		<array/>
		<key>uni2B46</key>
		<array/>
	</dict>
	<key>italic</key>
	<dict>
		<key>f</key>
		<integer>100</integer>
		<key>r</key>
		<integer>60</integer>
		<key>t</key>
		<integer>60</integer>
		<key>u1D41F</key>
		<integer>100</integer>
		<key>u1D42B</key>
		<integer>60</integer>
		<key>u1D42D</key>
		<integer>60</integer>
		<key>u1D431</key>
		<integer>20</integer>
		<key>u1D432</key>
		<integer>20</integer>
		<key>u1D433</key>
		<integer>20</integer>
		<key>u1D435</key>
		<integer>40</integer>
		<key>u1D436</key>
		<integer>80</integer>
		<key>u1D437</key>
		<integer>15</integer>
		<key>u1D438</key>
		<integer>60</integer>
		<key>u1D439</key>
		<integer>145</integer>
		<key>u1D43A</key>
		<integer>20</integer>
		<key>u1D43B</key>
		<integer>90</integer>
		<key>u1D43C</key>
		<integer>90</integer>
		<key>u1D43D</key>
		<integer>120</integer>
		<key>u1D43E</key>
		<integer>80</integer>
		<key>u1D440</key>
		<integer>90</integer>
		<key>u1D441</key>
		<integer>90</integer>
		<key>u1D442</key>
		<integer>20</integer>
		<key>u1D443</key>
		<integer>150</integer>
		<key>u1D446</key>
		<integer>70</integer>
		<key>u1D447</key>
		<integer>160</integer>
		<key>u1D448</key>
		<integer>110</integer>
		<key>u1D449</key>
		<integer>225</integer>
		<key>u1D44A</key>
		<integer>225</integer>
		<key>u1D44B</key>
		<integer>60</integer>
		<key>u1D44C</key>
		<integer>200</integer>
		<key>u1D44D</key>
		<integer>70</integer>
		<key>u1D450</key>
		<integer>50</integer>
		<key>u1D451</key>
		<integer>60</integer>
		<key>u1D453</key>
		<integer>120</integer>
		<key>u1D454</key>
		<integer>40</integer>
		<key>u1D457</key>
		<integer>60</integer>
		<key>u1D459</key>
		<integer>20</integer>
		<key>u1D45E</key>
		<integer>40</integer>
		<key>u1D469</key>
		<integer>40</integer>
		<key>u1D46A</key>
		<integer>84</integer>
		<key>u1D46B</key>
		<integer>68</integer>
		<key>u1D46C</key>
		<integer>62</integer>
		<key>u1D46D</key>
		<integer>155</integer>
		<key>u1D46E</key>
		<integer>20</integer>
		<key>u1D46F</key>
		<integer>95</integer>
		<key>u1D470</key>
		<integer>95</integer>
		<key>u1D471</key>
		<integer>128</integer>
		<key>u1D472</key>
		<integer>84</integer>
		<key>u1D474</key>
		<integer>95</integer>
		<key>u1D475</key>
		<integer>95</integer>
		<key>u1D476</key>
		<integer>20</integer>
		<key>u1D477</key>
		<integer>161</integer>
		<key>u1D479</key>
		<integer>25</integer>
		<key>u1D47A</key>
		<integer>73</integer>
		<key>u1D47B</key>
		<integer>172</integer>
		<key>u1D47C</key>
		<integer>117</integer>
		<key>u1D47D</key>
		<integer>244</integer>
		<key>u1D47E</key>
		<integer>244</integer>
		<key>u1D47F</key>
		<integer>62</integer>
		<key>u1D480</key>
		<integer>216</integer>
		<key>u1D481</key>
		<integer>73</integer>
		<key>u1D484</key>
		<integer>50</integer>
		<key>u1D485</key>
		<integer>40</integer>
		<key>u1D487</key>
		<integer>120</integer>
		<key>u1D48B</key>
		<integer>40</integer>
		<key>u1D492</key>
		<integer>40</integer>
		<key>u1D49C</key>
		<integer>125</integer>
		<key>u1D49E</key>
		<integer>145</integer>
		<key>u1D49E.cal</key>
		<integer>62</integer>
		<key>u1D49F</key>
		<integer>125</integer>
		<key>u1D4A2</key>
		<integer>145</integer>
		<key>u1D4A5</key>
		<integer>185</integer>
		<key>u1D4A5.cal</key>
		<integer>198</integer>
		<key>u1D4A6</key>
		<integer>125</integer>
		<key>u1D4A9</key>
		<integer>245</integer>
		<key>u1D4A9.cal</key>
		<integer>152</integer>
		<key>u1D4AA</key>
		<integer>95</integer>
		<key>u1D4AB</key>
		<integer>245</integer>
		<key>u1D4AB.cal</key>
		<integer>87</integer>
		<key>u1D4AC</key>
		<integer>45</integer>
		<key>u1D4AE</key>
		<integer>145</integer>
		<key>u1D4AE.cal</key>
		<integer>82</integer>
		<key>u1D4AF</key>
		<integer>245</integer>
		<key>u1D4AF.cal</key>
		<integer>266</integer>
		<key>u1D4B0</key>
		<integer>95</integer>
		<key>u1D4B0.cal</key>
		<integer>178</integer>
		<key>u1D4B1</key>
		<integer>245</integer>
		<key>u1D4B1.cal</key>
		<integer>87</integer>
		<key>u1D4B2</key>
		<integer>245</integer>
		<key>u1D4B2.cal</key>
		<integer>87</integer>
		<key>u1D4B3</key>
		<integer>125</integer>
		<key>u1D4B3.cal</key>
		<integer>92</integer>
		<key>u1D4B4</key>
		<integer>125</integer>
		<key>u1D4B4.cal</key>
		<integer>89</integer>
		<key>u1D4B5</key>
		<integer>125</integer>
		<key>u1D4BB</key>
		<integer>40</integer>
		<key>u1D4BE</key>
		<integer>40</integer>
		<key>u1D4BF</key>
		<integer>40</integer>
		<key>u1D4C1</key>
		<integer>40</integer>
		<key>u1D4C9</key>
		<integer>20</integer>
		<key>u1D4D2.cal</key>
		<integer>47</integer>
		<key>u1D4D3.cal</key>
		<integer>51</integer>
		<key>u1D4D4.cal</key>
		<integer>62</integer>
		<key>u1D4D5.cal</key>
		<integer>138</integer>
		<key>u1D4D7.cal</key>
		<integer>54</integer>
		<key>u1D4D8.cal</key>
		<integer>47</integer>
		<key>u1D4D9.cal</key>
		<integer>211</integer>
		<key>u1D4DD.cal</key>
		<integer>146</integer>
		<key>u1D4DE.cal</key>
		<integer>55</integer>
		<key>u1D4DF.cal</key>
		<integer>131</integer>
		<key>u1D4E2.cal</key>
		<integer>77</integer>
		<key>u1D4E3.cal</key>
		<integer>245</integer>
		<key>u1D4E4.cal</key>
		<integer>194</integer>
		<key>u1D4E5.cal</key>
		<integer>99</integer>
		<key>u1D4E6.cal</key>
		<integer>99</integer>
		<key>u1D4E7.cal</key>
		<integer>106</integer>
		<key>u1D4E8.cal</key>
		<integer>98</integer>
		<key>u1D4EF</key>
		<integer>40</integer>
		<key>u1D4F2</key>
		<integer>40</integer>
		<key>u1D4F3</key>
		<integer>40</integer>
		<key>u1D4F5</key>
		<integer>40</integer>
		<key>u1D4FD</key>
		<integer>40</integer>
		<key>u1D53B.it</key>
		<integer>20</integer>
		<key>u1D53C.bi</key>
		<integer>68</integer>
		<key>u1D53C.it</key>
		<integer>70</integer>
		<key>u1D53D</key>
		<integer>140</integer>
		<key>u1D53D.bf</key>
		<integer>166</integer>
		<key>u1D53D.bi</key>
		<integer>274</integer>
		<key>u1D53D.it</key>
		<integer>240</integer>
		<key>u1D540.bi</key>
		<integer>74</integer>
		<key>u1D540.it</key>
		<integer>75</integer>
		<key>u1D541.bi</key>
		<integer>86</integer>
		<key>u1D541.it</key>
		<integer>78</integer>
		<key>u1D542.it</key>
		<integer>75</integer>
		<key>u1D544.bi</key>
		<integer>76</integer>
		<key>u1D544.it</key>
		<integer>79</integer>
		<key>u1D54A.bi</key>
		<integer>70</integer>
		<key>u1D54A.it</key>
		<integer>90</integer>
		<key>u1D54B</key>
		<integer>140</integer>
		<key>u1D54B.bf</key>
		<integer>134</integer>
		<key>u1D54B.bi</key>
		<integer>253</integer>
		<key>u1D54B.it</key>
		<integer>264</integer>
		<key>u1D54C.bi</key>
		<integer>91</integer>
		<key>u1D54C.it</key>
		<integer>113</integer>
		<key>u1D54D</key>
		<integer>130</integer>
		<key>u1D54D.bf</key>
		<integer>122</integer>
		<key>u1D54D.bi</key>
		<integer>246</integer>
		<key>u1D54D.it</key>
		<integer>231</integer>
		<key>u1D54E</key>
		<integer>60</integer>
		<key>u1D54E.bf</key>
		<integer>64</integer>
		<key>u1D54E.bi</key>
		<integer>145</integer>
		<key>u1D54E.it</key>
		<integer>198</integer>
		<key>u1D54F.bi</key>
		<integer>118</integer>
		<key>u1D54F.it</key>
		<integer>165</integer>
		<key>u1D550</key>
		<integer>150</integer>
		<key>u1D550.bf</key>
		<integer>132</integer>
		<key>u1D550.bi</key>
		<integer>297</integer>
		<key>u1D550.it</key>
		<integer>245</integer>
		<key>u1D555.it</key>
		<integer>80</integer>
		<key>u1D557</key>
		<integer>105</integer>
		<key>u1D557.bf</key>
		<integer>107</integer>
		<key>u1D557.bi</key>
		<integer>225</integer>
		<key>u1D557.it</key>
		<integer>233</integer>
		<key>u1D55A.it</key>
		<integer>80</integer>
		<key>u1D55B.it</key>
		<integer>80</integer>
		<key>u1D55D.bi</key>
		<integer>86</integer>
		<key>u1D55D.it</key>
		<integer>95</integer>
		<key>u1D563</key>
		<integer>145</integer>
		<key>u1D563.bf</key>
		<integer>151</integer>
		<key>u1D563.bi</key>
		<integer>180</integer>
		<key>u1D563.it</key>
		<integer>180</integer>
		<key>u1D565.bi</key>
		<integer>64</integer>
		<key>u1D567</key>
		<integer>70</integer>
		<key>u1D567.bf</key>
		<integer>95</integer>
		<key>u1D567.bi</key>
		<integer>102</integer>
		<key>u1D567.it</key>
		<integer>112</integer>
		<key>u1D568</key>
		<integer>60</integer>
		<key>u1D568.bi</key>
		<integer>75</integer>
		<key>u1D568.it</key>
		<integer>96</integer>
		<key>u1D569.bi</key>
		<integer>72</integer>
		<key>u1D56A</key>
		<integer>80</integer>
		<key>u1D56A.bf</key>
		<integer>111</integer>
		<key>u1D56A.bi</key>
		<integer>104</integer>
		<key>u1D56A.it</key>
		<integer>112</integer>
		<key>u1D5BF</key>
		<integer>100</integer>
		<key>u1D5F3</key>
		<integer>100</integer>
		<key>u1D609</key>
		<integer>77</integer>
		<key>u1D60A</key>
		<integer>124</integer>
		<key>u1D60B</key>
		<integer>100</integer>
		<key>u1D60C</key>
		<integer>135</integer>
		<key>u1D60D</key>
		<integer>184</integer>
		<key>u1D60E</key>
		<integer>100</integer>
		<key>u1D60F</key>
		<integer>130</integer>
		<key>u1D610</key>
		<integer>150</integer>
		<key>u1D611</key>
		<integer>112</integer>
		<key>u1D612</key>
		<integer>135</integer>
		<key>u1D614</key>
		<integer>130</integer>
		<key>u1D615</key>
		<integer>130</integer>
		<key>u1D616</key>
		<integer>105</integer>
		<key>u1D617</key>
		<integer>153</integer>
		<key>u1D618</key>
		<integer>103</integer>
		<key>u1D619</key>
		<integer>90</integer>
		<key>u1D61A</key>
		<integer>96</integer>
		<key>u1D61B</key>
		<integer>180</integer>
		<key>u1D61C</key>
		<integer>106</integer>
		<key>u1D61D</key>
		<integer>174</integer>
		<key>u1D61E</key>
		<integer>176</integer>
		<key>u1D61F</key>
		<integer>146</integer>
		<key>u1D620</key>
		<integer>184</integer>
		<key>u1D621</key>
		<integer>166</integer>
		<key>u1D625</key>
		<integer>160</integer>
		<key>u1D627</key>
		<integer>240</integer>
		<key>u1D628</key>
		<integer>120</integer>
		<key>u1D629</key>
		<integer>60</integer>
		<key>u1D62A</key>
		<integer>145</integer>
		<key>u1D62B</key>
		<integer>140</integer>
		<key>u1D62C</key>
		<integer>120</integer>
		<key>u1D62D</key>
		<integer>160</integer>
		<key>u1D62E</key>
		<integer>60</integer>
		<key>u1D62F</key>
		<integer>60</integer>
		<key>u1D630</key>
		<integer>70</integer>
		<key>u1D631</key>
		<integer>80</integer>
		<key>u1D632</key>
		<integer>90</integer>
		<key>u1D633</key>
		<integer>140</integer>
		<key>u1D634</key>
		<integer>90</integer>
		<key>u1D635</key>
		<integer>120</integer>
		<key>u1D636</key>
		<integer>80</integer>
		<key>u1D637</key>
		<integer>80</integer>
		<key>u1D638</key>
		<integer>80</integer>
		<key>u1D639</key>
		<integer>80</integer>
		<key>u1D63A</key>
		<integer>80</integer>
		<key>u1D63B</key>
		<integer>80</integer>
		<key>u1D63D</key>
		<integer>95</integer>
		<key>u1D63E</key>
		<integer>114</integer>
		<key>u1D63F</key>
		<integer>103</integer>
		<key>u1D640</key>
		<integer>133</integer>
		<key>u1D641</key>
		<integer>183</integer>
		<key>u1D642</key>
		<integer>80</integer>
		<key>u1D643</key>
		<integer>128</integer>
		<key>u1D644</key>
		<integer>134</integer>
		<key>u1D645</key>
		<integer>133</integer>
		<key>u1D646</key>
		<integer>144</integer>
		<key>u1D648</key>
		<integer>128</integer>
		<key>u1D649</key>
		<integer>128</integer>
		<key>u1D64A</key>
		<integer>102</integer>
		<key>u1D64B</key>
		<integer>145</integer>
		<key>u1D64C</key>
		<integer>100</integer>
		<key>u1D64D</key>
		<integer>68</integer>
		<key>u1D64E</key>
		<integer>123</integer>
		<key>u1D64F</key>
		<integer>184</integer>
		<key>u1D650</key>
		<integer>133</integer>
		<key>u1D651</key>
		<integer>183</integer>
		<key>u1D652</key>
		<integer>178</integer>
		<key>u1D653</key>
		<integer>153</integer>
		<key>u1D654</key>
		<integer>188</integer>
		<key>u1D655</key>
		<integer>156</integer>
		<key>u1D657</key>
		<integer>90</integer>
		<key>u1D658</key>
		<integer>80</integer>
		<key>u1D659</key>
		<integer>140</integer>
		<key>u1D65A</key>
		<integer>80</integer>
		<key>u1D65B</key>
		<integer>240</integer>
		<key>u1D65C</key>
		<integer>140</integer>
		<key>u1D65D</key>
		<integer>40</integer>
		<key>u1D65E</key>
		<integer>140</integer>
		<key>u1D65F</key>
		<integer>140</integer>
		<key>u1D660</key>
		<integer>80</integer>
		<key>u1D661</key>
		<integer>140</integer>
		<key>u1D662</key>
		<integer>70</integer>
		<key>u1D663</key>
		<integer>70</integer>
		<key>u1D664</key>
		<integer>70</integer>
		<key>u1D665</key>
		<integer>70</integer>
		<key>u1D666</key>
		<integer>90</integer>
		<key>u1D667</key>
		<integer>120</integer>
		<key>u1D668</key>
		<integer>60</integer>
		<key>u1D669</key>
		<integer>100</integer>
		<key>u1D66A</key>
		<integer>60</integer>
		<key>u1D66B</key>
		<integer>100</integer>
		<key>u1D66C</key>
		<integer>100</integer>
		<key>u1D66D</key>
		<integer>100</integer>
		<key>u1D66E</key>
		<integer>100</integer>
		<key>u1D66F</key>
		<integer>100</integer>
		<key>u1D6E3</key>
		<integer>40</integer>
		<key>u1D6E4</key>
		<integer>145</integer>
		<key>u1D6E4.ss</key>
		<integer>148</integer>
		<key>u1D6E6</key>
		<integer>60</integer>
		<key>u1D6E6.ss</key>
		<integer>70</integer>
		<key>u1D6E7</key>
		<integer>70</integer>
		<key>u1D6E7.ss</key>
		<integer>107</integer>
		<key>u1D6E8</key>
		<integer>90</integer>
		<key>u1D6E8.ss</key>
		<integer>80</integer>
		<key>u1D6E9</key>
		<integer>15</integer>
		<key>u1D6EA</key>
		<integer>90</integer>
		<key>u1D6EA.ss</key>
		<integer>115</integer>
		<key>u1D6EB</key>
		<integer>70</integer>
		<key>u1D6EB.ss</key>
		<integer>129</integer>
		<key>u1D6ED</key>
		<integer>90</integer>
		<key>u1D6ED.ss</key>
		<integer>73</integer>
		<key>u1D6EE</key>
		<integer>90</integer>
		<key>u1D6EE.ss</key>
		<integer>73</integer>
		<key>u1D6EF</key>
		<integer>75</integer>
		<key>u1D6EF.ss</key>
		<integer>77</integer>
		<key>u1D6F0</key>
		<integer>20</integer>
		<key>u1D6F1</key>
		<integer>90</integer>
		<key>u1D6F1.ss</key>
		<integer>80</integer>
		<key>u1D6F2</key>
		<integer>150</integer>
		<key>u1D6F2.ss</key>
		<integer>131</integer>
		<key>u1D6F3</key>
		<integer>15</integer>
		<key>u1D6F4</key>
		<integer>65</integer>
		<key>u1D6F4.ss</key>
		<integer>97</integer>
		<key>u1D6F5</key>
		<integer>160</integer>
		<key>u1D6F5.ss</key>
		<integer>119</integer>
		<key>u1D6F6</key>
		<integer>160</integer>
		<key>u1D6F6.ss</key>
		<integer>177</integer>
		<key>u1D6F7</key>
		<integer>15</integer>
		<key>u1D6F8</key>
		<integer>60</integer>
		<key>u1D6F8.ss</key>
		<integer>118</integer>
		<key>u1D6F9</key>
		<integer>113</integer>
		<key>u1D6F9.ss</key>
		<integer>138</integer>
		<key>u1D6FA</key>
		<integer>20</integer>
		<key>u1D6FB</key>
		<integer>15</integer>
		<key>u1D6FC</key>
		<integer>40</integer>
		<key>u1D6FC.ss</key>
		<integer>75</integer>
		<key>u1D6FD</key>
		<integer>65</integer>
		<key>u1D6FD.ss</key>
		<integer>69</integer>
		<key>u1D6FE</key>
		<integer>80</integer>
		<key>u1D6FE.ss</key>
		<integer>69</integer>
		<key>u1D6FF</key>
		<integer>40</integer>
		<key>u1D6FF.ss</key>
		<integer>85</integer>
		<key>u1D701</key>
		<integer>100</integer>
		<key>u1D701.ss</key>
		<integer>70</integer>
		<key>u1D702</key>
		<integer>40</integer>
		<key>u1D703</key>
		<integer>40</integer>
		<key>u1D705</key>
		<integer>60</integer>
		<key>u1D705.ss</key>
		<integer>90</integer>
		<key>u1D707</key>
		<integer>30</integer>
		<key>u1D708</key>
		<integer>50</integer>
		<key>u1D708.ss</key>
		<integer>85</integer>
		<key>u1D709</key>
		<integer>50</integer>
		<key>u1D709.ss</key>
		<integer>75</integer>
		<key>u1D70B</key>
		<integer>50</integer>
		<key>u1D70B.ss</key>
		<integer>69</integer>
		<key>u1D70D</key>
		<integer>65</integer>
		<key>u1D70D.ss</key>
		<integer>109</integer>
		<key>u1D70E</key>
		<integer>50</integer>
		<key>u1D70E.ss</key>
		<integer>63</integer>
		<key>u1D70F</key>
		<integer>70</integer>
		<key>u1D70F.ss</key>
		<integer>63</integer>
		<key>u1D712</key>
		<integer>90</integer>
		<key>u1D712.ss</key>
		<integer>83</integer>
		<key>u1D713</key>
		<integer>90</integer>
		<key>u1D713.ss</key>
		<integer>96</integer>
		<key>u1D714.ss</key>
		<integer>51</integer>
		<key>u1D715</key>
		<integer>40</integer>
		<key>u1D716</key>
		<integer>40</integer>
		<key>u1D718</key>
		<integer>70</integer>
		<key>u1D71B</key>
		<integer>60</integer>
		<key>u1D71B.ss</key>
		<integer>61</integer>
		<key>u1D71D</key>
		<integer>20</integer>
		<key>u1D71E</key>
		<integer>155</integer>
		<key>u1D720</key>
		<integer>62</integer>
		<key>u1D721</key>
		<integer>73</integer>
		<key>u1D722</key>
		<integer>95</integer>
		<key>u1D723</key>
		<integer>68</integer>
		<key>u1D724</key>
		<integer>95</integer>
		<key>u1D725</key>
		<integer>84</integer>
		<key>u1D727</key>
		<integer>95</integer>
		<key>u1D728</key>
		<integer>95</integer>
		<key>u1D729</key>
		<integer>80</integer>
		<key>u1D72A</key>
		<integer>18</integer>
		<key>u1D72B</key>
		<integer>95</integer>
		<key>u1D72C</key>
		<integer>161</integer>
		<key>u1D72D</key>
		<integer>12</integer>
		<key>u1D72E</key>
		<integer>68</integer>
		<key>u1D72F</key>
		<integer>172</integer>
		<key>u1D730</key>
		<integer>172</integer>
		<key>u1D731</key>
		<integer>12</integer>
		<key>u1D732</key>
		<integer>18</integer>
		<key>u1D733</key>
		<integer>127</integer>
		<key>u1D734</key>
		<integer>20</integer>
		<key>u1D735</key>
		<integer>10</integer>
		<key>u1D736</key>
		<integer>40</integer>
		<key>u1D737</key>
		<integer>70</integer>
		<key>u1D738</key>
		<integer>53</integer>
		<key>u1D73B</key>
		<integer>90</integer>
		<key>u1D73F</key>
		<integer>50</integer>
		<key>u1D742</key>
		<integer>50</integer>
		<key>u1D743</key>
		<integer>50</integer>
		<key>u1D745</key>
		<integer>50</integer>
		<key>u1D747</key>
		<integer>70</integer>
		<key>u1D748</key>
		<integer>50</integer>
		<key>u1D749</key>
		<integer>80</integer>
		<key>u1D74C</key>
		<integer>95</integer>
		<key>u1D74D</key>
		<integer>95</integer>
		<key>u1D74F</key>
		<integer>40</integer>
		<key>u1D750</key>
		<integer>40</integer>
		<key>u1D752</key>
		<integer>60</integer>
		<key>u1D755</key>
		<integer>50</integer>
		<key>u1D7AA</key>
		<integer>65</integer>
		<key>u1D7AB</key>
		<integer>70</integer>
		<key>u1D7AC</key>
		<integer>90</integer>
		<key>u1D7AD</key>
		<integer>80</integer>
		<key>u1D7AF</key>
		<integer>120</integer>
		<key>u1D7B3</key>
		<integer>90</integer>
		<key>u1D7B6</key>
		<integer>90</integer>
		<key>u1D7B7</key>
		<integer>90</integer>
		<key>u1D7B9</key>
		<integer>80</integer>
		<key>u1D7BB</key>
		<integer>80</integer>
		<key>u1D7BC</key>
		<integer>60</integer>
		<key>u1D7BD</key>
		<integer>60</integer>
		<key>u1D7C0</key>
		<integer>110</integer>
		<key>u1D7C1</key>
		<integer>100</integer>
		<key>u1D7C3</key>
		<integer>40</integer>
		<key>u1D7C4</key>
		<integer>40</integer>
		<key>u1D7C6</key>
		<integer>60</integer>
		<key>u1D7C9</key>
		<integer>70</integer>
		<key>u1D7E2.ss</key>
		<integer>123</integer>
		<key>u1D7E4.ss</key>
		<integer>119</integer>
		<key>u1D7E5.ss</key>
		<integer>89</integer>
		<key>u1D7E6.ss</key>
		<integer>92</integer>
		<key>u1D7E7.ss</key>
		<integer>171</integer>
		<key>u1D7E8.ss</key>
		<integer>162</integer>
		<key>u1D7E9.ss</key>
		<integer>161</integer>
		<key>u1D7EA.ss</key>
		<integer>105</integer>
		<key>u1D7EB.ss</key>
		<integer>124</integer>
		<key>u1D7EC.ss</key>
		<integer>123</integer>
		<key>u1D7EE.ss</key>
		<integer>126</integer>
		<key>u1D7EF.ss</key>
		<integer>131</integer>
		<key>u1D7F0.ss</key>
		<integer>128</integer>
		<key>u1D7F1.ss</key>
		<integer>196</integer>
		<key>u1D7F2.ss</key>
		<integer>183</integer>
		<key>u1D7F3.ss</key>
		<integer>184</integer>
		<key>u1D7F4.ss</key>
		<integer>139</integer>
		<key>u1D7F5.ss</key>
		<integer>133</integer>
		<key>uni03C3.ss</key>
		<integer>61</integer>
		<key>uni03C7.ss</key>
		<integer>86</integer>
		<key>uni03C8</key>
		<integer>40</integer>
		<key>uni210B</key>
		<integer>145</integer>
		<key>uni210B.cal</key>
		<integer>59</integer>
		<key>uni2110</key>
		<integer>195</integer>
		<key>uni2110.cal</key>
		<integer>61</integer>
		<key>uni2112</key>
		<integer>125</integer>
		<key>uni211B</key>
		<integer>45</integer>
		<key>uni212C</key>
		<integer>45</integer>
		<key>uni2130</key>
		<integer>105</integer>
		<key>uni2130.cal</key>
		<integer>91</integer>
		<key>uni2131</key>
		<integer>195</integer>
		<key>uni2131.cal</key>
		<integer>103</integer>
		<key>uni2133</key>
		<integer>45</integer>
		<key>uni213E</key>
		<integer>130</integer>
		<key>uni2145</key>
		<integer>20</integer>
		<key>uni2146</key>
		<integer>80</integer>
		<key>uni2148</key>
		<integer>80</integer>
		<key>uni2149</key>
		<integer>80</integer>
		<key>uni222B</key>
		<integer>180</integer>
		<key>uni222B.display</key>
		<integer>550</integer>
		<key>uni222B.rtlm</key>
		<integer>180</integer>
		<key>uni222B.rtlm.display</key>
		<integer>550</integer>
		<key>uni222B.small</key>
		<integer>160</integer>
		<key>uni222B.up</key>
		<integer>142</integer>
		<key>uni222B.up.display</key>
		<integer>145</integer>
		<key>uni222C</key>
		<integer>180</integer>
		<key>uni222C.display</key>
		<integer>550</integer>
		<key>uni222C.rtlm</key>
		<integer>180</integer>
		<key>uni222C.rtlm.display</key>
		<integer>550</integer>
		<key>uni222C.small</key>
		<integer>160</integer>
		<key>uni222C.up</key>
		<integer>142</integer>
		<key>uni222C.up.display</key>
		<integer>145</integer>
		<key>uni222D</key>
		<integer>180</integer>
		<key>uni222D.display</key>
		<integer>550</integer>
		<key>uni222D.rtlm</key>
		<integer>180</integer>
		<key>uni222D.rtlm.display</key>
		<integer>550</integer>
		<key>uni222D.small</key>
		<integer>160</integer>
		<key>uni222D.up</key>
		<integer>142</integer>
		<key>uni222D.up.display</key>
		<integer>145</integer>
		<key>uni222E</key>
		<integer>180</integer>
		<key>uni222E.display</key>
		<integer>550</integer>
		<key>uni222E.rtlm</key>
		<integer>180</integer>
		<key>uni222E.rtlm.display</key>
		<integer>550</integer>
		<key>uni222E.small</key>
		<integer>160</integer>
		<key>uni222E.up</key>
		<integer>142</integer>
		<key>uni222E.up.display</key>
		<integer>145</integer>
		<key>uni222F</key>
		<integer>180</integer>
		<key>uni222F.display</key>
		<integer>550</integer>
		<key>uni222F.rtlm</key>
		<integer>180</integer>
		<key>uni222F.rtlm.display</key>
		<integer>550</integer>
		<key>uni222F.small</key>
		<integer>160</integer>
		<key>uni222F.up</key>
		<integer>142</integer>
		<key>uni222F.up.display</key>
		<integer>145</integer>
		<key>uni2230</key>
		<integer>180</integer>
		<key>uni2230.display</key>
		<integer>550</integer>
		<key>uni2230.rtlm</key>
		<integer>180</integer>
		<key>uni2230.rtlm.display</key>
		<integer>550</integer>
		<key>uni2230.small</key>
		<integer>160</integer>
		<key>uni2230.up</key>
		<integer>142</integer>
		<key>uni2230.up.display</key>
		<integer>145</integer>
		<key>uni2231</key>
		<integer>180</integer>
		<key>uni2231.display</key>
		<integer>550</integer>
		<key>uni2231.rtlm</key>
		<integer>180</integer>
		<key>uni2231.rtlm.display</key>
		<integer>550</integer>
		<key>uni2231.small</key>
		<integer>160</integer>
		<key>uni2231.up</key>
		<integer>142</integer>
		<key>uni2231.up.display</key>
		<integer>145</integer>
		<key>uni2232</key>
		<integer>180</integer>
		<key>uni2232.display</key>
		<integer>550</integer>
		<key>uni2232.rtlm</key>
		<integer>180</integer>
		<key>uni2232.rtlm.display</key>
		<integer>550</integer>
		<key>uni2232.small</key>
		<integer>160</integer>
		<key>uni2232.up</key>
		<integer>142</integer>
		<key>uni2232.up.display</key>
		<integer>145</integer>
		<key>uni2233</key>
		<integer>180</integer>
		<key>uni2233.display</key>
		<integer>550</integer>
		<key>uni2233.rtlm</key>
		<integer>180</integer>
		<key>uni2233.rtlm.display</key>
		<integer>550</integer>
		<key>uni2233.small</key>
		<integer>160</integer>
		<key>uni2233.up</key>
		<integer>142</integer>
		<key>uni2233.up.display</key>
		<integer>145</integer>
		<key>uni2A0B</key>
		<integer>180</integer>
		<key>uni2A0B.display</key>
		<integer>550</integer>
		<key>uni2A0B.rtlm</key>
		<integer>180</integer>
		<key>uni2A0B.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A0B.small</key>
		<integer>160</integer>
		<key>uni2A0B.up</key>
		<integer>142</integer>
		<key>uni2A0B.up.display</key>
		<integer>145</integer>
		<key>uni2A0C</key>
		<integer>180</integer>
		<key>uni2A0C.display</key>
		<integer>550</integer>
		<key>uni2A0C.rtlm</key>
		<integer>180</integer>
		<key>uni2A0C.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A0C.small</key>
		<integer>160</integer>
		<key>uni2A0C.up</key>
		<integer>142</integer>
		<key>uni2A0C.up.display</key>
		<integer>145</integer>
		<key>uni2A0D</key>
		<integer>180</integer>
		<key>uni2A0D.display</key>
		<integer>550</integer>
		<key>uni2A0D.rtlm</key>
		<integer>180</integer>
		<key>uni2A0D.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A0D.small</key>
		<integer>160</integer>
		<key>uni2A0D.up</key>
		<integer>142</integer>
		<key>uni2A0D.up.display</key>
		<integer>145</integer>
		<key>uni2A0E</key>
		<integer>180</integer>
		<key>uni2A0E.display</key>
		<integer>550</integer>
		<key>uni2A0E.rtlm</key>
		<integer>180</integer>
		<key>uni2A0E.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A0E.small</key>
		<integer>160</integer>
		<key>uni2A0E.up</key>
		<integer>142</integer>
		<key>uni2A0E.up.display</key>
		<integer>145</integer>
		<key>uni2A0F</key>
		<integer>180</integer>
		<key>uni2A0F.display</key>
		<integer>550</integer>
		<key>uni2A0F.rtlm</key>
		<integer>180</integer>
		<key>uni2A0F.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A0F.small</key>
		<integer>160</integer>
		<key>uni2A0F.up</key>
		<integer>142</integer>
		<key>uni2A0F.up.display</key>
		<integer>145</integer>
		<key>uni2A10</key>
		<integer>180</integer>
		<key>uni2A10.display</key>
		<integer>550</integer>
		<key>uni2A10.rtlm</key>
		<integer>180</integer>
		<key>uni2A10.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A10.small</key>
		<integer>160</integer>
		<key>uni2A10.up</key>
		<integer>142</integer>
		<key>uni2A10.up.display</key>
		<integer>145</integer>
		<key>uni2A11</key>
		<integer>180</integer>
		<key>uni2A11.display</key>
		<integer>550</integer>
		<key>uni2A11.rtlm</key>
		<integer>180</integer>
		<key>uni2A11.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A11.small</key>
		<integer>160</integer>
		<key>uni2A11.up</key>
		<integer>142</integer>
		<key>uni2A11.up.display</key>
		<integer>145</integer>
		<key>uni2A12</key>
		<integer>180</integer>
		<key>uni2A12.display</key>
		<integer>550</integer>
		<key>uni2A12.rtlm</key>
		<integer>180</integer>
		<key>uni2A12.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A12.small</key>
		<integer>160</integer>
		<key>uni2A12.up</key>
		<integer>142</integer>
		<key>uni2A12.up.display</key>
		<integer>145</integer>
		<key>uni2A13</key>
		<integer>180</integer>
		<key>uni2A13.display</key>
		<integer>550</integer>
		<key>uni2A13.rtlm</key>
		<integer>180</integer>
		<key>uni2A13.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A13.small</key>
		<integer>160</integer>
		<key>uni2A13.up</key>
		<integer>142</integer>
		<key>uni2A13.up.display</key>
		<integer>145</integer>
		<key>uni2A14</key>
		<integer>180</integer>
		<key>uni2A14.display</key>
		<integer>550</integer>
		<key>uni2A14.rtlm</key>
		<integer>180</integer>
		<key>uni2A14.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A14.small</key>
		<integer>160</integer>
		<key>uni2A14.up</key>
		<integer>142</integer>
		<key>uni2A14.up.display</key>
		<integer>145</integer>
		<key>uni2A15</key>
		<integer>180</integer>
		<key>uni2A15.display</key>
		<integer>550</integer>
		<key>uni2A15.rtlm</key>
		<integer>180</integer>
		<key>uni2A15.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A15.small</key>
		<integer>160</integer>
		<key>uni2A15.up</key>
		<integer>142</integer>
		<key>uni2A15.up.display</key>
		<integer>145</integer>
		<key>uni2A16</key>
		<integer>180</integer>
		<key>uni2A16.display</key>
		<integer>550</integer>
		<key>uni2A16.rtlm</key>
		<integer>180</integer>
		<key>uni2A16.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A16.small</key>
		<integer>160</integer>
		<key>uni2A16.up</key>
		<integer>142</integer>
		<key>uni2A16.up.display</key>
		<integer>145</integer>
		<key>uni2A17</key>
		<integer>180</integer>
		<key>uni2A17.display</key>
		<integer>550</integer>
		<key>uni2A17.rtlm</key>
		<integer>180</integer>
		<key>uni2A17.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A17.small</key>
		<integer>160</integer>
		<key>uni2A17.up</key>
		<integer>142</integer>
		<key>uni2A17.up.display</key>
		<integer>145</integer>
		<key>uni2A18</key>
		<integer>180</integer>
		<key>uni2A18.display</key>
		<integer>550</integer>
		<key>uni2A18.rtlm</key>
		<integer>180</integer>
		<key>uni2A18.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A18.small</key>
		<integer>160</integer>
		<key>uni2A18.up</key>
		<integer>142</integer>
		<key>uni2A18.up.display</key>
		<integer>145</integer>
		<key>uni2A19</key>
		<integer>180</integer>
		<key>uni2A19.display</key>
		<integer>550</integer>
		<key>uni2A19.rtlm</key>
		<integer>180</integer>
		<key>uni2A19.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A19.small</key>
		<integer>160</integer>
		<key>uni2A19.up</key>
		<integer>142</integer>
		<key>uni2A19.up.display</key>
		<integer>145</integer>
		<key>uni2A1A</key>
		<integer>180</integer>
		<key>uni2A1A.display</key>
		<integer>550</integer>
		<key>uni2A1A.rtlm</key>
		<integer>180</integer>
		<key>uni2A1A.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A1A.small</key>
		<integer>160</integer>
		<key>uni2A1A.up</key>
		<integer>142</integer>
		<key>uni2A1A.up.display</key>
		<integer>145</integer>
		<key>uni2A1B</key>
		<integer>180</integer>
		<key>uni2A1B.display</key>
		<integer>550</integer>
		<key>uni2A1B.rtlm</key>
		<integer>180</integer>
		<key>uni2A1B.rtlm.display</key>
		<integer>550</integer>
		<key>uni2A1B.small</key>
		<integer>160</integer>
		<key>uni2A1B.up</key>
		<integer>142</integer>
		<key>uni2A1B.up.display</key>
		<integer>140</integer>
		<key>uni2A1C</key>
		<integer>180</integer>
		<key>uni2A1C.display</key>
		<integer>300</integer>
		<key>uni2A1C.rtlm</key>
		<integer>180</integer>
		<key>uni2A1C.rtlm.display</key>
		<integer>300</integer>
		<key>uni2A1C.small</key>
		<integer>160</integer>
		<key>uni2A1C.up</key>
		<integer>50</integer>
		<key>uni2A1C.up.display</key>
		<integer>-200</integer>
		<key>y</key>
		<integer>20</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>bar</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>bar</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>bar</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
			</array>
		</dict>
		<key>braceleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A8</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
			</array>
		</dict>
		<key>braceright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AC</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
			</array>
		</dict>
		<key>bracketleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>950</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>950</integer>
				</dict>
			</array>
		</dict>
		<key>bracketright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>950</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>950</integer>
				</dict>
			</array>
		</dict>
		<key>parenleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>parenright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni2016</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2016</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2016</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
			</array>
		</dict>
		<key>uni2191</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23D0</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2191</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni2193</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2193</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23D0</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>uni2195</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2193</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23D0</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2191</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni21BE</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23D0</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21BE</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni21BF</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23D0</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21BF</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni21C2</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21C2</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23D0</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>uni21C3</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21C3</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23D0</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>uni21D1</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni21D1.ex</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21D1</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni21D3</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21D3</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni21D1.ex</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>uni21D5</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21D3</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni21D1.ex</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni21D1</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni221A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1855</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23B7</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>635</integer>
					<key>endConnector</key>
					<integer>300</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221A.ex</string>
					<key>startConnector</key>
					<integer>300</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>626</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.top</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni221A.rtlm</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1855</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.rtlm.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>635</integer>
					<key>endConnector</key>
					<integer>300</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221A.rtlm.ex</string>
					<key>startConnector</key>
					<integer>300</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>626</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.rtlm.top</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni221B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1855</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221B.base</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>635</integer>
					<key>endConnector</key>
					<integer>300</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221A.ex</string>
					<key>startConnector</key>
					<integer>300</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>626</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.top</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni221C</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1855</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221C.base</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>635</integer>
					<key>endConnector</key>
					<integer>300</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221A.ex</string>
					<key>startConnector</key>
					<integer>300</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>626</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.top</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
			</array>
		</dict>
		<key>uni2223</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>293</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2223</string>
					<key>startConnector</key>
					<integer>293</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>293</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2223</string>
					<key>startConnector</key>
					<integer>293</integer>
				</dict>
			</array>
		</dict>
		<key>uni2225</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>293</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2225</string>
					<key>startConnector</key>
					<integer>293</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>293</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2225</string>
					<key>startConnector</key>
					<integer>293</integer>
				</dict>
			</array>
		</dict>
		<key>uni2308</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>337</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>337</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>335</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>335</integer>
				</dict>
			</array>
		</dict>
		<key>uni2309</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>337</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>337</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>335</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>335</integer>
				</dict>
			</array>
		</dict>
		<key>uni230A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>335</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>335</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>337</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>337</integer>
				</dict>
			</array>
		</dict>
		<key>uni230B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>335</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>335</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>337</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>337</integer>
				</dict>
			</array>
		</dict>
		<key>uni23B0</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
			</array>
		</dict>
		<key>uni23B1</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EE</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EF</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1010</integer>
					<key>endConnector</key>
					<integer>90</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1005</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>90</integer>
				</dict>
			</array>
		</dict>
		<key>uni27F0</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27F0.ex</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27F0</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni27F1</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27F1</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27F0.ex</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>uni290A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni290A.ex</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni290A</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
			</array>
		</dict>
		<key>uni290B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>818</integer>
					<key>endConnector</key>
					<integer>273</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni290B</string>
					<key>startConnector</key>
					<integer>273</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>304</integer>
					<key>endConnector</key>
					<integer>101</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni290A.ex</string>
					<key>startConnector</key>
					<integer>101</integer>
				</dict>
			</array>
		</dict>
		<key>uni2980</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2980</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>879</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2980</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
			</array>
		</dict>
		<key>uni2AF4</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>884</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2AF4</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>884</integer>
					<key>endConnector</key>
					<integer>295</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2AF4</string>
					<key>startConnector</key>
					<integer>295</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.size1</string>
			<string>backslash.size2</string>
			<string>backslash.size3</string>
			<string>backslash.size4</string>
		</array>
		<key>bar</key>
		<array>
			<string>bar</string>
			<string>bar.size1</string>
			<string>bar.size2</string>
			<string>bar.size3</string>
			<string>bar.size4</string>
		</array>
		<key>braceleft</key>
		<array>
			<string>braceleft</string>
			<string>braceleft.size1</string>
			<string>braceleft.size2</string>
			<string>braceleft.size3</string>
			<string>braceleft.size4</string>
		</array>
		<key>braceright</key>
		<array>
			<string>braceright</string>
			<string>braceright.size1</string>
			<string>braceright.size2</string>
			<string>braceright.size3</string>
			<string>braceright.size4</string>
		</array>
		<key>bracketleft</key>
		<array>
			<string>bracketleft</string>
			<string>bracketleft.size1</string>
			<string>bracketleft.size2</string>
			<string>bracketleft.size3</string>
			<string>bracketleft.size4</string>
		</array>
		<key>bracketright</key>
		<array>
			<string>bracketright</string>
			<string>bracketright.size1</string>
			<string>bracketright.size2</string>
			<string>bracketright.size3</string>
			<string>bracketright.size4</string>
		</array>
		<key>fraction</key>
		<array>
			<string>fraction</string>
			<string>slash.size1</string>
			<string>slash.size2</string>
			<string>slash.size3</string>
			<string>slash.size4</string>
		</array>
		<key>parenleft</key>
		<array>
			<string>parenleft</string>
			<string>parenleft.size1</string>
			<string>parenleft.size2</string>
			<string>parenleft.size3</string>
			<string>parenleft.size4</string>
		</array>
		<key>parenright</key>
		<array>
			<string>parenright</string>
			<string>parenright.size1</string>
			<string>parenright.size2</string>
			<string>parenright.size3</string>
			<string>parenright.size4</string>
		</array>
		<key>slash</key>
		<array>
			<string>slash</string>
			<string>slash.size1</string>
			<string>slash.size2</string>
			<string>slash.size3</string>
			<string>slash.size4</string>
		</array>
		<key>uni0338</key>
		<array>
			<string>uni0338</string>
			<string>uni0338.size1</string>
			<string>uni0338.size2</string>
			<string>uni0338.size3</string>
			<string>uni0338.size4</string>
			<string>uni0338.size5</string>
			<string>uni0338.size6</string>
		</array>
		<key>uni0606</key>
		<array>
			<string>uni0606</string>
			<string>uni0606.size1</string>
			<string>uni0606.size2</string>
			<string>uni0606.size3</string>
		</array>
		<key>uni0607</key>
		<array>
			<string>uni0607</string>
			<string>uni0607.size1</string>
			<string>uni0607.size2</string>
			<string>uni0607.size3</string>
		</array>
		<key>uni2016</key>
		<array>
			<string>uni2016</string>
			<string>uni2016.size1</string>
			<string>uni2016.size2</string>
			<string>uni2016.size3</string>
			<string>uni2016.size4</string>
		</array>
		<key>uni20D2</key>
		<array>
			<string>uni20D2</string>
			<string>uni20D2.size1</string>
			<string>uni20D2.size2</string>
			<string>uni20D2.size3</string>
			<string>uni20D2.size4</string>
			<string>uni20D2.size5</string>
			<string>uni20D2.size6</string>
		</array>
		<key>uni2140</key>
		<array>
			<string>uni2140</string>
			<string>uni2140.display</string>
		</array>
		<key>uni2140.rtlm</key>
		<array>
			<string>uni2140.rtlm.display</string>
		</array>
		<key>uni2191</key>
		<array/>
		<key>uni2193</key>
		<array/>
		<key>uni2195</key>
		<array/>
		<key>uni21BE</key>
		<array/>
		<key>uni21BF</key>
		<array/>
		<key>uni21C2</key>
		<array/>
		<key>uni21C3</key>
		<array/>
		<key>uni21D1</key>
		<array/>
		<key>uni21D3</key>
		<array/>
		<key>uni21D5</key>
		<array/>
		<key>uni220F</key>
		<array>
			<string>uni220F</string>
			<string>uni220F.display</string>
		</array>
		<key>uni2210</key>
		<array>
			<string>uni2210</string>
			<string>uni2210.display</string>
		</array>
		<key>uni2211</key>
		<array>
			<string>uni2211</string>
			<string>uni2211.display</string>
		</array>
		<key>uni2211.rtlm</key>
		<array>
			<string>uni2211.rtlm.display</string>
		</array>
		<key>uni221A</key>
		<array>
			<string>uni221A</string>
			<string>uni221A.size1</string>
			<string>uni221A.size2</string>
			<string>uni221A.size3</string>
		</array>
		<key>uni221A.rtlm</key>
		<array>
			<string>uni221A.rtlm.size1</string>
			<string>uni221A.rtlm.size2</string>
			<string>uni221A.rtlm.size3</string>
		</array>
		<key>uni221B</key>
		<array>
			<string>uni221B</string>
			<string>uni221B.size1</string>
			<string>uni221B.size2</string>
			<string>uni221B.size3</string>
		</array>
		<key>uni221B.rtlm</key>
		<array>
			<string>uni221B.rtlm.size1</string>
			<string>uni221B.rtlm.size2</string>
			<string>uni221B.rtlm.size3</string>
		</array>
		<key>uni221C</key>
		<array>
			<string>uni221C</string>
			<string>uni221C.size1</string>
			<string>uni221C.size2</string>
			<string>uni221C.size3</string>
		</array>
		<key>uni221C.rtlm</key>
		<array>
			<string>uni221C.rtlm.size1</string>
			<string>uni221C.rtlm.size2</string>
			<string>uni221C.rtlm.size3</string>
		</array>
		<key>uni2223</key>
		<array/>
		<key>uni2225</key>
		<array/>
		<key>uni222B</key>
		<array>
			<string>uni222B</string>
			<string>uni222B.display</string>
		</array>
		<key>uni222B.rtlm</key>
		<array>
			<string>uni222B.rtlm.display</string>
		</array>
		<key>uni222B.up</key>
		<array>
			<string>uni222B.up</string>
			<string>uni222B.up.display</string>
		</array>
		<key>uni222C</key>
		<array>
			<string>uni222C</string>
			<string>uni222C.display</string>
		</array>
		<key>uni222C.rtlm</key>
		<array>
			<string>uni222C.rtlm.display</string>
		</array>
		<key>uni222C.up</key>
		<array>
			<string>uni222C.up</string>
			<string>uni222C.up.display</string>
		</array>
		<key>uni222D</key>
		<array>
			<string>uni222D</string>
			<string>uni222D.display</string>
		</array>
		<key>uni222D.rtlm</key>
		<array>
			<string>uni222D.rtlm.display</string>
		</array>
		<key>uni222D.up</key>
		<array>
			<string>uni222D.up</string>
			<string>uni222D.up.display</string>
		</array>
		<key>uni222E</key>
		<array>
			<string>uni222E</string>
			<string>uni222E.display</string>
		</array>
		<key>uni222E.rtlm</key>
		<array>
			<string>uni222E.rtlm.display</string>
		</array>
		<key>uni222E.up</key>
		<array>
			<string>uni222E.up</string>
			<string>uni222E.up.display</string>
		</array>
		<key>uni222F</key>
		<array>
			<string>uni222F</string>
			<string>uni222F.display</string>
		</array>
		<key>uni222F.rtlm</key>
		<array>
			<string>uni222F.rtlm.display</string>
		</array>
		<key>uni222F.up</key>
		<array>
			<string>uni222F.up</string>
			<string>uni222F.up.display</string>
		</array>
		<key>uni2230</key>
		<array>
			<string>uni2230</string>
			<string>uni2230.display</string>
		</array>
		<key>uni2230.rtlm</key>
		<array>
			<string>uni2230.rtlm.display</string>
		</array>
		<key>uni2230.up</key>
		<array>
			<string>uni2230.up</string>
			<string>uni2230.up.display</string>
		</array>
		<key>uni2231</key>
		<array>
			<string>uni2231</string>
			<string>uni2231.display</string>
		</array>
		<key>uni2231.rtlm</key>
		<array>
			<string>uni2231.rtlm.display</string>
		</array>
		<key>uni2231.up</key>
		<array>
			<string>uni2231.up</string>
			<string>uni2231.up.display</string>
		</array>
		<key>uni2232</key>
		<array>
			<string>uni2232</string>
			<string>uni2232.display</string>
		</array>
		<key>uni2232.rtlm</key>
		<array>
			<string>uni2232.rtlm.display</string>
		</array>
		<key>uni2232.up</key>
		<array>
			<string>uni2232.up</string>
			<string>uni2232.up.display</string>
		</array>
		<key>uni2233</key>
		<array>
			<string>uni2233</string>
			<string>uni2233.display</string>
		</array>
		<key>uni2233.rtlm</key>
		<array>
			<string>uni2233.rtlm.display</string>
		</array>
		<key>uni2233.up</key>
		<array>
			<string>uni2233.up</string>
			<string>uni2233.up.display</string>
		</array>
		<key>uni22C0</key>
		<array>
			<string>uni22C0</string>
			<string>uni22C0.display</string>
		</array>
		<key>uni22C1</key>
		<array>
			<string>uni22C1</string>
			<string>uni22C1.display</string>
		</array>
		<key>uni22C2</key>
		<array>
			<string>uni22C2</string>
			<string>uni22C2.display</string>
		</array>
		<key>uni22C3</key>
		<array>
			<string>uni22C3</string>
			<string>uni22C3.display</string>
		</array>
		<key>uni2308</key>
		<array>
			<string>uni2308</string>
			<string>uni2308.size1</string>
			<string>uni2308.size2</string>
			<string>uni2308.size3</string>
			<string>uni2308.size4</string>
		</array>
		<key>uni2309</key>
		<array>
			<string>uni2309</string>
			<string>uni2309.size1</string>
			<string>uni2309.size2</string>
			<string>uni2309.size3</string>
			<string>uni2309.size4</string>
		</array>
		<key>uni230A</key>
		<array>
			<string>uni230A</string>
			<string>uni230A.size1</string>
			<string>uni230A.size2</string>
			<string>uni230A.size3</string>
			<string>uni230A.size4</string>
		</array>
		<key>uni230B</key>
		<array>
			<string>uni230B</string>
			<string>uni230B.size1</string>
			<string>uni230B.size2</string>
			<string>uni230B.size3</string>
			<string>uni230B.size4</string>
		</array>
		<key>uni23B0</key>
		<array>
			<string>uni23B0</string>
		</array>
		<key>uni23B1</key>
		<array>
			<string>uni23B1</string>
		</array>
		<key>uni2772</key>
		<array>
			<string>uni2772</string>
			<string>uni2772.size1</string>
			<string>uni2772.size2</string>
			<string>uni2772.size3</string>
			<string>uni2772.size4</string>
		</array>
		<key>uni2773</key>
		<array>
			<string>uni2773</string>
			<string>uni2773.size1</string>
			<string>uni2773.size2</string>
			<string>uni2773.size3</string>
			<string>uni2773.size4</string>
		</array>
		<key>uni27E6</key>
		<array>
			<string>uni27E6</string>
			<string>uni27E6.size1</string>
			<string>uni27E6.size2</string>
			<string>uni27E6.size3</string>
			<string>uni27E6.size4</string>
		</array>
		<key>uni27E7</key>
		<array>
			<string>uni27E7</string>
			<string>uni27E7.size1</string>
			<string>uni27E7.size2</string>
			<string>uni27E7.size3</string>
			<string>uni27E7.size4</string>
		</array>
		<key>uni27E8</key>
		<array>
			<string>uni27E8</string>
			<string>uni27E8.size1</string>
			<string>uni27E8.size2</string>
			<string>uni27E8.size3</string>
			<string>uni27E8.size4</string>
		</array>
		<key>uni27E9</key>
		<array>
			<string>uni27E9</string>
			<string>uni27E9.size1</string>
			<string>uni27E9.size2</string>
			<string>uni27E9.size3</string>
			<string>uni27E9.size4</string>
		</array>
		<key>uni27EA</key>
		<array>
			<string>uni27EA</string>
			<string>uni27EA.size1</string>
			<string>uni27EA.size2</string>
			<string>uni27EA.size3</string>
			<string>uni27EA.size4</string>
		</array>
		<key>uni27EB</key>
		<array>
			<string>uni27EB</string>
			<string>uni27EB.size1</string>
			<string>uni27EB.size2</string>
			<string>uni27EB.size3</string>
			<string>uni27EB.size4</string>
		</array>
		<key>uni27EE</key>
		<array>
			<string>uni27EE</string>
		</array>
		<key>uni27EF</key>
		<array>
			<string>uni27EF</string>
		</array>
		<key>uni27F0</key>
		<array/>
		<key>uni27F1</key>
		<array/>
		<key>uni290A</key>
		<array/>
		<key>uni290B</key>
		<array/>
		<key>uni2980</key>
		<array>
			<string>uni2980</string>
			<string>uni2980.size1</string>
			<string>uni2980.size2</string>
			<string>uni2980.size3</string>
			<string>uni2980.size4</string>
		</array>
		<key>uni2983</key>
		<array>
			<string>uni2983</string>
			<string>uni2983.size1</string>
			<string>uni2983.size2</string>
			<string>uni2983.size3</string>
			<string>uni2983.size4</string>
		</array>
		<key>uni2984</key>
		<array>
			<string>uni2984</string>
			<string>uni2984.size1</string>
			<string>uni2984.size2</string>
			<string>uni2984.size3</string>
			<string>uni2984.size4</string>
		</array>
		<key>uni2985</key>
		<array>
			<string>uni2985</string>
			<string>uni2985.size1</string>
			<string>uni2985.size2</string>
			<string>uni2985.size3</string>
			<string>uni2985.size4</string>
		</array>
		<key>uni2986</key>
		<array>
			<string>uni2986</string>
			<string>uni2986.size1</string>
			<string>uni2986.size2</string>
			<string>uni2986.size3</string>
			<string>uni2986.size4</string>
		</array>
		<key>uni29F8</key>
		<array>
			<string>uni29F8</string>
			<string>uni29F8.size1</string>
		</array>
		<key>uni29F9</key>
		<array>
			<string>uni29F9</string>
			<string>uni29F9.size1</string>
		</array>
		<key>uni2A00</key>
		<array>
			<string>uni2A00</string>
			<string>uni2A00.display</string>
		</array>
		<key>uni2A01</key>
		<array>
			<string>uni2A01</string>
			<string>uni2A01.display</string>
		</array>
		<key>uni2A02</key>
		<array>
			<string>uni2A02</string>
			<string>uni2A02.display</string>
		</array>
		<key>uni2A03</key>
		<array>
			<string>uni2A03</string>
			<string>uni2A03.display</string>
		</array>
		<key>uni2A04</key>
		<array>
			<string>uni2A04</string>
			<string>uni2A04.display</string>
		</array>
		<key>uni2A05</key>
		<array>
			<string>uni2A05</string>
			<string>uni2A05.display</string>
		</array>
		<key>uni2A06</key>
		<array>
			<string>uni2A06</string>
			<string>uni2A06.display</string>
		</array>
		<key>uni2A07</key>
		<array>
			<string>uni2A07</string>
			<string>uni2A07.display</string>
		</array>
		<key>uni2A08</key>
		<array>
			<string>uni2A08</string>
			<string>uni2A08.display</string>
		</array>
		<key>uni2A09</key>
		<array>
			<string>uni2A09</string>
			<string>uni2A09.display</string>
		</array>
		<key>uni2A0A</key>
		<array>
			<string>uni2A0A</string>
			<string>uni2A0A.display</string>
		</array>
		<key>uni2A0A.rtlm</key>
		<array>
			<string>uni2A0A.rtlm.display</string>
		</array>
		<key>uni2A0B</key>
		<array>
			<string>uni2A0B</string>
			<string>uni2A0B.display</string>
		</array>
		<key>uni2A0B.rtlm</key>
		<array>
			<string>uni2A0B.rtlm.display</string>
		</array>
		<key>uni2A0B.up</key>
		<array>
			<string>uni2A0B.up</string>
			<string>uni2A0B.up.display</string>
		</array>
		<key>uni2A0C</key>
		<array>
			<string>uni2A0C</string>
			<string>uni2A0C.display</string>
		</array>
		<key>uni2A0C.rtlm</key>
		<array>
			<string>uni2A0C.rtlm.display</string>
		</array>
		<key>uni2A0C.up</key>
		<array>
			<string>uni2A0C.up</string>
			<string>uni2A0C.up.display</string>
		</array>
		<key>uni2A0D</key>
		<array>
			<string>uni2A0D</string>
			<string>uni2A0D.display</string>
		</array>
		<key>uni2A0D.rtlm</key>
		<array>
			<string>uni2A0D.rtlm.display</string>
		</array>
		<key>uni2A0D.up</key>
		<array>
			<string>uni2A0D.up</string>
			<string>uni2A0D.up.display</string>
		</array>
		<key>uni2A0E</key>
		<array>
			<string>uni2A0E</string>
			<string>uni2A0E.display</string>
		</array>
		<key>uni2A0E.rtlm</key>
		<array>
			<string>uni2A0E.rtlm.display</string>
		</array>
		<key>uni2A0E.up</key>
		<array>
			<string>uni2A0E.up</string>
			<string>uni2A0E.up.display</string>
		</array>
		<key>uni2A0F</key>
		<array>
			<string>uni2A0F</string>
			<string>uni2A0F.display</string>
		</array>
		<key>uni2A0F.rtlm</key>
		<array>
			<string>uni2A0F.rtlm.display</string>
		</array>
		<key>uni2A0F.up</key>
		<array>
			<string>uni2A0F.up</string>
			<string>uni2A0F.up.display</string>
		</array>
		<key>uni2A10</key>
		<array>
			<string>uni2A10</string>
			<string>uni2A10.display</string>
		</array>
		<key>uni2A10.rtlm</key>
		<array>
			<string>uni2A10.rtlm.display</string>
		</array>
		<key>uni2A10.up</key>
		<array>
			<string>uni2A10.up</string>
			<string>uni2A10.up.display</string>
		</array>
		<key>uni2A11</key>
		<array>
			<string>uni2A11</string>
			<string>uni2A11.display</string>
		</array>
		<key>uni2A11.rtlm</key>
		<array>
			<string>uni2A11.rtlm.display</string>
		</array>
		<key>uni2A11.up</key>
		<array>
			<string>uni2A11.up</string>
			<string>uni2A11.up.display</string>
		</array>
		<key>uni2A12</key>
		<array>
			<string>uni2A12</string>
			<string>uni2A12.display</string>
		</array>
		<key>uni2A12.rtlm</key>
		<array>
			<string>uni2A12.rtlm.display</string>
		</array>
		<key>uni2A12.up</key>
		<array>
			<string>uni2A12.up</string>
			<string>uni2A12.up.display</string>
		</array>
		<key>uni2A13</key>
		<array>
			<string>uni2A13</string>
			<string>uni2A13.display</string>
		</array>
		<key>uni2A13.rtlm</key>
		<array>
			<string>uni2A13.rtlm.display</string>
		</array>
		<key>uni2A13.up</key>
		<array>
			<string>uni2A13.up</string>
			<string>uni2A13.up.display</string>
		</array>
		<key>uni2A14</key>
		<array>
			<string>uni2A14</string>
			<string>uni2A14.display</string>
		</array>
		<key>uni2A14.rtlm</key>
		<array>
			<string>uni2A14.rtlm.display</string>
		</array>
		<key>uni2A14.up</key>
		<array>
			<string>uni2A14.up</string>
			<string>uni2A14.up.display</string>
		</array>
		<key>uni2A15</key>
		<array>
			<string>uni2A15</string>
			<string>uni2A15.display</string>
		</array>
		<key>uni2A15.rtlm</key>
		<array>
			<string>uni2A15.rtlm.display</string>
		</array>
		<key>uni2A15.up</key>
		<array>
			<string>uni2A15.up</string>
			<string>uni2A15.up.display</string>
		</array>
		<key>uni2A16</key>
		<array>
			<string>uni2A16</string>
			<string>uni2A16.display</string>
		</array>
		<key>uni2A16.rtlm</key>
		<array>
			<string>uni2A16.rtlm.display</string>
		</array>
		<key>uni2A16.up</key>
		<array>
			<string>uni2A16.up</string>
			<string>uni2A16.up.display</string>
		</array>
		<key>uni2A17</key>
		<array>
			<string>uni2A17</string>
			<string>uni2A17.display</string>
		</array>
		<key>uni2A17.rtlm</key>
		<array>
			<string>uni2A17.rtlm.display</string>
		</array>
		<key>uni2A17.up</key>
		<array>
			<string>uni2A17.up</string>
			<string>uni2A17.up.display</string>
		</array>
		<key>uni2A18</key>
		<array>
			<string>uni2A18</string>
			<string>uni2A18.display</string>
		</array>
		<key>uni2A18.rtlm</key>
		<array>
			<string>uni2A18.rtlm.display</string>
		</array>
		<key>uni2A18.up</key>
		<array>
			<string>uni2A18.up</string>
			<string>uni2A18.up.display</string>
		</array>
		<key>uni2A19</key>
		<array>
			<string>uni2A19</string>
			<string>uni2A19.display</string>
		</array>
		<key>uni2A19.rtlm</key>
		<array>
			<string>uni2A19.rtlm.display</string>
		</array>
		<key>uni2A19.up</key>
		<array>
			<string>uni2A19.up</string>
			<string>uni2A19.up.display</string>
		</array>
		<key>uni2A1A</key>
		<array>
			<string>uni2A1A</string>
			<string>uni2A1A.display</string>
		</array>
		<key>uni2A1A.rtlm</key>
		<array>
			<string>uni2A1A.rtlm.display</string>
		</array>
		<key>uni2A1A.up</key>
		<array>
			<string>uni2A1A.up</string>
			<string>uni2A1A.up.display</string>
		</array>
		<key>uni2A1B</key>
		<array>
			<string>uni2A1B</string>
			<string>uni2A1B.display</string>
		</array>
		<key>uni2A1B.rtlm</key>
		<array>
			<string>uni2A1B.rtlm.display</string>
		</array>
		<key>uni2A1B.up</key>
		<array>
			<string>uni2A1B.up</string>
			<string>uni2A1B.up.display</string>
		</array>
		<key>uni2A1C</key>
		<array>
			<string>uni2A1C</string>
			<string>uni2A1C.display</string>
		</array>
		<key>uni2A1C.rtlm</key>
		<array>
			<string>uni2A1C.rtlm.display</string>
		</array>
		<key>uni2A1C.up</key>
		<array>
			<string>uni2A1C.up</string>
			<string>uni2A1C.up.display</string>
		</array>
		<key>uni2AF4</key>
		<array/>
		<key>uni2AFC</key>
		<array>
			<string>uni2AFC</string>
			<string>uni2AFC.display</string>
		</array>
		<key>uni2AFF</key>
		<array>
			<string>uni2AFF</string>
			<string>uni2AFF.display</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
