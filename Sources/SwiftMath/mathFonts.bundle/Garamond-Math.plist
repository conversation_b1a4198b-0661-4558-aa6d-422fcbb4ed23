<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>A</key>
		<integer>354</integer>
		<key>A.s</key>
		<integer>357</integer>
		<key>A.ss</key>
		<integer>359</integer>
		<key>B</key>
		<integer>274</integer>
		<key>B.s</key>
		<integer>279</integer>
		<key>B.ss</key>
		<integer>285</integer>
		<key>C</key>
		<integer>415</integer>
		<key>C.s</key>
		<integer>415</integer>
		<key>C.ss</key>
		<integer>417</integer>
		<key>D</key>
		<integer>340</integer>
		<key>D.s</key>
		<integer>346</integer>
		<key>D.ss</key>
		<integer>351</integer>
		<key>Delta</key>
		<integer>337</integer>
		<key>Delta.s</key>
		<integer>340</integer>
		<key>Delta.ss</key>
		<integer>345</integer>
		<key>E</key>
		<integer>272</integer>
		<key>E.s</key>
		<integer>270</integer>
		<key>E.ss</key>
		<integer>269</integer>
		<key>F</key>
		<integer>263</integer>
		<key>F.s</key>
		<integer>264</integer>
		<key>F.ss</key>
		<integer>265</integer>
		<key>G</key>
		<integer>404</integer>
		<key>G.s</key>
		<integer>405</integer>
		<key>G.ss</key>
		<integer>407</integer>
		<key>Gamma</key>
		<integer>276</integer>
		<key>Gamma.s</key>
		<integer>274</integer>
		<key>Gamma.ss</key>
		<integer>274</integer>
		<key>H</key>
		<integer>412</integer>
		<key>H.s</key>
		<integer>404</integer>
		<key>H.ss</key>
		<integer>404</integer>
		<key>I</key>
		<integer>176</integer>
		<key>I.s</key>
		<integer>176</integer>
		<key>I.ss</key>
		<integer>177</integer>
		<key>Ifraktur</key>
		<integer>470</integer>
		<key>J</key>
		<integer>177</integer>
		<key>J.s</key>
		<integer>181</integer>
		<key>J.ss</key>
		<integer>184</integer>
		<key>K</key>
		<integer>353</integer>
		<key>K.s</key>
		<integer>358</integer>
		<key>K.ss</key>
		<integer>363</integer>
		<key>L</key>
		<integer>177</integer>
		<key>L.s</key>
		<integer>177</integer>
		<key>L.ss</key>
		<integer>178</integer>
		<key>Lambda</key>
		<integer>350</integer>
		<key>Lambda.s</key>
		<integer>352</integer>
		<key>Lambda.ss</key>
		<integer>353</integer>
		<key>M</key>
		<integer>454</integer>
		<key>M.s</key>
		<integer>450</integer>
		<key>M.ss</key>
		<integer>452</integer>
		<key>N</key>
		<integer>394</integer>
		<key>N.s</key>
		<integer>394</integer>
		<key>N.ss</key>
		<integer>396</integer>
		<key>O</key>
		<integer>382</integer>
		<key>O.s</key>
		<integer>381</integer>
		<key>O.ss</key>
		<integer>381</integer>
		<key>Omega</key>
		<integer>418</integer>
		<key>Omega.s</key>
		<integer>418</integer>
		<key>Omega.ss</key>
		<integer>418</integer>
		<key>P</key>
		<integer>274</integer>
		<key>P.s</key>
		<integer>280</integer>
		<key>P.ss</key>
		<integer>286</integer>
		<key>Phi</key>
		<integer>383</integer>
		<key>Phi.s</key>
		<integer>381</integer>
		<key>Phi.ss</key>
		<integer>380</integer>
		<key>Pi</key>
		<integer>414</integer>
		<key>Pi.s</key>
		<integer>411</integer>
		<key>Pi.ss</key>
		<integer>408</integer>
		<key>Psi</key>
		<integer>376</integer>
		<key>Psi.s</key>
		<integer>383</integer>
		<key>Psi.ss</key>
		<integer>390</integer>
		<key>Q</key>
		<integer>382</integer>
		<key>Q.s</key>
		<integer>381</integer>
		<key>Q.ss</key>
		<integer>381</integer>
		<key>R</key>
		<integer>278</integer>
		<key>R.s</key>
		<integer>283</integer>
		<key>R.ss</key>
		<integer>288</integer>
		<key>Rfraktur</key>
		<integer>378</integer>
		<key>S</key>
		<integer>252</integer>
		<key>S.s</key>
		<integer>253</integer>
		<key>S.ss</key>
		<integer>255</integer>
		<key>Sigma</key>
		<integer>289</integer>
		<key>Sigma.s</key>
		<integer>289</integer>
		<key>Sigma.ss</key>
		<integer>289</integer>
		<key>T</key>
		<integer>336</integer>
		<key>T.s</key>
		<integer>339</integer>
		<key>T.ss</key>
		<integer>342</integer>
		<key>Theta</key>
		<integer>388</integer>
		<key>Theta.s</key>
		<integer>386</integer>
		<key>Theta.ss</key>
		<integer>386</integer>
		<key>U</key>
		<integer>376</integer>
		<key>U.s</key>
		<integer>375</integer>
		<key>U.ss</key>
		<integer>375</integer>
		<key>Upsilon</key>
		<integer>381</integer>
		<key>Upsilon.s</key>
		<integer>380</integer>
		<key>Upsilon.ss</key>
		<integer>379</integer>
		<key>V</key>
		<integer>339</integer>
		<key>V.s</key>
		<integer>340</integer>
		<key>V.ss</key>
		<integer>342</integer>
		<key>W</key>
		<integer>465</integer>
		<key>W.s</key>
		<integer>471</integer>
		<key>W.ss</key>
		<integer>476</integer>
		<key>X</key>
		<integer>357</integer>
		<key>X.s</key>
		<integer>358</integer>
		<key>X.ss</key>
		<integer>360</integer>
		<key>Xi</key>
		<integer>348</integer>
		<key>Xi.s</key>
		<integer>351</integer>
		<key>Xi.ss</key>
		<integer>353</integer>
		<key>Y</key>
		<integer>310</integer>
		<key>Y.s</key>
		<integer>314</integer>
		<key>Y.ss</key>
		<integer>317</integer>
		<key>Z</key>
		<integer>322</integer>
		<key>Z.s</key>
		<integer>321</integer>
		<key>Z.ss</key>
		<integer>322</integer>
		<key>a</key>
		<integer>220</integer>
		<key>a.s</key>
		<integer>207</integer>
		<key>a.ss</key>
		<integer>209</integer>
		<key>alpha</key>
		<integer>259</integer>
		<key>alpha.s</key>
		<integer>273</integer>
		<key>alpha.ss</key>
		<integer>275</integer>
		<key>b</key>
		<integer>110</integer>
		<key>b.s</key>
		<integer>74</integer>
		<key>b.ss</key>
		<integer>116</integer>
		<key>beta</key>
		<integer>236</integer>
		<key>beta.s</key>
		<integer>235</integer>
		<key>beta.ss</key>
		<integer>233</integer>
		<key>c</key>
		<integer>247</integer>
		<key>c.s</key>
		<integer>247</integer>
		<key>c.ss</key>
		<integer>246</integer>
		<key>chi</key>
		<integer>219</integer>
		<key>chi.s</key>
		<integer>222</integer>
		<key>chi.ss</key>
		<integer>224</integer>
		<key>d</key>
		<integer>387</integer>
		<key>d.s</key>
		<integer>389</integer>
		<key>d.ss</key>
		<integer>391</integer>
		<key>delta</key>
		<integer>171</integer>
		<key>delta.s</key>
		<integer>163</integer>
		<key>delta.ss</key>
		<integer>167</integer>
		<key>dotlessi</key>
		<integer>150</integer>
		<key>dotlessi.ss</key>
		<integer>98</integer>
		<key>dotlessj</key>
		<integer>146</integer>
		<key>dotlessj.ss</key>
		<integer>135</integer>
		<key>e</key>
		<integer>221</integer>
		<key>e.s</key>
		<integer>223</integer>
		<key>e.ss</key>
		<integer>225</integer>
		<key>eight</key>
		<integer>251</integer>
		<key>eight.prp</key>
		<integer>248</integer>
		<key>eight.prp.s</key>
		<integer>252</integer>
		<key>eight.prp.ss</key>
		<integer>257</integer>
		<key>eight.s</key>
		<integer>251</integer>
		<key>eight.ss</key>
		<integer>251</integer>
		<key>epsilon</key>
		<integer>202</integer>
		<key>epsilon.s</key>
		<integer>204</integer>
		<key>epsilon.ss</key>
		<integer>206</integer>
		<key>eta</key>
		<integer>235</integer>
		<key>eta.s</key>
		<integer>240</integer>
		<key>eta.ss</key>
		<integer>245</integer>
		<key>f</key>
		<integer>268</integer>
		<key>f.s</key>
		<integer>272</integer>
		<key>f.ss</key>
		<integer>276</integer>
		<key>five</key>
		<integer>267</integer>
		<key>five.prp</key>
		<integer>249</integer>
		<key>five.prp.s</key>
		<integer>248</integer>
		<key>five.prp.ss</key>
		<integer>247</integer>
		<key>five.s</key>
		<integer>267</integer>
		<key>five.ss</key>
		<integer>267</integer>
		<key>four</key>
		<integer>318</integer>
		<key>four.prp</key>
		<integer>327</integer>
		<key>four.prp.s</key>
		<integer>330</integer>
		<key>four.prp.ss</key>
		<integer>334</integer>
		<key>four.s</key>
		<integer>318</integer>
		<key>four.ss</key>
		<integer>318</integer>
		<key>g</key>
		<integer>213</integer>
		<key>g.s</key>
		<integer>215</integer>
		<key>g.ss</key>
		<integer>219</integer>
		<key>gamma</key>
		<integer>219</integer>
		<key>gamma.s</key>
		<integer>231</integer>
		<key>gamma.ss</key>
		<integer>236</integer>
		<key>h</key>
		<integer>120</integer>
		<key>h.s</key>
		<integer>122</integer>
		<key>h.ss</key>
		<integer>126</integer>
		<key>i</key>
		<integer>124</integer>
		<key>i.s</key>
		<integer>126</integer>
		<key>i.ss</key>
		<integer>130</integer>
		<key>iota</key>
		<integer>111</integer>
		<key>iota.s</key>
		<integer>113</integer>
		<key>iota.ss</key>
		<integer>116</integer>
		<key>j</key>
		<integer>116</integer>
		<key>j.s</key>
		<integer>118</integer>
		<key>j.ss</key>
		<integer>121</integer>
		<key>k</key>
		<integer>120</integer>
		<key>k.s</key>
		<integer>122</integer>
		<key>k.ss</key>
		<integer>126</integer>
		<key>kappa</key>
		<integer>246</integer>
		<key>kappa.s</key>
		<integer>246</integer>
		<key>kappa.ss</key>
		<integer>248</integer>
		<key>l</key>
		<integer>120</integer>
		<key>l.s</key>
		<integer>122</integer>
		<key>l.ss</key>
		<integer>126</integer>
		<key>lambda</key>
		<integer>110</integer>
		<key>lambda.s</key>
		<integer>108</integer>
		<key>lambda.ss</key>
		<integer>105</integer>
		<key>m</key>
		<integer>375</integer>
		<key>m.s</key>
		<integer>376</integer>
		<key>m.ss</key>
		<integer>381</integer>
		<key>mu</key>
		<integer>259</integer>
		<key>mu.s</key>
		<integer>258</integer>
		<key>mu.ss</key>
		<integer>276</integer>
		<key>n</key>
		<integer>247</integer>
		<key>n.s</key>
		<integer>250</integer>
		<key>n.ss</key>
		<integer>256</integer>
		<key>nabla</key>
		<integer>351</integer>
		<key>nabla.s</key>
		<integer>353</integer>
		<key>nabla.ss</key>
		<integer>356</integer>
		<key>nine</key>
		<integer>245</integer>
		<key>nine.prp</key>
		<integer>253</integer>
		<key>nine.prp.s</key>
		<integer>258</integer>
		<key>nine.prp.ss</key>
		<integer>263</integer>
		<key>nine.s</key>
		<integer>246</integer>
		<key>nine.ss</key>
		<integer>248</integer>
		<key>nu</key>
		<integer>206</integer>
		<key>nu.s</key>
		<integer>211</integer>
		<key>nu.ss</key>
		<integer>205</integer>
		<key>o</key>
		<integer>255</integer>
		<key>o.s</key>
		<integer>255</integer>
		<key>o.ss</key>
		<integer>255</integer>
		<key>omega</key>
		<integer>288</integer>
		<key>omega.s</key>
		<integer>289</integer>
		<key>omega.ss</key>
		<integer>291</integer>
		<key>one</key>
		<integer>254</integer>
		<key>one.prp</key>
		<integer>150</integer>
		<key>one.prp.s</key>
		<integer>158</integer>
		<key>one.prp.ss</key>
		<integer>202</integer>
		<key>one.s</key>
		<integer>253</integer>
		<key>one.ss</key>
		<integer>253</integer>
		<key>p</key>
		<integer>237</integer>
		<key>p.s</key>
		<integer>243</integer>
		<key>p.ss</key>
		<integer>245</integer>
		<key>partialdiff</key>
		<integer>170</integer>
		<key>partialdiff.s</key>
		<integer>171</integer>
		<key>partialdiff.ss</key>
		<integer>172</integer>
		<key>partialdiff.x</key>
		<integer>198</integer>
		<key>partialdiff.x.s</key>
		<integer>201</integer>
		<key>partialdiff.x.ss</key>
		<integer>203</integer>
		<key>phi</key>
		<integer>302</integer>
		<key>phi.s</key>
		<integer>301</integer>
		<key>phi.ss</key>
		<integer>301</integer>
		<key>pi</key>
		<integer>289</integer>
		<key>pi.s</key>
		<integer>288</integer>
		<key>pi.ss</key>
		<integer>287</integer>
		<key>psi</key>
		<integer>307</integer>
		<key>psi.s</key>
		<integer>307</integer>
		<key>psi.ss</key>
		<integer>307</integer>
		<key>q</key>
		<integer>317</integer>
		<key>q.s</key>
		<integer>314</integer>
		<key>q.ss</key>
		<integer>311</integer>
		<key>r</key>
		<integer>164</integer>
		<key>r.s</key>
		<integer>169</integer>
		<key>r.ss</key>
		<integer>174</integer>
		<key>rho</key>
		<integer>236</integer>
		<key>rho.s</key>
		<integer>234</integer>
		<key>rho.ss</key>
		<integer>233</integer>
		<key>s</key>
		<integer>171</integer>
		<key>s.s</key>
		<integer>174</integer>
		<key>s.ss</key>
		<integer>178</integer>
		<key>seven</key>
		<integer>250</integer>
		<key>seven.prp</key>
		<integer>256</integer>
		<key>seven.prp.s</key>
		<integer>260</integer>
		<key>seven.prp.ss</key>
		<integer>266</integer>
		<key>seven.s</key>
		<integer>256</integer>
		<key>seven.ss</key>
		<integer>258</integer>
		<key>sigma</key>
		<integer>269</integer>
		<key>sigma.s</key>
		<integer>276</integer>
		<key>sigma.ss</key>
		<integer>283</integer>
		<key>six</key>
		<integer>320</integer>
		<key>six.prp</key>
		<integer>310</integer>
		<key>six.prp.s</key>
		<integer>312</integer>
		<key>six.prp.ss</key>
		<integer>315</integer>
		<key>six.s</key>
		<integer>320</integer>
		<key>six.ss</key>
		<integer>320</integer>
		<key>t</key>
		<integer>148</integer>
		<key>t.s</key>
		<integer>155</integer>
		<key>t.ss</key>
		<integer>163</integer>
		<key>tau</key>
		<integer>226</integer>
		<key>tau.s</key>
		<integer>227</integer>
		<key>tau.ss</key>
		<integer>228</integer>
		<key>theta</key>
		<integer>222</integer>
		<key>theta.s</key>
		<integer>224</integer>
		<key>theta.ss</key>
		<integer>225</integer>
		<key>three</key>
		<integer>233</integer>
		<key>three.prp</key>
		<integer>239</integer>
		<key>three.prp.s</key>
		<integer>238</integer>
		<key>three.prp.ss</key>
		<integer>236</integer>
		<key>three.s</key>
		<integer>233</integer>
		<key>three.ss</key>
		<integer>232</integer>
		<key>two</key>
		<integer>234</integer>
		<key>two.prp</key>
		<integer>253</integer>
		<key>two.prp.s</key>
		<integer>254</integer>
		<key>two.prp.ss</key>
		<integer>256</integer>
		<key>two.s</key>
		<integer>233</integer>
		<key>two.ss</key>
		<integer>234</integer>
		<key>u</key>
		<integer>261</integer>
		<key>u.s</key>
		<integer>263</integer>
		<key>u.ss</key>
		<integer>264</integer>
		<key>u1D400</key>
		<integer>377</integer>
		<key>u1D400.eb</key>
		<integer>382</integer>
		<key>u1D400.s</key>
		<integer>379</integer>
		<key>u1D400.sb</key>
		<integer>372</integer>
		<key>u1D400.ss</key>
		<integer>381</integer>
		<key>u1D401</key>
		<integer>299</integer>
		<key>u1D401.eb</key>
		<integer>313</integer>
		<key>u1D401.s</key>
		<integer>304</integer>
		<key>u1D401.sb</key>
		<integer>278</integer>
		<key>u1D401.ss</key>
		<integer>310</integer>
		<key>u1D402</key>
		<integer>433</integer>
		<key>u1D402.eb</key>
		<integer>439</integer>
		<key>u1D402.s</key>
		<integer>436</integer>
		<key>u1D402.sb</key>
		<integer>426</integer>
		<key>u1D402.ss</key>
		<integer>438</integer>
		<key>u1D403</key>
		<integer>333</integer>
		<key>u1D403.eb</key>
		<integer>348</integer>
		<key>u1D403.s</key>
		<integer>339</integer>
		<key>u1D403.sb</key>
		<integer>312</integer>
		<key>u1D403.ss</key>
		<integer>345</integer>
		<key>u1D404</key>
		<integer>275</integer>
		<key>u1D404.eb</key>
		<integer>280</integer>
		<key>u1D404.s</key>
		<integer>277</integer>
		<key>u1D404.sb</key>
		<integer>266</integer>
		<key>u1D404.ss</key>
		<integer>278</integer>
		<key>u1D405</key>
		<integer>281</integer>
		<key>u1D405.eb</key>
		<integer>287</integer>
		<key>u1D405.s</key>
		<integer>284</integer>
		<key>u1D405.sb</key>
		<integer>272</integer>
		<key>u1D405.ss</key>
		<integer>285</integer>
		<key>u1D406</key>
		<integer>418</integer>
		<key>u1D406.eb</key>
		<integer>422</integer>
		<key>u1D406.s</key>
		<integer>420</integer>
		<key>u1D406.sb</key>
		<integer>416</integer>
		<key>u1D406.ss</key>
		<integer>421</integer>
		<key>u1D407</key>
		<integer>413</integer>
		<key>u1D407.eb</key>
		<integer>417</integer>
		<key>u1D407.s</key>
		<integer>415</integer>
		<key>u1D407.sb</key>
		<integer>406</integer>
		<key>u1D407.ss</key>
		<integer>416</integer>
		<key>u1D408</key>
		<integer>193</integer>
		<key>u1D408.eb</key>
		<integer>198</integer>
		<key>u1D408.s</key>
		<integer>194</integer>
		<key>u1D408.sb</key>
		<integer>185</integer>
		<key>u1D408.ss</key>
		<integer>197</integer>
		<key>u1D409</key>
		<integer>222</integer>
		<key>u1D409.eb</key>
		<integer>228</integer>
		<key>u1D409.s</key>
		<integer>224</integer>
		<key>u1D409.sb</key>
		<integer>210</integer>
		<key>u1D409.ss</key>
		<integer>227</integer>
		<key>u1D40A</key>
		<integer>403</integer>
		<key>u1D40A.eb</key>
		<integer>417</integer>
		<key>u1D40A.s</key>
		<integer>408</integer>
		<key>u1D40A.sb</key>
		<integer>378</integer>
		<key>u1D40A.ss</key>
		<integer>414</integer>
		<key>u1D40B</key>
		<integer>192</integer>
		<key>u1D40B.eb</key>
		<integer>197</integer>
		<key>u1D40B.s</key>
		<integer>194</integer>
		<key>u1D40B.sb</key>
		<integer>183</integer>
		<key>u1D40B.ss</key>
		<integer>197</integer>
		<key>u1D40C</key>
		<integer>454</integer>
		<key>u1D40C.eb</key>
		<integer>458</integer>
		<key>u1D40C.s</key>
		<integer>456</integer>
		<key>u1D40C.sb</key>
		<integer>449</integer>
		<key>u1D40C.ss</key>
		<integer>457</integer>
		<key>u1D40D</key>
		<integer>412</integer>
		<key>u1D40D.eb</key>
		<integer>416</integer>
		<key>u1D40D.s</key>
		<integer>413</integer>
		<key>u1D40D.sb</key>
		<integer>404</integer>
		<key>u1D40D.ss</key>
		<integer>415</integer>
		<key>u1D40E</key>
		<integer>393</integer>
		<key>u1D40E.eb</key>
		<integer>397</integer>
		<key>u1D40E.s</key>
		<integer>395</integer>
		<key>u1D40E.sb</key>
		<integer>390</integer>
		<key>u1D40E.ss</key>
		<integer>396</integer>
		<key>u1D40F</key>
		<integer>299</integer>
		<key>u1D40F.eb</key>
		<integer>314</integer>
		<key>u1D40F.s</key>
		<integer>305</integer>
		<key>u1D40F.sb</key>
		<integer>278</integer>
		<key>u1D40F.ss</key>
		<integer>312</integer>
		<key>u1D410</key>
		<integer>396</integer>
		<key>u1D410.eb</key>
		<integer>400</integer>
		<key>u1D410.s</key>
		<integer>398</integer>
		<key>u1D410.sb</key>
		<integer>393</integer>
		<key>u1D410.ss</key>
		<integer>399</integer>
		<key>u1D411</key>
		<integer>302</integer>
		<key>u1D411.eb</key>
		<integer>316</integer>
		<key>u1D411.s</key>
		<integer>308</integer>
		<key>u1D411.sb</key>
		<integer>282</integer>
		<key>u1D411.ss</key>
		<integer>313</integer>
		<key>u1D412</key>
		<integer>285</integer>
		<key>u1D412.eb</key>
		<integer>294</integer>
		<key>u1D412.s</key>
		<integer>289</integer>
		<key>u1D412.sb</key>
		<integer>272</integer>
		<key>u1D412.ss</key>
		<integer>292</integer>
		<key>u1D413</key>
		<integer>354</integer>
		<key>u1D413.eb</key>
		<integer>393</integer>
		<key>u1D413.s</key>
		<integer>356</integer>
		<key>u1D413.sb</key>
		<integer>343</integer>
		<key>u1D413.ss</key>
		<integer>360</integer>
		<key>u1D414</key>
		<integer>383</integer>
		<key>u1D414.eb</key>
		<integer>386</integer>
		<key>u1D414.s</key>
		<integer>384</integer>
		<key>u1D414.sb</key>
		<integer>381</integer>
		<key>u1D414.ss</key>
		<integer>385</integer>
		<key>u1D415</key>
		<integer>352</integer>
		<key>u1D415.eb</key>
		<integer>355</integer>
		<key>u1D415.s</key>
		<integer>353</integer>
		<key>u1D415.sb</key>
		<integer>347</integer>
		<key>u1D415.ss</key>
		<integer>355</integer>
		<key>u1D416</key>
		<integer>510</integer>
		<key>u1D416.eb</key>
		<integer>519</integer>
		<key>u1D416.s</key>
		<integer>514</integer>
		<key>u1D416.sb</key>
		<integer>494</integer>
		<key>u1D416.ss</key>
		<integer>517</integer>
		<key>u1D417</key>
		<integer>382</integer>
		<key>u1D417.eb</key>
		<integer>384</integer>
		<key>u1D417.s</key>
		<integer>382</integer>
		<key>u1D417.sb</key>
		<integer>377</integer>
		<key>u1D417.ss</key>
		<integer>383</integer>
		<key>u1D418</key>
		<integer>337</integer>
		<key>u1D418.eb</key>
		<integer>342</integer>
		<key>u1D418.s</key>
		<integer>339</integer>
		<key>u1D418.sb</key>
		<integer>328</integer>
		<key>u1D418.ss</key>
		<integer>341</integer>
		<key>u1D419</key>
		<integer>325</integer>
		<key>u1D419.eb</key>
		<integer>328</integer>
		<key>u1D419.s</key>
		<integer>325</integer>
		<key>u1D419.sb</key>
		<integer>320</integer>
		<key>u1D419.ss</key>
		<integer>327</integer>
		<key>u1D41A</key>
		<integer>244</integer>
		<key>u1D41A.eb</key>
		<integer>252</integer>
		<key>u1D41A.s</key>
		<integer>247</integer>
		<key>u1D41A.sb</key>
		<integer>230</integer>
		<key>u1D41A.ss</key>
		<integer>251</integer>
		<key>u1D41B</key>
		<integer>136</integer>
		<key>u1D41B.eb</key>
		<integer>142</integer>
		<key>u1D41B.s</key>
		<integer>138</integer>
		<key>u1D41B.sb</key>
		<integer>128</integer>
		<key>u1D41B.ss</key>
		<integer>141</integer>
		<key>u1D41C</key>
		<integer>253</integer>
		<key>u1D41C.eb</key>
		<integer>256</integer>
		<key>u1D41C.s</key>
		<integer>254</integer>
		<key>u1D41C.sb</key>
		<integer>250</integer>
		<key>u1D41C.ss</key>
		<integer>255</integer>
		<key>u1D41D</key>
		<integer>417</integer>
		<key>u1D41D.eb</key>
		<integer>425</integer>
		<key>u1D41D.s</key>
		<integer>420</integer>
		<key>u1D41D.sb</key>
		<integer>406</integer>
		<key>u1D41D.ss</key>
		<integer>423</integer>
		<key>u1D41E</key>
		<integer>245</integer>
		<key>u1D41E.eb</key>
		<integer>250</integer>
		<key>u1D41E.s</key>
		<integer>247</integer>
		<key>u1D41E.sb</key>
		<integer>238</integer>
		<key>u1D41E.ss</key>
		<integer>250</integer>
		<key>u1D41F</key>
		<integer>308</integer>
		<key>u1D41F.eb</key>
		<integer>316</integer>
		<key>u1D41F.s</key>
		<integer>311</integer>
		<key>u1D41F.sb</key>
		<integer>295</integer>
		<key>u1D41F.ss</key>
		<integer>315</integer>
		<key>u1D420</key>
		<integer>252</integer>
		<key>u1D420.eb</key>
		<integer>259</integer>
		<key>u1D420.s</key>
		<integer>255</integer>
		<key>u1D420.sb</key>
		<integer>242</integer>
		<key>u1D420.ss</key>
		<integer>258</integer>
		<key>u1D421</key>
		<integer>145</integer>
		<key>u1D421.eb</key>
		<integer>152</integer>
		<key>u1D421.s</key>
		<integer>147</integer>
		<key>u1D421.sb</key>
		<integer>135</integer>
		<key>u1D421.ss</key>
		<integer>151</integer>
		<key>u1D422</key>
		<integer>152</integer>
		<key>u1D422.eb</key>
		<integer>158</integer>
		<key>u1D422.s</key>
		<integer>154</integer>
		<key>u1D422.sb</key>
		<integer>142</integer>
		<key>u1D422.ss</key>
		<integer>157</integer>
		<key>u1D423</key>
		<integer>157</integer>
		<key>u1D423.eb</key>
		<integer>165</integer>
		<key>u1D423.s</key>
		<integer>160</integer>
		<key>u1D423.sb</key>
		<integer>145</integer>
		<key>u1D423.ss</key>
		<integer>163</integer>
		<key>u1D424</key>
		<integer>145</integer>
		<key>u1D424.eb</key>
		<integer>152</integer>
		<key>u1D424.s</key>
		<integer>147</integer>
		<key>u1D424.sb</key>
		<integer>135</integer>
		<key>u1D424.ss</key>
		<integer>151</integer>
		<key>u1D425</key>
		<integer>145</integer>
		<key>u1D425.eb</key>
		<integer>152</integer>
		<key>u1D425.s</key>
		<integer>147</integer>
		<key>u1D425.sb</key>
		<integer>135</integer>
		<key>u1D425.ss</key>
		<integer>151</integer>
		<key>u1D426</key>
		<integer>411</integer>
		<key>u1D426.eb</key>
		<integer>422</integer>
		<key>u1D426.s</key>
		<integer>421</integer>
		<key>u1D426.sb</key>
		<integer>395</integer>
		<key>u1D426.ss</key>
		<integer>420</integer>
		<key>u1D427</key>
		<integer>282</integer>
		<key>u1D427.eb</key>
		<integer>293</integer>
		<key>u1D427.s</key>
		<integer>286</integer>
		<key>u1D427.sb</key>
		<integer>265</integer>
		<key>u1D427.ss</key>
		<integer>290</integer>
		<key>u1D428</key>
		<integer>265</integer>
		<key>u1D428.eb</key>
		<integer>269</integer>
		<key>u1D428.s</key>
		<integer>266</integer>
		<key>u1D428.sb</key>
		<integer>260</integer>
		<key>u1D428.ss</key>
		<integer>268</integer>
		<key>u1D429</key>
		<integer>277</integer>
		<key>u1D429.eb</key>
		<integer>287</integer>
		<key>u1D429.s</key>
		<integer>281</integer>
		<key>u1D429.sb</key>
		<integer>264</integer>
		<key>u1D429.ss</key>
		<integer>285</integer>
		<key>u1D42A</key>
		<integer>311</integer>
		<key>u1D42A.eb</key>
		<integer>310</integer>
		<key>u1D42A.s</key>
		<integer>311</integer>
		<key>u1D42A.sb</key>
		<integer>312</integer>
		<key>u1D42A.ss</key>
		<integer>310</integer>
		<key>u1D42B</key>
		<integer>267</integer>
		<key>u1D42B.eb</key>
		<integer>209</integer>
		<key>u1D42B.s</key>
		<integer>203</integer>
		<key>u1D42B.sb</key>
		<integer>186</integer>
		<key>u1D42B.ss</key>
		<integer>207</integer>
		<key>u1D42C</key>
		<integer>205</integer>
		<key>u1D42C.eb</key>
		<integer>214</integer>
		<key>u1D42C.s</key>
		<integer>209</integer>
		<key>u1D42C.sb</key>
		<integer>193</integer>
		<key>u1D42C.ss</key>
		<integer>213</integer>
		<key>u1D42D</key>
		<integer>199</integer>
		<key>u1D42D.eb</key>
		<integer>211</integer>
		<key>u1D42D.s</key>
		<integer>204</integer>
		<key>u1D42D.sb</key>
		<integer>178</integer>
		<key>u1D42D.ss</key>
		<integer>209</integer>
		<key>u1D42E</key>
		<integer>276</integer>
		<key>u1D42E.eb</key>
		<integer>283</integer>
		<key>u1D42E.s</key>
		<integer>278</integer>
		<key>u1D42E.sb</key>
		<integer>267</integer>
		<key>u1D42E.ss</key>
		<integer>281</integer>
		<key>u1D42F</key>
		<integer>246</integer>
		<key>u1D42F.eb</key>
		<integer>251</integer>
		<key>u1D42F.s</key>
		<integer>248</integer>
		<key>u1D42F.sb</key>
		<integer>238</integer>
		<key>u1D42F.ss</key>
		<integer>250</integer>
		<key>u1D430</key>
		<integer>378</integer>
		<key>u1D430.eb</key>
		<integer>386</integer>
		<key>u1D430.s</key>
		<integer>381</integer>
		<key>u1D430.sb</key>
		<integer>366</integer>
		<key>u1D430.ss</key>
		<integer>385</integer>
		<key>u1D431</key>
		<integer>257</integer>
		<key>u1D431.eb</key>
		<integer>266</integer>
		<key>u1D431.s</key>
		<integer>260</integer>
		<key>u1D431.sb</key>
		<integer>245</integer>
		<key>u1D431.ss</key>
		<integer>264</integer>
		<key>u1D432</key>
		<integer>251</integer>
		<key>u1D432.eb</key>
		<integer>255</integer>
		<key>u1D432.s</key>
		<integer>253</integer>
		<key>u1D432.sb</key>
		<integer>242</integer>
		<key>u1D432.ss</key>
		<integer>254</integer>
		<key>u1D433</key>
		<integer>239</integer>
		<key>u1D433.eb</key>
		<integer>247</integer>
		<key>u1D433.s</key>
		<integer>242</integer>
		<key>u1D433.sb</key>
		<integer>225</integer>
		<key>u1D433.ss</key>
		<integer>246</integer>
		<key>u1D434</key>
		<integer>611</integer>
		<key>u1D434.s</key>
		<integer>620</integer>
		<key>u1D434.ss</key>
		<integer>628</integer>
		<key>u1D435</key>
		<integer>389</integer>
		<key>u1D435.s</key>
		<integer>400</integer>
		<key>u1D435.ss</key>
		<integer>410</integer>
		<key>u1D436</key>
		<integer>460</integer>
		<key>u1D436.s</key>
		<integer>463</integer>
		<key>u1D436.ss</key>
		<integer>468</integer>
		<key>u1D437</key>
		<integer>415</integer>
		<key>u1D437.s</key>
		<integer>425</integer>
		<key>u1D437.ss</key>
		<integer>432</integer>
		<key>u1D438</key>
		<integer>412</integer>
		<key>u1D438.s</key>
		<integer>414</integer>
		<key>u1D438.ss</key>
		<integer>415</integer>
		<key>u1D439</key>
		<integer>401</integer>
		<key>u1D439.s</key>
		<integer>405</integer>
		<key>u1D439.ss</key>
		<integer>408</integer>
		<key>u1D43A</key>
		<integer>467</integer>
		<key>u1D43A.s</key>
		<integer>474</integer>
		<key>u1D43A.ss</key>
		<integer>480</integer>
		<key>u1D43B</key>
		<integer>513</integer>
		<key>u1D43B.s</key>
		<integer>518</integer>
		<key>u1D43B.ss</key>
		<integer>522</integer>
		<key>u1D43C</key>
		<integer>320</integer>
		<key>u1D43C.s</key>
		<integer>323</integer>
		<key>u1D43C.ss</key>
		<integer>325</integer>
		<key>u1D43D</key>
		<integer>385</integer>
		<key>u1D43D.s</key>
		<integer>391</integer>
		<key>u1D43D.ss</key>
		<integer>398</integer>
		<key>u1D43E</key>
		<integer>476</integer>
		<key>u1D43E.s</key>
		<integer>483</integer>
		<key>u1D43E.ss</key>
		<integer>491</integer>
		<key>u1D43F</key>
		<integer>333</integer>
		<key>u1D43F.s</key>
		<integer>335</integer>
		<key>u1D43F.ss</key>
		<integer>334</integer>
		<key>u1D440</key>
		<integer>665</integer>
		<key>u1D440.s</key>
		<integer>665</integer>
		<key>u1D440.ss</key>
		<integer>663</integer>
		<key>u1D441</key>
		<integer>513</integer>
		<key>u1D441.s</key>
		<integer>514</integer>
		<key>u1D441.ss</key>
		<integer>515</integer>
		<key>u1D442</key>
		<integer>443</integer>
		<key>u1D442.s</key>
		<integer>446</integer>
		<key>u1D442.ss</key>
		<integer>448</integer>
		<key>u1D443</key>
		<integer>357</integer>
		<key>u1D443.s</key>
		<integer>396</integer>
		<key>u1D443.ss</key>
		<integer>406</integer>
		<key>u1D444</key>
		<integer>448</integer>
		<key>u1D444.s</key>
		<integer>452</integer>
		<key>u1D444.ss</key>
		<integer>454</integer>
		<key>u1D445</key>
		<integer>352</integer>
		<key>u1D445.s</key>
		<integer>397</integer>
		<key>u1D445.ss</key>
		<integer>408</integer>
		<key>u1D446</key>
		<integer>358</integer>
		<key>u1D446.s</key>
		<integer>364</integer>
		<key>u1D446.ss</key>
		<integer>369</integer>
		<key>u1D447</key>
		<integer>356</integer>
		<key>u1D447.s</key>
		<integer>365</integer>
		<key>u1D447.ss</key>
		<integer>373</integer>
		<key>u1D448</key>
		<integer>469</integer>
		<key>u1D448.s</key>
		<integer>471</integer>
		<key>u1D448.ss</key>
		<integer>472</integer>
		<key>u1D449</key>
		<integer>396</integer>
		<key>u1D449.s</key>
		<integer>401</integer>
		<key>u1D449.ss</key>
		<integer>405</integer>
		<key>u1D44A</key>
		<integer>579</integer>
		<key>u1D44A.s</key>
		<integer>584</integer>
		<key>u1D44A.ss</key>
		<integer>589</integer>
		<key>u1D44B</key>
		<integer>479</integer>
		<key>u1D44B.s</key>
		<integer>479</integer>
		<key>u1D44B.ss</key>
		<integer>479</integer>
		<key>u1D44C</key>
		<integer>346</integer>
		<key>u1D44C.s</key>
		<integer>350</integer>
		<key>u1D44C.ss</key>
		<integer>355</integer>
		<key>u1D44D</key>
		<integer>435</integer>
		<key>u1D44D.s</key>
		<integer>439</integer>
		<key>u1D44D.ss</key>
		<integer>444</integer>
		<key>u1D44E</key>
		<integer>381</integer>
		<key>u1D44E.s</key>
		<integer>385</integer>
		<key>u1D44E.ss</key>
		<integer>387</integer>
		<key>u1D44F</key>
		<integer>242</integer>
		<key>u1D44F.s</key>
		<integer>243</integer>
		<key>u1D44F.ss</key>
		<integer>246</integer>
		<key>u1D450</key>
		<integer>280</integer>
		<key>u1D450.s</key>
		<integer>282</integer>
		<key>u1D450.ss</key>
		<integer>285</integer>
		<key>u1D451</key>
		<integer>502</integer>
		<key>u1D451.s</key>
		<integer>509</integer>
		<key>u1D451.ss</key>
		<integer>517</integer>
		<key>u1D452</key>
		<integer>285</integer>
		<key>u1D452.s</key>
		<integer>287</integer>
		<key>u1D452.ss</key>
		<integer>290</integer>
		<key>u1D453</key>
		<integer>473</integer>
		<key>u1D453.s</key>
		<integer>478</integer>
		<key>u1D453.ss</key>
		<integer>484</integer>
		<key>u1D454</key>
		<integer>283</integer>
		<key>u1D454.s</key>
		<integer>289</integer>
		<key>u1D454.ss</key>
		<integer>294</integer>
		<key>u1D456</key>
		<integer>288</integer>
		<key>u1D456.s</key>
		<integer>291</integer>
		<key>u1D456.ss</key>
		<integer>294</integer>
		<key>u1D457</key>
		<integer>371</integer>
		<key>u1D457.s</key>
		<integer>378</integer>
		<key>u1D457.ss</key>
		<integer>384</integer>
		<key>u1D458</key>
		<integer>255</integer>
		<key>u1D458.s</key>
		<integer>258</integer>
		<key>u1D458.ss</key>
		<integer>260</integer>
		<key>u1D459</key>
		<integer>270</integer>
		<key>u1D459.s</key>
		<integer>267</integer>
		<key>u1D459.ss</key>
		<integer>270</integer>
		<key>u1D45A</key>
		<integer>401</integer>
		<key>u1D45A.s</key>
		<integer>408</integer>
		<key>u1D45A.ss</key>
		<integer>415</integer>
		<key>u1D45B</key>
		<integer>285</integer>
		<key>u1D45B.s</key>
		<integer>292</integer>
		<key>u1D45B.ss</key>
		<integer>298</integer>
		<key>u1D45C</key>
		<integer>269</integer>
		<key>u1D45C.s</key>
		<integer>278</integer>
		<key>u1D45C.ss</key>
		<integer>283</integer>
		<key>u1D45D</key>
		<integer>295</integer>
		<key>u1D45D.s</key>
		<integer>299</integer>
		<key>u1D45D.ss</key>
		<integer>303</integer>
		<key>u1D45E</key>
		<integer>347</integer>
		<key>u1D45E.s</key>
		<integer>349</integer>
		<key>u1D45E.ss</key>
		<integer>351</integer>
		<key>u1D45F</key>
		<integer>262</integer>
		<key>u1D45F.s</key>
		<integer>270</integer>
		<key>u1D45F.ss</key>
		<integer>278</integer>
		<key>u1D460</key>
		<integer>254</integer>
		<key>u1D460.s</key>
		<integer>257</integer>
		<key>u1D460.ss</key>
		<integer>260</integer>
		<key>u1D461</key>
		<integer>246</integer>
		<key>u1D461.s</key>
		<integer>253</integer>
		<key>u1D461.ss</key>
		<integer>260</integer>
		<key>u1D462</key>
		<integer>330</integer>
		<key>u1D462.s</key>
		<integer>334</integer>
		<key>u1D462.ss</key>
		<integer>337</integer>
		<key>u1D463</key>
		<integer>262</integer>
		<key>u1D463.s</key>
		<integer>268</integer>
		<key>u1D463.ss</key>
		<integer>274</integer>
		<key>u1D464</key>
		<integer>391</integer>
		<key>u1D464.s</key>
		<integer>398</integer>
		<key>u1D464.ss</key>
		<integer>404</integer>
		<key>u1D465</key>
		<integer>315</integer>
		<key>u1D465.s</key>
		<integer>320</integer>
		<key>u1D465.ss</key>
		<integer>325</integer>
		<key>u1D466</key>
		<integer>275</integer>
		<key>u1D466.s</key>
		<integer>283</integer>
		<key>u1D466.ss</key>
		<integer>291</integer>
		<key>u1D467</key>
		<integer>273</integer>
		<key>u1D467.s</key>
		<integer>264</integer>
		<key>u1D467.ss</key>
		<integer>253</integer>
		<key>u1D468</key>
		<integer>674</integer>
		<key>u1D468.eb</key>
		<integer>669</integer>
		<key>u1D468.s</key>
		<integer>678</integer>
		<key>u1D468.sb</key>
		<integer>638</integer>
		<key>u1D468.ss</key>
		<integer>679</integer>
		<key>u1D469</key>
		<integer>432</integer>
		<key>u1D469.eb</key>
		<integer>448</integer>
		<key>u1D469.s</key>
		<integer>437</integer>
		<key>u1D469.sb</key>
		<integer>408</integer>
		<key>u1D469.ss</key>
		<integer>439</integer>
		<key>u1D46A</key>
		<integer>498</integer>
		<key>u1D46A.eb</key>
		<integer>507</integer>
		<key>u1D46A.s</key>
		<integer>501</integer>
		<key>u1D46A.sb</key>
		<integer>485</integer>
		<key>u1D46A.ss</key>
		<integer>502</integer>
		<key>u1D46B</key>
		<integer>437</integer>
		<key>u1D46B.eb</key>
		<integer>451</integer>
		<key>u1D46B.s</key>
		<integer>441</integer>
		<key>u1D46B.sb</key>
		<integer>415</integer>
		<key>u1D46B.ss</key>
		<integer>442</integer>
		<key>u1D46C</key>
		<integer>425</integer>
		<key>u1D46C.eb</key>
		<integer>429</integer>
		<key>u1D46C.s</key>
		<integer>426</integer>
		<key>u1D46C.sb</key>
		<integer>421</integer>
		<key>u1D46C.ss</key>
		<integer>426</integer>
		<key>u1D46D</key>
		<integer>429</integer>
		<key>u1D46D.eb</key>
		<integer>435</integer>
		<key>u1D46D.s</key>
		<integer>431</integer>
		<key>u1D46D.sb</key>
		<integer>419</integer>
		<key>u1D46D.ss</key>
		<integer>432</integer>
		<key>u1D46E</key>
		<integer>516</integer>
		<key>u1D46E.eb</key>
		<integer>527</integer>
		<key>u1D46E.s</key>
		<integer>520</integer>
		<key>u1D46E.sb</key>
		<integer>499</integer>
		<key>u1D46E.ss</key>
		<integer>520</integer>
		<key>u1D46F</key>
		<integer>548</integer>
		<key>u1D46F.eb</key>
		<integer>556</integer>
		<key>u1D46F.s</key>
		<integer>551</integer>
		<key>u1D46F.sb</key>
		<integer>537</integer>
		<key>u1D46F.ss</key>
		<integer>551</integer>
		<key>u1D470</key>
		<integer>340</integer>
		<key>u1D470.eb</key>
		<integer>344</integer>
		<key>u1D470.s</key>
		<integer>342</integer>
		<key>u1D470.sb</key>
		<integer>333</integer>
		<key>u1D470.ss</key>
		<integer>342</integer>
		<key>u1D471</key>
		<integer>434</integer>
		<key>u1D471.eb</key>
		<integer>503</integer>
		<key>u1D471.s</key>
		<integer>437</integer>
		<key>u1D471.sb</key>
		<integer>417</integer>
		<key>u1D471.ss</key>
		<integer>437</integer>
		<key>u1D472</key>
		<integer>530</integer>
		<key>u1D472.eb</key>
		<integer>540</integer>
		<key>u1D472.s</key>
		<integer>534</integer>
		<key>u1D472.sb</key>
		<integer>512</integer>
		<key>u1D472.ss</key>
		<integer>534</integer>
		<key>u1D473</key>
		<integer>339</integer>
		<key>u1D473.eb</key>
		<integer>341</integer>
		<key>u1D473.s</key>
		<integer>339</integer>
		<key>u1D473.sb</key>
		<integer>337</integer>
		<key>u1D473.ss</key>
		<integer>340</integer>
		<key>u1D474</key>
		<integer>657</integer>
		<key>u1D474.eb</key>
		<integer>655</integer>
		<key>u1D474.s</key>
		<integer>657</integer>
		<key>u1D474.sb</key>
		<integer>659</integer>
		<key>u1D474.ss</key>
		<integer>657</integer>
		<key>u1D475</key>
		<integer>518</integer>
		<key>u1D475.eb</key>
		<integer>519</integer>
		<key>u1D475.s</key>
		<integer>518</integer>
		<key>u1D475.sb</key>
		<integer>516</integer>
		<key>u1D475.ss</key>
		<integer>518</integer>
		<key>u1D476</key>
		<integer>464</integer>
		<key>u1D476.eb</key>
		<integer>469</integer>
		<key>u1D476.s</key>
		<integer>465</integer>
		<key>u1D476.sb</key>
		<integer>457</integer>
		<key>u1D476.ss</key>
		<integer>465</integer>
		<key>u1D477</key>
		<integer>426</integer>
		<key>u1D477.eb</key>
		<integer>441</integer>
		<key>u1D477.s</key>
		<integer>431</integer>
		<key>u1D477.sb</key>
		<integer>377</integer>
		<key>u1D477.ss</key>
		<integer>432</integer>
		<key>u1D478</key>
		<integer>468</integer>
		<key>u1D478.eb</key>
		<integer>484</integer>
		<key>u1D478.s</key>
		<integer>480</integer>
		<key>u1D478.sb</key>
		<integer>468</integer>
		<key>u1D478.ss</key>
		<integer>480</integer>
		<key>u1D479</key>
		<integer>427</integer>
		<key>u1D479.eb</key>
		<integer>417</integer>
		<key>u1D479.s</key>
		<integer>432</integer>
		<key>u1D479.sb</key>
		<integer>403</integer>
		<key>u1D479.ss</key>
		<integer>407</integer>
		<key>u1D47A</key>
		<integer>396</integer>
		<key>u1D47A.eb</key>
		<integer>402</integer>
		<key>u1D47A.s</key>
		<integer>397</integer>
		<key>u1D47A.sb</key>
		<integer>386</integer>
		<key>u1D47A.ss</key>
		<integer>398</integer>
		<key>u1D47B</key>
		<integer>419</integer>
		<key>u1D47B.eb</key>
		<integer>432</integer>
		<key>u1D47B.s</key>
		<integer>423</integer>
		<key>u1D47B.sb</key>
		<integer>397</integer>
		<key>u1D47B.ss</key>
		<integer>424</integer>
		<key>u1D47C</key>
		<integer>480</integer>
		<key>u1D47C.eb</key>
		<integer>482</integer>
		<key>u1D47C.s</key>
		<integer>480</integer>
		<key>u1D47C.sb</key>
		<integer>476</integer>
		<key>u1D47C.ss</key>
		<integer>481</integer>
		<key>u1D47D</key>
		<integer>429</integer>
		<key>u1D47D.eb</key>
		<integer>437</integer>
		<key>u1D47D.s</key>
		<integer>432</integer>
		<key>u1D47D.sb</key>
		<integer>418</integer>
		<key>u1D47D.ss</key>
		<integer>431</integer>
		<key>u1D47E</key>
		<integer>613</integer>
		<key>u1D47E.eb</key>
		<integer>621</integer>
		<key>u1D47E.s</key>
		<integer>616</integer>
		<key>u1D47E.sb</key>
		<integer>602</integer>
		<key>u1D47E.ss</key>
		<integer>616</integer>
		<key>u1D47F</key>
		<integer>482</integer>
		<key>u1D47F.eb</key>
		<integer>483</integer>
		<key>u1D47F.s</key>
		<integer>481</integer>
		<key>u1D47F.sb</key>
		<integer>481</integer>
		<key>u1D47F.ss</key>
		<integer>482</integer>
		<key>u1D480</key>
		<integer>381</integer>
		<key>u1D480.eb</key>
		<integer>389</integer>
		<key>u1D480.s</key>
		<integer>383</integer>
		<key>u1D480.sb</key>
		<integer>369</integer>
		<key>u1D480.ss</key>
		<integer>383</integer>
		<key>u1D481</key>
		<integer>467</integer>
		<key>u1D481.eb</key>
		<integer>475</integer>
		<key>u1D481.s</key>
		<integer>470</integer>
		<key>u1D481.sb</key>
		<integer>457</integer>
		<key>u1D481.ss</key>
		<integer>470</integer>
		<key>u1D482</key>
		<integer>401</integer>
		<key>u1D482.eb</key>
		<integer>405</integer>
		<key>u1D482.s</key>
		<integer>402</integer>
		<key>u1D482.sb</key>
		<integer>394</integer>
		<key>u1D482.ss</key>
		<integer>403</integer>
		<key>u1D483</key>
		<integer>258</integer>
		<key>u1D483.eb</key>
		<integer>262</integer>
		<key>u1D483.s</key>
		<integer>259</integer>
		<key>u1D483.sb</key>
		<integer>253</integer>
		<key>u1D483.ss</key>
		<integer>259</integer>
		<key>u1D484</key>
		<integer>296</integer>
		<key>u1D484.eb</key>
		<integer>299</integer>
		<key>u1D484.s</key>
		<integer>297</integer>
		<key>u1D484.sb</key>
		<integer>291</integer>
		<key>u1D484.ss</key>
		<integer>297</integer>
		<key>u1D485</key>
		<integer>556</integer>
		<key>u1D485.eb</key>
		<integer>568</integer>
		<key>u1D485.s</key>
		<integer>561</integer>
		<key>u1D485.sb</key>
		<integer>539</integer>
		<key>u1D485.ss</key>
		<integer>561</integer>
		<key>u1D486</key>
		<integer>304</integer>
		<key>u1D486.eb</key>
		<integer>308</integer>
		<key>u1D486.s</key>
		<integer>305</integer>
		<key>u1D486.sb</key>
		<integer>298</integer>
		<key>u1D486.ss</key>
		<integer>305</integer>
		<key>u1D487</key>
		<integer>514</integer>
		<key>u1D487.eb</key>
		<integer>522</integer>
		<key>u1D487.s</key>
		<integer>516</integer>
		<key>u1D487.sb</key>
		<integer>500</integer>
		<key>u1D487.ss</key>
		<integer>517</integer>
		<key>u1D488</key>
		<integer>325</integer>
		<key>u1D488.eb</key>
		<integer>335</integer>
		<key>u1D488.s</key>
		<integer>328</integer>
		<key>u1D488.sb</key>
		<integer>311</integer>
		<key>u1D488.ss</key>
		<integer>329</integer>
		<key>u1D489</key>
		<integer>267</integer>
		<key>u1D489.eb</key>
		<integer>273</integer>
		<key>u1D489.s</key>
		<integer>270</integer>
		<key>u1D489.sb</key>
		<integer>258</integer>
		<key>u1D489.ss</key>
		<integer>270</integer>
		<key>u1D489.var</key>
		<integer>267</integer>
		<key>u1D489.var.eb</key>
		<integer>274</integer>
		<key>u1D489.var.s</key>
		<integer>270</integer>
		<key>u1D489.var.sb</key>
		<integer>259</integer>
		<key>u1D489.var.ss</key>
		<integer>270</integer>
		<key>u1D48A</key>
		<integer>311</integer>
		<key>u1D48A.eb</key>
		<integer>315</integer>
		<key>u1D48A.s</key>
		<integer>313</integer>
		<key>u1D48A.sb</key>
		<integer>304</integer>
		<key>u1D48A.ss</key>
		<integer>313</integer>
		<key>u1D48B</key>
		<integer>419</integer>
		<key>u1D48B.eb</key>
		<integer>429</integer>
		<key>u1D48B.s</key>
		<integer>423</integer>
		<key>u1D48B.sb</key>
		<integer>404</integer>
		<key>u1D48B.ss</key>
		<integer>423</integer>
		<key>u1D48C</key>
		<integer>278</integer>
		<key>u1D48C.eb</key>
		<integer>282</integer>
		<key>u1D48C.s</key>
		<integer>279</integer>
		<key>u1D48C.sb</key>
		<integer>271</integer>
		<key>u1D48C.ss</key>
		<integer>279</integer>
		<key>u1D48D</key>
		<integer>281</integer>
		<key>u1D48D.eb</key>
		<integer>285</integer>
		<key>u1D48D.s</key>
		<integer>283</integer>
		<key>u1D48D.sb</key>
		<integer>277</integer>
		<key>u1D48D.ss</key>
		<integer>284</integer>
		<key>u1D48E</key>
		<integer>455</integer>
		<key>u1D48E.eb</key>
		<integer>467</integer>
		<key>u1D48E.s</key>
		<integer>459</integer>
		<key>u1D48E.sb</key>
		<integer>437</integer>
		<key>u1D48E.ss</key>
		<integer>460</integer>
		<key>u1D48F</key>
		<integer>340</integer>
		<key>u1D48F.eb</key>
		<integer>354</integer>
		<key>u1D48F.s</key>
		<integer>345</integer>
		<key>u1D48F.sb</key>
		<integer>318</integer>
		<key>u1D48F.ss</key>
		<integer>347</integer>
		<key>u1D490</key>
		<integer>318</integer>
		<key>u1D490.eb</key>
		<integer>320</integer>
		<key>u1D490.s</key>
		<integer>314</integer>
		<key>u1D490.sb</key>
		<integer>299</integer>
		<key>u1D490.ss</key>
		<integer>315</integer>
		<key>u1D491</key>
		<integer>323</integer>
		<key>u1D491.eb</key>
		<integer>329</integer>
		<key>u1D491.s</key>
		<integer>325</integer>
		<key>u1D491.sb</key>
		<integer>314</integer>
		<key>u1D491.ss</key>
		<integer>327</integer>
		<key>u1D492</key>
		<integer>361</integer>
		<key>u1D492.eb</key>
		<integer>366</integer>
		<key>u1D492.s</key>
		<integer>364</integer>
		<key>u1D492.sb</key>
		<integer>357</integer>
		<key>u1D492.ss</key>
		<integer>364</integer>
		<key>u1D493</key>
		<integer>322</integer>
		<key>u1D493.eb</key>
		<integer>335</integer>
		<key>u1D493.s</key>
		<integer>325</integer>
		<key>u1D493.sb</key>
		<integer>301</integer>
		<key>u1D493.ss</key>
		<integer>326</integer>
		<key>u1D494</key>
		<integer>279</integer>
		<key>u1D494.eb</key>
		<integer>284</integer>
		<key>u1D494.s</key>
		<integer>281</integer>
		<key>u1D494.sb</key>
		<integer>271</integer>
		<key>u1D494.ss</key>
		<integer>281</integer>
		<key>u1D495</key>
		<integer>296</integer>
		<key>u1D495.eb</key>
		<integer>306</integer>
		<key>u1D495.s</key>
		<integer>300</integer>
		<key>u1D495.sb</key>
		<integer>280</integer>
		<key>u1D495.ss</key>
		<integer>300</integer>
		<key>u1D496</key>
		<integer>365</integer>
		<key>u1D496.eb</key>
		<integer>371</integer>
		<key>u1D496.s</key>
		<integer>367</integer>
		<key>u1D496.sb</key>
		<integer>349</integer>
		<key>u1D496.ss</key>
		<integer>368</integer>
		<key>u1D497</key>
		<integer>334</integer>
		<key>u1D497.eb</key>
		<integer>348</integer>
		<key>u1D497.s</key>
		<integer>338</integer>
		<key>u1D497.sb</key>
		<integer>315</integer>
		<key>u1D497.ss</key>
		<integer>339</integer>
		<key>u1D498</key>
		<integer>443</integer>
		<key>u1D498.eb</key>
		<integer>454</integer>
		<key>u1D498.s</key>
		<integer>446</integer>
		<key>u1D498.sb</key>
		<integer>426</integer>
		<key>u1D498.ss</key>
		<integer>447</integer>
		<key>u1D499</key>
		<integer>356</integer>
		<key>u1D499.eb</key>
		<integer>365</integer>
		<key>u1D499.s</key>
		<integer>360</integer>
		<key>u1D499.sb</key>
		<integer>343</integer>
		<key>u1D499.ss</key>
		<integer>360</integer>
		<key>u1D49A</key>
		<integer>333</integer>
		<key>u1D49A.eb</key>
		<integer>346</integer>
		<key>u1D49A.s</key>
		<integer>337</integer>
		<key>u1D49A.sb</key>
		<integer>314</integer>
		<key>u1D49A.ss</key>
		<integer>338</integer>
		<key>u1D49B</key>
		<integer>321</integer>
		<key>u1D49B.eb</key>
		<integer>330</integer>
		<key>u1D49B.s</key>
		<integer>324</integer>
		<key>u1D49B.sb</key>
		<integer>305</integer>
		<key>u1D49B.ss</key>
		<integer>324</integer>
		<key>u1D49C</key>
		<integer>677</integer>
		<key>u1D49C.c</key>
		<integer>584</integer>
		<key>u1D49C.gm</key>
		<integer>761</integer>
		<key>u1D49E</key>
		<integer>655</integer>
		<key>u1D49E.c</key>
		<integer>452</integer>
		<key>u1D49E.gm</key>
		<integer>446</integer>
		<key>u1D49F</key>
		<integer>687</integer>
		<key>u1D49F.c</key>
		<integer>401</integer>
		<key>u1D49F.gm</key>
		<integer>405</integer>
		<key>u1D4A2</key>
		<integer>569</integer>
		<key>u1D4A2.c</key>
		<integer>448</integer>
		<key>u1D4A2.gm</key>
		<integer>443</integer>
		<key>u1D4A5</key>
		<integer>539</integer>
		<key>u1D4A5.c</key>
		<integer>670</integer>
		<key>u1D4A5.gm</key>
		<integer>648</integer>
		<key>u1D4A6</key>
		<integer>668</integer>
		<key>u1D4A6.c</key>
		<integer>435</integer>
		<key>u1D4A6.gm</key>
		<integer>572</integer>
		<key>u1D4A9</key>
		<integer>633</integer>
		<key>u1D4A9.c</key>
		<integer>593</integer>
		<key>u1D4A9.gm</key>
		<integer>695</integer>
		<key>u1D4AA</key>
		<integer>437</integer>
		<key>u1D4AA.c</key>
		<integer>482</integer>
		<key>u1D4AA.gm</key>
		<integer>451</integer>
		<key>u1D4AB</key>
		<integer>683</integer>
		<key>u1D4AB.c</key>
		<integer>419</integer>
		<key>u1D4AB.gm</key>
		<integer>446</integer>
		<key>u1D4AC</key>
		<integer>446</integer>
		<key>u1D4AC.c</key>
		<integer>457</integer>
		<key>u1D4AC.gm</key>
		<integer>447</integer>
		<key>u1D4AE</key>
		<integer>443</integer>
		<key>u1D4AE.c</key>
		<integer>470</integer>
		<key>u1D4AE.gm</key>
		<integer>422</integer>
		<key>u1D4AF</key>
		<integer>600</integer>
		<key>u1D4AF.c</key>
		<integer>426</integer>
		<key>u1D4AF.gm</key>
		<integer>423</integer>
		<key>u1D4B0</key>
		<integer>519</integer>
		<key>u1D4B0.c</key>
		<integer>491</integer>
		<key>u1D4B0.gm</key>
		<integer>601</integer>
		<key>u1D4B1</key>
		<integer>690</integer>
		<key>u1D4B1.c</key>
		<integer>298</integer>
		<key>u1D4B1.gm</key>
		<integer>475</integer>
		<key>u1D4B2</key>
		<integer>756</integer>
		<key>u1D4B2.c</key>
		<integer>512</integer>
		<key>u1D4B2.gm</key>
		<integer>613</integer>
		<key>u1D4B3</key>
		<integer>502</integer>
		<key>u1D4B3.c</key>
		<integer>510</integer>
		<key>u1D4B3.gm</key>
		<integer>537</integer>
		<key>u1D4B4</key>
		<integer>525</integer>
		<key>u1D4B4.c</key>
		<integer>387</integer>
		<key>u1D4B4.gm</key>
		<integer>517</integer>
		<key>u1D4B5</key>
		<integer>472</integer>
		<key>u1D4B5.c</key>
		<integer>458</integer>
		<key>u1D4B5.gm</key>
		<integer>502</integer>
		<key>u1D4B6</key>
		<integer>438</integer>
		<key>u1D4B6.gm</key>
		<integer>349</integer>
		<key>u1D4B7</key>
		<integer>416</integer>
		<key>u1D4B7.gm</key>
		<integer>267</integer>
		<key>u1D4B8</key>
		<integer>417</integer>
		<key>u1D4B8.gm</key>
		<integer>259</integer>
		<key>u1D4B9</key>
		<integer>692</integer>
		<key>u1D4B9.gm</key>
		<integer>472</integer>
		<key>u1D4BB</key>
		<integer>422</integer>
		<key>u1D4BB.gm</key>
		<integer>386</integer>
		<key>u1D4BD</key>
		<integer>389</integer>
		<key>u1D4BD.gm</key>
		<integer>261</integer>
		<key>u1D4BE</key>
		<integer>305</integer>
		<key>u1D4BE.gm</key>
		<integer>270</integer>
		<key>u1D4BF</key>
		<integer>448</integer>
		<key>u1D4BF.gm</key>
		<integer>330</integer>
		<key>u1D4C0</key>
		<integer>388</integer>
		<key>u1D4C0.gm</key>
		<integer>271</integer>
		<key>u1D4C1</key>
		<integer>434</integer>
		<key>u1D4C1.gm</key>
		<integer>323</integer>
		<key>u1D4C2</key>
		<integer>505</integer>
		<key>u1D4C2.gm</key>
		<integer>409</integer>
		<key>u1D4C3</key>
		<integer>369</integer>
		<key>u1D4C3.gm</key>
		<integer>266</integer>
		<key>u1D4C5</key>
		<integer>398</integer>
		<key>u1D4C5.gm</key>
		<integer>297</integer>
		<key>u1D4C6</key>
		<integer>444</integer>
		<key>u1D4C6.gm</key>
		<integer>309</integer>
		<key>u1D4C7</key>
		<integer>370</integer>
		<key>u1D4C7.gm</key>
		<integer>271</integer>
		<key>u1D4C8</key>
		<integer>242</integer>
		<key>u1D4C8.gm</key>
		<integer>212</integer>
		<key>u1D4C9</key>
		<integer>337</integer>
		<key>u1D4C9.gm</key>
		<integer>230</integer>
		<key>u1D4CA</key>
		<integer>384</integer>
		<key>u1D4CA.gm</key>
		<integer>329</integer>
		<key>u1D4CB</key>
		<integer>337</integer>
		<key>u1D4CB.gm</key>
		<integer>293</integer>
		<key>u1D4CC</key>
		<integer>500</integer>
		<key>u1D4CC.gm</key>
		<integer>404</integer>
		<key>u1D4CD</key>
		<integer>369</integer>
		<key>u1D4CD.gm</key>
		<integer>318</integer>
		<key>u1D4CE</key>
		<integer>391</integer>
		<key>u1D4CE.gm</key>
		<integer>316</integer>
		<key>u1D4CF</key>
		<integer>305</integer>
		<key>u1D4CF.gm</key>
		<integer>252</integer>
		<key>u1D4D0</key>
		<integer>772</integer>
		<key>u1D4D0.c</key>
		<integer>636</integer>
		<key>u1D4D1</key>
		<integer>737</integer>
		<key>u1D4D1.c</key>
		<integer>435</integer>
		<key>u1D4D2</key>
		<integer>691</integer>
		<key>u1D4D2.c</key>
		<integer>469</integer>
		<key>u1D4D3</key>
		<integer>699</integer>
		<key>u1D4D3.c</key>
		<integer>450</integer>
		<key>u1D4D4</key>
		<integer>598</integer>
		<key>u1D4D4.c</key>
		<integer>472</integer>
		<key>u1D4D5</key>
		<integer>722</integer>
		<key>u1D4D5.c</key>
		<integer>601</integer>
		<key>u1D4D6</key>
		<integer>605</integer>
		<key>u1D4D6.c</key>
		<integer>515</integer>
		<key>u1D4D7</key>
		<integer>690</integer>
		<key>u1D4D7.c</key>
		<integer>595</integer>
		<key>u1D4D8</key>
		<integer>728</integer>
		<key>u1D4D8.c</key>
		<integer>433</integer>
		<key>u1D4D9</key>
		<integer>586</integer>
		<key>u1D4D9.c</key>
		<integer>724</integer>
		<key>u1D4DA</key>
		<integer>750</integer>
		<key>u1D4DA.c</key>
		<integer>466</integer>
		<key>u1D4DB</key>
		<integer>800</integer>
		<key>u1D4DB.c</key>
		<integer>516</integer>
		<key>u1D4DC</key>
		<integer>918</integer>
		<key>u1D4DC.c</key>
		<integer>749</integer>
		<key>u1D4DD</key>
		<integer>752</integer>
		<key>u1D4DD.c</key>
		<integer>637</integer>
		<key>u1D4DE</key>
		<integer>494</integer>
		<key>u1D4DE.c</key>
		<integer>514</integer>
		<key>u1D4DF</key>
		<integer>717</integer>
		<key>u1D4DF.c</key>
		<integer>467</integer>
		<key>u1D4E0</key>
		<integer>508</integer>
		<key>u1D4E0.c</key>
		<integer>479</integer>
		<key>u1D4E1</key>
		<integer>706</integer>
		<key>u1D4E1.c</key>
		<integer>470</integer>
		<key>u1D4E2</key>
		<integer>474</integer>
		<key>u1D4E2.c</key>
		<integer>503</integer>
		<key>u1D4E3</key>
		<integer>655</integer>
		<key>u1D4E3.c</key>
		<integer>452</integer>
		<key>u1D4E4</key>
		<integer>583</integer>
		<key>u1D4E4.c</key>
		<integer>526</integer>
		<key>u1D4E5</key>
		<integer>762</integer>
		<key>u1D4E5.c</key>
		<integer>340</integer>
		<key>u1D4E6</key>
		<integer>908</integer>
		<key>u1D4E6.c</key>
		<integer>569</integer>
		<key>u1D4E7</key>
		<integer>563</integer>
		<key>u1D4E7.c</key>
		<integer>544</integer>
		<key>u1D4E8</key>
		<integer>597</integer>
		<key>u1D4E8.c</key>
		<integer>426</integer>
		<key>u1D4E9</key>
		<integer>525</integer>
		<key>u1D4E9.c</key>
		<integer>491</integer>
		<key>u1D4EA</key>
		<integer>529</integer>
		<key>u1D4EB</key>
		<integer>433</integer>
		<key>u1D4EC</key>
		<integer>505</integer>
		<key>u1D4ED</key>
		<integer>763</integer>
		<key>u1D4EE</key>
		<integer>479</integer>
		<key>u1D4EF</key>
		<integer>472</integer>
		<key>u1D4F0</key>
		<integer>528</integer>
		<key>u1D4F1</key>
		<integer>414</integer>
		<key>u1D4F2</key>
		<integer>403</integer>
		<key>u1D4F3</key>
		<integer>510</integer>
		<key>u1D4F4</key>
		<integer>415</integer>
		<key>u1D4F5</key>
		<integer>466</integer>
		<key>u1D4F6</key>
		<integer>572</integer>
		<key>u1D4F7</key>
		<integer>432</integer>
		<key>u1D4F8</key>
		<integer>521</integer>
		<key>u1D4F9</key>
		<integer>452</integer>
		<key>u1D4FA</key>
		<integer>528</integer>
		<key>u1D4FB</key>
		<integer>430</integer>
		<key>u1D4FC</key>
		<integer>245</integer>
		<key>u1D4FD</key>
		<integer>397</integer>
		<key>u1D4FE</key>
		<integer>460</integer>
		<key>u1D4FF</key>
		<integer>409</integer>
		<key>u1D500</key>
		<integer>577</integer>
		<key>u1D501</key>
		<integer>407</integer>
		<key>u1D502</key>
		<integer>468</integer>
		<key>u1D503</key>
		<integer>338</integer>
		<key>u1D504</key>
		<integer>436</integer>
		<key>u1D505</key>
		<integer>378</integer>
		<key>u1D507</key>
		<integer>322</integer>
		<key>u1D508</key>
		<integer>330</integer>
		<key>u1D509</key>
		<integer>417</integer>
		<key>u1D50A</key>
		<integer>370</integer>
		<key>u1D50D</key>
		<integer>490</integer>
		<key>u1D50E</key>
		<integer>354</integer>
		<key>u1D50F</key>
		<integer>316</integer>
		<key>u1D510</key>
		<integer>561</integer>
		<key>u1D511</key>
		<integer>399</integer>
		<key>u1D512</key>
		<integer>364</integer>
		<key>u1D513</key>
		<integer>392</integer>
		<key>u1D514</key>
		<integer>364</integer>
		<key>u1D516</key>
		<integer>427</integer>
		<key>u1D517</key>
		<integer>419</integer>
		<key>u1D518</key>
		<integer>347</integer>
		<key>u1D519</key>
		<integer>393</integer>
		<key>u1D51A</key>
		<integer>587</integer>
		<key>u1D51B</key>
		<integer>330</integer>
		<key>u1D51C</key>
		<integer>391</integer>
		<key>u1D51E</key>
		<integer>230</integer>
		<key>u1D51F</key>
		<integer>119</integer>
		<key>u1D520</key>
		<integer>195</integer>
		<key>u1D521</key>
		<integer>203</integer>
		<key>u1D522</key>
		<integer>216</integer>
		<key>u1D523</key>
		<integer>161</integer>
		<key>u1D524</key>
		<integer>237</integer>
		<key>u1D525</key>
		<integer>126</integer>
		<key>u1D526</key>
		<integer>136</integer>
		<key>u1D527</key>
		<integer>140</integer>
		<key>u1D528</key>
		<integer>150</integer>
		<key>u1D529</key>
		<integer>121</integer>
		<key>u1D52A</key>
		<integer>379</integer>
		<key>u1D52B</key>
		<integer>255</integer>
		<key>u1D52C</key>
		<integer>280</integer>
		<key>u1D52D</key>
		<integer>240</integer>
		<key>u1D52E</key>
		<integer>228</integer>
		<key>u1D52F</key>
		<integer>201</integer>
		<key>u1D530</key>
		<integer>187</integer>
		<key>u1D531</key>
		<integer>168</integer>
		<key>u1D532</key>
		<integer>271</integer>
		<key>u1D533</key>
		<integer>247</integer>
		<key>u1D534</key>
		<integer>382</integer>
		<key>u1D535</key>
		<integer>209</integer>
		<key>u1D536</key>
		<integer>259</integer>
		<key>u1D537</key>
		<integer>188</integer>
		<key>u1D538</key>
		<integer>362</integer>
		<key>u1D538.gm</key>
		<integer>337</integer>
		<key>u1D539</key>
		<integer>294</integer>
		<key>u1D539.gm</key>
		<integer>301</integer>
		<key>u1D53B</key>
		<integer>359</integer>
		<key>u1D53B.gm</key>
		<integer>299</integer>
		<key>u1D53C</key>
		<integer>263</integer>
		<key>u1D53C.gm</key>
		<integer>323</integer>
		<key>u1D53D</key>
		<integer>267</integer>
		<key>u1D53D.gm</key>
		<integer>323</integer>
		<key>u1D53E</key>
		<integer>424</integer>
		<key>u1D53E.gm</key>
		<integer>375</integer>
		<key>u1D540</key>
		<integer>195</integer>
		<key>u1D540.gm</key>
		<integer>155</integer>
		<key>u1D541</key>
		<integer>214</integer>
		<key>u1D541.gm</key>
		<integer>388</integer>
		<key>u1D542</key>
		<integer>393</integer>
		<key>u1D542.gm</key>
		<integer>351</integer>
		<key>u1D543</key>
		<integer>193</integer>
		<key>u1D543.gm</key>
		<integer>155</integer>
		<key>u1D544</key>
		<integer>453</integer>
		<key>u1D544.gm</key>
		<integer>429</integer>
		<key>u1D546</key>
		<integer>403</integer>
		<key>u1D546.gm</key>
		<integer>382</integer>
		<key>u1D54A</key>
		<integer>266</integer>
		<key>u1D54A.gm</key>
		<integer>284</integer>
		<key>u1D54B</key>
		<integer>318</integer>
		<key>u1D54B.gm</key>
		<integer>307</integer>
		<key>u1D54C</key>
		<integer>384</integer>
		<key>u1D54C.gm</key>
		<integer>345</integer>
		<key>u1D54D</key>
		<integer>356</integer>
		<key>u1D54D.gm</key>
		<integer>330</integer>
		<key>u1D54E</key>
		<integer>487</integer>
		<key>u1D54E.gm</key>
		<integer>498</integer>
		<key>u1D54F</key>
		<integer>403</integer>
		<key>u1D54F.gm</key>
		<integer>344</integer>
		<key>u1D550</key>
		<integer>334</integer>
		<key>u1D550.gm</key>
		<integer>332</integer>
		<key>u1D552</key>
		<integer>229</integer>
		<key>u1D552.gm</key>
		<integer>284</integer>
		<key>u1D553</key>
		<integer>133</integer>
		<key>u1D553.gm</key>
		<integer>129</integer>
		<key>u1D554</key>
		<integer>255</integer>
		<key>u1D554.gm</key>
		<integer>260</integer>
		<key>u1D555</key>
		<integer>408</integer>
		<key>u1D555.gm</key>
		<integer>436</integer>
		<key>u1D556</key>
		<integer>240</integer>
		<key>u1D556.gm</key>
		<integer>258</integer>
		<key>u1D557</key>
		<integer>320</integer>
		<key>u1D557.gm</key>
		<integer>279</integer>
		<key>u1D558</key>
		<integer>233</integer>
		<key>u1D558.gm</key>
		<integer>315</integer>
		<key>u1D559</key>
		<integer>140</integer>
		<key>u1D559.gm</key>
		<integer>127</integer>
		<key>u1D55A</key>
		<integer>147</integer>
		<key>u1D55A.gm</key>
		<integer>128</integer>
		<key>u1D55B</key>
		<integer>145</integer>
		<key>u1D55B.gm</key>
		<integer>159</integer>
		<key>u1D55C</key>
		<integer>140</integer>
		<key>u1D55C.gm</key>
		<integer>127</integer>
		<key>u1D55D</key>
		<integer>140</integer>
		<key>u1D55D.gm</key>
		<integer>128</integer>
		<key>u1D55E</key>
		<integer>414</integer>
		<key>u1D55E.gm</key>
		<integer>395</integer>
		<key>u1D55F</key>
		<integer>288</integer>
		<key>u1D55F.gm</key>
		<integer>252</integer>
		<key>u1D560</key>
		<integer>272</integer>
		<key>u1D560.gm</key>
		<integer>266</integer>
		<key>u1D561</key>
		<integer>287</integer>
		<key>u1D561.gm</key>
		<integer>262</integer>
		<key>u1D562</key>
		<integer>327</integer>
		<key>u1D562.gm</key>
		<integer>305</integer>
		<key>u1D563</key>
		<integer>256</integer>
		<key>u1D563.gm</key>
		<integer>240</integer>
		<key>u1D564</key>
		<integer>179</integer>
		<key>u1D564.gm</key>
		<integer>229</integer>
		<key>u1D565</key>
		<integer>163</integer>
		<key>u1D565.gm</key>
		<integer>173</integer>
		<key>u1D566</key>
		<integer>272</integer>
		<key>u1D566.gm</key>
		<integer>267</integer>
		<key>u1D567</key>
		<integer>235</integer>
		<key>u1D567.gm</key>
		<integer>234</integer>
		<key>u1D568</key>
		<integer>361</integer>
		<key>u1D568.gm</key>
		<integer>364</integer>
		<key>u1D569</key>
		<integer>254</integer>
		<key>u1D569.gm</key>
		<integer>247</integer>
		<key>u1D56A</key>
		<integer>244</integer>
		<key>u1D56A.gm</key>
		<integer>253</integer>
		<key>u1D56B</key>
		<integer>208</integer>
		<key>u1D56B.gm</key>
		<integer>255</integer>
		<key>u1D56C</key>
		<integer>454</integer>
		<key>u1D56D</key>
		<integer>406</integer>
		<key>u1D56E</key>
		<integer>356</integer>
		<key>u1D56F</key>
		<integer>330</integer>
		<key>u1D570</key>
		<integer>355</integer>
		<key>u1D571</key>
		<integer>433</integer>
		<key>u1D572</key>
		<integer>366</integer>
		<key>u1D573</key>
		<integer>324</integer>
		<key>u1D574</key>
		<integer>506</integer>
		<key>u1D575</key>
		<integer>508</integer>
		<key>u1D576</key>
		<integer>382</integer>
		<key>u1D577</key>
		<integer>312</integer>
		<key>u1D578</key>
		<integer>523</integer>
		<key>u1D579</key>
		<integer>415</integer>
		<key>u1D57A</key>
		<integer>324</integer>
		<key>u1D57B</key>
		<integer>401</integer>
		<key>u1D57C</key>
		<integer>361</integer>
		<key>u1D57D</key>
		<integer>420</integer>
		<key>u1D57E</key>
		<integer>372</integer>
		<key>u1D57F</key>
		<integer>381</integer>
		<key>u1D580</key>
		<integer>375</integer>
		<key>u1D581</key>
		<integer>407</integer>
		<key>u1D582</key>
		<integer>587</integer>
		<key>u1D583</key>
		<integer>330</integer>
		<key>u1D584</key>
		<integer>403</integer>
		<key>u1D585</key>
		<integer>353</integer>
		<key>u1D586</key>
		<integer>272</integer>
		<key>u1D587</key>
		<integer>148</integer>
		<key>u1D588</key>
		<integer>231</integer>
		<key>u1D589</key>
		<integer>167</integer>
		<key>u1D58A</key>
		<integer>228</integer>
		<key>u1D58B</key>
		<integer>191</integer>
		<key>u1D58C</key>
		<integer>277</integer>
		<key>u1D58D</key>
		<integer>153</integer>
		<key>u1D58E</key>
		<integer>157</integer>
		<key>u1D58F</key>
		<integer>164</integer>
		<key>u1D590</key>
		<integer>165</integer>
		<key>u1D591</key>
		<integer>149</integer>
		<key>u1D592</key>
		<integer>397</integer>
		<key>u1D593</key>
		<integer>280</integer>
		<key>u1D594</key>
		<integer>281</integer>
		<key>u1D595</key>
		<integer>282</integer>
		<key>u1D596</key>
		<integer>253</integer>
		<key>u1D597</key>
		<integer>226</integer>
		<key>u1D598</key>
		<integer>218</integer>
		<key>u1D599</key>
		<integer>206</integer>
		<key>u1D59A</key>
		<integer>284</integer>
		<key>u1D59B</key>
		<integer>292</integer>
		<key>u1D59C</key>
		<integer>403</integer>
		<key>u1D59D</key>
		<integer>228</integer>
		<key>u1D59E</key>
		<integer>283</integer>
		<key>u1D59F</key>
		<integer>196</integer>
		<key>u1D5A0</key>
		<integer>314</integer>
		<key>u1D5A1</key>
		<integer>247</integer>
		<key>u1D5A2</key>
		<integer>357</integer>
		<key>u1D5A3</key>
		<integer>285</integer>
		<key>u1D5A4</key>
		<integer>256</integer>
		<key>u1D5A5</key>
		<integer>256</integer>
		<key>u1D5A6</key>
		<integer>377</integer>
		<key>u1D5A7</key>
		<integer>336</integer>
		<key>u1D5A8</key>
		<integer>120</integer>
		<key>u1D5A9</key>
		<integer>174</integer>
		<key>u1D5AA</key>
		<integer>321</integer>
		<key>u1D5AB</key>
		<integer>120</integer>
		<key>u1D5AC</key>
		<integer>427</integer>
		<key>u1D5AD</key>
		<integer>338</integer>
		<key>u1D5AE</key>
		<integer>362</integer>
		<key>u1D5AF</key>
		<integer>242</integer>
		<key>u1D5B0</key>
		<integer>365</integer>
		<key>u1D5B1</key>
		<integer>238</integer>
		<key>u1D5B2</key>
		<integer>241</integer>
		<key>u1D5B3</key>
		<integer>262</integer>
		<key>u1D5B4</key>
		<integer>317</integer>
		<key>u1D5B5</key>
		<integer>305</integer>
		<key>u1D5B6</key>
		<integer>465</integer>
		<key>u1D5B7</key>
		<integer>277</integer>
		<key>u1D5B8</key>
		<integer>274</integer>
		<key>u1D5B9</key>
		<integer>293</integer>
		<key>u1D5BA</key>
		<integer>209</integer>
		<key>u1D5BB</key>
		<integer>103</integer>
		<key>u1D5BC</key>
		<integer>258</integer>
		<key>u1D5BD</key>
		<integer>385</integer>
		<key>u1D5BE</key>
		<integer>240</integer>
		<key>u1D5BF</key>
		<integer>230</integer>
		<key>u1D5C0</key>
		<integer>224</integer>
		<key>u1D5C1</key>
		<integer>102</integer>
		<key>u1D5C2</key>
		<integer>103</integer>
		<key>u1D5C3</key>
		<integer>138</integer>
		<key>u1D5C4</key>
		<integer>102</integer>
		<key>u1D5C5</key>
		<integer>102</integer>
		<key>u1D5C6</key>
		<integer>358</integer>
		<key>u1D5C7</key>
		<integer>222</integer>
		<key>u1D5C8</key>
		<integer>250</integer>
		<key>u1D5C9</key>
		<integer>212</integer>
		<key>u1D5CA</key>
		<integer>244</integer>
		<key>u1D5CB</key>
		<integer>177</integer>
		<key>u1D5CC</key>
		<integer>198</integer>
		<key>u1D5CD</key>
		<integer>135</integer>
		<key>u1D5CE</key>
		<integer>236</integer>
		<key>u1D5CF</key>
		<integer>214</integer>
		<key>u1D5D0</key>
		<integer>343</integer>
		<key>u1D5D1</key>
		<integer>215</integer>
		<key>u1D5D2</key>
		<integer>236</integer>
		<key>u1D5D3</key>
		<integer>211</integer>
		<key>u1D5D4</key>
		<integer>345</integer>
		<key>u1D5D5</key>
		<integer>259</integer>
		<key>u1D5D6</key>
		<integer>397</integer>
		<key>u1D5D7</key>
		<integer>295</integer>
		<key>u1D5D8</key>
		<integer>292</integer>
		<key>u1D5D9</key>
		<integer>289</integer>
		<key>u1D5DA</key>
		<integer>380</integer>
		<key>u1D5DB</key>
		<integer>367</integer>
		<key>u1D5DC</key>
		<integer>150</integer>
		<key>u1D5DD</key>
		<integer>245</integer>
		<key>u1D5DE</key>
		<integer>373</integer>
		<key>u1D5DF</key>
		<integer>148</integer>
		<key>u1D5E0</key>
		<integer>481</integer>
		<key>u1D5E1</key>
		<integer>367</integer>
		<key>u1D5E2</key>
		<integer>392</integer>
		<key>u1D5E3</key>
		<integer>260</integer>
		<key>u1D5E4</key>
		<integer>395</integer>
		<key>u1D5E5</key>
		<integer>275</integer>
		<key>u1D5E6</key>
		<integer>271</integer>
		<key>u1D5E7</key>
		<integer>298</integer>
		<key>u1D5E8</key>
		<integer>342</integer>
		<key>u1D5E9</key>
		<integer>326</integer>
		<key>u1D5EA</key>
		<integer>510</integer>
		<key>u1D5EB</key>
		<integer>322</integer>
		<key>u1D5EC</key>
		<integer>303</integer>
		<key>u1D5ED</key>
		<integer>341</integer>
		<key>u1D5EE</key>
		<integer>248</integer>
		<key>u1D5EF</key>
		<integer>130</integer>
		<key>u1D5F0</key>
		<integer>282</integer>
		<key>u1D5F1</key>
		<integer>430</integer>
		<key>u1D5F2</key>
		<integer>267</integer>
		<key>u1D5F3</key>
		<integer>301</integer>
		<key>u1D5F4</key>
		<integer>254</integer>
		<key>u1D5F5</key>
		<integer>129</integer>
		<key>u1D5F6</key>
		<integer>127</integer>
		<key>u1D5F7</key>
		<integer>171</integer>
		<key>u1D5F8</key>
		<integer>129</integer>
		<key>u1D5F9</key>
		<integer>129</integer>
		<key>u1D5FA</key>
		<integer>410</integer>
		<key>u1D5FB</key>
		<integer>266</integer>
		<key>u1D5FC</key>
		<integer>278</integer>
		<key>u1D5FD</key>
		<integer>270</integer>
		<key>u1D5FE</key>
		<integer>316</integer>
		<key>u1D5FF</key>
		<integer>184</integer>
		<key>u1D600</key>
		<integer>215</integer>
		<key>u1D601</key>
		<integer>160</integer>
		<key>u1D602</key>
		<integer>270</integer>
		<key>u1D603</key>
		<integer>257</integer>
		<key>u1D604</key>
		<integer>391</integer>
		<key>u1D605</key>
		<integer>250</integer>
		<key>u1D606</key>
		<integer>260</integer>
		<key>u1D607</key>
		<integer>243</integer>
		<key>u1D608</key>
		<integer>426</integer>
		<key>u1D609</key>
		<integer>347</integer>
		<key>u1D60A</key>
		<integer>436</integer>
		<key>u1D60B</key>
		<integer>392</integer>
		<key>u1D60C</key>
		<integer>369</integer>
		<key>u1D60D</key>
		<integer>369</integer>
		<key>u1D60E</key>
		<integer>459</integer>
		<key>u1D60F</key>
		<integer>433</integer>
		<key>u1D610</key>
		<integer>233</integer>
		<key>u1D611</key>
		<integer>301</integer>
		<key>u1D612</key>
		<integer>422</integer>
		<key>u1D613</key>
		<integer>233</integer>
		<key>u1D614</key>
		<integer>516</integer>
		<key>u1D615</key>
		<integer>444</integer>
		<key>u1D616</key>
		<integer>414</integer>
		<key>u1D617</key>
		<integer>364</integer>
		<key>u1D618</key>
		<integer>417</integer>
		<key>u1D619</key>
		<integer>354</integer>
		<key>u1D61A</key>
		<integer>334</integer>
		<key>u1D61B</key>
		<integer>292</integer>
		<key>u1D61C</key>
		<integer>415</integer>
		<key>u1D61D</key>
		<integer>332</integer>
		<key>u1D61E</key>
		<integer>482</integer>
		<key>u1D61F</key>
		<integer>386</integer>
		<key>u1D620</key>
		<integer>303</integer>
		<key>u1D621</key>
		<integer>408</integer>
		<key>u1D622</key>
		<integer>339</integer>
		<key>u1D623</key>
		<integer>229</integer>
		<key>u1D624</key>
		<integer>277</integer>
		<key>u1D625</key>
		<integer>492</integer>
		<key>u1D626</key>
		<integer>282</integer>
		<key>u1D627</key>
		<integer>421</integer>
		<key>u1D628</key>
		<integer>295</integer>
		<key>u1D629</key>
		<integer>224</integer>
		<key>u1D62A</key>
		<integer>205</integer>
		<key>u1D62B</key>
		<integer>262</integer>
		<key>u1D62C</key>
		<integer>224</integer>
		<key>u1D62D</key>
		<integer>214</integer>
		<key>u1D62E</key>
		<integer>403</integer>
		<key>u1D62F</key>
		<integer>280</integer>
		<key>u1D630</key>
		<integer>291</integer>
		<key>u1D631</key>
		<integer>286</integer>
		<key>u1D632</key>
		<integer>306</integer>
		<key>u1D633</key>
		<integer>222</integer>
		<key>u1D634</key>
		<integer>245</integer>
		<key>u1D635</key>
		<integer>205</integer>
		<key>u1D636</key>
		<integer>292</integer>
		<key>u1D637</key>
		<integer>231</integer>
		<key>u1D638</key>
		<integer>356</integer>
		<key>u1D639</key>
		<integer>296</integer>
		<key>u1D63A</key>
		<integer>275</integer>
		<key>u1D63B</key>
		<integer>287</integer>
		<key>u1D63C</key>
		<integer>467</integer>
		<key>u1D63D</key>
		<integer>388</integer>
		<key>u1D63E</key>
		<integer>487</integer>
		<key>u1D63F</key>
		<integer>404</integer>
		<key>u1D640</key>
		<integer>413</integer>
		<key>u1D641</key>
		<integer>411</integer>
		<key>u1D642</key>
		<integer>468</integer>
		<key>u1D643</key>
		<integer>489</integer>
		<key>u1D644</key>
		<integer>270</integer>
		<key>u1D645</key>
		<integer>401</integer>
		<key>u1D646</key>
		<integer>494</integer>
		<key>u1D647</key>
		<integer>267</integer>
		<key>u1D648</key>
		<integer>594</integer>
		<key>u1D649</key>
		<integer>487</integer>
		<key>u1D64A</key>
		<integer>495</integer>
		<key>u1D64B</key>
		<integer>411</integer>
		<key>u1D64C</key>
		<integer>499</integer>
		<key>u1D64D</key>
		<integer>419</integer>
		<key>u1D64E</key>
		<integer>388</integer>
		<key>u1D64F</key>
		<integer>335</integer>
		<key>u1D650</key>
		<integer>454</integer>
		<key>u1D651</key>
		<integer>371</integer>
		<key>u1D652</key>
		<integer>553</integer>
		<key>u1D653</key>
		<integer>440</integer>
		<key>u1D654</key>
		<integer>334</integer>
		<key>u1D655</key>
		<integer>460</integer>
		<key>u1D656</key>
		<integer>340</integer>
		<key>u1D657</key>
		<integer>261</integer>
		<key>u1D658</key>
		<integer>346</integer>
		<key>u1D659</key>
		<integer>557</integer>
		<key>u1D65A</key>
		<integer>330</integer>
		<key>u1D65B</key>
		<integer>479</integer>
		<key>u1D65C</key>
		<integer>347</integer>
		<key>u1D65D</key>
		<integer>259</integer>
		<key>u1D65E</key>
		<integer>261</integer>
		<key>u1D65F</key>
		<integer>348</integer>
		<key>u1D660</key>
		<integer>260</integer>
		<key>u1D661</key>
		<integer>259</integer>
		<key>u1D662</key>
		<integer>488</integer>
		<key>u1D663</key>
		<integer>344</integer>
		<key>u1D664</key>
		<integer>344</integer>
		<key>u1D665</key>
		<integer>365</integer>
		<key>u1D666</key>
		<integer>373</integer>
		<key>u1D667</key>
		<integer>286</integer>
		<key>u1D668</key>
		<integer>283</integer>
		<key>u1D669</key>
		<integer>240</integer>
		<key>u1D66A</key>
		<integer>343</integer>
		<key>u1D66B</key>
		<integer>286</integer>
		<key>u1D66C</key>
		<integer>423</integer>
		<key>u1D66D</key>
		<integer>329</integer>
		<key>u1D66E</key>
		<integer>294</integer>
		<key>u1D66F</key>
		<integer>328</integer>
		<key>u1D670</key>
		<integer>262</integer>
		<key>u1D671</key>
		<integer>197</integer>
		<key>u1D672</key>
		<integer>295</integer>
		<key>u1D673</key>
		<integer>181</integer>
		<key>u1D674</key>
		<integer>254</integer>
		<key>u1D675</key>
		<integer>260</integer>
		<key>u1D676</key>
		<integer>260</integer>
		<key>u1D677</key>
		<integer>262</integer>
		<key>u1D678</key>
		<integer>262</integer>
		<key>u1D679</key>
		<integer>373</integer>
		<key>u1D67A</key>
		<integer>261</integer>
		<key>u1D67B</key>
		<integer>143</integer>
		<key>u1D67C</key>
		<integer>263</integer>
		<key>u1D67D</key>
		<integer>262</integer>
		<key>u1D67E</key>
		<integer>262</integer>
		<key>u1D67F</key>
		<integer>205</integer>
		<key>u1D680</key>
		<integer>262</integer>
		<key>u1D681</key>
		<integer>169</integer>
		<key>u1D682</key>
		<integer>239</integer>
		<key>u1D683</key>
		<integer>262</integer>
		<key>u1D684</key>
		<integer>262</integer>
		<key>u1D685</key>
		<integer>262</integer>
		<key>u1D686</key>
		<integer>262</integer>
		<key>u1D687</key>
		<integer>253</integer>
		<key>u1D688</key>
		<integer>262</integer>
		<key>u1D689</key>
		<integer>269</integer>
		<key>u1D68A</key>
		<integer>220</integer>
		<key>u1D68B</key>
		<integer>131</integer>
		<key>u1D68C</key>
		<integer>298</integer>
		<key>u1D68D</key>
		<integer>392</integer>
		<key>u1D68E</key>
		<integer>268</integer>
		<key>u1D68F</key>
		<integer>325</integer>
		<key>u1D690</key>
		<integer>242</integer>
		<key>u1D691</key>
		<integer>130</integer>
		<key>u1D692</key>
		<integer>259</integer>
		<key>u1D693</key>
		<integer>318</integer>
		<key>u1D694</key>
		<integer>137</integer>
		<key>u1D695</key>
		<integer>261</integer>
		<key>u1D696</key>
		<integer>238</integer>
		<key>u1D697</key>
		<integer>213</integer>
		<key>u1D698</key>
		<integer>263</integer>
		<key>u1D699</key>
		<integer>211</integer>
		<key>u1D69A</key>
		<integer>301</integer>
		<key>u1D69B</key>
		<integer>254</integer>
		<key>u1D69C</key>
		<integer>262</integer>
		<key>u1D69D</key>
		<integer>186</integer>
		<key>u1D69E</key>
		<integer>262</integer>
		<key>u1D69F</key>
		<integer>262</integer>
		<key>u1D6A0</key>
		<integer>262</integer>
		<key>u1D6A1</key>
		<integer>257</integer>
		<key>u1D6A2</key>
		<integer>262</integer>
		<key>u1D6A3</key>
		<integer>259</integer>
		<key>u1D6A4</key>
		<integer>198</integer>
		<key>u1D6A5</key>
		<integer>309</integer>
		<key>u1D6AA</key>
		<integer>282</integer>
		<key>u1D6AA.eb</key>
		<integer>284</integer>
		<key>u1D6AA.s</key>
		<integer>282</integer>
		<key>u1D6AA.sb</key>
		<integer>277</integer>
		<key>u1D6AA.ss</key>
		<integer>284</integer>
		<key>u1D6AB</key>
		<integer>312</integer>
		<key>u1D6AB.eb</key>
		<integer>307</integer>
		<key>u1D6AB.s</key>
		<integer>315</integer>
		<key>u1D6AB.sb</key>
		<integer>314</integer>
		<key>u1D6AB.ss</key>
		<integer>318</integer>
		<key>u1D6AF</key>
		<integer>394</integer>
		<key>u1D6AF.eb</key>
		<integer>397</integer>
		<key>u1D6AF.s</key>
		<integer>396</integer>
		<key>u1D6AF.sb</key>
		<integer>389</integer>
		<key>u1D6AF.ss</key>
		<integer>396</integer>
		<key>u1D6B2</key>
		<integer>411</integer>
		<key>u1D6B2.eb</key>
		<integer>364</integer>
		<key>u1D6B2.s</key>
		<integer>366</integer>
		<key>u1D6B2.sb</key>
		<integer>358</integer>
		<key>u1D6B2.ss</key>
		<integer>368</integer>
		<key>u1D6B5</key>
		<integer>373</integer>
		<key>u1D6B5.eb</key>
		<integer>382</integer>
		<key>u1D6B5.s</key>
		<integer>377</integer>
		<key>u1D6B5.sb</key>
		<integer>361</integer>
		<key>u1D6B5.ss</key>
		<integer>380</integer>
		<key>u1D6B7</key>
		<integer>405</integer>
		<key>u1D6B7.eb</key>
		<integer>409</integer>
		<key>u1D6B7.s</key>
		<integer>406</integer>
		<key>u1D6B7.sb</key>
		<integer>400</integer>
		<key>u1D6B7.ss</key>
		<integer>409</integer>
		<key>u1D6B9</key>
		<integer>393</integer>
		<key>u1D6B9.eb</key>
		<integer>397</integer>
		<key>u1D6B9.s</key>
		<integer>395</integer>
		<key>u1D6B9.sb</key>
		<integer>390</integer>
		<key>u1D6B9.ss</key>
		<integer>396</integer>
		<key>u1D6BA</key>
		<integer>295</integer>
		<key>u1D6BA.eb</key>
		<integer>298</integer>
		<key>u1D6BA.s</key>
		<integer>297</integer>
		<key>u1D6BA.sb</key>
		<integer>291</integer>
		<key>u1D6BA.ss</key>
		<integer>298</integer>
		<key>u1D6BC</key>
		<integer>374</integer>
		<key>u1D6BC.eb</key>
		<integer>376</integer>
		<key>u1D6BC.s</key>
		<integer>374</integer>
		<key>u1D6BC.sb</key>
		<integer>371</integer>
		<key>u1D6BC.ss</key>
		<integer>376</integer>
		<key>u1D6BD</key>
		<integer>382</integer>
		<key>u1D6BD.eb</key>
		<integer>383</integer>
		<key>u1D6BD.s</key>
		<integer>383</integer>
		<key>u1D6BD.sb</key>
		<integer>380</integer>
		<key>u1D6BD.ss</key>
		<integer>383</integer>
		<key>u1D6BF</key>
		<integer>439</integer>
		<key>u1D6BF.eb</key>
		<integer>452</integer>
		<key>u1D6BF.s</key>
		<integer>444</integer>
		<key>u1D6BF.sb</key>
		<integer>421</integer>
		<key>u1D6BF.ss</key>
		<integer>450</integer>
		<key>u1D6C0</key>
		<integer>428</integer>
		<key>u1D6C0.eb</key>
		<integer>434</integer>
		<key>u1D6C0.s</key>
		<integer>431</integer>
		<key>u1D6C0.sb</key>
		<integer>419</integer>
		<key>u1D6C0.ss</key>
		<integer>433</integer>
		<key>u1D6C1</key>
		<integer>313</integer>
		<key>u1D6C1.eb</key>
		<integer>313</integer>
		<key>u1D6C1.s</key>
		<integer>313</integer>
		<key>u1D6C1.sb</key>
		<integer>311</integer>
		<key>u1D6C1.ss</key>
		<integer>314</integer>
		<key>u1D6C2</key>
		<integer>292</integer>
		<key>u1D6C2.eb</key>
		<integer>296</integer>
		<key>u1D6C2.s</key>
		<integer>293</integer>
		<key>u1D6C2.sb</key>
		<integer>284</integer>
		<key>u1D6C2.ss</key>
		<integer>296</integer>
		<key>u1D6C3</key>
		<integer>241</integer>
		<key>u1D6C3.eb</key>
		<integer>242</integer>
		<key>u1D6C3.s</key>
		<integer>242</integer>
		<key>u1D6C3.sb</key>
		<integer>241</integer>
		<key>u1D6C3.ss</key>
		<integer>242</integer>
		<key>u1D6C4</key>
		<integer>294</integer>
		<key>u1D6C4.eb</key>
		<integer>296</integer>
		<key>u1D6C4.s</key>
		<integer>295</integer>
		<key>u1D6C4.sb</key>
		<integer>288</integer>
		<key>u1D6C4.ss</key>
		<integer>296</integer>
		<key>u1D6C5</key>
		<integer>204</integer>
		<key>u1D6C5.eb</key>
		<integer>214</integer>
		<key>u1D6C5.s</key>
		<integer>208</integer>
		<key>u1D6C5.sb</key>
		<integer>188</integer>
		<key>u1D6C5.ss</key>
		<integer>212</integer>
		<key>u1D6C6</key>
		<integer>225</integer>
		<key>u1D6C6.eb</key>
		<integer>231</integer>
		<key>u1D6C6.s</key>
		<integer>228</integer>
		<key>u1D6C6.sb</key>
		<integer>215</integer>
		<key>u1D6C6.ss</key>
		<integer>230</integer>
		<key>u1D6C7</key>
		<integer>255</integer>
		<key>u1D6C7.eb</key>
		<integer>261</integer>
		<key>u1D6C7.s</key>
		<integer>258</integer>
		<key>u1D6C7.sb</key>
		<integer>248</integer>
		<key>u1D6C7.ss</key>
		<integer>260</integer>
		<key>u1D6C8</key>
		<integer>285</integer>
		<key>u1D6C8.eb</key>
		<integer>294</integer>
		<key>u1D6C8.s</key>
		<integer>288</integer>
		<key>u1D6C8.sb</key>
		<integer>268</integer>
		<key>u1D6C8.ss</key>
		<integer>292</integer>
		<key>u1D6C9</key>
		<integer>238</integer>
		<key>u1D6C9.eb</key>
		<integer>243</integer>
		<key>u1D6C9.s</key>
		<integer>240</integer>
		<key>u1D6C9.sb</key>
		<integer>231</integer>
		<key>u1D6C9.ss</key>
		<integer>242</integer>
		<key>u1D6CA</key>
		<integer>113</integer>
		<key>u1D6CA.eb</key>
		<integer>117</integer>
		<key>u1D6CA.s</key>
		<integer>115</integer>
		<key>u1D6CA.sb</key>
		<integer>107</integer>
		<key>u1D6CA.ss</key>
		<integer>116</integer>
		<key>u1D6CB</key>
		<integer>267</integer>
		<key>u1D6CB.eb</key>
		<integer>273</integer>
		<key>u1D6CB.s</key>
		<integer>269</integer>
		<key>u1D6CB.sb</key>
		<integer>258</integer>
		<key>u1D6CB.ss</key>
		<integer>272</integer>
		<key>u1D6CC</key>
		<integer>103</integer>
		<key>u1D6CC.eb</key>
		<integer>105</integer>
		<key>u1D6CC.s</key>
		<integer>103</integer>
		<key>u1D6CC.sb</key>
		<integer>101</integer>
		<key>u1D6CC.ss</key>
		<integer>105</integer>
		<key>u1D6CD</key>
		<integer>250</integer>
		<key>u1D6CD.eb</key>
		<integer>255</integer>
		<key>u1D6CD.s</key>
		<integer>252</integer>
		<key>u1D6CD.sb</key>
		<integer>244</integer>
		<key>u1D6CD.ss</key>
		<integer>254</integer>
		<key>u1D6CE</key>
		<integer>255</integer>
		<key>u1D6CE.eb</key>
		<integer>268</integer>
		<key>u1D6CE.s</key>
		<integer>263</integer>
		<key>u1D6CE.sb</key>
		<integer>249</integer>
		<key>u1D6CE.ss</key>
		<integer>266</integer>
		<key>u1D6CF</key>
		<integer>254</integer>
		<key>u1D6CF.eb</key>
		<integer>260</integer>
		<key>u1D6CF.s</key>
		<integer>257</integer>
		<key>u1D6CF.sb</key>
		<integer>247</integer>
		<key>u1D6CF.ss</key>
		<integer>258</integer>
		<key>u1D6D0</key>
		<integer>229</integer>
		<key>u1D6D0.eb</key>
		<integer>234</integer>
		<key>u1D6D0.s</key>
		<integer>231</integer>
		<key>u1D6D0.sb</key>
		<integer>223</integer>
		<key>u1D6D0.ss</key>
		<integer>233</integer>
		<key>u1D6D1</key>
		<integer>309</integer>
		<key>u1D6D1.eb</key>
		<integer>312</integer>
		<key>u1D6D1.s</key>
		<integer>311</integer>
		<key>u1D6D1.sb</key>
		<integer>322</integer>
		<key>u1D6D1.ss</key>
		<integer>312</integer>
		<key>u1D6D2</key>
		<integer>237</integer>
		<key>u1D6D2.eb</key>
		<integer>240</integer>
		<key>u1D6D2.s</key>
		<integer>239</integer>
		<key>u1D6D2.sb</key>
		<integer>233</integer>
		<key>u1D6D2.ss</key>
		<integer>239</integer>
		<key>u1D6D3</key>
		<integer>270</integer>
		<key>u1D6D3.eb</key>
		<integer>275</integer>
		<key>u1D6D3.s</key>
		<integer>272</integer>
		<key>u1D6D3.sb</key>
		<integer>263</integer>
		<key>u1D6D3.ss</key>
		<integer>274</integer>
		<key>u1D6D4</key>
		<integer>300</integer>
		<key>u1D6D4.eb</key>
		<integer>351</integer>
		<key>u1D6D4.s</key>
		<integer>302</integer>
		<key>u1D6D4.sb</key>
		<integer>313</integer>
		<key>u1D6D4.ss</key>
		<integer>305</integer>
		<key>u1D6D5</key>
		<integer>249</integer>
		<key>u1D6D5.eb</key>
		<integer>252</integer>
		<key>u1D6D5.s</key>
		<integer>250</integer>
		<key>u1D6D5.sb</key>
		<integer>244</integer>
		<key>u1D6D5.ss</key>
		<integer>251</integer>
		<key>u1D6D6</key>
		<integer>268</integer>
		<key>u1D6D6.eb</key>
		<integer>278</integer>
		<key>u1D6D6.s</key>
		<integer>272</integer>
		<key>u1D6D6.sb</key>
		<integer>252</integer>
		<key>u1D6D6.ss</key>
		<integer>276</integer>
		<key>u1D6D7</key>
		<integer>316</integer>
		<key>u1D6D7.eb</key>
		<integer>318</integer>
		<key>u1D6D7.s</key>
		<integer>317</integer>
		<key>u1D6D7.sb</key>
		<integer>312</integer>
		<key>u1D6D7.ss</key>
		<integer>317</integer>
		<key>u1D6D8</key>
		<integer>255</integer>
		<key>u1D6D8.eb</key>
		<integer>258</integer>
		<key>u1D6D8.s</key>
		<integer>255</integer>
		<key>u1D6D8.sb</key>
		<integer>252</integer>
		<key>u1D6D8.ss</key>
		<integer>257</integer>
		<key>u1D6D9</key>
		<integer>343</integer>
		<key>u1D6D9.eb</key>
		<integer>349</integer>
		<key>u1D6D9.s</key>
		<integer>346</integer>
		<key>u1D6D9.sb</key>
		<integer>335</integer>
		<key>u1D6D9.ss</key>
		<integer>347</integer>
		<key>u1D6DA</key>
		<integer>304</integer>
		<key>u1D6DA.eb</key>
		<integer>308</integer>
		<key>u1D6DA.s</key>
		<integer>305</integer>
		<key>u1D6DA.sb</key>
		<integer>298</integer>
		<key>u1D6DA.ss</key>
		<integer>307</integer>
		<key>u1D6DB</key>
		<integer>195</integer>
		<key>u1D6DB.eb</key>
		<integer>199</integer>
		<key>u1D6DB.s</key>
		<integer>196</integer>
		<key>u1D6DB.sb</key>
		<integer>189</integer>
		<key>u1D6DB.ss</key>
		<integer>198</integer>
		<key>u1D6DC</key>
		<integer>227</integer>
		<key>u1D6DC.eb</key>
		<integer>228</integer>
		<key>u1D6DC.s</key>
		<integer>227</integer>
		<key>u1D6DC.sb</key>
		<integer>225</integer>
		<key>u1D6DC.ss</key>
		<integer>228</integer>
		<key>u1D6DD</key>
		<integer>260</integer>
		<key>u1D6DD.eb</key>
		<integer>266</integer>
		<key>u1D6DD.s</key>
		<integer>262</integer>
		<key>u1D6DD.sb</key>
		<integer>251</integer>
		<key>u1D6DD.ss</key>
		<integer>265</integer>
		<key>u1D6DE</key>
		<integer>287</integer>
		<key>u1D6DE.eb</key>
		<integer>294</integer>
		<key>u1D6DE.s</key>
		<integer>290</integer>
		<key>u1D6DE.sb</key>
		<integer>277</integer>
		<key>u1D6DE.ss</key>
		<integer>293</integer>
		<key>u1D6DF</key>
		<integer>295</integer>
		<key>u1D6DF.eb</key>
		<integer>303</integer>
		<key>u1D6DF.s</key>
		<integer>298</integer>
		<key>u1D6DF.sb</key>
		<integer>280</integer>
		<key>u1D6DF.ss</key>
		<integer>301</integer>
		<key>u1D6E0</key>
		<integer>247</integer>
		<key>u1D6E0.eb</key>
		<integer>253</integer>
		<key>u1D6E0.s</key>
		<integer>249</integer>
		<key>u1D6E0.sb</key>
		<integer>239</integer>
		<key>u1D6E0.ss</key>
		<integer>250</integer>
		<key>u1D6E1</key>
		<integer>290</integer>
		<key>u1D6E1.eb</key>
		<integer>291</integer>
		<key>u1D6E1.s</key>
		<integer>290</integer>
		<key>u1D6E1.sb</key>
		<integer>262</integer>
		<key>u1D6E1.ss</key>
		<integer>291</integer>
		<key>u1D6E4</key>
		<integer>418</integer>
		<key>u1D6E4.s</key>
		<integer>420</integer>
		<key>u1D6E4.ss</key>
		<integer>421</integer>
		<key>u1D6E5</key>
		<integer>547</integer>
		<key>u1D6E5.s</key>
		<integer>555</integer>
		<key>u1D6E5.ss</key>
		<integer>563</integer>
		<key>u1D6E9</key>
		<integer>443</integer>
		<key>u1D6E9.s</key>
		<integer>446</integer>
		<key>u1D6E9.ss</key>
		<integer>448</integer>
		<key>u1D6EC</key>
		<integer>607</integer>
		<key>u1D6EC.s</key>
		<integer>619</integer>
		<key>u1D6EC.ss</key>
		<integer>631</integer>
		<key>u1D6EF</key>
		<integer>484</integer>
		<key>u1D6EF.s</key>
		<integer>491</integer>
		<key>u1D6EF.ss</key>
		<integer>496</integer>
		<key>u1D6F1</key>
		<integer>537</integer>
		<key>u1D6F1.s</key>
		<integer>536</integer>
		<key>u1D6F1.ss</key>
		<integer>536</integer>
		<key>u1D6F3</key>
		<integer>443</integer>
		<key>u1D6F3.s</key>
		<integer>446</integer>
		<key>u1D6F3.ss</key>
		<integer>448</integer>
		<key>u1D6F4</key>
		<integer>424</integer>
		<key>u1D6F4.s</key>
		<integer>427</integer>
		<key>u1D6F4.ss</key>
		<integer>428</integer>
		<key>u1D6F6</key>
		<integer>376</integer>
		<key>u1D6F6.s</key>
		<integer>384</integer>
		<key>u1D6F6.ss</key>
		<integer>390</integer>
		<key>u1D6F7</key>
		<integer>492</integer>
		<key>u1D6F7.s</key>
		<integer>494</integer>
		<key>u1D6F7.ss</key>
		<integer>495</integer>
		<key>u1D6F9</key>
		<integer>425</integer>
		<key>u1D6F9.s</key>
		<integer>440</integer>
		<key>u1D6F9.ss</key>
		<integer>453</integer>
		<key>u1D6FA</key>
		<integer>524</integer>
		<key>u1D6FA.s</key>
		<integer>524</integer>
		<key>u1D6FA.ss</key>
		<integer>528</integer>
		<key>u1D6FB</key>
		<integer>424</integer>
		<key>u1D6FB.s</key>
		<integer>430</integer>
		<key>u1D6FB.ss</key>
		<integer>436</integer>
		<key>u1D6FC</key>
		<integer>353</integer>
		<key>u1D6FC.s</key>
		<integer>358</integer>
		<key>u1D6FC.ss</key>
		<integer>363</integer>
		<key>u1D6FD</key>
		<integer>395</integer>
		<key>u1D6FD.s</key>
		<integer>395</integer>
		<key>u1D6FD.ss</key>
		<integer>398</integer>
		<key>u1D6FE</key>
		<integer>294</integer>
		<key>u1D6FE.s</key>
		<integer>299</integer>
		<key>u1D6FE.ss</key>
		<integer>302</integer>
		<key>u1D6FF</key>
		<integer>214</integer>
		<key>u1D6FF.s</key>
		<integer>227</integer>
		<key>u1D6FF.ss</key>
		<integer>241</integer>
		<key>u1D700</key>
		<integer>279</integer>
		<key>u1D700.s</key>
		<integer>281</integer>
		<key>u1D700.ss</key>
		<integer>283</integer>
		<key>u1D701</key>
		<integer>406</integer>
		<key>u1D701.s</key>
		<integer>409</integer>
		<key>u1D701.ss</key>
		<integer>412</integer>
		<key>u1D702</key>
		<integer>309</integer>
		<key>u1D702.s</key>
		<integer>312</integer>
		<key>u1D702.ss</key>
		<integer>314</integer>
		<key>u1D703</key>
		<integer>339</integer>
		<key>u1D703.s</key>
		<integer>342</integer>
		<key>u1D703.ss</key>
		<integer>345</integer>
		<key>u1D704</key>
		<integer>178</integer>
		<key>u1D704.s</key>
		<integer>175</integer>
		<key>u1D704.ss</key>
		<integer>179</integer>
		<key>u1D705</key>
		<integer>278</integer>
		<key>u1D705.s</key>
		<integer>285</integer>
		<key>u1D705.ss</key>
		<integer>293</integer>
		<key>u1D706</key>
		<integer>249</integer>
		<key>u1D706.s</key>
		<integer>256</integer>
		<key>u1D706.ss</key>
		<integer>264</integer>
		<key>u1D707</key>
		<integer>326</integer>
		<key>u1D707.s</key>
		<integer>345</integer>
		<key>u1D707.ss</key>
		<integer>350</integer>
		<key>u1D708</key>
		<integer>249</integer>
		<key>u1D708.s</key>
		<integer>255</integer>
		<key>u1D708.ss</key>
		<integer>260</integer>
		<key>u1D709</key>
		<integer>394</integer>
		<key>u1D709.s</key>
		<integer>397</integer>
		<key>u1D709.ss</key>
		<integer>400</integer>
		<key>u1D70A</key>
		<integer>261</integer>
		<key>u1D70A.s</key>
		<integer>264</integer>
		<key>u1D70A.ss</key>
		<integer>266</integer>
		<key>u1D70B</key>
		<integer>316</integer>
		<key>u1D70B.s</key>
		<integer>317</integer>
		<key>u1D70B.ss</key>
		<integer>318</integer>
		<key>u1D70C</key>
		<integer>414</integer>
		<key>u1D70C.s</key>
		<integer>421</integer>
		<key>u1D70C.ss</key>
		<integer>427</integer>
		<key>u1D70D</key>
		<integer>308</integer>
		<key>u1D70D.s</key>
		<integer>311</integer>
		<key>u1D70D.ss</key>
		<integer>315</integer>
		<key>u1D70E</key>
		<integer>303</integer>
		<key>u1D70E.s</key>
		<integer>307</integer>
		<key>u1D70E.ss</key>
		<integer>311</integer>
		<key>u1D70F</key>
		<integer>248</integer>
		<key>u1D70F.s</key>
		<integer>252</integer>
		<key>u1D70F.ss</key>
		<integer>254</integer>
		<key>u1D710</key>
		<integer>267</integer>
		<key>u1D710.s</key>
		<integer>271</integer>
		<key>u1D710.ss</key>
		<integer>273</integer>
		<key>u1D711</key>
		<integer>363</integer>
		<key>u1D711.s</key>
		<integer>379</integer>
		<key>u1D711.ss</key>
		<integer>377</integer>
		<key>u1D712</key>
		<integer>335</integer>
		<key>u1D712.s</key>
		<integer>342</integer>
		<key>u1D712.ss</key>
		<integer>348</integer>
		<key>u1D713</key>
		<integer>423</integer>
		<key>u1D713.s</key>
		<integer>424</integer>
		<key>u1D713.ss</key>
		<integer>425</integer>
		<key>u1D714</key>
		<integer>374</integer>
		<key>u1D714.s</key>
		<integer>377</integer>
		<key>u1D714.ss</key>
		<integer>380</integer>
		<key>u1D715</key>
		<integer>233</integer>
		<key>u1D715.s</key>
		<integer>270</integer>
		<key>u1D715.ss</key>
		<integer>278</integer>
		<key>u1D715.x</key>
		<integer>253</integer>
		<key>u1D715.x.s</key>
		<integer>265</integer>
		<key>u1D715.x.ss</key>
		<integer>279</integer>
		<key>u1D716</key>
		<integer>292</integer>
		<key>u1D716.s</key>
		<integer>294</integer>
		<key>u1D716.ss</key>
		<integer>296</integer>
		<key>u1D717</key>
		<integer>354</integer>
		<key>u1D717.s</key>
		<integer>353</integer>
		<key>u1D717.ss</key>
		<integer>351</integer>
		<key>u1D718</key>
		<integer>315</integer>
		<key>u1D718.s</key>
		<integer>319</integer>
		<key>u1D718.ss</key>
		<integer>324</integer>
		<key>u1D719</key>
		<integer>396</integer>
		<key>u1D719.s</key>
		<integer>399</integer>
		<key>u1D719.ss</key>
		<integer>401</integer>
		<key>u1D71A</key>
		<integer>303</integer>
		<key>u1D71A.s</key>
		<integer>307</integer>
		<key>u1D71A.ss</key>
		<integer>312</integer>
		<key>u1D71B</key>
		<integer>326</integer>
		<key>u1D71B.s</key>
		<integer>324</integer>
		<key>u1D71B.ss</key>
		<integer>330</integer>
		<key>u1D71E</key>
		<integer>430</integer>
		<key>u1D71E.eb</key>
		<integer>433</integer>
		<key>u1D71E.s</key>
		<integer>431</integer>
		<key>u1D71E.sb</key>
		<integer>427</integer>
		<key>u1D71E.ss</key>
		<integer>431</integer>
		<key>u1D71F</key>
		<integer>586</integer>
		<key>u1D71F.eb</key>
		<integer>598</integer>
		<key>u1D71F.s</key>
		<integer>590</integer>
		<key>u1D71F.sb</key>
		<integer>568</integer>
		<key>u1D71F.ss</key>
		<integer>590</integer>
		<key>u1D723</key>
		<integer>466</integer>
		<key>u1D723.eb</key>
		<integer>469</integer>
		<key>u1D723.s</key>
		<integer>465</integer>
		<key>u1D723.sb</key>
		<integer>457</integer>
		<key>u1D723.ss</key>
		<integer>465</integer>
		<key>u1D726</key>
		<integer>676</integer>
		<key>u1D726.eb</key>
		<integer>694</integer>
		<key>u1D726.s</key>
		<integer>681</integer>
		<key>u1D726.sb</key>
		<integer>650</integer>
		<key>u1D726.ss</key>
		<integer>682</integer>
		<key>u1D729</key>
		<integer>530</integer>
		<key>u1D729.eb</key>
		<integer>539</integer>
		<key>u1D729.s</key>
		<integer>533</integer>
		<key>u1D729.sb</key>
		<integer>514</integer>
		<key>u1D729.ss</key>
		<integer>534</integer>
		<key>u1D72B</key>
		<integer>536</integer>
		<key>u1D72B.eb</key>
		<integer>537</integer>
		<key>u1D72B.s</key>
		<integer>536</integer>
		<key>u1D72B.sb</key>
		<integer>536</integer>
		<key>u1D72B.ss</key>
		<integer>537</integer>
		<key>u1D72D</key>
		<integer>464</integer>
		<key>u1D72D.eb</key>
		<integer>469</integer>
		<key>u1D72D.s</key>
		<integer>465</integer>
		<key>u1D72D.sb</key>
		<integer>457</integer>
		<key>u1D72D.ss</key>
		<integer>465</integer>
		<key>u1D72E</key>
		<integer>440</integer>
		<key>u1D72E.eb</key>
		<integer>443</integer>
		<key>u1D72E.s</key>
		<integer>440</integer>
		<key>u1D72E.sb</key>
		<integer>434</integer>
		<key>u1D72E.ss</key>
		<integer>441</integer>
		<key>u1D730</key>
		<integer>422</integer>
		<key>u1D730.eb</key>
		<integer>427</integer>
		<key>u1D730.s</key>
		<integer>424</integer>
		<key>u1D730.sb</key>
		<integer>412</integer>
		<key>u1D730.ss</key>
		<integer>424</integer>
		<key>u1D731</key>
		<integer>506</integer>
		<key>u1D731.eb</key>
		<integer>509</integer>
		<key>u1D731.s</key>
		<integer>508</integer>
		<key>u1D731.sb</key>
		<integer>501</integer>
		<key>u1D731.ss</key>
		<integer>508</integer>
		<key>u1D733</key>
		<integer>474</integer>
		<key>u1D733.eb</key>
		<integer>482</integer>
		<key>u1D733.s</key>
		<integer>477</integer>
		<key>u1D733.sb</key>
		<integer>464</integer>
		<key>u1D733.ss</key>
		<integer>477</integer>
		<key>u1D734</key>
		<integer>553</integer>
		<key>u1D734.eb</key>
		<integer>560</integer>
		<key>u1D734.s</key>
		<integer>555</integer>
		<key>u1D734.sb</key>
		<integer>542</integer>
		<key>u1D734.ss</key>
		<integer>555</integer>
		<key>u1D735</key>
		<integer>470</integer>
		<key>u1D735.eb</key>
		<integer>480</integer>
		<key>u1D735.s</key>
		<integer>473</integer>
		<key>u1D735.sb</key>
		<integer>455</integer>
		<key>u1D735.ss</key>
		<integer>474</integer>
		<key>u1D736</key>
		<integer>392</integer>
		<key>u1D736.eb</key>
		<integer>401</integer>
		<key>u1D736.s</key>
		<integer>395</integer>
		<key>u1D736.sb</key>
		<integer>379</integer>
		<key>u1D736.ss</key>
		<integer>395</integer>
		<key>u1D737</key>
		<integer>406</integer>
		<key>u1D737.eb</key>
		<integer>408</integer>
		<key>u1D737.s</key>
		<integer>407</integer>
		<key>u1D737.sb</key>
		<integer>403</integer>
		<key>u1D737.ss</key>
		<integer>407</integer>
		<key>u1D738</key>
		<integer>326</integer>
		<key>u1D738.eb</key>
		<integer>334</integer>
		<key>u1D738.s</key>
		<integer>329</integer>
		<key>u1D738.sb</key>
		<integer>314</integer>
		<key>u1D738.ss</key>
		<integer>330</integer>
		<key>u1D739</key>
		<integer>314</integer>
		<key>u1D739.eb</key>
		<integer>336</integer>
		<key>u1D739.s</key>
		<integer>321</integer>
		<key>u1D739.sb</key>
		<integer>282</integer>
		<key>u1D739.ss</key>
		<integer>323</integer>
		<key>u1D73A</key>
		<integer>291</integer>
		<key>u1D73A.eb</key>
		<integer>293</integer>
		<key>u1D73A.s</key>
		<integer>293</integer>
		<key>u1D73A.sb</key>
		<integer>288</integer>
		<key>u1D73A.ss</key>
		<integer>292</integer>
		<key>u1D73B</key>
		<integer>430</integer>
		<key>u1D73B.eb</key>
		<integer>434</integer>
		<key>u1D73B.s</key>
		<integer>431</integer>
		<key>u1D73B.sb</key>
		<integer>422</integer>
		<key>u1D73B.ss</key>
		<integer>431</integer>
		<key>u1D73C</key>
		<integer>334</integer>
		<key>u1D73C.eb</key>
		<integer>346</integer>
		<key>u1D73C.s</key>
		<integer>338</integer>
		<key>u1D73C.sb</key>
		<integer>317</integer>
		<key>u1D73C.ss</key>
		<integer>338</integer>
		<key>u1D73D</key>
		<integer>363</integer>
		<key>u1D73D.eb</key>
		<integer>368</integer>
		<key>u1D73D.s</key>
		<integer>365</integer>
		<key>u1D73D.sb</key>
		<integer>355</integer>
		<key>u1D73D.ss</key>
		<integer>365</integer>
		<key>u1D73E</key>
		<integer>202</integer>
		<key>u1D73E.eb</key>
		<integer>206</integer>
		<key>u1D73E.s</key>
		<integer>203</integer>
		<key>u1D73E.sb</key>
		<integer>186</integer>
		<key>u1D73E.ss</key>
		<integer>204</integer>
		<key>u1D73F</key>
		<integer>336</integer>
		<key>u1D73F.eb</key>
		<integer>350</integer>
		<key>u1D73F.s</key>
		<integer>341</integer>
		<key>u1D73F.sb</key>
		<integer>315</integer>
		<key>u1D73F.ss</key>
		<integer>342</integer>
		<key>u1D740</key>
		<integer>307</integer>
		<key>u1D740.eb</key>
		<integer>319</integer>
		<key>u1D740.s</key>
		<integer>311</integer>
		<key>u1D740.sb</key>
		<integer>288</integer>
		<key>u1D740.ss</key>
		<integer>311</integer>
		<key>u1D741</key>
		<integer>365</integer>
		<key>u1D741.eb</key>
		<integer>375</integer>
		<key>u1D741.s</key>
		<integer>369</integer>
		<key>u1D741.sb</key>
		<integer>353</integer>
		<key>u1D741.ss</key>
		<integer>370</integer>
		<key>u1D742</key>
		<integer>294</integer>
		<key>u1D742.eb</key>
		<integer>303</integer>
		<key>u1D742.s</key>
		<integer>297</integer>
		<key>u1D742.sb</key>
		<integer>278</integer>
		<key>u1D742.ss</key>
		<integer>298</integer>
		<key>u1D743</key>
		<integer>417</integer>
		<key>u1D743.eb</key>
		<integer>421</integer>
		<key>u1D743.s</key>
		<integer>418</integer>
		<key>u1D743.sb</key>
		<integer>410</integer>
		<key>u1D743.ss</key>
		<integer>419</integer>
		<key>u1D744</key>
		<integer>278</integer>
		<key>u1D744.eb</key>
		<integer>274</integer>
		<key>u1D744.s</key>
		<integer>279</integer>
		<key>u1D744.sb</key>
		<integer>273</integer>
		<key>u1D744.ss</key>
		<integer>280</integer>
		<key>u1D745</key>
		<integer>330</integer>
		<key>u1D745.eb</key>
		<integer>333</integer>
		<key>u1D745.s</key>
		<integer>331</integer>
		<key>u1D745.sb</key>
		<integer>325</integer>
		<key>u1D745.ss</key>
		<integer>332</integer>
		<key>u1D746</key>
		<integer>445</integer>
		<key>u1D746.eb</key>
		<integer>454</integer>
		<key>u1D746.s</key>
		<integer>467</integer>
		<key>u1D746.sb</key>
		<integer>447</integer>
		<key>u1D746.ss</key>
		<integer>468</integer>
		<key>u1D747</key>
		<integer>332</integer>
		<key>u1D747.eb</key>
		<integer>337</integer>
		<key>u1D747.s</key>
		<integer>333</integer>
		<key>u1D747.sb</key>
		<integer>324</integer>
		<key>u1D747.ss</key>
		<integer>333</integer>
		<key>u1D748</key>
		<integer>329</integer>
		<key>u1D748.eb</key>
		<integer>336</integer>
		<key>u1D748.s</key>
		<integer>332</integer>
		<key>u1D748.sb</key>
		<integer>322</integer>
		<key>u1D748.ss</key>
		<integer>332</integer>
		<key>u1D749</key>
		<integer>271</integer>
		<key>u1D749.eb</key>
		<integer>277</integer>
		<key>u1D749.s</key>
		<integer>273</integer>
		<key>u1D749.sb</key>
		<integer>265</integer>
		<key>u1D749.ss</key>
		<integer>273</integer>
		<key>u1D74A</key>
		<integer>294</integer>
		<key>u1D74A.eb</key>
		<integer>301</integer>
		<key>u1D74A.s</key>
		<integer>296</integer>
		<key>u1D74A.sb</key>
		<integer>286</integer>
		<key>u1D74A.ss</key>
		<integer>297</integer>
		<key>u1D74B</key>
		<integer>397</integer>
		<key>u1D74B.eb</key>
		<integer>362</integer>
		<key>u1D74B.s</key>
		<integer>364</integer>
		<key>u1D74B.sb</key>
		<integer>371</integer>
		<key>u1D74B.ss</key>
		<integer>363</integer>
		<key>u1D74C</key>
		<integer>380</integer>
		<key>u1D74C.eb</key>
		<integer>391</integer>
		<key>u1D74C.s</key>
		<integer>383</integer>
		<key>u1D74C.sb</key>
		<integer>365</integer>
		<key>u1D74C.ss</key>
		<integer>384</integer>
		<key>u1D74D</key>
		<integer>439</integer>
		<key>u1D74D.eb</key>
		<integer>443</integer>
		<key>u1D74D.s</key>
		<integer>440</integer>
		<key>u1D74D.sb</key>
		<integer>433</integer>
		<key>u1D74D.ss</key>
		<integer>441</integer>
		<key>u1D74E</key>
		<integer>397</integer>
		<key>u1D74E.eb</key>
		<integer>401</integer>
		<key>u1D74E.s</key>
		<integer>398</integer>
		<key>u1D74E.sb</key>
		<integer>390</integer>
		<key>u1D74E.ss</key>
		<integer>398</integer>
		<key>u1D74F</key>
		<integer>294</integer>
		<key>u1D74F.eb</key>
		<integer>334</integer>
		<key>u1D74F.s</key>
		<integer>325</integer>
		<key>u1D74F.sb</key>
		<integer>274</integer>
		<key>u1D74F.ss</key>
		<integer>299</integer>
		<key>u1D750</key>
		<integer>304</integer>
		<key>u1D750.eb</key>
		<integer>305</integer>
		<key>u1D750.s</key>
		<integer>304</integer>
		<key>u1D750.sb</key>
		<integer>300</integer>
		<key>u1D750.ss</key>
		<integer>305</integer>
		<key>u1D751</key>
		<integer>342</integer>
		<key>u1D751.eb</key>
		<integer>339</integer>
		<key>u1D751.s</key>
		<integer>341</integer>
		<key>u1D751.sb</key>
		<integer>347</integer>
		<key>u1D751.ss</key>
		<integer>341</integer>
		<key>u1D752</key>
		<integer>347</integer>
		<key>u1D752.eb</key>
		<integer>355</integer>
		<key>u1D752.s</key>
		<integer>351</integer>
		<key>u1D752.sb</key>
		<integer>337</integer>
		<key>u1D752.ss</key>
		<integer>351</integer>
		<key>u1D753</key>
		<integer>414</integer>
		<key>u1D753.eb</key>
		<integer>419</integer>
		<key>u1D753.s</key>
		<integer>416</integer>
		<key>u1D753.sb</key>
		<integer>409</integer>
		<key>u1D753.ss</key>
		<integer>417</integer>
		<key>u1D754</key>
		<integer>319</integer>
		<key>u1D754.eb</key>
		<integer>324</integer>
		<key>u1D754.s</key>
		<integer>339</integer>
		<key>u1D754.sb</key>
		<integer>325</integer>
		<key>u1D754.ss</key>
		<integer>340</integer>
		<key>u1D755</key>
		<integer>338</integer>
		<key>u1D755.eb</key>
		<integer>346</integer>
		<key>u1D755.s</key>
		<integer>341</integer>
		<key>u1D755.sb</key>
		<integer>352</integer>
		<key>u1D755.ss</key>
		<integer>341</integer>
		<key>u1D758</key>
		<integer>292</integer>
		<key>u1D759</key>
		<integer>338</integer>
		<key>u1D75D</key>
		<integer>392</integer>
		<key>u1D75E</key>
		<integer>149</integer>
		<key>u1D760</key>
		<integer>335</integer>
		<key>u1D763</key>
		<integer>321</integer>
		<key>u1D765</key>
		<integer>369</integer>
		<key>u1D767</key>
		<integer>392</integer>
		<key>u1D768</key>
		<integer>272</integer>
		<key>u1D76A</key>
		<integer>303</integer>
		<key>u1D76B</key>
		<integer>355</integer>
		<key>u1D76D</key>
		<integer>373</integer>
		<key>u1D76E</key>
		<integer>374</integer>
		<key>u1D7CE</key>
		<integer>247</integer>
		<key>u1D7CE.eb</key>
		<integer>249</integer>
		<key>u1D7CE.prp</key>
		<integer>290</integer>
		<key>u1D7CE.prp.eb</key>
		<integer>297</integer>
		<key>u1D7CE.prp.s</key>
		<integer>292</integer>
		<key>u1D7CE.prp.sb</key>
		<integer>279</integer>
		<key>u1D7CE.prp.ss</key>
		<integer>295</integer>
		<key>u1D7CE.s</key>
		<integer>247</integer>
		<key>u1D7CE.sb</key>
		<integer>247</integer>
		<key>u1D7CE.ss</key>
		<integer>248</integer>
		<key>u1D7CF</key>
		<integer>253</integer>
		<key>u1D7CF.eb</key>
		<integer>253</integer>
		<key>u1D7CF.prp</key>
		<integer>197</integer>
		<key>u1D7CF.prp.eb</key>
		<integer>204</integer>
		<key>u1D7CF.prp.s</key>
		<integer>207</integer>
		<key>u1D7CF.prp.sb</key>
		<integer>186</integer>
		<key>u1D7CF.prp.ss</key>
		<integer>209</integer>
		<key>u1D7CF.s</key>
		<integer>252</integer>
		<key>u1D7CF.sb</key>
		<integer>253</integer>
		<key>u1D7CF.ss</key>
		<integer>252</integer>
		<key>u1D7D0</key>
		<integer>235</integer>
		<key>u1D7D0.eb</key>
		<integer>234</integer>
		<key>u1D7D0.prp</key>
		<integer>233</integer>
		<key>u1D7D0.prp.eb</key>
		<integer>235</integer>
		<key>u1D7D0.prp.s</key>
		<integer>234</integer>
		<key>u1D7D0.prp.sb</key>
		<integer>229</integer>
		<key>u1D7D0.prp.ss</key>
		<integer>235</integer>
		<key>u1D7D0.s</key>
		<integer>235</integer>
		<key>u1D7D0.sb</key>
		<integer>234</integer>
		<key>u1D7D0.ss</key>
		<integer>235</integer>
		<key>u1D7D1</key>
		<integer>233</integer>
		<key>u1D7D1.eb</key>
		<integer>233</integer>
		<key>u1D7D1.prp</key>
		<integer>219</integer>
		<key>u1D7D1.prp.eb</key>
		<integer>221</integer>
		<key>u1D7D1.prp.s</key>
		<integer>220</integer>
		<key>u1D7D1.prp.sb</key>
		<integer>215</integer>
		<key>u1D7D1.prp.ss</key>
		<integer>221</integer>
		<key>u1D7D1.s</key>
		<integer>233</integer>
		<key>u1D7D1.sb</key>
		<integer>232</integer>
		<key>u1D7D1.ss</key>
		<integer>233</integer>
		<key>u1D7D2</key>
		<integer>318</integer>
		<key>u1D7D2.eb</key>
		<integer>317</integer>
		<key>u1D7D2.prp</key>
		<integer>345</integer>
		<key>u1D7D2.prp.eb</key>
		<integer>351</integer>
		<key>u1D7D2.prp.s</key>
		<integer>347</integer>
		<key>u1D7D2.prp.sb</key>
		<integer>335</integer>
		<key>u1D7D2.prp.ss</key>
		<integer>349</integer>
		<key>u1D7D2.s</key>
		<integer>317</integer>
		<key>u1D7D2.sb</key>
		<integer>317</integer>
		<key>u1D7D2.ss</key>
		<integer>317</integer>
		<key>u1D7D3</key>
		<integer>238</integer>
		<key>u1D7D3.eb</key>
		<integer>267</integer>
		<key>u1D7D3.prp</key>
		<integer>245</integer>
		<key>u1D7D3.prp.eb</key>
		<integer>248</integer>
		<key>u1D7D3.prp.s</key>
		<integer>246</integer>
		<key>u1D7D3.prp.sb</key>
		<integer>241</integer>
		<key>u1D7D3.prp.ss</key>
		<integer>248</integer>
		<key>u1D7D3.s</key>
		<integer>267</integer>
		<key>u1D7D3.sb</key>
		<integer>267</integer>
		<key>u1D7D3.ss</key>
		<integer>267</integer>
		<key>u1D7D4</key>
		<integer>322</integer>
		<key>u1D7D4.eb</key>
		<integer>322</integer>
		<key>u1D7D4.prp</key>
		<integer>334</integer>
		<key>u1D7D4.prp.eb</key>
		<integer>338</integer>
		<key>u1D7D4.prp.s</key>
		<integer>335</integer>
		<key>u1D7D4.prp.sb</key>
		<integer>329</integer>
		<key>u1D7D4.prp.ss</key>
		<integer>337</integer>
		<key>u1D7D4.s</key>
		<integer>321</integer>
		<key>u1D7D4.sb</key>
		<integer>322</integer>
		<key>u1D7D4.ss</key>
		<integer>322</integer>
		<key>u1D7D5</key>
		<integer>266</integer>
		<key>u1D7D5.eb</key>
		<integer>268</integer>
		<key>u1D7D5.prp</key>
		<integer>271</integer>
		<key>u1D7D5.prp.eb</key>
		<integer>279</integer>
		<key>u1D7D5.prp.s</key>
		<integer>274</integer>
		<key>u1D7D5.prp.sb</key>
		<integer>261</integer>
		<key>u1D7D5.prp.ss</key>
		<integer>278</integer>
		<key>u1D7D5.s</key>
		<integer>267</integer>
		<key>u1D7D5.sb</key>
		<integer>262</integer>
		<key>u1D7D5.ss</key>
		<integer>268</integer>
		<key>u1D7D6</key>
		<integer>252</integer>
		<key>u1D7D6.eb</key>
		<integer>252</integer>
		<key>u1D7D6.prp</key>
		<integer>270</integer>
		<key>u1D7D6.prp.eb</key>
		<integer>276</integer>
		<key>u1D7D6.prp.s</key>
		<integer>272</integer>
		<key>u1D7D6.prp.sb</key>
		<integer>260</integer>
		<key>u1D7D6.prp.ss</key>
		<integer>275</integer>
		<key>u1D7D6.s</key>
		<integer>252</integer>
		<key>u1D7D6.sb</key>
		<integer>252</integer>
		<key>u1D7D6.ss</key>
		<integer>253</integer>
		<key>u1D7D7</key>
		<integer>257</integer>
		<key>u1D7D7.eb</key>
		<integer>260</integer>
		<key>u1D7D7.prp</key>
		<integer>278</integer>
		<key>u1D7D7.prp.eb</key>
		<integer>288</integer>
		<key>u1D7D7.prp.s</key>
		<integer>283</integer>
		<key>u1D7D7.prp.sb</key>
		<integer>266</integer>
		<key>u1D7D7.prp.ss</key>
		<integer>286</integer>
		<key>u1D7D7.s</key>
		<integer>258</integer>
		<key>u1D7D7.sb</key>
		<integer>253</integer>
		<key>u1D7D7.ss</key>
		<integer>259</integer>
		<key>u1D7D8</key>
		<integer>247</integer>
		<key>u1D7D8.gm</key>
		<integer>244</integer>
		<key>u1D7D9</key>
		<integer>254</integer>
		<key>u1D7D9.gm</key>
		<integer>273</integer>
		<key>u1D7DA</key>
		<integer>226</integer>
		<key>u1D7DA.gm</key>
		<integer>217</integer>
		<key>u1D7DB</key>
		<integer>217</integer>
		<key>u1D7DB.gm</key>
		<integer>217</integer>
		<key>u1D7DC</key>
		<integer>315</integer>
		<key>u1D7DC.gm</key>
		<integer>375</integer>
		<key>u1D7DD</key>
		<integer>260</integer>
		<key>u1D7DD.gm</key>
		<integer>297</integer>
		<key>u1D7DE</key>
		<integer>333</integer>
		<key>u1D7DE.gm</key>
		<integer>305</integer>
		<key>u1D7DF</key>
		<integer>261</integer>
		<key>u1D7DF.gm</key>
		<integer>267</integer>
		<key>u1D7E0</key>
		<integer>252</integer>
		<key>u1D7E0.gm</key>
		<integer>245</integer>
		<key>u1D7E1</key>
		<integer>241</integer>
		<key>u1D7E1.gm</key>
		<integer>240</integer>
		<key>u1D7E2</key>
		<integer>250</integer>
		<key>u1D7E3</key>
		<integer>278</integer>
		<key>u1D7E4</key>
		<integer>230</integer>
		<key>u1D7E5</key>
		<integer>227</integer>
		<key>u1D7E6</key>
		<integer>330</integer>
		<key>u1D7E7</key>
		<integer>258</integer>
		<key>u1D7E8</key>
		<integer>322</integer>
		<key>u1D7E9</key>
		<integer>238</integer>
		<key>u1D7EA</key>
		<integer>252</integer>
		<key>u1D7EB</key>
		<integer>233</integer>
		<key>u1D7EC</key>
		<integer>247</integer>
		<key>u1D7ED</key>
		<integer>274</integer>
		<key>u1D7EE</key>
		<integer>220</integer>
		<key>u1D7EF</key>
		<integer>223</integer>
		<key>u1D7F0</key>
		<integer>310</integer>
		<key>u1D7F1</key>
		<integer>255</integer>
		<key>u1D7F2</key>
		<integer>345</integer>
		<key>u1D7F3</key>
		<integer>242</integer>
		<key>u1D7F4</key>
		<integer>250</integer>
		<key>u1D7F5</key>
		<integer>241</integer>
		<key>u1D7F6</key>
		<integer>261</integer>
		<key>u1D7F7</key>
		<integer>277</integer>
		<key>u1D7F8</key>
		<integer>254</integer>
		<key>u1D7F9</key>
		<integer>254</integer>
		<key>u1D7FA</key>
		<integer>322</integer>
		<key>u1D7FB</key>
		<integer>263</integer>
		<key>u1D7FC</key>
		<integer>317</integer>
		<key>u1D7FD</key>
		<integer>263</integer>
		<key>u1D7FE</key>
		<integer>261</integer>
		<key>u1D7FF</key>
		<integer>259</integer>
		<key>uni03C2</key>
		<integer>249</integer>
		<key>uni03C2.s</key>
		<integer>250</integer>
		<key>uni03C2.ss</key>
		<integer>250</integer>
		<key>uni03D1</key>
		<integer>244</integer>
		<key>uni03D1.s</key>
		<integer>243</integer>
		<key>uni03D1.ss</key>
		<integer>242</integer>
		<key>uni03D5</key>
		<integer>260</integer>
		<key>uni03D5.s</key>
		<integer>260</integer>
		<key>uni03D5.ss</key>
		<integer>262</integer>
		<key>uni03D6</key>
		<integer>270</integer>
		<key>uni03D6.s</key>
		<integer>267</integer>
		<key>uni03D6.ss</key>
		<integer>265</integer>
		<key>uni03F0</key>
		<integer>264</integer>
		<key>uni03F0.s</key>
		<integer>265</integer>
		<key>uni03F0.ss</key>
		<integer>267</integer>
		<key>uni03F1</key>
		<integer>236</integer>
		<key>uni03F1.s</key>
		<integer>235</integer>
		<key>uni03F1.ss</key>
		<integer>233</integer>
		<key>uni03F4</key>
		<integer>382</integer>
		<key>uni03F4.s</key>
		<integer>381</integer>
		<key>uni03F4.ss</key>
		<integer>381</integer>
		<key>uni03F5</key>
		<integer>232</integer>
		<key>uni03F5.s</key>
		<integer>231</integer>
		<key>uni03F5.ss</key>
		<integer>230</integer>
		<key>uni2102</key>
		<integer>434</integer>
		<key>uni2102.gm</key>
		<integer>375</integer>
		<key>uni210B</key>
		<integer>608</integer>
		<key>uni210B.c</key>
		<integer>546</integer>
		<key>uni210B.gm</key>
		<integer>550</integer>
		<key>uni210C</key>
		<integer>308</integer>
		<key>uni210D</key>
		<integer>417</integer>
		<key>uni210D.gm</key>
		<integer>361</integer>
		<key>uni210E</key>
		<integer>239</integer>
		<key>uni210E.s</key>
		<integer>243</integer>
		<key>uni210E.ss</key>
		<integer>246</integer>
		<key>uni210E.var</key>
		<integer>239</integer>
		<key>uni210E.var.s</key>
		<integer>243</integer>
		<key>uni210E.var.ss</key>
		<integer>246</integer>
		<key>uni210F</key>
		<integer>243</integer>
		<key>uni210F.bar</key>
		<integer>239</integer>
		<key>uni210F.bar.s</key>
		<integer>243</integer>
		<key>uni210F.bar.ss</key>
		<integer>246</integer>
		<key>uni210F.s</key>
		<integer>246</integer>
		<key>uni210F.ss</key>
		<integer>250</integer>
		<key>uni210F.var</key>
		<integer>244</integer>
		<key>uni210F.var.bar</key>
		<integer>240</integer>
		<key>uni210F.var.bar.s</key>
		<integer>243</integer>
		<key>uni210F.var.bar.ss</key>
		<integer>247</integer>
		<key>uni210F.var.s</key>
		<integer>246</integer>
		<key>uni210F.var.ss</key>
		<integer>250</integer>
		<key>uni2110</key>
		<integer>660</integer>
		<key>uni2110.c</key>
		<integer>407</integer>
		<key>uni2110.gm</key>
		<integer>430</integer>
		<key>uni2112</key>
		<integer>751</integer>
		<key>uni2112.c</key>
		<integer>480</integer>
		<key>uni2112.gm</key>
		<integer>481</integer>
		<key>uni2113</key>
		<integer>327</integer>
		<key>uni2115</key>
		<integer>393</integer>
		<key>uni2115.gm</key>
		<integer>351</integer>
		<key>uni2119</key>
		<integer>285</integer>
		<key>uni2119.gm</key>
		<integer>320</integer>
		<key>uni211A</key>
		<integer>405</integer>
		<key>uni211A.gm</key>
		<integer>384</integer>
		<key>uni211B</key>
		<integer>682</integer>
		<key>uni211B.c</key>
		<integer>416</integer>
		<key>uni211B.gm</key>
		<integer>437</integer>
		<key>uni211D</key>
		<integer>285</integer>
		<key>uni211D.gm</key>
		<integer>320</integer>
		<key>uni2124</key>
		<integer>322</integer>
		<key>uni2124.gm</key>
		<integer>356</integer>
		<key>uni2128</key>
		<integer>332</integer>
		<key>uni212C</key>
		<integer>722</integer>
		<key>uni212C.c</key>
		<integer>401</integer>
		<key>uni212C.gm</key>
		<integer>507</integer>
		<key>uni212D</key>
		<integer>330</integer>
		<key>uni2130</key>
		<integer>567</integer>
		<key>uni2130.c</key>
		<integer>445</integer>
		<key>uni2130.gm</key>
		<integer>383</integer>
		<key>uni2131</key>
		<integer>666</integer>
		<key>uni2131.c</key>
		<integer>547</integer>
		<key>uni2131.gm</key>
		<integer>580</integer>
		<key>uni2133</key>
		<integer>772</integer>
		<key>uni2133.c</key>
		<integer>702</integer>
		<key>uni2133.gm</key>
		<integer>864</integer>
		<key>uni213C</key>
		<integer>295</integer>
		<key>uni213C.gm</key>
		<integer>289</integer>
		<key>uni213D</key>
		<integer>292</integer>
		<key>uni213D.gm</key>
		<integer>324</integer>
		<key>uni213E</key>
		<integer>282</integer>
		<key>uni213E.gm</key>
		<integer>323</integer>
		<key>uni213F</key>
		<integer>415</integer>
		<key>uni213F.gm</key>
		<integer>361</integer>
		<key>uni2145</key>
		<integer>394</integer>
		<key>uni2145.gm</key>
		<integer>381</integer>
		<key>uni2146</key>
		<integer>528</integer>
		<key>uni2146.gm</key>
		<integer>565</integer>
		<key>uni2147</key>
		<integer>321</integer>
		<key>uni2147.gm</key>
		<integer>309</integer>
		<key>uni2148</key>
		<integer>320</integer>
		<key>uni2148.gm</key>
		<integer>271</integer>
		<key>uni2149</key>
		<integer>403</integer>
		<key>uni2149.gm</key>
		<integer>341</integer>
		<key>upsilon</key>
		<integer>222</integer>
		<key>upsilon.s</key>
		<integer>226</integer>
		<key>upsilon.ss</key>
		<integer>231</integer>
		<key>v</key>
		<integer>224</integer>
		<key>v.s</key>
		<integer>227</integer>
		<key>v.ss</key>
		<integer>230</integer>
		<key>w</key>
		<integer>347</integer>
		<key>w.s</key>
		<integer>351</integer>
		<key>w.ss</key>
		<integer>355</integer>
		<key>x</key>
		<integer>221</integer>
		<key>x.s</key>
		<integer>225</integer>
		<key>x.ss</key>
		<integer>229</integer>
		<key>xi</key>
		<integer>230</integer>
		<key>xi.s</key>
		<integer>229</integer>
		<key>xi.ss</key>
		<integer>229</integer>
		<key>y</key>
		<integer>224</integer>
		<key>y.s</key>
		<integer>226</integer>
		<key>y.ss</key>
		<integer>229</integer>
		<key>z</key>
		<integer>210</integer>
		<key>z.s</key>
		<integer>212</integer>
		<key>z.ss</key>
		<integer>216</integer>
		<key>zero</key>
		<integer>248</integer>
		<key>zero.prp</key>
		<integer>256</integer>
		<key>zero.prp.s</key>
		<integer>260</integer>
		<key>zero.prp.ss</key>
		<integer>265</integer>
		<key>zero.s</key>
		<integer>247</integer>
		<key>zero.ss</key>
		<integer>247</integer>
		<key>zeta</key>
		<integer>226</integer>
		<key>zeta.s</key>
		<integer>229</integer>
		<key>zeta.ss</key>
		<integer>230</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>450</integer>
		<key>AxisHeight</key>
		<integer>250</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1300</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1300</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>420</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>598</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>100</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>266</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>634</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>100</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>430</integer>
		<key>FractionRuleThickness</key>
		<integer>50</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>433</integer>
		<key>LowerLimitGapMin</key>
		<integer>100</integer>
		<key>MathLeading</key>
		<integer>140</integer>
		<key>MinConnectorOverlap</key>
		<integer>26</integer>
		<key>OverbarExtraAscender</key>
		<integer>50</integer>
		<key>OverbarRuleThickness</key>
		<integer>50</integer>
		<key>OverbarVerticalGap</key>
		<integer>150</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>60</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>150</integer>
		<key>RadicalExtraAscender</key>
		<integer>50</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-355</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>40</integer>
		<key>RadicalRuleThickness</key>
		<integer>50</integer>
		<key>RadicalVerticalGap</key>
		<integer>65</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>70</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>50</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>300</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>70</integer>
		<key>SpaceAfterScript</key>
		<integer>50</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>598</integer>
		<key>StackBottomShiftDown</key>
		<integer>266</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>350</integer>
		<key>StackGapMin</key>
		<integer>150</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>634</integer>
		<key>StackTopShiftUp</key>
		<integer>430</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>433</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>100</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>100</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>100</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>250</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>150</integer>
		<key>SubscriptShiftDown</key>
		<integer>250</integer>
		<key>SubscriptTopMax</key>
		<integer>320</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>230</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>350</integer>
		<key>SuperscriptBottomMin</key>
		<integer>150</integer>
		<key>SuperscriptShiftUp</key>
		<integer>430</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>300</integer>
		<key>UnderbarExtraDescender</key>
		<integer>50</integer>
		<key>UnderbarRuleThickness</key>
		<integer>50</integer>
		<key>UnderbarVerticalGap</key>
		<integer>150</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>100</integer>
		<key>UpperLimitGapMin</key>
		<integer>100</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>arrowboth</key>
		<array/>
		<key>arrowdblboth</key>
		<array/>
		<key>arrowdblleft</key>
		<array/>
		<key>arrowdblright</key>
		<array/>
		<key>arrowleft</key>
		<array/>
		<key>arrowright</key>
		<array/>
		<key>brevebelowcmb</key>
		<array>
			<string>brevebelowcmb</string>
			<string>brevebelowcmb.h1</string>
			<string>brevebelowcmb.h2</string>
			<string>brevebelowcmb.h3</string>
			<string>brevebelowcmb.h4</string>
			<string>brevebelowcmb.h5</string>
			<string>brevebelowcmb.h6</string>
		</array>
		<key>brevecmb</key>
		<array>
			<string>brevecmb</string>
			<string>brevecmb.h1</string>
			<string>brevecmb.h2</string>
			<string>brevecmb.h3</string>
			<string>brevecmb.h4</string>
			<string>brevecmb.h5</string>
			<string>brevecmb.h6</string>
		</array>
		<key>breveinvertedbelowcmb</key>
		<array>
			<string>breveinvertedbelowcmb</string>
			<string>breveinvertedbelowcmb.h1</string>
			<string>breveinvertedbelowcmb.h2</string>
			<string>breveinvertedbelowcmb.h3</string>
			<string>breveinvertedbelowcmb.h4</string>
			<string>breveinvertedbelowcmb.h5</string>
			<string>breveinvertedbelowcmb.h6</string>
		</array>
		<key>breveinvertedcmb</key>
		<array>
			<string>breveinvertedcmb</string>
			<string>breveinvertedcmb.h1</string>
			<string>breveinvertedcmb.h2</string>
			<string>breveinvertedcmb.h3</string>
			<string>breveinvertedcmb.h4</string>
			<string>breveinvertedcmb.h5</string>
			<string>breveinvertedcmb.h6</string>
		</array>
		<key>caronbelowcmb</key>
		<array>
			<string>caronbelowcmb</string>
			<string>caronbelowcmb.h1</string>
			<string>caronbelowcmb.h2</string>
			<string>caronbelowcmb.h3</string>
			<string>caronbelowcmb.h4</string>
			<string>caronbelowcmb.h5</string>
			<string>caronbelowcmb.h6</string>
			<string>caronbelowcmb.h7</string>
			<string>caronbelowcmb.h8</string>
			<string>caronbelowcmb.h9</string>
			<string>caronbelowcmb.h10</string>
			<string>caronbelowcmb.h11</string>
			<string>caronbelowcmb.h12</string>
			<string>caronbelowcmb.h13</string>
			<string>caronbelowcmb.h14</string>
			<string>caronbelowcmb.h15</string>
			<string>caronbelowcmb.h16</string>
			<string>caronbelowcmb.h17</string>
			<string>caronbelowcmb.h18</string>
			<string>caronbelowcmb.h19</string>
			<string>caronbelowcmb.h20</string>
			<string>caronbelowcmb.h21</string>
			<string>caronbelowcmb.h22</string>
			<string>caronbelowcmb.h23</string>
			<string>caronbelowcmb.h24</string>
			<string>caronbelowcmb.h25</string>
			<string>caronbelowcmb.h26</string>
			<string>caronbelowcmb.h27</string>
			<string>caronbelowcmb.h28</string>
			<string>caronbelowcmb.h29</string>
			<string>caronbelowcmb.h30</string>
			<string>caronbelowcmb.h31</string>
		</array>
		<key>caroncmb</key>
		<array>
			<string>caroncmb</string>
			<string>caroncmb.h1</string>
			<string>caroncmb.h2</string>
			<string>caroncmb.h3</string>
			<string>caroncmb.h4</string>
			<string>caroncmb.h5</string>
			<string>caroncmb.h6</string>
			<string>caroncmb.h7</string>
			<string>caroncmb.h8</string>
			<string>caroncmb.h9</string>
			<string>caroncmb.h10</string>
			<string>caroncmb.h11</string>
			<string>caroncmb.h12</string>
			<string>caroncmb.h13</string>
			<string>caroncmb.h14</string>
			<string>caroncmb.h15</string>
			<string>caroncmb.h16</string>
			<string>caroncmb.h17</string>
			<string>caroncmb.h18</string>
			<string>caroncmb.h19</string>
			<string>caroncmb.h20</string>
			<string>caroncmb.h21</string>
			<string>caroncmb.h22</string>
			<string>caroncmb.h23</string>
			<string>caroncmb.h24</string>
			<string>caroncmb.h25</string>
			<string>caroncmb.h26</string>
			<string>caroncmb.h27</string>
			<string>caroncmb.h28</string>
			<string>caroncmb.h29</string>
			<string>caroncmb.h30</string>
			<string>caroncmb.h31</string>
		</array>
		<key>circumflexbelowcmb</key>
		<array>
			<string>circumflexbelowcmb</string>
			<string>circumflexbelowcmb.h1</string>
			<string>circumflexbelowcmb.h2</string>
			<string>circumflexbelowcmb.h3</string>
			<string>circumflexbelowcmb.h4</string>
			<string>circumflexbelowcmb.h5</string>
			<string>circumflexbelowcmb.h6</string>
			<string>circumflexbelowcmb.h7</string>
			<string>circumflexbelowcmb.h8</string>
			<string>circumflexbelowcmb.h9</string>
			<string>circumflexbelowcmb.h10</string>
			<string>circumflexbelowcmb.h11</string>
			<string>circumflexbelowcmb.h12</string>
			<string>circumflexbelowcmb.h13</string>
			<string>circumflexbelowcmb.h14</string>
			<string>circumflexbelowcmb.h15</string>
			<string>circumflexbelowcmb.h16</string>
			<string>circumflexbelowcmb.h17</string>
			<string>circumflexbelowcmb.h18</string>
			<string>circumflexbelowcmb.h19</string>
			<string>circumflexbelowcmb.h20</string>
			<string>circumflexbelowcmb.h21</string>
			<string>circumflexbelowcmb.h22</string>
			<string>circumflexbelowcmb.h23</string>
			<string>circumflexbelowcmb.h24</string>
			<string>circumflexbelowcmb.h25</string>
			<string>circumflexbelowcmb.h26</string>
			<string>circumflexbelowcmb.h27</string>
			<string>circumflexbelowcmb.h28</string>
			<string>circumflexbelowcmb.h29</string>
			<string>circumflexbelowcmb.h30</string>
			<string>circumflexbelowcmb.h31</string>
		</array>
		<key>circumflexcmb</key>
		<array>
			<string>circumflexcmb</string>
			<string>circumflexcmb.h1</string>
			<string>circumflexcmb.h2</string>
			<string>circumflexcmb.h3</string>
			<string>circumflexcmb.h4</string>
			<string>circumflexcmb.h5</string>
			<string>circumflexcmb.h6</string>
			<string>circumflexcmb.h7</string>
			<string>circumflexcmb.h8</string>
			<string>circumflexcmb.h9</string>
			<string>circumflexcmb.h10</string>
			<string>circumflexcmb.h11</string>
			<string>circumflexcmb.h12</string>
			<string>circumflexcmb.h13</string>
			<string>circumflexcmb.h14</string>
			<string>circumflexcmb.h15</string>
			<string>circumflexcmb.h16</string>
			<string>circumflexcmb.h17</string>
			<string>circumflexcmb.h18</string>
			<string>circumflexcmb.h19</string>
			<string>circumflexcmb.h20</string>
			<string>circumflexcmb.h21</string>
			<string>circumflexcmb.h22</string>
			<string>circumflexcmb.h23</string>
			<string>circumflexcmb.h24</string>
			<string>circumflexcmb.h25</string>
			<string>circumflexcmb.h26</string>
			<string>circumflexcmb.h27</string>
			<string>circumflexcmb.h28</string>
			<string>circumflexcmb.h29</string>
			<string>circumflexcmb.h30</string>
			<string>circumflexcmb.h31</string>
		</array>
		<key>equal</key>
		<array/>
		<key>equal.large</key>
		<array/>
		<key>tildebelowcmb</key>
		<array>
			<string>tildebelowcmb</string>
			<string>tildebelowcmb.h1</string>
			<string>tildebelowcmb.h2</string>
			<string>tildebelowcmb.h3</string>
			<string>tildebelowcmb.h4</string>
			<string>tildebelowcmb.h5</string>
			<string>tildebelowcmb.h6</string>
			<string>tildebelowcmb.h7</string>
			<string>tildebelowcmb.h8</string>
			<string>tildebelowcmb.h9</string>
			<string>tildebelowcmb.h10</string>
			<string>tildebelowcmb.h11</string>
			<string>tildebelowcmb.h12</string>
			<string>tildebelowcmb.h13</string>
			<string>tildebelowcmb.h14</string>
			<string>tildebelowcmb.h15</string>
			<string>tildebelowcmb.h16</string>
			<string>tildebelowcmb.h17</string>
			<string>tildebelowcmb.h18</string>
			<string>tildebelowcmb.h19</string>
			<string>tildebelowcmb.h20</string>
			<string>tildebelowcmb.h21</string>
			<string>tildebelowcmb.h22</string>
			<string>tildebelowcmb.h23</string>
			<string>tildebelowcmb.h24</string>
			<string>tildebelowcmb.h25</string>
			<string>tildebelowcmb.h26</string>
			<string>tildebelowcmb.h27</string>
			<string>tildebelowcmb.h28</string>
			<string>tildebelowcmb.h29</string>
			<string>tildebelowcmb.h30</string>
			<string>tildebelowcmb.h31</string>
		</array>
		<key>tildebelowcmb.norm</key>
		<array>
			<string>tildebelowcmb</string>
			<string>tildebelowcmb.h1</string>
			<string>tildebelowcmb.h2</string>
			<string>tildebelowcmb.h3</string>
			<string>tildebelowcmb.h4</string>
			<string>tildebelowcmb.h5</string>
			<string>tildebelowcmb.h6</string>
			<string>tildebelowcmb.h7</string>
			<string>tildebelowcmb.h8</string>
			<string>tildebelowcmb.h9</string>
			<string>tildebelowcmb.h10</string>
			<string>tildebelowcmb.h11</string>
			<string>tildebelowcmb.h12</string>
			<string>tildebelowcmb.h13</string>
			<string>tildebelowcmb.h14</string>
			<string>tildebelowcmb.h15</string>
			<string>tildebelowcmb.h16</string>
			<string>tildebelowcmb.h17</string>
			<string>tildebelowcmb.h18</string>
			<string>tildebelowcmb.h19</string>
			<string>tildebelowcmb.h20</string>
			<string>tildebelowcmb.h21</string>
			<string>tildebelowcmb.h22</string>
			<string>tildebelowcmb.h23</string>
			<string>tildebelowcmb.h24</string>
			<string>tildebelowcmb.h25</string>
			<string>tildebelowcmb.h26</string>
			<string>tildebelowcmb.h27</string>
			<string>tildebelowcmb.h28</string>
			<string>tildebelowcmb.h29</string>
			<string>tildebelowcmb.h30</string>
			<string>tildebelowcmb.h31</string>
		</array>
		<key>tildecomb</key>
		<array>
			<string>tildecomb</string>
			<string>tildecomb.h1</string>
			<string>tildecomb.h2</string>
			<string>tildecomb.h3</string>
			<string>tildecomb.h4</string>
			<string>tildecomb.h5</string>
			<string>tildecomb.h6</string>
			<string>tildecomb.h7</string>
			<string>tildecomb.h8</string>
			<string>tildecomb.h9</string>
			<string>tildecomb.h10</string>
			<string>tildecomb.h11</string>
			<string>tildecomb.h12</string>
			<string>tildecomb.h13</string>
			<string>tildecomb.h14</string>
			<string>tildecomb.h15</string>
			<string>tildecomb.h16</string>
			<string>tildecomb.h17</string>
			<string>tildecomb.h18</string>
			<string>tildecomb.h19</string>
			<string>tildecomb.h20</string>
			<string>tildecomb.h21</string>
			<string>tildecomb.h22</string>
			<string>tildecomb.h23</string>
			<string>tildecomb.h24</string>
			<string>tildecomb.h25</string>
			<string>tildecomb.h26</string>
			<string>tildecomb.h27</string>
			<string>tildecomb.h28</string>
			<string>tildecomb.h29</string>
			<string>tildecomb.h30</string>
			<string>tildecomb.h31</string>
		</array>
		<key>tildecomb.norm</key>
		<array>
			<string>tildecomb.norm</string>
			<string>tildecomb.h1</string>
			<string>tildecomb.h2</string>
			<string>tildecomb.h3</string>
			<string>tildecomb.h4</string>
			<string>tildecomb.h5</string>
			<string>tildecomb.h6</string>
			<string>tildecomb.h7</string>
			<string>tildecomb.h8</string>
			<string>tildecomb.h9</string>
			<string>tildecomb.h10</string>
			<string>tildecomb.h11</string>
			<string>tildecomb.h12</string>
			<string>tildecomb.h13</string>
			<string>tildecomb.h14</string>
			<string>tildecomb.h15</string>
			<string>tildecomb.h16</string>
			<string>tildecomb.h17</string>
			<string>tildecomb.h18</string>
			<string>tildecomb.h19</string>
			<string>tildecomb.h20</string>
			<string>tildecomb.h21</string>
			<string>tildecomb.h22</string>
			<string>tildecomb.h23</string>
			<string>tildecomb.h24</string>
			<string>tildecomb.h25</string>
			<string>tildecomb.h26</string>
			<string>tildecomb.h27</string>
			<string>tildecomb.h28</string>
			<string>tildecomb.h29</string>
			<string>tildecomb.h30</string>
			<string>tildecomb.h31</string>
		</array>
		<key>uni034D</key>
		<array/>
		<key>uni20D0</key>
		<array/>
		<key>uni20D1</key>
		<array/>
		<key>uni20D6</key>
		<array/>
		<key>uni20D7</key>
		<array/>
		<key>uni20E1</key>
		<array/>
		<key>uni20E9</key>
		<array>
			<string>uni20E9</string>
			<string>uni23B4.h1</string>
			<string>uni23B4.h2</string>
			<string>uni23B4.h3</string>
			<string>uni23B4.h4</string>
			<string>uni23B4.h5</string>
			<string>uni23B4.h6</string>
			<string>uni23B4.h7</string>
			<string>uni23B4.h8</string>
			<string>uni23B4.h9</string>
			<string>uni23B4.h10</string>
			<string>uni23B4.h11</string>
			<string>uni23B4.h12</string>
			<string>uni23B4.h13</string>
			<string>uni23B4.h14</string>
			<string>uni23B4.h15</string>
			<string>uni23B4.h16</string>
			<string>uni23B4.h17</string>
			<string>uni23B4.h18</string>
			<string>uni23B4.h19</string>
			<string>uni23B4.h20</string>
		</array>
		<key>uni20EC</key>
		<array/>
		<key>uni20ED</key>
		<array/>
		<key>uni20EE</key>
		<array/>
		<key>uni20EF</key>
		<array/>
		<key>uni23B4</key>
		<array>
			<string>uni23B4</string>
			<string>uni23B4.h1</string>
			<string>uni23B4.h2</string>
			<string>uni23B4.h3</string>
			<string>uni23B4.h4</string>
			<string>uni23B4.h5</string>
			<string>uni23B4.h6</string>
			<string>uni23B4.h7</string>
			<string>uni23B4.h8</string>
			<string>uni23B4.h9</string>
			<string>uni23B4.h10</string>
			<string>uni23B4.h11</string>
			<string>uni23B4.h12</string>
			<string>uni23B4.h13</string>
			<string>uni23B4.h14</string>
			<string>uni23B4.h15</string>
			<string>uni23B4.h16</string>
			<string>uni23B4.h17</string>
			<string>uni23B4.h18</string>
			<string>uni23B4.h19</string>
			<string>uni23B4.h20</string>
		</array>
		<key>uni23B5</key>
		<array>
			<string>uni23B5</string>
			<string>uni23B5.h1</string>
			<string>uni23B5.h2</string>
			<string>uni23B5.h3</string>
			<string>uni23B5.h4</string>
			<string>uni23B5.h5</string>
			<string>uni23B5.h6</string>
			<string>uni23B5.h7</string>
			<string>uni23B5.h8</string>
			<string>uni23B5.h9</string>
			<string>uni23B5.h10</string>
			<string>uni23B5.h11</string>
			<string>uni23B5.h12</string>
			<string>uni23B5.h13</string>
			<string>uni23B5.h14</string>
			<string>uni23B5.h15</string>
			<string>uni23B5.h16</string>
			<string>uni23B5.h17</string>
			<string>uni23B5.h18</string>
			<string>uni23B5.h19</string>
			<string>uni23B5.h20</string>
		</array>
		<key>uni23DC</key>
		<array>
			<string>uni23DC</string>
			<string>uni23DC.h1</string>
			<string>uni23DC.h2</string>
			<string>uni23DC.h3</string>
			<string>uni23DC.h4</string>
			<string>uni23DC.h5</string>
			<string>uni23DC.h6</string>
			<string>uni23DC.h7</string>
			<string>uni23DC.h8</string>
			<string>uni23DC.h9</string>
			<string>uni23DC.h10</string>
			<string>uni23DC.h11</string>
			<string>uni23DC.h12</string>
			<string>uni23DC.h13</string>
			<string>uni23DC.h14</string>
			<string>uni23DC.h15</string>
			<string>uni23DC.h16</string>
			<string>uni23DC.h17</string>
			<string>uni23DC.h18</string>
			<string>uni23DC.h19</string>
			<string>uni23DC.h20</string>
		</array>
		<key>uni23DD</key>
		<array>
			<string>uni23DD</string>
			<string>uni23DD.h1</string>
			<string>uni23DD.h2</string>
			<string>uni23DD.h3</string>
			<string>uni23DD.h4</string>
			<string>uni23DD.h5</string>
			<string>uni23DD.h6</string>
			<string>uni23DD.h7</string>
			<string>uni23DD.h8</string>
			<string>uni23DD.h9</string>
			<string>uni23DD.h10</string>
			<string>uni23DD.h11</string>
			<string>uni23DD.h12</string>
			<string>uni23DD.h13</string>
			<string>uni23DD.h14</string>
			<string>uni23DD.h15</string>
			<string>uni23DD.h16</string>
			<string>uni23DD.h17</string>
			<string>uni23DD.h18</string>
			<string>uni23DD.h19</string>
			<string>uni23DD.h20</string>
		</array>
		<key>uni23DE</key>
		<array>
			<string>uni23DE</string>
			<string>uni23DE.h1</string>
			<string>uni23DE.h2</string>
			<string>uni23DE.h3</string>
			<string>uni23DE.h4</string>
			<string>uni23DE.h5</string>
			<string>uni23DE.h6</string>
			<string>uni23DE.h7</string>
			<string>uni23DE.h8</string>
			<string>uni23DE.h9</string>
			<string>uni23DE.h10</string>
			<string>uni23DE.h11</string>
			<string>uni23DE.h12</string>
			<string>uni23DE.h13</string>
			<string>uni23DE.h14</string>
			<string>uni23DE.h15</string>
			<string>uni23DE.h16</string>
			<string>uni23DE.h17</string>
			<string>uni23DE.h18</string>
			<string>uni23DE.h19</string>
			<string>uni23DE.h20</string>
		</array>
		<key>uni23DF</key>
		<array>
			<string>uni23DF</string>
			<string>uni23DF.h1</string>
			<string>uni23DF.h2</string>
			<string>uni23DF.h3</string>
			<string>uni23DF.h4</string>
			<string>uni23DF.h5</string>
			<string>uni23DF.h6</string>
			<string>uni23DF.h7</string>
			<string>uni23DF.h8</string>
			<string>uni23DF.h9</string>
			<string>uni23DF.h10</string>
			<string>uni23DF.h11</string>
			<string>uni23DF.h12</string>
			<string>uni23DF.h13</string>
			<string>uni23DF.h14</string>
			<string>uni23DF.h15</string>
			<string>uni23DF.h16</string>
			<string>uni23DF.h17</string>
			<string>uni23DF.h18</string>
			<string>uni23DF.h19</string>
			<string>uni23DF.h20</string>
		</array>
		<key>uni23E0</key>
		<array>
			<string>uni23E0</string>
			<string>uni23E0.h1</string>
			<string>uni23E0.h2</string>
			<string>uni23E0.h3</string>
			<string>uni23E0.h4</string>
			<string>uni23E0.h5</string>
			<string>uni23E0.h6</string>
			<string>uni23E0.h7</string>
			<string>uni23E0.h8</string>
			<string>uni23E0.h9</string>
			<string>uni23E0.h10</string>
			<string>uni23E0.h11</string>
			<string>uni23E0.h12</string>
			<string>uni23E0.h13</string>
			<string>uni23E0.h14</string>
			<string>uni23E0.h15</string>
			<string>uni23E0.h16</string>
			<string>uni23E0.h17</string>
			<string>uni23E0.h18</string>
			<string>uni23E0.h19</string>
			<string>uni23E0.h20</string>
		</array>
		<key>uni23E1</key>
		<array>
			<string>uni23E1</string>
			<string>uni23E1.h1</string>
			<string>uni23E1.h2</string>
			<string>uni23E1.h3</string>
			<string>uni23E1.h4</string>
			<string>uni23E1.h5</string>
			<string>uni23E1.h6</string>
			<string>uni23E1.h7</string>
			<string>uni23E1.h8</string>
			<string>uni23E1.h9</string>
			<string>uni23E1.h10</string>
			<string>uni23E1.h11</string>
			<string>uni23E1.h12</string>
			<string>uni23E1.h13</string>
			<string>uni23E1.h14</string>
			<string>uni23E1.h15</string>
			<string>uni23E1.h16</string>
			<string>uni23E1.h17</string>
			<string>uni23E1.h18</string>
			<string>uni23E1.h19</string>
			<string>uni23E1.h20</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>contourintegral</key>
		<integer>260</integer>
		<key>contourintegral.dp</key>
		<integer>364</integer>
		<key>integral</key>
		<integer>260</integer>
		<key>integral.dp</key>
		<integer>364</integer>
		<key>uni222B.var</key>
		<integer>280</integer>
		<key>uni222B.var.dp</key>
		<integer>550</integer>
		<key>uni222C</key>
		<integer>260</integer>
		<key>uni222C.dp</key>
		<integer>364</integer>
		<key>uni222C.var</key>
		<integer>280</integer>
		<key>uni222C.var.dp</key>
		<integer>550</integer>
		<key>uni222D</key>
		<integer>260</integer>
		<key>uni222D.dp</key>
		<integer>364</integer>
		<key>uni222D.var</key>
		<integer>280</integer>
		<key>uni222D.var.dp</key>
		<integer>550</integer>
		<key>uni222E.var</key>
		<integer>280</integer>
		<key>uni222E.var.dp</key>
		<integer>550</integer>
		<key>uni222F</key>
		<integer>260</integer>
		<key>uni222F.dp</key>
		<integer>364</integer>
		<key>uni222F.var</key>
		<integer>280</integer>
		<key>uni222F.var.dp</key>
		<integer>550</integer>
		<key>uni2230</key>
		<integer>260</integer>
		<key>uni2230.dp</key>
		<integer>364</integer>
		<key>uni2230.var</key>
		<integer>280</integer>
		<key>uni2230.var.dp</key>
		<integer>550</integer>
		<key>uni2231</key>
		<integer>260</integer>
		<key>uni2231.dp</key>
		<integer>364</integer>
		<key>uni2231.var</key>
		<integer>280</integer>
		<key>uni2231.var.dp</key>
		<integer>550</integer>
		<key>uni2232</key>
		<integer>260</integer>
		<key>uni2232.dp</key>
		<integer>364</integer>
		<key>uni2232.var</key>
		<integer>280</integer>
		<key>uni2232.var.dp</key>
		<integer>550</integer>
		<key>uni2233</key>
		<integer>260</integer>
		<key>uni2233.dp</key>
		<integer>364</integer>
		<key>uni2233.var</key>
		<integer>280</integer>
		<key>uni2233.var.dp</key>
		<integer>550</integer>
		<key>uni2A0C</key>
		<integer>260</integer>
		<key>uni2A0C.dp</key>
		<integer>364</integer>
		<key>uni2A0C.var</key>
		<integer>280</integer>
		<key>uni2A0C.var.dp</key>
		<integer>550</integer>
		<key>uni2A11</key>
		<integer>260</integer>
		<key>uni2A11.dp</key>
		<integer>364</integer>
		<key>uni2A11.var</key>
		<integer>280</integer>
		<key>uni2A11.var.dp</key>
		<integer>550</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>arrowdbldown</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>50</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown.hd</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uniE013</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>268</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown.bt</string>
					<key>startConnector</key>
					<integer>89</integer>
				</dict>
			</array>
		</dict>
		<key>arrowdblup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>268</integer>
					<key>endConnector</key>
					<integer>89</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uniE013</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup.hd</string>
					<key>startConnector</key>
					<integer>50</integer>
				</dict>
			</array>
		</dict>
		<key>arrowdown</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>50</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown.hd</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uniE012</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>268</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown.bt</string>
					<key>startConnector</key>
					<integer>89</integer>
				</dict>
			</array>
		</dict>
		<key>arrowup</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>268</integer>
					<key>endConnector</key>
					<integer>89</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uniE012</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup.hd</string>
					<key>startConnector</key>
					<integer>50</integer>
				</dict>
			</array>
		</dict>
		<key>arrowupdn</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>50</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown.hd</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uniE012</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup.hd</string>
					<key>startConnector</key>
					<integer>50</integer>
				</dict>
			</array>
		</dict>
		<key>bar</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2375</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>divides.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>500</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>divides.ex</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2375</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>divides.tp</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
			</array>
		</dict>
		<key>braceleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1734</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>800</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>braceleft.ex</string>
					<key>startConnector</key>
					<integer>800</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3464</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A8</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>800</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>braceleft.ex</string>
					<key>startConnector</key>
					<integer>800</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1737</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
			</array>
		</dict>
		<key>braceright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1734</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>800</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>braceright.ex</string>
					<key>startConnector</key>
					<integer>800</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3464</integer>
					<key>endConnector</key>
					<integer>400</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AC</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>800</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>braceright.ex</string>
					<key>startConnector</key>
					<integer>800</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1737</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>400</integer>
				</dict>
			</array>
		</dict>
		<key>bracketleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>bracketright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>dblverticalbar</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2375</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>parallel.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>parallel.ex</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2375</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>parallel.tp</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>divides</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>796</integer>
					<key>endConnector</key>
					<integer>398</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>divides.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>796</integer>
					<key>endConnector</key>
					<integer>796</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>divides.ex</string>
					<key>startConnector</key>
					<integer>796</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>796</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>divides.tp</string>
					<key>startConnector</key>
					<integer>398</integer>
				</dict>
			</array>
		</dict>
		<key>parallel</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>796</integer>
					<key>endConnector</key>
					<integer>398</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>parallel.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>796</integer>
					<key>endConnector</key>
					<integer>796</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>parallel.ex</string>
					<key>startConnector</key>
					<integer>796</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>796</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>parallel.tp</string>
					<key>startConnector</key>
					<integer>398</integer>
				</dict>
			</array>
		</dict>
		<key>parenleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3194</integer>
					<key>endConnector</key>
					<integer>250</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>500</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2376</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>250</integer>
				</dict>
			</array>
		</dict>
		<key>parenright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3194</integer>
					<key>endConnector</key>
					<integer>250</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>500</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2376</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>250</integer>
				</dict>
			</array>
		</dict>
		<key>radical</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3650</integer>
					<key>endConnector</key>
					<integer>750</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23B7</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1500</integer>
					<key>endConnector</key>
					<integer>1500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>radical.ex</string>
					<key>startConnector</key>
					<integer>1500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1166</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>radical.tp</string>
					<key>startConnector</key>
					<integer>750</integer>
				</dict>
			</array>
		</dict>
		<key>uni21D5</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>50</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown.hd</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uniE013</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>450</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup.hd</string>
					<key>startConnector</key>
					<integer>50</integer>
				</dict>
			</array>
		</dict>
		<key>uni2308</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2308.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni2309</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2309.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni230A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>810</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni230A.tp</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni230B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni230B.tp</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni27E6</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E6.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27E6.ex</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E6.tp</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni27E7</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2357</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E7.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27E7.ex</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2359</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27E7.tp</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EE</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3194</integer>
					<key>endConnector</key>
					<integer>250</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EE.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>500</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27EE.ex</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2736</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EE.tp</string>
					<key>startConnector</key>
					<integer>250</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EF</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3194</integer>
					<key>endConnector</key>
					<integer>250</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EF.bt</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>500</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27EF.ex</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2736</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EF.tp</string>
					<key>startConnector</key>
					<integer>250</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>angleleft</key>
		<array>
			<string>angleleft</string>
			<string>uni27E8.v1</string>
			<string>uni27E8.v2</string>
			<string>uni27E8.v3</string>
			<string>uni27E8.v4</string>
			<string>uni27E8.v5</string>
			<string>uni27E8.v6</string>
			<string>uni27E8.v7</string>
			<string>uni27E8.v8</string>
			<string>uni27E8.v9</string>
			<string>uni27E8.v10</string>
			<string>uni27E8.v11</string>
			<string>uni27E8.v12</string>
			<string>uni27E8.v13</string>
			<string>uni27E8.v14</string>
			<string>uni27E8.v15</string>
			<string>uni27E8.v16</string>
			<string>uni27E8.v17</string>
			<string>uni27E8.v18</string>
			<string>uni27E8.v19</string>
			<string>uni27E8.v20</string>
		</array>
		<key>angleright</key>
		<array>
			<string>angleright</string>
			<string>uni27E9.v1</string>
			<string>uni27E9.v2</string>
			<string>uni27E9.v3</string>
			<string>uni27E9.v4</string>
			<string>uni27E9.v5</string>
			<string>uni27E9.v6</string>
			<string>uni27E9.v7</string>
			<string>uni27E9.v8</string>
			<string>uni27E9.v9</string>
			<string>uni27E9.v10</string>
			<string>uni27E9.v11</string>
			<string>uni27E9.v12</string>
			<string>uni27E9.v13</string>
			<string>uni27E9.v14</string>
			<string>uni27E9.v15</string>
			<string>uni27E9.v16</string>
			<string>uni27E9.v17</string>
			<string>uni27E9.v18</string>
			<string>uni27E9.v19</string>
			<string>uni27E9.v20</string>
		</array>
		<key>arrowdbldown</key>
		<array/>
		<key>arrowdblup</key>
		<array/>
		<key>arrowdown</key>
		<array/>
		<key>arrowup</key>
		<array/>
		<key>arrowupdn</key>
		<array/>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.v1</string>
			<string>backslash.v2</string>
			<string>backslash.v3</string>
			<string>backslash.v4</string>
			<string>backslash.v5</string>
			<string>backslash.v6</string>
			<string>backslash.v7</string>
			<string>backslash.v8</string>
			<string>backslash.v9</string>
			<string>backslash.v10</string>
			<string>backslash.v11</string>
			<string>backslash.v12</string>
			<string>backslash.v13</string>
			<string>backslash.v14</string>
			<string>backslash.v15</string>
			<string>backslash.v16</string>
			<string>backslash.v17</string>
			<string>backslash.v18</string>
			<string>backslash.v19</string>
			<string>backslash.v20</string>
		</array>
		<key>bar</key>
		<array>
			<string>bar</string>
			<string>divides.v1</string>
			<string>divides.v2</string>
			<string>divides.v3</string>
			<string>divides.v4</string>
			<string>divides.v5</string>
			<string>divides.v6</string>
			<string>divides.v7</string>
			<string>divides.v8</string>
			<string>divides.v9</string>
			<string>divides.v10</string>
			<string>divides.v11</string>
			<string>divides.v12</string>
			<string>divides.v13</string>
			<string>divides.v14</string>
			<string>divides.v15</string>
			<string>divides.v16</string>
			<string>divides.v17</string>
			<string>divides.v18</string>
			<string>divides.v19</string>
			<string>divides.v20</string>
		</array>
		<key>braceleft</key>
		<array>
			<string>braceleft</string>
			<string>braceleft.v1</string>
			<string>braceleft.v2</string>
			<string>braceleft.v3</string>
			<string>braceleft.v4</string>
			<string>braceleft.v5</string>
			<string>braceleft.v6</string>
			<string>braceleft.v7</string>
			<string>braceleft.v8</string>
			<string>braceleft.v9</string>
			<string>braceleft.v10</string>
			<string>braceleft.v11</string>
			<string>braceleft.v12</string>
			<string>braceleft.v13</string>
			<string>braceleft.v14</string>
			<string>braceleft.v15</string>
			<string>braceleft.v16</string>
			<string>braceleft.v17</string>
			<string>braceleft.v18</string>
			<string>braceleft.v19</string>
			<string>braceleft.v20</string>
		</array>
		<key>braceright</key>
		<array>
			<string>braceright</string>
			<string>braceright.v1</string>
			<string>braceright.v2</string>
			<string>braceright.v3</string>
			<string>braceright.v4</string>
			<string>braceright.v5</string>
			<string>braceright.v6</string>
			<string>braceright.v7</string>
			<string>braceright.v8</string>
			<string>braceright.v9</string>
			<string>braceright.v10</string>
			<string>braceright.v11</string>
			<string>braceright.v12</string>
			<string>braceright.v13</string>
			<string>braceright.v14</string>
			<string>braceright.v15</string>
			<string>braceright.v16</string>
			<string>braceright.v17</string>
			<string>braceright.v18</string>
			<string>braceright.v19</string>
			<string>braceright.v20</string>
		</array>
		<key>bracketleft</key>
		<array>
			<string>bracketleft</string>
			<string>bracketleft.v1</string>
			<string>bracketleft.v2</string>
			<string>bracketleft.v3</string>
			<string>bracketleft.v4</string>
			<string>bracketleft.v5</string>
			<string>bracketleft.v6</string>
			<string>bracketleft.v7</string>
			<string>bracketleft.v8</string>
			<string>bracketleft.v9</string>
			<string>bracketleft.v10</string>
			<string>bracketleft.v11</string>
			<string>bracketleft.v12</string>
			<string>bracketleft.v13</string>
			<string>bracketleft.v14</string>
			<string>bracketleft.v15</string>
			<string>bracketleft.v16</string>
			<string>bracketleft.v17</string>
			<string>bracketleft.v18</string>
			<string>bracketleft.v19</string>
			<string>bracketleft.v20</string>
		</array>
		<key>bracketright</key>
		<array>
			<string>bracketright</string>
			<string>bracketright.v1</string>
			<string>bracketright.v2</string>
			<string>bracketright.v3</string>
			<string>bracketright.v4</string>
			<string>bracketright.v5</string>
			<string>bracketright.v6</string>
			<string>bracketright.v7</string>
			<string>bracketright.v8</string>
			<string>bracketright.v9</string>
			<string>bracketright.v10</string>
			<string>bracketright.v11</string>
			<string>bracketright.v12</string>
			<string>bracketright.v13</string>
			<string>bracketright.v14</string>
			<string>bracketright.v15</string>
			<string>bracketright.v16</string>
			<string>bracketright.v17</string>
			<string>bracketright.v18</string>
			<string>bracketright.v19</string>
			<string>bracketright.v20</string>
		</array>
		<key>contourintegral</key>
		<array>
			<string>contourintegral</string>
			<string>contourintegral.dp</string>
		</array>
		<key>dblverticalbar</key>
		<array>
			<string>dblverticalbar</string>
			<string>parallel.v1</string>
			<string>parallel.v2</string>
			<string>parallel.v3</string>
			<string>parallel.v4</string>
			<string>parallel.v5</string>
			<string>parallel.v6</string>
			<string>parallel.v7</string>
			<string>parallel.v8</string>
			<string>parallel.v9</string>
			<string>parallel.v10</string>
			<string>parallel.v11</string>
			<string>parallel.v12</string>
			<string>parallel.v13</string>
			<string>parallel.v14</string>
			<string>parallel.v15</string>
			<string>parallel.v16</string>
			<string>parallel.v17</string>
			<string>parallel.v18</string>
			<string>parallel.v19</string>
			<string>parallel.v20</string>
		</array>
		<key>divides</key>
		<array>
			<string>divides</string>
			<string>divides.v1</string>
			<string>divides.v2</string>
			<string>divides.v3</string>
			<string>divides.v4</string>
			<string>divides.v5</string>
			<string>divides.v6</string>
			<string>divides.v7</string>
			<string>divides.v8</string>
			<string>divides.v9</string>
			<string>divides.v10</string>
			<string>divides.v11</string>
			<string>divides.v12</string>
			<string>divides.v13</string>
			<string>divides.v14</string>
			<string>divides.v15</string>
			<string>divides.v16</string>
			<string>divides.v17</string>
			<string>divides.v18</string>
			<string>divides.v19</string>
			<string>divides.v20</string>
		</array>
		<key>fraction</key>
		<array>
			<string>slash</string>
			<string>slash.v1</string>
			<string>slash.v2</string>
			<string>slash.v3</string>
			<string>slash.v4</string>
			<string>slash.v5</string>
			<string>slash.v6</string>
			<string>slash.v7</string>
			<string>slash.v8</string>
			<string>slash.v9</string>
			<string>slash.v10</string>
			<string>slash.v11</string>
			<string>slash.v12</string>
			<string>slash.v13</string>
			<string>slash.v14</string>
			<string>slash.v15</string>
			<string>slash.v16</string>
			<string>slash.v17</string>
			<string>slash.v18</string>
			<string>slash.v19</string>
			<string>slash.v20</string>
		</array>
		<key>integral</key>
		<array>
			<string>integral</string>
			<string>integral.dp</string>
		</array>
		<key>parallel</key>
		<array>
			<string>parallel</string>
			<string>parallel.v1</string>
			<string>parallel.v2</string>
			<string>parallel.v3</string>
			<string>parallel.v4</string>
			<string>parallel.v5</string>
			<string>parallel.v6</string>
			<string>parallel.v7</string>
			<string>parallel.v8</string>
			<string>parallel.v9</string>
			<string>parallel.v10</string>
			<string>parallel.v11</string>
			<string>parallel.v12</string>
			<string>parallel.v13</string>
			<string>parallel.v14</string>
			<string>parallel.v15</string>
			<string>parallel.v16</string>
			<string>parallel.v17</string>
			<string>parallel.v18</string>
			<string>parallel.v19</string>
			<string>parallel.v20</string>
		</array>
		<key>parenleft</key>
		<array>
			<string>parenleft</string>
			<string>parenleft.v1</string>
			<string>parenleft.v2</string>
			<string>parenleft.v3</string>
			<string>parenleft.v4</string>
			<string>parenleft.v5</string>
			<string>parenleft.v6</string>
			<string>parenleft.v7</string>
			<string>parenleft.v8</string>
			<string>parenleft.v9</string>
			<string>parenleft.v10</string>
			<string>parenleft.v11</string>
			<string>parenleft.v12</string>
			<string>parenleft.v13</string>
			<string>parenleft.v14</string>
			<string>parenleft.v15</string>
			<string>parenleft.v16</string>
			<string>parenleft.v17</string>
			<string>parenleft.v18</string>
			<string>parenleft.v19</string>
			<string>parenleft.v20</string>
		</array>
		<key>parenright</key>
		<array>
			<string>parenright</string>
			<string>parenright.v1</string>
			<string>parenright.v2</string>
			<string>parenright.v3</string>
			<string>parenright.v4</string>
			<string>parenright.v5</string>
			<string>parenright.v6</string>
			<string>parenright.v7</string>
			<string>parenright.v8</string>
			<string>parenright.v9</string>
			<string>parenright.v10</string>
			<string>parenright.v11</string>
			<string>parenright.v12</string>
			<string>parenright.v13</string>
			<string>parenright.v14</string>
			<string>parenright.v15</string>
			<string>parenright.v16</string>
			<string>parenright.v17</string>
			<string>parenright.v18</string>
			<string>parenright.v19</string>
			<string>parenright.v20</string>
		</array>
		<key>product</key>
		<array>
			<string>product</string>
			<string>product.dp</string>
		</array>
		<key>radical</key>
		<array>
			<string>radical</string>
			<string>radical.v1</string>
			<string>radical.v2</string>
			<string>radical.v3</string>
			<string>radical.v4</string>
			<string>radical.v5</string>
			<string>radical.v6</string>
			<string>radical.v7</string>
			<string>radical.v8</string>
			<string>radical.v9</string>
			<string>radical.v10</string>
			<string>radical.v11</string>
			<string>radical.v12</string>
			<string>radical.v13</string>
			<string>radical.v14</string>
			<string>radical.v15</string>
			<string>radical.v16</string>
			<string>radical.v17</string>
			<string>radical.v18</string>
			<string>radical.v19</string>
			<string>radical.v20</string>
		</array>
		<key>slash</key>
		<array>
			<string>slash</string>
			<string>slash.v1</string>
			<string>slash.v2</string>
			<string>slash.v3</string>
			<string>slash.v4</string>
			<string>slash.v5</string>
			<string>slash.v6</string>
			<string>slash.v7</string>
			<string>slash.v8</string>
			<string>slash.v9</string>
			<string>slash.v10</string>
			<string>slash.v11</string>
			<string>slash.v12</string>
			<string>slash.v13</string>
			<string>slash.v14</string>
			<string>slash.v15</string>
			<string>slash.v16</string>
			<string>slash.v17</string>
			<string>slash.v18</string>
			<string>slash.v19</string>
			<string>slash.v20</string>
		</array>
		<key>summation</key>
		<array>
			<string>summation</string>
			<string>summation.dp</string>
		</array>
		<key>uni2140</key>
		<array>
			<string>uni2140</string>
			<string>uni2140.dp</string>
		</array>
		<key>uni2140.gm</key>
		<array>
			<string>uni2140.gm</string>
			<string>uni2140.gm.dp</string>
		</array>
		<key>uni21D5</key>
		<array/>
		<key>uni2210</key>
		<array>
			<string>uni2210</string>
			<string>uni2210.dp</string>
		</array>
		<key>uni2215</key>
		<array>
			<string>slash</string>
			<string>slash.v1</string>
			<string>slash.v2</string>
			<string>slash.v3</string>
			<string>slash.v4</string>
			<string>slash.v5</string>
			<string>slash.v6</string>
			<string>slash.v7</string>
			<string>slash.v8</string>
			<string>slash.v9</string>
			<string>slash.v10</string>
			<string>slash.v11</string>
			<string>slash.v12</string>
			<string>slash.v13</string>
			<string>slash.v14</string>
			<string>slash.v15</string>
			<string>slash.v16</string>
			<string>slash.v17</string>
			<string>slash.v18</string>
			<string>slash.v19</string>
			<string>slash.v20</string>
		</array>
		<key>uni2216</key>
		<array>
			<string>uni2216</string>
			<string>backslash</string>
			<string>backslash.v1</string>
			<string>backslash.v2</string>
			<string>backslash.v3</string>
			<string>backslash.v4</string>
			<string>backslash.v5</string>
			<string>backslash.v6</string>
			<string>backslash.v7</string>
			<string>backslash.v8</string>
			<string>backslash.v9</string>
			<string>backslash.v10</string>
			<string>backslash.v11</string>
			<string>backslash.v12</string>
			<string>backslash.v13</string>
			<string>backslash.v14</string>
			<string>backslash.v15</string>
			<string>backslash.v16</string>
			<string>backslash.v17</string>
			<string>backslash.v18</string>
			<string>backslash.v19</string>
			<string>backslash.v20</string>
		</array>
		<key>uni222B.var</key>
		<array>
			<string>uni222B.var</string>
			<string>uni222B.var.dp</string>
		</array>
		<key>uni222C</key>
		<array>
			<string>uni222C</string>
			<string>uni222C.dp</string>
		</array>
		<key>uni222C.var</key>
		<array>
			<string>uni222C.var</string>
			<string>uni222C.var.dp</string>
		</array>
		<key>uni222D</key>
		<array>
			<string>uni222D</string>
			<string>uni222D.dp</string>
		</array>
		<key>uni222D.var</key>
		<array>
			<string>uni222D.var</string>
			<string>uni222D.var.dp</string>
		</array>
		<key>uni222E.var</key>
		<array>
			<string>uni222E.var</string>
			<string>uni222E.var.dp</string>
		</array>
		<key>uni222F</key>
		<array>
			<string>uni222F</string>
			<string>uni222F.dp</string>
		</array>
		<key>uni222F.var</key>
		<array>
			<string>uni222F.var</string>
			<string>uni222F.var.dp</string>
		</array>
		<key>uni2230</key>
		<array>
			<string>uni2230</string>
			<string>uni2230.dp</string>
		</array>
		<key>uni2230.var</key>
		<array>
			<string>uni2230.var</string>
			<string>uni2230.var.dp</string>
		</array>
		<key>uni2231</key>
		<array>
			<string>uni2231</string>
			<string>uni2231.dp</string>
		</array>
		<key>uni2231.var</key>
		<array>
			<string>uni2231.var</string>
			<string>uni2231.var.dp</string>
		</array>
		<key>uni2232</key>
		<array>
			<string>uni2232</string>
			<string>uni2232.dp</string>
		</array>
		<key>uni2232.var</key>
		<array>
			<string>uni2232.var</string>
			<string>uni2232.var.dp</string>
		</array>
		<key>uni2233</key>
		<array>
			<string>uni2233</string>
			<string>uni2233.dp</string>
		</array>
		<key>uni2233.var</key>
		<array>
			<string>uni2233.var</string>
			<string>uni2233.var.dp</string>
		</array>
		<key>uni22C0</key>
		<array>
			<string>uni22C0</string>
			<string>uni22C0.dp</string>
		</array>
		<key>uni22C1</key>
		<array>
			<string>uni22C1</string>
			<string>uni22C1.dp</string>
		</array>
		<key>uni22C2</key>
		<array>
			<string>uni22C2</string>
			<string>uni22C2.dp</string>
		</array>
		<key>uni22C3</key>
		<array>
			<string>uni22C3</string>
			<string>uni22C3.dp</string>
		</array>
		<key>uni2308</key>
		<array>
			<string>uni2308</string>
			<string>uni2308.v1</string>
			<string>uni2308.v2</string>
			<string>uni2308.v3</string>
			<string>uni2308.v4</string>
			<string>uni2308.v5</string>
			<string>uni2308.v6</string>
			<string>uni2308.v7</string>
			<string>uni2308.v8</string>
			<string>uni2308.v9</string>
			<string>uni2308.v10</string>
			<string>uni2308.v11</string>
			<string>uni2308.v12</string>
			<string>uni2308.v13</string>
			<string>uni2308.v14</string>
			<string>uni2308.v15</string>
			<string>uni2308.v16</string>
			<string>uni2308.v17</string>
			<string>uni2308.v18</string>
			<string>uni2308.v19</string>
			<string>uni2308.v20</string>
		</array>
		<key>uni2309</key>
		<array>
			<string>uni2309</string>
			<string>uni2309.v1</string>
			<string>uni2309.v2</string>
			<string>uni2309.v3</string>
			<string>uni2309.v4</string>
			<string>uni2309.v5</string>
			<string>uni2309.v6</string>
			<string>uni2309.v7</string>
			<string>uni2309.v8</string>
			<string>uni2309.v9</string>
			<string>uni2309.v10</string>
			<string>uni2309.v11</string>
			<string>uni2309.v12</string>
			<string>uni2309.v13</string>
			<string>uni2309.v14</string>
			<string>uni2309.v15</string>
			<string>uni2309.v16</string>
			<string>uni2309.v17</string>
			<string>uni2309.v18</string>
			<string>uni2309.v19</string>
			<string>uni2309.v20</string>
		</array>
		<key>uni230A</key>
		<array>
			<string>uni230A</string>
			<string>uni230A.v1</string>
			<string>uni230A.v2</string>
			<string>uni230A.v3</string>
			<string>uni230A.v4</string>
			<string>uni230A.v5</string>
			<string>uni230A.v6</string>
			<string>uni230A.v7</string>
			<string>uni230A.v8</string>
			<string>uni230A.v9</string>
			<string>uni230A.v10</string>
			<string>uni230A.v11</string>
			<string>uni230A.v12</string>
			<string>uni230A.v13</string>
			<string>uni230A.v14</string>
			<string>uni230A.v15</string>
			<string>uni230A.v16</string>
			<string>uni230A.v17</string>
			<string>uni230A.v18</string>
			<string>uni230A.v19</string>
			<string>uni230A.v20</string>
		</array>
		<key>uni230B</key>
		<array>
			<string>uni230B</string>
			<string>uni230B.v1</string>
			<string>uni230B.v2</string>
			<string>uni230B.v3</string>
			<string>uni230B.v4</string>
			<string>uni230B.v5</string>
			<string>uni230B.v6</string>
			<string>uni230B.v7</string>
			<string>uni230B.v8</string>
			<string>uni230B.v9</string>
			<string>uni230B.v10</string>
			<string>uni230B.v11</string>
			<string>uni230B.v12</string>
			<string>uni230B.v13</string>
			<string>uni230B.v14</string>
			<string>uni230B.v15</string>
			<string>uni230B.v16</string>
			<string>uni230B.v17</string>
			<string>uni230B.v18</string>
			<string>uni230B.v19</string>
			<string>uni230B.v20</string>
		</array>
		<key>uni27E6</key>
		<array>
			<string>uni27E6</string>
			<string>uni27E6.v1</string>
			<string>uni27E6.v2</string>
			<string>uni27E6.v3</string>
			<string>uni27E6.v4</string>
			<string>uni27E6.v5</string>
			<string>uni27E6.v6</string>
			<string>uni27E6.v7</string>
			<string>uni27E6.v8</string>
			<string>uni27E6.v9</string>
			<string>uni27E6.v10</string>
			<string>uni27E6.v11</string>
			<string>uni27E6.v12</string>
			<string>uni27E6.v13</string>
			<string>uni27E6.v14</string>
			<string>uni27E6.v15</string>
			<string>uni27E6.v16</string>
			<string>uni27E6.v17</string>
			<string>uni27E6.v18</string>
			<string>uni27E6.v19</string>
			<string>uni27E6.v20</string>
		</array>
		<key>uni27E7</key>
		<array>
			<string>uni27E7</string>
			<string>uni27E7.v1</string>
			<string>uni27E7.v2</string>
			<string>uni27E7.v3</string>
			<string>uni27E7.v4</string>
			<string>uni27E7.v5</string>
			<string>uni27E7.v6</string>
			<string>uni27E7.v7</string>
			<string>uni27E7.v8</string>
			<string>uni27E7.v9</string>
			<string>uni27E7.v10</string>
			<string>uni27E7.v11</string>
			<string>uni27E7.v12</string>
			<string>uni27E7.v13</string>
			<string>uni27E7.v14</string>
			<string>uni27E7.v15</string>
			<string>uni27E7.v16</string>
			<string>uni27E7.v17</string>
			<string>uni27E7.v18</string>
			<string>uni27E7.v19</string>
			<string>uni27E7.v20</string>
		</array>
		<key>uni27E8</key>
		<array>
			<string>uni27E8</string>
			<string>uni27E8.v1</string>
			<string>uni27E8.v2</string>
			<string>uni27E8.v3</string>
			<string>uni27E8.v4</string>
			<string>uni27E8.v5</string>
			<string>uni27E8.v6</string>
			<string>uni27E8.v7</string>
			<string>uni27E8.v8</string>
			<string>uni27E8.v9</string>
			<string>uni27E8.v10</string>
			<string>uni27E8.v11</string>
			<string>uni27E8.v12</string>
			<string>uni27E8.v13</string>
			<string>uni27E8.v14</string>
			<string>uni27E8.v15</string>
			<string>uni27E8.v16</string>
			<string>uni27E8.v17</string>
			<string>uni27E8.v18</string>
			<string>uni27E8.v19</string>
			<string>uni27E8.v20</string>
		</array>
		<key>uni27E9</key>
		<array>
			<string>uni27E9</string>
			<string>uni27E9.v1</string>
			<string>uni27E9.v2</string>
			<string>uni27E9.v3</string>
			<string>uni27E9.v4</string>
			<string>uni27E9.v5</string>
			<string>uni27E9.v6</string>
			<string>uni27E9.v7</string>
			<string>uni27E9.v8</string>
			<string>uni27E9.v9</string>
			<string>uni27E9.v10</string>
			<string>uni27E9.v11</string>
			<string>uni27E9.v12</string>
			<string>uni27E9.v13</string>
			<string>uni27E9.v14</string>
			<string>uni27E9.v15</string>
			<string>uni27E9.v16</string>
			<string>uni27E9.v17</string>
			<string>uni27E9.v18</string>
			<string>uni27E9.v19</string>
			<string>uni27E9.v20</string>
		</array>
		<key>uni27EA</key>
		<array>
			<string>uni27EA</string>
			<string>uni27EA.v1</string>
			<string>uni27EA.v2</string>
			<string>uni27EA.v3</string>
			<string>uni27EA.v4</string>
			<string>uni27EA.v5</string>
			<string>uni27EA.v6</string>
			<string>uni27EA.v7</string>
			<string>uni27EA.v8</string>
			<string>uni27EA.v9</string>
			<string>uni27EA.v10</string>
			<string>uni27EA.v11</string>
			<string>uni27EA.v12</string>
			<string>uni27EA.v13</string>
			<string>uni27EA.v14</string>
			<string>uni27EA.v15</string>
			<string>uni27EA.v16</string>
			<string>uni27EA.v17</string>
			<string>uni27EA.v18</string>
			<string>uni27EA.v19</string>
			<string>uni27EA.v20</string>
		</array>
		<key>uni27EB</key>
		<array>
			<string>uni27EB</string>
			<string>uni27EB.v1</string>
			<string>uni27EB.v2</string>
			<string>uni27EB.v3</string>
			<string>uni27EB.v4</string>
			<string>uni27EB.v5</string>
			<string>uni27EB.v6</string>
			<string>uni27EB.v7</string>
			<string>uni27EB.v8</string>
			<string>uni27EB.v9</string>
			<string>uni27EB.v10</string>
			<string>uni27EB.v11</string>
			<string>uni27EB.v12</string>
			<string>uni27EB.v13</string>
			<string>uni27EB.v14</string>
			<string>uni27EB.v15</string>
			<string>uni27EB.v16</string>
			<string>uni27EB.v17</string>
			<string>uni27EB.v18</string>
			<string>uni27EB.v19</string>
			<string>uni27EB.v20</string>
		</array>
		<key>uni27EE</key>
		<array>
			<string>uni27EE</string>
			<string>uni27EE.v1</string>
			<string>uni27EE.v2</string>
			<string>uni27EE.v3</string>
			<string>uni27EE.v4</string>
			<string>uni27EE.v5</string>
			<string>uni27EE.v6</string>
			<string>uni27EE.v7</string>
			<string>uni27EE.v8</string>
			<string>uni27EE.v9</string>
			<string>uni27EE.v10</string>
			<string>uni27EE.v11</string>
			<string>uni27EE.v12</string>
			<string>uni27EE.v13</string>
			<string>uni27EE.v14</string>
			<string>uni27EE.v15</string>
			<string>uni27EE.v16</string>
			<string>uni27EE.v17</string>
			<string>uni27EE.v18</string>
			<string>uni27EE.v19</string>
			<string>uni27EE.v20</string>
		</array>
		<key>uni27EF</key>
		<array>
			<string>uni27EF</string>
			<string>uni27EF.v1</string>
			<string>uni27EF.v2</string>
			<string>uni27EF.v3</string>
			<string>uni27EF.v4</string>
			<string>uni27EF.v5</string>
			<string>uni27EF.v6</string>
			<string>uni27EF.v7</string>
			<string>uni27EF.v8</string>
			<string>uni27EF.v9</string>
			<string>uni27EF.v10</string>
			<string>uni27EF.v11</string>
			<string>uni27EF.v12</string>
			<string>uni27EF.v13</string>
			<string>uni27EF.v14</string>
			<string>uni27EF.v15</string>
			<string>uni27EF.v16</string>
			<string>uni27EF.v17</string>
			<string>uni27EF.v18</string>
			<string>uni27EF.v19</string>
			<string>uni27EF.v20</string>
		</array>
		<key>uni29F5</key>
		<array>
			<string>backslash</string>
			<string>backslash.v1</string>
			<string>backslash.v2</string>
			<string>backslash.v3</string>
			<string>backslash.v4</string>
			<string>backslash.v5</string>
			<string>backslash.v6</string>
			<string>backslash.v7</string>
			<string>backslash.v8</string>
			<string>backslash.v9</string>
			<string>backslash.v10</string>
			<string>backslash.v11</string>
			<string>backslash.v12</string>
			<string>backslash.v13</string>
			<string>backslash.v14</string>
			<string>backslash.v15</string>
			<string>backslash.v16</string>
			<string>backslash.v17</string>
			<string>backslash.v18</string>
			<string>backslash.v19</string>
			<string>backslash.v20</string>
		</array>
		<key>uni2A00</key>
		<array>
			<string>uni2A00</string>
			<string>uni2A00.dp</string>
		</array>
		<key>uni2A01</key>
		<array>
			<string>uni2A01</string>
			<string>uni2A01.dp</string>
		</array>
		<key>uni2A02</key>
		<array>
			<string>uni2A02</string>
			<string>uni2A02.dp</string>
		</array>
		<key>uni2A03</key>
		<array>
			<string>uni2A03</string>
			<string>uni2A03.dp</string>
		</array>
		<key>uni2A04</key>
		<array>
			<string>uni2A04</string>
			<string>uni2A04.dp</string>
		</array>
		<key>uni2A05</key>
		<array>
			<string>uni2A05</string>
			<string>uni2A05.dp</string>
		</array>
		<key>uni2A06</key>
		<array>
			<string>uni2A06</string>
			<string>uni2A06.dp</string>
		</array>
		<key>uni2A09</key>
		<array>
			<string>uni2A09</string>
			<string>uni2A09.dp</string>
		</array>
		<key>uni2A0C</key>
		<array>
			<string>uni2A0C</string>
			<string>uni2A0C.dp</string>
		</array>
		<key>uni2A0C.var</key>
		<array>
			<string>uni2A0C.var</string>
			<string>uni2A0C.var.dp</string>
		</array>
		<key>uni2A11</key>
		<array>
			<string>uni2A11</string>
			<string>uni2A11.dp</string>
		</array>
		<key>uni2A11.var</key>
		<array>
			<string>uni2A11.var</string>
			<string>uni2A11.var.dp</string>
		</array>
		<key>uniFF0F</key>
		<array>
			<string>slash</string>
			<string>slash.v1</string>
			<string>slash.v2</string>
			<string>slash.v3</string>
			<string>slash.v4</string>
			<string>slash.v5</string>
			<string>slash.v6</string>
			<string>slash.v7</string>
			<string>slash.v8</string>
			<string>slash.v9</string>
			<string>slash.v10</string>
			<string>slash.v11</string>
			<string>slash.v12</string>
			<string>slash.v13</string>
			<string>slash.v14</string>
			<string>slash.v15</string>
			<string>slash.v16</string>
			<string>slash.v17</string>
			<string>slash.v18</string>
			<string>slash.v19</string>
			<string>slash.v20</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
