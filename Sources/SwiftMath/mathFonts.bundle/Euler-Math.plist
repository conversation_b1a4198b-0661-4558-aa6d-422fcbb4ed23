<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>BbbZ.ssty1</key>
		<integer>304</integer>
		<key>acute</key>
		<integer>100</integer>
		<key>acutecomb</key>
		<integer>-226</integer>
		<key>acutedbl</key>
		<integer>-185</integer>
		<key>asciicircum</key>
		<integer>248</integer>
		<key>bar</key>
		<integer>-249</integer>
		<key>breve</key>
		<integer>141</integer>
		<key>brevebelow</key>
		<integer>-236</integer>
		<key>caron</key>
		<integer>248</integer>
		<key>cdotp</key>
		<integer>138</integer>
		<key>circumflex</key>
		<integer>248</integer>
		<key>ddddot</key>
		<integer>-450</integer>
		<key>dddot</key>
		<integer>-350</integer>
		<key>ddot</key>
		<integer>-250</integer>
		<key>ddotbelow</key>
		<integer>-250</integer>
		<key>diaeresis</key>
		<integer>150</integer>
		<key>dot</key>
		<integer>-250</integer>
		<key>dotaccent</key>
		<integer>50</integer>
		<key>dotbelow</key>
		<integer>-175</integer>
		<key>grave</key>
		<integer>100</integer>
		<key>gravecomb</key>
		<integer>-273</integer>
		<key>gravedbl</key>
		<integer>-315</integer>
		<key>invbreve</key>
		<integer>-249</integer>
		<key>invbrevebelow</key>
		<integer>-236</integer>
		<key>macron</key>
		<integer>156</integer>
		<key>macronbelow</key>
		<integer>-249</integer>
		<key>mathunderbar</key>
		<integer>-249</integer>
		<key>mbfC</key>
		<integer>456</integer>
		<key>mbfC.ssty1</key>
		<integer>529</integer>
		<key>mbfC.ssty2</key>
		<integer>633</integer>
		<key>mbfChi</key>
		<integer>394</integer>
		<key>mbfDelta</key>
		<integer>422</integer>
		<key>mbfDelta.ssty2</key>
		<integer>594</integer>
		<key>mbfG</key>
		<integer>453</integer>
		<key>mbfG.ssty2</key>
		<integer>630</integer>
		<key>mbfH</key>
		<integer>459</integer>
		<key>mbfH.ssty2</key>
		<integer>636</integer>
		<key>mbfLambda</key>
		<integer>454</integer>
		<key>mbfLambda.ssty2</key>
		<integer>631</integer>
		<key>mbfO</key>
		<integer>471</integer>
		<key>mbfO.ssty2</key>
		<integer>651</integer>
		<key>mbfOmicron</key>
		<integer>471</integer>
		<key>mbfPi</key>
		<integer>453</integer>
		<key>mbfPi.ssty2</key>
		<integer>630</integer>
		<key>mbfPsi</key>
		<integer>399</integer>
		<key>mbfPsi.ssty2</key>
		<integer>593</integer>
		<key>mbfS</key>
		<integer>358</integer>
		<key>mbfS.ssty2</key>
		<integer>519</integer>
		<key>mbfTau</key>
		<integer>325</integer>
		<key>mbfTheta</key>
		<integer>515</integer>
		<key>mbfTheta.ssty2</key>
		<integer>701</integer>
		<key>mbfUpsilon</key>
		<integer>408</integer>
		<key>mbfUpsilon.ssty2</key>
		<integer>601</integer>
		<key>mbfV</key>
		<integer>434</integer>
		<key>mbfV.ssty2</key>
		<integer>608</integer>
		<key>mbfW</key>
		<integer>624</integer>
		<key>mbfW.ssty2</key>
		<integer>828</integer>
		<key>mbfX</key>
		<integer>430</integer>
		<key>mbfX.ssty2</key>
		<integer>603</integer>
		<key>mbfY</key>
		<integer>353</integer>
		<key>mbfY.ssty2</key>
		<integer>514</integer>
		<key>mbfZ</key>
		<integer>428</integer>
		<key>mbfZ.ssty2</key>
		<integer>601</integer>
		<key>mbfZeta</key>
		<integer>428</integer>
		<key>mbfa</key>
		<integer>363</integer>
		<key>mbfa.ssty1</key>
		<integer>427</integer>
		<key>mbfa.ssty2</key>
		<integer>525</integer>
		<key>mbfc</key>
		<integer>305</integer>
		<key>mbfc.ssty2</key>
		<integer>458</integer>
		<key>mbfe</key>
		<integer>337</integer>
		<key>mbfe.ssty2</key>
		<integer>496</integer>
		<key>mbff</key>
		<integer>337</integer>
		<key>mbff.ssty2</key>
		<integer>496</integer>
		<key>mbfg</key>
		<integer>360</integer>
		<key>mbfg.ssty2</key>
		<integer>522</integer>
		<key>mbfh</key>
		<integer>309</integer>
		<key>mbflambda</key>
		<integer>237</integer>
		<key>mbflambda.ssty2</key>
		<integer>380</integer>
		<key>mbfo</key>
		<integer>367</integer>
		<key>mbfo.ssty2</key>
		<integer>530</integer>
		<key>mbfomega</key>
		<integer>517</integer>
		<key>mbfomega.ssty2</key>
		<integer>704</integer>
		<key>mbfphi</key>
		<integer>435</integer>
		<key>mbfphi.ssty2</key>
		<integer>646</integer>
		<key>mbfpi</key>
		<integer>393</integer>
		<key>mbfpi.ssty2</key>
		<integer>561</integer>
		<key>mbfpsi</key>
		<integer>421</integer>
		<key>mbfpsi.ssty2</key>
		<integer>616</integer>
		<key>mbfrho</key>
		<integer>336</integer>
		<key>mbfrho.ssty2</key>
		<integer>494</integer>
		<key>mbfs</key>
		<integer>280</integer>
		<key>mbfs.ssty2</key>
		<integer>429</integer>
		<key>mbfscrA</key>
		<integer>600</integer>
		<key>mbfscrA.ssty2</key>
		<integer>783</integer>
		<key>mbfscrI</key>
		<integer>344</integer>
		<key>mbfscrI.ssty2</key>
		<integer>494</integer>
		<key>mbfscrJ</key>
		<integer>348</integer>
		<key>mbfscrJ.ssty2</key>
		<integer>498</integer>
		<key>mbftau</key>
		<integer>351</integer>
		<key>mbftau.ssty2</key>
		<integer>512</integer>
		<key>mbftheta</key>
		<integer>355</integer>
		<key>mbftheta.ssty2</key>
		<integer>516</integer>
		<key>mbfz</key>
		<integer>303</integer>
		<key>mbfz.ssty2</key>
		<integer>456</integer>
		<key>mbfzeta</key>
		<integer>323</integer>
		<key>mbfzeta.ssty2</key>
		<integer>479</integer>
		<key>mscrA</key>
		<integer>524</integer>
		<key>mscrA.ssty2</key>
		<integer>725</integer>
		<key>mscrI</key>
		<integer>271</integer>
		<key>mscrI.ssty2</key>
		<integer>421</integer>
		<key>mscrJ</key>
		<integer>309</integer>
		<key>mscrJ.ssty2</key>
		<integer>467</integer>
		<key>mupC</key>
		<integer>413</integer>
		<key>mupC.ssty1</key>
		<integer>496</integer>
		<key>mupChi</key>
		<integer>389</integer>
		<key>mupDelta</key>
		<integer>384</integer>
		<key>mupG</key>
		<integer>410</integer>
		<key>mupG.ssty1</key>
		<integer>493</integer>
		<key>mupLambda</key>
		<integer>413</integer>
		<key>mupLambda.ssty1</key>
		<integer>496</integer>
		<key>mupLambda.ssty2</key>
		<integer>592</integer>
		<key>mupO</key>
		<integer>429</integer>
		<key>mupO.ssty1</key>
		<integer>515</integer>
		<key>mupOmicron</key>
		<integer>429</integer>
		<key>mupPi</key>
		<integer>389</integer>
		<key>mupPi.ssty1</key>
		<integer>469</integer>
		<key>mupPsi</key>
		<integer>379</integer>
		<key>mupPsi.ssty2</key>
		<integer>552</integer>
		<key>mupS</key>
		<integer>306</integer>
		<key>mupTheta</key>
		<integer>406</integer>
		<key>mupTheta.ssty1</key>
		<integer>489</integer>
		<key>mupTheta.ssty2</key>
		<integer>584</integer>
		<key>mupUpsilon</key>
		<integer>386</integer>
		<key>mupUpsilon.ssty1</key>
		<integer>466</integer>
		<key>mupUpsilon.ssty2</key>
		<integer>560</integer>
		<key>mupV</key>
		<integer>379</integer>
		<key>mupW</key>
		<integer>549</integer>
		<key>mupX</key>
		<integer>349</integer>
		<key>mupY</key>
		<integer>333</integer>
		<key>mupZ</key>
		<integer>389</integer>
		<key>mupa</key>
		<integer>332</integer>
		<key>mupc</key>
		<integer>271</integer>
		<key>mupe</key>
		<integer>277</integer>
		<key>mupf</key>
		<integer>321</integer>
		<key>mupg</key>
		<integer>312</integer>
		<key>muph</key>
		<integer>294</integer>
		<key>muplambda</key>
		<integer>215</integer>
		<key>mupo</key>
		<integer>309</integer>
		<key>mupomega</key>
		<integer>453</integer>
		<key>mupphi</key>
		<integer>355</integer>
		<key>muppi</key>
		<integer>360</integer>
		<key>muppsi</key>
		<integer>393</integer>
		<key>muprho</key>
		<integer>302</integer>
		<key>mups</key>
		<integer>255</integer>
		<key>muptau</key>
		<integer>312</integer>
		<key>muptheta</key>
		<integer>305</integer>
		<key>mupx</key>
		<integer>247</integer>
		<key>mupz</key>
		<integer>263</integer>
		<key>mupzeta</key>
		<integer>312</integer>
		<key>notaccent</key>
		<integer>251</integer>
		<key>ocirc</key>
		<integer>-249</integer>
		<key>ocircbelow</key>
		<integer>-249</integer>
		<key>overleftarrow</key>
		<integer>-209</integer>
		<key>overleftharpoon</key>
		<integer>-209</integer>
		<key>overleftrightarrow</key>
		<integer>-225</integer>
		<key>overrightarc</key>
		<integer>-304</integer>
		<key>overrightarrow</key>
		<integer>-209</integer>
		<key>overrightharpoon</key>
		<integer>-209</integer>
		<key>ovhook</key>
		<integer>-106</integer>
		<key>ring</key>
		<integer>100</integer>
		<key>tilde</key>
		<integer>161</integer>
		<key>widebreve</key>
		<integer>-249</integer>
		<key>widebreve.h0</key>
		<integer>-240</integer>
		<key>widecheck</key>
		<integer>-248</integer>
		<key>widecheck.h0</key>
		<integer>-305</integer>
		<key>widehat</key>
		<integer>-280</integer>
		<key>widehat.h0</key>
		<integer>-305</integer>
		<key>wideoverbar</key>
		<integer>-249</integer>
		<key>widetilde</key>
		<integer>-248</integer>
		<key>widetilde.h0</key>
		<integer>-225</integer>
		<key>wideutilde</key>
		<integer>-228</integer>
		<key>wp</key>
		<integer>358</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>413</integer>
		<key>AxisHeight</key>
		<integer>250</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1500</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1400</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>665</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>120</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>686</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>40</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>345</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>120</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>677</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>40</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>394</integer>
		<key>FractionRuleThickness</key>
		<integer>40</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>600</integer>
		<key>LowerLimitGapMin</key>
		<integer>167</integer>
		<key>MathLeading</key>
		<integer>150</integer>
		<key>MinConnectorOverlap</key>
		<integer>20</integer>
		<key>OverbarExtraAscender</key>
		<integer>40</integer>
		<key>OverbarRuleThickness</key>
		<integer>40</integer>
		<key>OverbarVerticalGap</key>
		<integer>120</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>60</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>148</integer>
		<key>RadicalExtraAscender</key>
		<integer>40</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-600</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>220</integer>
		<key>RadicalRuleThickness</key>
		<integer>40</integer>
		<key>RadicalVerticalGap</key>
		<integer>50</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>70</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>50</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>400</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>70</integer>
		<key>SpaceAfterScript</key>
		<integer>50</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>686</integer>
		<key>StackBottomShiftDown</key>
		<integer>345</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>280</integer>
		<key>StackGapMin</key>
		<integer>120</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>677</integer>
		<key>StackTopShiftUp</key>
		<integer>444</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>600</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>167</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>111</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>200</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>160</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>50</integer>
		<key>SubscriptShiftDown</key>
		<integer>150</integer>
		<key>SubscriptTopMax</key>
		<integer>344</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>247</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>344</integer>
		<key>SuperscriptBottomMin</key>
		<integer>108</integer>
		<key>SuperscriptShiftUp</key>
		<integer>363</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>289</integer>
		<key>UnderbarExtraDescender</key>
		<integer>40</integer>
		<key>UnderbarRuleThickness</key>
		<integer>40</integer>
		<key>UnderbarVerticalGap</key>
		<integer>120</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>200</integer>
		<key>UpperLimitGapMin</key>
		<integer>111</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>Leftarrow</key>
		<array>
			<string>Leftarrow</string>
			<string>Longleftarrow</string>
		</array>
		<key>Leftrightarrow</key>
		<array>
			<string>Leftrightarrow</string>
			<string>Longleftrightarrow</string>
		</array>
		<key>Mapsfrom</key>
		<array>
			<string>Mapsfrom</string>
			<string>Longmapsfrom</string>
		</array>
		<key>Mapsto</key>
		<array>
			<string>Mapsto</string>
			<string>Longmapsto</string>
		</array>
		<key>Rightarrow</key>
		<array>
			<string>Rightarrow</string>
			<string>Longrightarrow</string>
		</array>
		<key>equal</key>
		<array/>
		<key>equiv</key>
		<array/>
		<key>hookleftarrow</key>
		<array/>
		<key>hookrightarrow</key>
		<array/>
		<key>leftarrow</key>
		<array>
			<string>leftarrow</string>
			<string>longleftarrow</string>
		</array>
		<key>leftarrowtail</key>
		<array/>
		<key>leftharpoondown</key>
		<array/>
		<key>leftharpoonup</key>
		<array/>
		<key>leftrightarrow</key>
		<array>
			<string>leftrightarrow</string>
			<string>longleftrightarrow</string>
		</array>
		<key>leftrightarrows</key>
		<array/>
		<key>leftrightharpoons</key>
		<array/>
		<key>mapsfrom</key>
		<array>
			<string>mapsfrom</string>
			<string>longmapsfrom</string>
		</array>
		<key>mapsto</key>
		<array>
			<string>mapsto</string>
			<string>longmapsto</string>
		</array>
		<key>mathunderbar</key>
		<array/>
		<key>minus</key>
		<array/>
		<key>overbrace</key>
		<array>
			<string>overbrace</string>
			<string>overbrace.h1</string>
			<string>overbrace.h2</string>
			<string>overbrace.h3</string>
			<string>overbrace.h4</string>
		</array>
		<key>overbracket</key>
		<array>
			<string>overbracket</string>
			<string>overbracket.h1</string>
			<string>overbracket.h2</string>
			<string>overbracket.h3</string>
		</array>
		<key>overleftarrow</key>
		<array>
			<string>overleftarrow</string>
			<string>overleftarrow.h1</string>
			<string>overleftarrow.h2</string>
			<string>overleftarrow.h3</string>
		</array>
		<key>overleftharpoon</key>
		<array>
			<string>overleftharpoon</string>
			<string>overleftharpoon.h1</string>
			<string>overleftharpoon.h2</string>
			<string>overleftharpoon.h3</string>
		</array>
		<key>overleftrightarrow</key>
		<array>
			<string>overleftrightarrow</string>
			<string>overleftrightarrow.h1</string>
			<string>overleftrightarrow.h2</string>
			<string>overleftrightarrow.h3</string>
		</array>
		<key>overparen</key>
		<array>
			<string>overparen</string>
			<string>overparen.h1</string>
			<string>overparen.h2</string>
			<string>overparen.h3</string>
			<string>overparen.h4</string>
		</array>
		<key>overrightarc</key>
		<array>
			<string>overrightarc</string>
			<string>overrightarc.h1</string>
			<string>overrightarc.h2</string>
			<string>overrightarc.h3</string>
			<string>overrightarc.h4</string>
		</array>
		<key>overrightarrow</key>
		<array>
			<string>overrightarrow</string>
			<string>overrightarrow.h1</string>
			<string>overrightarrow.h2</string>
			<string>overrightarrow.h3</string>
		</array>
		<key>overrightharpoon</key>
		<array>
			<string>overrightharpoon</string>
			<string>overrightharpoon.h1</string>
			<string>overrightharpoon.h2</string>
			<string>overrightharpoon.h3</string>
		</array>
		<key>rightarrow</key>
		<array>
			<string>rightarrow</string>
			<string>longrightarrow</string>
		</array>
		<key>rightarrowtail</key>
		<array/>
		<key>rightharpoondown</key>
		<array/>
		<key>rightharpoonup</key>
		<array/>
		<key>rightleftarrows</key>
		<array/>
		<key>rightleftharpoons</key>
		<array/>
		<key>twoheadleftarrow</key>
		<array/>
		<key>twoheadrightarrow</key>
		<array/>
		<key>underbrace</key>
		<array>
			<string>underbrace</string>
			<string>underbrace.h1</string>
			<string>underbrace.h2</string>
			<string>underbrace.h3</string>
			<string>underbrace.h4</string>
		</array>
		<key>underbracket</key>
		<array>
			<string>underbracket</string>
			<string>underbracket.h1</string>
			<string>underbracket.h2</string>
			<string>underbracket.h3</string>
		</array>
		<key>underleftarrow</key>
		<array>
			<string>underleftarrow</string>
			<string>underleftarrow.h1</string>
			<string>underleftarrow.h2</string>
			<string>underleftarrow.h3</string>
		</array>
		<key>underleftharpoondown</key>
		<array>
			<string>underleftharpoondown</string>
			<string>underleftharpoondown.h1</string>
			<string>underleftharpoondown.h2</string>
			<string>underleftharpoondown.h3</string>
		</array>
		<key>underleftrightarrow</key>
		<array>
			<string>underleftrightarrow</string>
			<string>underleftrightarrow.h1</string>
			<string>underleftrightarrow.h2</string>
			<string>underleftrightarrow.h3</string>
		</array>
		<key>underparen</key>
		<array>
			<string>underparen</string>
			<string>underparen.h1</string>
			<string>underparen.h2</string>
			<string>underparen.h3</string>
			<string>underparen.h4</string>
		</array>
		<key>underrightarrow</key>
		<array>
			<string>underrightarrow</string>
			<string>underrightarrow.h1</string>
			<string>underrightarrow.h2</string>
			<string>underrightarrow.h3</string>
		</array>
		<key>underrightharpoondown</key>
		<array>
			<string>underrightharpoondown</string>
			<string>underrightharpoondown.h1</string>
			<string>underrightharpoondown.h2</string>
			<string>underrightharpoondown.h3</string>
		</array>
		<key>widearc</key>
		<array>
			<string>widearc</string>
			<string>widearc.h1</string>
			<string>widearc.h2</string>
			<string>widearc.h3</string>
			<string>widearc.h4</string>
		</array>
		<key>widebreve</key>
		<array>
			<string>widebreve</string>
			<string>widebreve.h0</string>
			<string>widebreve.h1</string>
			<string>widebreve.h2</string>
			<string>widebreve.h3</string>
			<string>widebreve.h4</string>
			<string>widebreve.h5</string>
			<string>widebreve.h6</string>
			<string>widebreve.h7</string>
		</array>
		<key>widecheck</key>
		<array>
			<string>widecheck</string>
			<string>widecheck.h0</string>
			<string>widecheck.h1</string>
			<string>widecheck.h2</string>
			<string>widecheck.h3</string>
			<string>widecheck.h4</string>
			<string>widecheck.h5</string>
			<string>widecheck.h6</string>
			<string>widecheck.h7</string>
		</array>
		<key>widehat</key>
		<array>
			<string>widehat</string>
			<string>widehat.h0</string>
			<string>widehat.h1</string>
			<string>widehat.h2</string>
			<string>widehat.h3</string>
			<string>widehat.h4</string>
			<string>widehat.h5</string>
			<string>widehat.h6</string>
			<string>widehat.h7</string>
		</array>
		<key>wideoverbar</key>
		<array/>
		<key>widetilde</key>
		<array>
			<string>widetilde</string>
			<string>widetilde.h0</string>
			<string>widetilde.h1</string>
			<string>widetilde.h2</string>
			<string>widetilde.h3</string>
			<string>widetilde.h4</string>
			<string>widetilde.h5</string>
			<string>widetilde.h6</string>
			<string>widetilde.h7</string>
		</array>
		<key>wideutilde</key>
		<array>
			<string>wideutilde</string>
			<string>wideutilde.h0</string>
			<string>wideutilde.h1</string>
			<string>wideutilde.h2</string>
			<string>wideutilde.h3</string>
			<string>wideutilde.h4</string>
			<string>wideutilde.h5</string>
			<string>wideutilde.h6</string>
			<string>wideutilde.h7</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>iiiint</key>
		<integer>56</integer>
		<key>iiint</key>
		<integer>56</integer>
		<key>iint</key>
		<integer>56</integer>
		<key>int</key>
		<integer>56</integer>
		<key>int.ssty1</key>
		<integer>56</integer>
		<key>int.ssty2</key>
		<integer>56</integer>
		<key>mbfGamma</key>
		<integer>61</integer>
		<key>mbfGamma.ssty2</key>
		<integer>71</integer>
		<key>mbfT</key>
		<integer>122</integer>
		<key>mbfT.ssty2</key>
		<integer>142</integer>
		<key>mbfV</key>
		<integer>61</integer>
		<key>mbfV.ssty2</key>
		<integer>71</integer>
		<key>mbfW</key>
		<integer>31</integer>
		<key>mbfW.ssty2</key>
		<integer>35</integer>
		<key>mbfY</key>
		<integer>92</integer>
		<key>mbfY.ssty2</key>
		<integer>106</integer>
		<key>mbfscrT</key>
		<integer>61</integer>
		<key>mbfscrT.ssty2</key>
		<integer>68</integer>
		<key>mid.ssty1</key>
		<integer>60</integer>
		<key>mscrF</key>
		<integer>27</integer>
		<key>mscrF.ssty2</key>
		<integer>32</integer>
		<key>mscrH</key>
		<integer>13</integer>
		<key>mscrH.ssty2</key>
		<integer>16</integer>
		<key>mscrN</key>
		<integer>14</integer>
		<key>mscrN.ssty2</key>
		<integer>16</integer>
		<key>mscrT</key>
		<integer>41</integer>
		<key>mscrT.ssty2</key>
		<integer>49</integer>
		<key>mupGamma</key>
		<integer>111</integer>
		<key>mupGamma.ssty2</key>
		<integer>133</integer>
		<key>mupP</key>
		<integer>27</integer>
		<key>mupP.ssty1</key>
		<integer>31</integer>
		<key>mupT</key>
		<integer>111</integer>
		<key>mupV</key>
		<integer>83</integer>
		<key>mupW</key>
		<integer>56</integer>
		<key>mupY</key>
		<integer>56</integer>
		<key>oint</key>
		<integer>56</integer>
		<key>vysmwhtcircle.ssty1</key>
		<integer>60</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>Downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>868</integer>
					<key>endConnector</key>
					<integer>289</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Downarrow</string>
					<key>startConnector</key>
					<integer>289</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Downarrow.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>Uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Uparrow.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>868</integer>
					<key>endConnector</key>
					<integer>289</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Uparrow</string>
					<key>startConnector</key>
					<integer>289</integer>
				</dict>
			</array>
		</dict>
		<key>Vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vert.v4</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vert.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>Vvert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vvert.v4</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vvert.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>887</integer>
					<key>endConnector</key>
					<integer>296</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>downarrow</string>
					<key>startConnector</key>
					<integer>296</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>downarrow.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>int</key>
		<dict>
			<key>italic</key>
			<integer>230</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1185</integer>
					<key>endConnector</key>
					<integer>395</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>intbottom</string>
					<key>startConnector</key>
					<integer>395</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>381</integer>
					<key>endConnector</key>
					<integer>127</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>intextender</string>
					<key>startConnector</key>
					<integer>127</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1185</integer>
					<key>endConnector</key>
					<integer>395</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>inttop</string>
					<key>startConnector</key>
					<integer>395</integer>
				</dict>
			</array>
		</dict>
		<key>lBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.bt</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lBrack.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.tp</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>lbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.bt</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbracemid</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.tp</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
			</array>
		</dict>
		<key>lbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.bt</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lbrack.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.tp</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>lceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lceilfloor.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lceil.v4</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>lfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lfloor.v4</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lceilfloor.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>lparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.bt</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lparen.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.tp</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>mid</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>267</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>mid</string>
					<key>startConnector</key>
					<integer>267</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>mid.ex</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
			</array>
		</dict>
		<key>ni.ssty1</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1820</integer>
					<key>endConnector</key>
					<integer>607</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrtbottom</string>
					<key>startConnector</key>
					<integer>607</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>570</integer>
					<key>endConnector</key>
					<integer>190</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>sqrt.ex</string>
					<key>startConnector</key>
					<integer>190</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>585</integer>
					<key>endConnector</key>
					<integer>195</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrt.tp</string>
					<key>startConnector</key>
					<integer>195</integer>
				</dict>
			</array>
		</dict>
		<key>parallel</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>267</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>parallel</string>
					<key>startConnector</key>
					<integer>267</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>parallel.ex</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
			</array>
		</dict>
		<key>rBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.bt</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rBrack.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.tp</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>rbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.bt</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbracemid</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>300</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1000</integer>
					<key>endConnector</key>
					<integer>100</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.tp</string>
					<key>startConnector</key>
					<integer>100</integer>
				</dict>
			</array>
		</dict>
		<key>rbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.bt</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rbrack.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.tp</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>rceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rceilfloor.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rceil.v4</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>rfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rfloor.v4</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rceilfloor.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>rparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.bt</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rparen.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1800</integer>
					<key>endConnector</key>
					<integer>600</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.tp</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
		<key>sqrt</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1820</integer>
					<key>endConnector</key>
					<integer>607</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrtbottom</string>
					<key>startConnector</key>
					<integer>607</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>570</integer>
					<key>endConnector</key>
					<integer>190</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>sqrt.ex</string>
					<key>startConnector</key>
					<integer>190</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>585</integer>
					<key>endConnector</key>
					<integer>195</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrt.tp</string>
					<key>startConnector</key>
					<integer>195</integer>
				</dict>
			</array>
		</dict>
		<key>uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uparrow.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>887</integer>
					<key>endConnector</key>
					<integer>296</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uparrow</string>
					<key>startConnector</key>
					<integer>296</integer>
				</dict>
			</array>
		</dict>
		<key>vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3000</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>vert.v4</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>200</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vert.ex</string>
					<key>startConnector</key>
					<integer>200</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>Bbbsum</key>
		<array>
			<string>Bbbsum</string>
			<string>Bbbsum.v1</string>
		</array>
		<key>Downarrow</key>
		<array>
			<string>Downarrow</string>
		</array>
		<key>Uparrow</key>
		<array>
			<string>Uparrow</string>
		</array>
		<key>Vert</key>
		<array>
			<string>Vert</string>
			<string>Vert.v1</string>
			<string>Vert.v2</string>
			<string>Vert.v3</string>
			<string>Vert.v4</string>
		</array>
		<key>Vvert</key>
		<array>
			<string>Vvert</string>
			<string>Vvert.v1</string>
			<string>Vvert.v2</string>
			<string>Vvert.v3</string>
			<string>Vvert.v4</string>
		</array>
		<key>awint</key>
		<array>
			<string>awint</string>
			<string>awint.v1</string>
		</array>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.v1</string>
			<string>backslash.v2</string>
			<string>backslash.v3</string>
			<string>backslash.v4</string>
		</array>
		<key>bigcap</key>
		<array>
			<string>bigcap</string>
			<string>bigcap.v1</string>
		</array>
		<key>bigcup</key>
		<array>
			<string>bigcup</string>
			<string>bigcup.v1</string>
		</array>
		<key>bigcupdot</key>
		<array>
			<string>bigcupdot</string>
			<string>bigcupdot.v1</string>
		</array>
		<key>bigodot</key>
		<array>
			<string>bigodot</string>
			<string>bigodot.v1</string>
		</array>
		<key>bigoplus</key>
		<array>
			<string>bigoplus</string>
			<string>bigoplus.v1</string>
		</array>
		<key>bigotimes</key>
		<array>
			<string>bigotimes</string>
			<string>bigotimes.v1</string>
		</array>
		<key>bigsqcap</key>
		<array>
			<string>bigsqcap</string>
			<string>bigsqcap.v1</string>
		</array>
		<key>bigsqcup</key>
		<array>
			<string>bigsqcup</string>
			<string>bigsqcup.v1</string>
		</array>
		<key>bigtimes</key>
		<array>
			<string>bigtimes</string>
			<string>bigtimes.v1</string>
		</array>
		<key>biguplus</key>
		<array>
			<string>biguplus</string>
			<string>biguplus.v1</string>
		</array>
		<key>bigvee</key>
		<array>
			<string>bigvee</string>
			<string>bigvee.v1</string>
		</array>
		<key>bigwedge</key>
		<array>
			<string>bigwedge</string>
			<string>bigwedge.v1</string>
		</array>
		<key>coprod</key>
		<array>
			<string>coprod</string>
			<string>coprod.v1</string>
		</array>
		<key>cuberoot</key>
		<array>
			<string>cuberoot</string>
			<string>cuberoot.v1</string>
			<string>cuberoot.v2</string>
			<string>cuberoot.v3</string>
			<string>cuberoot.v4</string>
		</array>
		<key>downarrow</key>
		<array>
			<string>downarrow</string>
		</array>
		<key>fourthroot</key>
		<array>
			<string>fourthroot</string>
			<string>fourthroot.v1</string>
			<string>fourthroot.v2</string>
			<string>fourthroot.v3</string>
			<string>fourthroot.v4</string>
		</array>
		<key>iiiint</key>
		<array>
			<string>iiiint</string>
			<string>iiiint.v1</string>
		</array>
		<key>iiint</key>
		<array>
			<string>iiint</string>
			<string>iiint.v1</string>
		</array>
		<key>iint</key>
		<array>
			<string>iint</string>
			<string>iint.v1</string>
		</array>
		<key>int</key>
		<array>
			<string>int</string>
			<string>int.v1</string>
		</array>
		<key>intclockwise</key>
		<array>
			<string>intclockwise</string>
			<string>intclockwise.v1</string>
		</array>
		<key>lAngle</key>
		<array>
			<string>lAngle</string>
			<string>lAngle.v1</string>
			<string>lAngle.v2</string>
			<string>lAngle.v3</string>
			<string>lAngle.v4</string>
		</array>
		<key>lBrack</key>
		<array>
			<string>lBrack</string>
			<string>lBrack.v1</string>
			<string>lBrack.v2</string>
			<string>lBrack.v3</string>
			<string>lBrack.v4</string>
		</array>
		<key>langle</key>
		<array>
			<string>langle</string>
			<string>langle.v1</string>
			<string>langle.v2</string>
			<string>langle.v3</string>
			<string>langle.v4</string>
		</array>
		<key>lbrace</key>
		<array>
			<string>lbrace</string>
			<string>lbrace.v1</string>
			<string>lbrace.v2</string>
			<string>lbrace.v3</string>
			<string>lbrace.v4</string>
		</array>
		<key>lbrack</key>
		<array>
			<string>lbrack</string>
			<string>lbrack.v1</string>
			<string>lbrack.v2</string>
			<string>lbrack.v3</string>
			<string>lbrack.v4</string>
		</array>
		<key>lceil</key>
		<array>
			<string>lceil</string>
			<string>lceil.v1</string>
			<string>lceil.v2</string>
			<string>lceil.v3</string>
			<string>lceil.v4</string>
		</array>
		<key>lfloor</key>
		<array>
			<string>lfloor</string>
			<string>lfloor.v1</string>
			<string>lfloor.v2</string>
			<string>lfloor.v3</string>
			<string>lfloor.v4</string>
		</array>
		<key>lparen</key>
		<array>
			<string>lparen</string>
			<string>lparen.v1</string>
			<string>lparen.v2</string>
			<string>lparen.v3</string>
			<string>lparen.v4</string>
		</array>
		<key>mathslash</key>
		<array>
			<string>mathslash</string>
			<string>mathslash.v1</string>
			<string>mathslash.v2</string>
			<string>mathslash.v3</string>
			<string>mathslash.v4</string>
		</array>
		<key>mid</key>
		<array>
			<string>mid</string>
		</array>
		<key>ni.ssty1</key>
		<array>
			<string>sqrt.ssty1</string>
			<string>sqrt.v1</string>
			<string>sqrt.v2</string>
			<string>sqrt.v3</string>
			<string>sqrt.v4</string>
		</array>
		<key>oiiint</key>
		<array>
			<string>oiiint</string>
			<string>oiiint.v1</string>
		</array>
		<key>oiint</key>
		<array>
			<string>oiint</string>
			<string>oiint.v1</string>
		</array>
		<key>oint</key>
		<array>
			<string>oint</string>
			<string>oint.v1</string>
		</array>
		<key>ointctrclockwise</key>
		<array>
			<string>ointctrclockwise</string>
			<string>ointctrclockwise.v1</string>
		</array>
		<key>parallel</key>
		<array>
			<string>parallel</string>
		</array>
		<key>prod</key>
		<array>
			<string>prod</string>
			<string>prod.v1</string>
		</array>
		<key>rAngle</key>
		<array>
			<string>rAngle</string>
			<string>rAngle.v1</string>
			<string>rAngle.v2</string>
			<string>rAngle.v3</string>
			<string>rAngle.v4</string>
		</array>
		<key>rBrack</key>
		<array>
			<string>rBrack</string>
			<string>rBrack.v1</string>
			<string>rBrack.v2</string>
			<string>rBrack.v3</string>
			<string>rBrack.v4</string>
		</array>
		<key>rangle</key>
		<array>
			<string>rangle</string>
			<string>rangle.v1</string>
			<string>rangle.v2</string>
			<string>rangle.v3</string>
			<string>rangle.v4</string>
		</array>
		<key>rbrace</key>
		<array>
			<string>rbrace</string>
			<string>rbrace.v1</string>
			<string>rbrace.v2</string>
			<string>rbrace.v3</string>
			<string>rbrace.v4</string>
		</array>
		<key>rbrack</key>
		<array>
			<string>rbrack</string>
			<string>rbrack.v1</string>
			<string>rbrack.v2</string>
			<string>rbrack.v3</string>
			<string>rbrack.v4</string>
		</array>
		<key>rceil</key>
		<array>
			<string>rceil</string>
			<string>rceil.v1</string>
			<string>rceil.v2</string>
			<string>rceil.v3</string>
			<string>rceil.v4</string>
		</array>
		<key>rfloor</key>
		<array>
			<string>rfloor</string>
			<string>rfloor.v1</string>
			<string>rfloor.v2</string>
			<string>rfloor.v3</string>
			<string>rfloor.v4</string>
		</array>
		<key>rparen</key>
		<array>
			<string>rparen</string>
			<string>rparen.v1</string>
			<string>rparen.v2</string>
			<string>rparen.v3</string>
			<string>rparen.v4</string>
		</array>
		<key>sqrt</key>
		<array>
			<string>sqrt</string>
			<string>sqrt.v1</string>
			<string>sqrt.v2</string>
			<string>sqrt.v3</string>
			<string>sqrt.v4</string>
		</array>
		<key>sum</key>
		<array>
			<string>sum</string>
			<string>sum.v1</string>
		</array>
		<key>uparrow</key>
		<array>
			<string>uparrow</string>
		</array>
		<key>varointclockwise</key>
		<array>
			<string>varointclockwise</string>
			<string>varointclockwise.v1</string>
		</array>
		<key>vert</key>
		<array>
			<string>vert</string>
			<string>vert.v1</string>
			<string>vert.v2</string>
			<string>vert.v3</string>
			<string>vert.v4</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
