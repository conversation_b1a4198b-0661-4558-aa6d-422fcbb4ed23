<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>Planckconst.sst</key>
		<integer>302</integer>
		<key>Planckconst.st</key>
		<integer>302</integer>
		<key>acutecomb</key>
		<integer>180</integer>
		<key>acutedbl</key>
		<integer>-238</integer>
		<key>bar</key>
		<integer>-205</integer>
		<key>candracomb</key>
		<integer>-220</integer>
		<key>ddddot</key>
		<integer>-406</integer>
		<key>dddot</key>
		<integer>-312</integer>
		<key>ddot</key>
		<integer>-212</integer>
		<key>dot</key>
		<integer>-200</integer>
		<key>dotlessi</key>
		<integer>124</integer>
		<key>dotlessj</key>
		<integer>112</integer>
		<key>ell</key>
		<integer>371</integer>
		<key>gravecomb</key>
		<integer>180</integer>
		<key>hookabovecomb</key>
		<integer>-220</integer>
		<key>imath</key>
		<integer>149</integer>
		<key>imath.sst</key>
		<integer>160</integer>
		<key>imath.st</key>
		<integer>164</integer>
		<key>jmath</key>
		<integer>181</integer>
		<key>jmath.sst</key>
		<integer>176</integer>
		<key>jmath.st</key>
		<integer>163</integer>
		<key>mbfBeta</key>
		<integer>304</integer>
		<key>mbfGamma</key>
		<integer>347</integer>
		<key>mbfLambda</key>
		<integer>314</integer>
		<key>mbfPhi</key>
		<integer>354</integer>
		<key>mbfPi</key>
		<integer>424</integer>
		<key>mbfPsi</key>
		<integer>390</integer>
		<key>mbfcalA</key>
		<integer>661</integer>
		<key>mbfcalB</key>
		<integer>448</integer>
		<key>mbfcalC</key>
		<integer>350</integer>
		<key>mbfcalD</key>
		<integer>476</integer>
		<key>mbfcalE</key>
		<integer>406</integer>
		<key>mbfcalF</key>
		<integer>562</integer>
		<key>mbfcalG</key>
		<integer>419</integer>
		<key>mbfcalH</key>
		<integer>542</integer>
		<key>mbfcalI</key>
		<integer>478</integer>
		<key>mbfcalJ</key>
		<integer>619</integer>
		<key>mbfcalK</key>
		<integer>416</integer>
		<key>mbfcalL</key>
		<integer>462</integer>
		<key>mbfcalM</key>
		<integer>740</integer>
		<key>mbfcalN</key>
		<integer>672</integer>
		<key>mbfcalO</key>
		<integer>566</integer>
		<key>mbfcalP</key>
		<integer>494</integer>
		<key>mbfcalQ</key>
		<integer>570</integer>
		<key>mbfcalR</key>
		<integer>496</integer>
		<key>mbfcalS</key>
		<integer>471</integer>
		<key>mbfcalT</key>
		<integer>522</integer>
		<key>mbfcalU</key>
		<integer>478</integer>
		<key>mbfcalV</key>
		<integer>434</integer>
		<key>mbfcalW</key>
		<integer>683</integer>
		<key>mbfcalX</key>
		<integer>568</integer>
		<key>mbfcalY</key>
		<integer>510</integer>
		<key>mbfcalZ</key>
		<integer>529</integer>
		<key>mbfdotlessi</key>
		<integer>140</integer>
		<key>mbfdotlessj</key>
		<integer>154</integer>
		<key>mbfepsilon</key>
		<integer>299</integer>
		<key>mbfgamma</key>
		<integer>388</integer>
		<key>mbfimath</key>
		<integer>210</integer>
		<key>mbfitA</key>
		<integer>462</integer>
		<key>mbfitAlpha</key>
		<integer>454</integer>
		<key>mbfitB</key>
		<integer>380</integer>
		<key>mbfitBeta</key>
		<integer>380</integer>
		<key>mbfitC</key>
		<integer>404</integer>
		<key>mbfitChi</key>
		<integer>460</integer>
		<key>mbfitD</key>
		<integer>378</integer>
		<key>mbfitDelta</key>
		<integer>440</integer>
		<key>mbfitE</key>
		<integer>362</integer>
		<key>mbfitEpsilon</key>
		<integer>350</integer>
		<key>mbfitEta</key>
		<integer>408</integer>
		<key>mbfitF</key>
		<integer>346</integer>
		<key>mbfitG</key>
		<integer>432</integer>
		<key>mbfitGamma</key>
		<integer>353</integer>
		<key>mbfitH</key>
		<integer>394</integer>
		<key>mbfitI</key>
		<integer>172</integer>
		<key>mbfitIota</key>
		<integer>176</integer>
		<key>mbfitJ</key>
		<integer>318</integer>
		<key>mbfitK</key>
		<integer>348</integer>
		<key>mbfitKappa</key>
		<integer>366</integer>
		<key>mbfitL</key>
		<integer>280</integer>
		<key>mbfitLambda</key>
		<integer>482</integer>
		<key>mbfitM</key>
		<integer>456</integer>
		<key>mbfitMu</key>
		<integer>468</integer>
		<key>mbfitN</key>
		<integer>406</integer>
		<key>mbfitNu</key>
		<integer>416</integer>
		<key>mbfitO</key>
		<integer>442</integer>
		<key>mbfitOmega</key>
		<integer>465</integer>
		<key>mbfitOmicron</key>
		<integer>454</integer>
		<key>mbfitP</key>
		<integer>349</integer>
		<key>mbfitPhi</key>
		<integer>502</integer>
		<key>mbfitPi</key>
		<integer>470</integer>
		<key>mbfitPsi</key>
		<integer>474</integer>
		<key>mbfitQ</key>
		<integer>447</integer>
		<key>mbfitR</key>
		<integer>370</integer>
		<key>mbfitRho</key>
		<integer>349</integer>
		<key>mbfitS</key>
		<integer>374</integer>
		<key>mbfitSigma</key>
		<integer>437</integer>
		<key>mbfitT</key>
		<integer>352</integer>
		<key>mbfitTau</key>
		<integer>336</integer>
		<key>mbfitTheta</key>
		<integer>444</integer>
		<key>mbfitU</key>
		<integer>408</integer>
		<key>mbfitUpsilon</key>
		<integer>408</integer>
		<key>mbfitV</key>
		<integer>344</integer>
		<key>mbfitW</key>
		<integer>496</integer>
		<key>mbfitX</key>
		<integer>458</integer>
		<key>mbfitXi</key>
		<integer>433</integer>
		<key>mbfitY</key>
		<integer>344</integer>
		<key>mbfitZ</key>
		<integer>361</integer>
		<key>mbfitZeta</key>
		<integer>327</integer>
		<key>mbfita</key>
		<integer>344</integer>
		<key>mbfitalpha</key>
		<integer>362</integer>
		<key>mbfitb</key>
		<integer>322</integer>
		<key>mbfitbeta</key>
		<integer>408</integer>
		<key>mbfitc</key>
		<integer>303</integer>
		<key>mbfitchi</key>
		<integer>384</integer>
		<key>mbfitd</key>
		<integer>326</integer>
		<key>mbfitdelta</key>
		<integer>350</integer>
		<key>mbfite</key>
		<integer>304</integer>
		<key>mbfitepsilon</key>
		<integer>318</integer>
		<key>mbfiteta</key>
		<integer>342</integer>
		<key>mbfitf</key>
		<integer>280</integer>
		<key>mbfitg</key>
		<integer>333</integer>
		<key>mbfitgamma</key>
		<integer>394</integer>
		<key>mbfith</key>
		<integer>302</integer>
		<key>mbfiti</key>
		<integer>176</integer>
		<key>mbfitiota</key>
		<integer>195</integer>
		<key>mbfitj</key>
		<integer>198</integer>
		<key>mbfitk</key>
		<integer>290</integer>
		<key>mbfitkappa</key>
		<integer>336</integer>
		<key>mbfitl</key>
		<integer>168</integer>
		<key>mbfitlambda</key>
		<integer>382</integer>
		<key>mbfitm</key>
		<integer>491</integer>
		<key>mbfitmu</key>
		<integer>378</integer>
		<key>mbfitn</key>
		<integer>350</integer>
		<key>mbfitnabla</key>
		<integer>359</integer>
		<key>mbfitnu</key>
		<integer>361</integer>
		<key>mbfito</key>
		<integer>321</integer>
		<key>mbfitomega</key>
		<integer>396</integer>
		<key>mbfitomicron</key>
		<integer>327</integer>
		<key>mbfitp</key>
		<integer>354</integer>
		<key>mbfitpartial</key>
		<integer>332</integer>
		<key>mbfitphi</key>
		<integer>422</integer>
		<key>mbfitpi</key>
		<integer>378</integer>
		<key>mbfitpsi</key>
		<integer>428</integer>
		<key>mbfitq</key>
		<integer>325</integer>
		<key>mbfitr</key>
		<integer>254</integer>
		<key>mbfitrho</key>
		<integer>358</integer>
		<key>mbfits</key>
		<integer>278</integer>
		<key>mbfitsigma</key>
		<integer>340</integer>
		<key>mbfitt</key>
		<integer>208</integer>
		<key>mbfittau</key>
		<integer>304</integer>
		<key>mbfittheta</key>
		<integer>384</integer>
		<key>mbfitu</key>
		<integer>328</integer>
		<key>mbfitupsilon</key>
		<integer>337</integer>
		<key>mbfitv</key>
		<integer>290</integer>
		<key>mbfitvarTheta</key>
		<integer>444</integer>
		<key>mbfitvarepsilon</key>
		<integer>301</integer>
		<key>mbfitvarphi</key>
		<integer>403</integer>
		<key>mbfitvarpi</key>
		<integer>396</integer>
		<key>mbfitvarrho</key>
		<integer>342</integer>
		<key>mbfitvarsigma</key>
		<integer>346</integer>
		<key>mbfitvartheta</key>
		<integer>365</integer>
		<key>mbfitw</key>
		<integer>401</integer>
		<key>mbfitx</key>
		<integer>340</integer>
		<key>mbfitxi</key>
		<integer>360</integer>
		<key>mbfity</key>
		<integer>324</integer>
		<key>mbfitz</key>
		<integer>294</integer>
		<key>mbfitzeta</key>
		<integer>345</integer>
		<key>mbfj</key>
		<integer>154</integer>
		<key>mbfjmath</key>
		<integer>250</integer>
		<key>mbflambda</key>
		<integer>282</integer>
		<key>mbfscrA</key>
		<integer>736</integer>
		<key>mbfscrB</key>
		<integer>706</integer>
		<key>mbfscrC</key>
		<integer>628</integer>
		<key>mbfscrD</key>
		<integer>560</integer>
		<key>mbfscrE</key>
		<integer>482</integer>
		<key>mbfscrF</key>
		<integer>638</integer>
		<key>mbfscrG</key>
		<integer>532</integer>
		<key>mbfscrH</key>
		<integer>678</integer>
		<key>mbfscrI</key>
		<integer>790</integer>
		<key>mbfscrJ</key>
		<integer>682</integer>
		<key>mbfscrK</key>
		<integer>734</integer>
		<key>mbfscrL</key>
		<integer>732</integer>
		<key>mbfscrM</key>
		<integer>838</integer>
		<key>mbfscrN</key>
		<integer>850</integer>
		<key>mbfscrO</key>
		<integer>604</integer>
		<key>mbfscrP</key>
		<integer>724</integer>
		<key>mbfscrQ</key>
		<integer>554</integer>
		<key>mbfscrR</key>
		<integer>720</integer>
		<key>mbfscrS</key>
		<integer>774</integer>
		<key>mbfscrT</key>
		<integer>674</integer>
		<key>mbfscrU</key>
		<integer>572</integer>
		<key>mbfscrV</key>
		<integer>622</integer>
		<key>mbfscrW</key>
		<integer>788</integer>
		<key>mbfscrX</key>
		<integer>650</integer>
		<key>mbfscrY</key>
		<integer>608</integer>
		<key>mbfscrZ</key>
		<integer>650</integer>
		<key>mbftheta</key>
		<integer>350</integer>
		<key>mbfvarepsilon</key>
		<integer>266</integer>
		<key>mcalA</key>
		<integer>612</integer>
		<key>mcalB</key>
		<integer>417</integer>
		<key>mcalC</key>
		<integer>328</integer>
		<key>mcalD</key>
		<integer>416</integer>
		<key>mcalE</key>
		<integer>386</integer>
		<key>mcalF</key>
		<integer>510</integer>
		<key>mcalG</key>
		<integer>400</integer>
		<key>mcalH</key>
		<integer>478</integer>
		<key>mcalI</key>
		<integer>414</integer>
		<key>mcalJ</key>
		<integer>530</integer>
		<key>mcalK</key>
		<integer>400</integer>
		<key>mcalL</key>
		<integer>429</integer>
		<key>mcalM</key>
		<integer>678</integer>
		<key>mcalN</key>
		<integer>614</integer>
		<key>mcalO</key>
		<integer>462</integer>
		<key>mcalP</key>
		<integer>422</integer>
		<key>mcalQ</key>
		<integer>448</integer>
		<key>mcalR</key>
		<integer>416</integer>
		<key>mcalS</key>
		<integer>446</integer>
		<key>mcalT</key>
		<integer>453</integer>
		<key>mcalU</key>
		<integer>463</integer>
		<key>mcalV</key>
		<integer>370</integer>
		<key>mcalW</key>
		<integer>593</integer>
		<key>mcalX</key>
		<integer>532</integer>
		<key>mcalY</key>
		<integer>498</integer>
		<key>mcalZ</key>
		<integer>482</integer>
		<key>mitA</key>
		<integer>440</integer>
		<key>mitA.sst</key>
		<integer>428</integer>
		<key>mitA.st</key>
		<integer>438</integer>
		<key>mitAlpha</key>
		<integer>434</integer>
		<key>mitAlpha.sst</key>
		<integer>438</integer>
		<key>mitAlpha.st</key>
		<integer>436</integer>
		<key>mitB</key>
		<integer>346</integer>
		<key>mitB.sst</key>
		<integer>370</integer>
		<key>mitB.st</key>
		<integer>370</integer>
		<key>mitBeta</key>
		<integer>332</integer>
		<key>mitBeta.sst</key>
		<integer>370</integer>
		<key>mitBeta.st</key>
		<integer>370</integer>
		<key>mitC</key>
		<integer>386</integer>
		<key>mitC.sst</key>
		<integer>394</integer>
		<key>mitC.st</key>
		<integer>404</integer>
		<key>mitChi</key>
		<integer>446</integer>
		<key>mitChi.sst</key>
		<integer>392</integer>
		<key>mitChi.st</key>
		<integer>392</integer>
		<key>mitD</key>
		<integer>375</integer>
		<key>mitD.sst</key>
		<integer>378</integer>
		<key>mitD.st</key>
		<integer>378</integer>
		<key>mitDelta</key>
		<integer>467</integer>
		<key>mitDelta.sst</key>
		<integer>450</integer>
		<key>mitDelta.st</key>
		<integer>440</integer>
		<key>mitE</key>
		<integer>338</integer>
		<key>mitE.sst</key>
		<integer>350</integer>
		<key>mitE.st</key>
		<integer>350</integer>
		<key>mitEpsilon</key>
		<integer>332</integer>
		<key>mitEpsilon.sst</key>
		<integer>350</integer>
		<key>mitEpsilon.st</key>
		<integer>350</integer>
		<key>mitEta</key>
		<integer>412</integer>
		<key>mitEta.sst</key>
		<integer>408</integer>
		<key>mitEta.st</key>
		<integer>408</integer>
		<key>mitF</key>
		<integer>342</integer>
		<key>mitF.sst</key>
		<integer>346</integer>
		<key>mitF.st</key>
		<integer>346</integer>
		<key>mitG</key>
		<integer>424</integer>
		<key>mitG.sst</key>
		<integer>432</integer>
		<key>mitG.st</key>
		<integer>432</integer>
		<key>mitGamma</key>
		<integer>354</integer>
		<key>mitGamma.sst</key>
		<integer>383</integer>
		<key>mitGamma.st</key>
		<integer>393</integer>
		<key>mitH</key>
		<integer>392</integer>
		<key>mitH.sst</key>
		<integer>394</integer>
		<key>mitH.st</key>
		<integer>394</integer>
		<key>mitI</key>
		<integer>154</integer>
		<key>mitI.sst</key>
		<integer>198</integer>
		<key>mitI.st</key>
		<integer>172</integer>
		<key>mitIota</key>
		<integer>162</integer>
		<key>mitIota.sst</key>
		<integer>194</integer>
		<key>mitIota.st</key>
		<integer>176</integer>
		<key>mitJ</key>
		<integer>384</integer>
		<key>mitJ.sst</key>
		<integer>318</integer>
		<key>mitJ.st</key>
		<integer>318</integer>
		<key>mitK</key>
		<integer>363</integer>
		<key>mitK.sst</key>
		<integer>348</integer>
		<key>mitK.st</key>
		<integer>348</integer>
		<key>mitKappa</key>
		<integer>355</integer>
		<key>mitKappa.sst</key>
		<integer>366</integer>
		<key>mitKappa.st</key>
		<integer>366</integer>
		<key>mitL</key>
		<integer>254</integer>
		<key>mitL.sst</key>
		<integer>268</integer>
		<key>mitL.st</key>
		<integer>268</integer>
		<key>mitLambda</key>
		<integer>456</integer>
		<key>mitLambda.sst</key>
		<integer>452</integer>
		<key>mitLambda.st</key>
		<integer>488</integer>
		<key>mitM</key>
		<integer>416</integer>
		<key>mitM.sst</key>
		<integer>456</integer>
		<key>mitM.st</key>
		<integer>446</integer>
		<key>mitMu</key>
		<integer>442</integer>
		<key>mitMu.sst</key>
		<integer>468</integer>
		<key>mitMu.st</key>
		<integer>459</integer>
		<key>mitN</key>
		<integer>396</integer>
		<key>mitN.sst</key>
		<integer>406</integer>
		<key>mitN.st</key>
		<integer>406</integer>
		<key>mitNu</key>
		<integer>420</integer>
		<key>mitNu.sst</key>
		<integer>416</integer>
		<key>mitNu.st</key>
		<integer>416</integer>
		<key>mitO</key>
		<integer>422</integer>
		<key>mitO.sst</key>
		<integer>442</integer>
		<key>mitO.st</key>
		<integer>442</integer>
		<key>mitOmega</key>
		<integer>500</integer>
		<key>mitOmega.sst</key>
		<integer>499</integer>
		<key>mitOmega.st</key>
		<integer>509</integer>
		<key>mitOmicron</key>
		<integer>452</integer>
		<key>mitOmicron.sst</key>
		<integer>454</integer>
		<key>mitOmicron.st</key>
		<integer>454</integer>
		<key>mitP</key>
		<integer>333</integer>
		<key>mitP.sst</key>
		<integer>349</integer>
		<key>mitP.st</key>
		<integer>349</integer>
		<key>mitPhi</key>
		<integer>420</integer>
		<key>mitPhi.sst</key>
		<integer>442</integer>
		<key>mitPhi.st</key>
		<integer>458</integer>
		<key>mitPi</key>
		<integer>450</integer>
		<key>mitPi.sst</key>
		<integer>476</integer>
		<key>mitPi.st</key>
		<integer>476</integer>
		<key>mitPsi</key>
		<integer>440</integer>
		<key>mitPsi.sst</key>
		<integer>470</integer>
		<key>mitPsi.st</key>
		<integer>460</integer>
		<key>mitQ</key>
		<integer>468</integer>
		<key>mitQ.sst</key>
		<integer>437</integer>
		<key>mitQ.st</key>
		<integer>447</integer>
		<key>mitR</key>
		<integer>351</integer>
		<key>mitR.sst</key>
		<integer>348</integer>
		<key>mitR.st</key>
		<integer>348</integer>
		<key>mitRho</key>
		<integer>349</integer>
		<key>mitRho.sst</key>
		<integer>349</integer>
		<key>mitRho.st</key>
		<integer>349</integer>
		<key>mitS</key>
		<integer>360</integer>
		<key>mitS.sst</key>
		<integer>350</integer>
		<key>mitS.st</key>
		<integer>350</integer>
		<key>mitSigma</key>
		<integer>433</integer>
		<key>mitSigma.sst</key>
		<integer>437</integer>
		<key>mitSigma.st</key>
		<integer>437</integer>
		<key>mitT</key>
		<integer>354</integer>
		<key>mitT.sst</key>
		<integer>348</integer>
		<key>mitT.st</key>
		<integer>348</integer>
		<key>mitTau</key>
		<integer>330</integer>
		<key>mitTau.sst</key>
		<integer>348</integer>
		<key>mitTau.st</key>
		<integer>338</integer>
		<key>mitTheta</key>
		<integer>453</integer>
		<key>mitTheta.sst</key>
		<integer>465</integer>
		<key>mitTheta.st</key>
		<integer>468</integer>
		<key>mitU</key>
		<integer>406</integer>
		<key>mitU.sst</key>
		<integer>407</integer>
		<key>mitU.st</key>
		<integer>408</integer>
		<key>mitUpsilon</key>
		<integer>412</integer>
		<key>mitUpsilon.sst</key>
		<integer>412</integer>
		<key>mitUpsilon.st</key>
		<integer>412</integer>
		<key>mitV</key>
		<integer>316</integer>
		<key>mitV.sst</key>
		<integer>338</integer>
		<key>mitV.st</key>
		<integer>348</integer>
		<key>mitW</key>
		<integer>502</integer>
		<key>mitW.sst</key>
		<integer>470</integer>
		<key>mitW.st</key>
		<integer>484</integer>
		<key>mitX</key>
		<integer>418</integer>
		<key>mitX.sst</key>
		<integer>376</integer>
		<key>mitX.st</key>
		<integer>376</integer>
		<key>mitXi</key>
		<integer>406</integer>
		<key>mitXi.sst</key>
		<integer>429</integer>
		<key>mitXi.st</key>
		<integer>439</integer>
		<key>mitY</key>
		<integer>338</integer>
		<key>mitY.sst</key>
		<integer>306</integer>
		<key>mitY.st</key>
		<integer>316</integer>
		<key>mitZ</key>
		<integer>377</integer>
		<key>mitZ.sst</key>
		<integer>319</integer>
		<key>mitZ.st</key>
		<integer>339</integer>
		<key>mitZeta</key>
		<integer>393</integer>
		<key>mitZeta.sst</key>
		<integer>327</integer>
		<key>mitZeta.st</key>
		<integer>337</integer>
		<key>mita</key>
		<integer>342</integer>
		<key>mita.sst</key>
		<integer>322</integer>
		<key>mita.st</key>
		<integer>322</integer>
		<key>mitalpha</key>
		<integer>348</integer>
		<key>mitalpha.sst</key>
		<integer>328</integer>
		<key>mitalpha.st</key>
		<integer>338</integer>
		<key>mitb</key>
		<integer>292</integer>
		<key>mitb.sst</key>
		<integer>322</integer>
		<key>mitb.st</key>
		<integer>322</integer>
		<key>mitbeta</key>
		<integer>389</integer>
		<key>mitbeta.sst</key>
		<integer>388</integer>
		<key>mitbeta.st</key>
		<integer>372</integer>
		<key>mitc</key>
		<integer>305</integer>
		<key>mitc.sst</key>
		<integer>303</integer>
		<key>mitc.st</key>
		<integer>303</integer>
		<key>mitchi</key>
		<integer>375</integer>
		<key>mitchi.sst</key>
		<integer>374</integer>
		<key>mitchi.st</key>
		<integer>374</integer>
		<key>mitd</key>
		<integer>376</integer>
		<key>mitd.sst</key>
		<integer>326</integer>
		<key>mitd.st</key>
		<integer>326</integer>
		<key>mitdelta</key>
		<integer>337</integer>
		<key>mitdelta.sst</key>
		<integer>330</integer>
		<key>mitdelta.st</key>
		<integer>350</integer>
		<key>mite</key>
		<integer>320</integer>
		<key>mite.sst</key>
		<integer>304</integer>
		<key>mite.st</key>
		<integer>304</integer>
		<key>mitepsilon</key>
		<integer>322</integer>
		<key>mitepsilon.sst</key>
		<integer>298</integer>
		<key>mitepsilon.st</key>
		<integer>298</integer>
		<key>miteta</key>
		<integer>328</integer>
		<key>miteta.sst</key>
		<integer>322</integer>
		<key>miteta.st</key>
		<integer>322</integer>
		<key>mitf</key>
		<integer>272</integer>
		<key>mitf.sst</key>
		<integer>280</integer>
		<key>mitf.st</key>
		<integer>280</integer>
		<key>mitg</key>
		<integer>358</integer>
		<key>mitg.sst</key>
		<integer>333</integer>
		<key>mitg.st</key>
		<integer>333</integer>
		<key>mitgamma</key>
		<integer>406</integer>
		<key>mitgamma.sst</key>
		<integer>364</integer>
		<key>mitgamma.st</key>
		<integer>384</integer>
		<key>miti</key>
		<integer>158</integer>
		<key>miti.sst</key>
		<integer>176</integer>
		<key>miti.st</key>
		<integer>176</integer>
		<key>mitiota</key>
		<integer>186</integer>
		<key>mitiota.sst</key>
		<integer>175</integer>
		<key>mitiota.st</key>
		<integer>185</integer>
		<key>mitj</key>
		<integer>160</integer>
		<key>mitj.sst</key>
		<integer>198</integer>
		<key>mitj.st</key>
		<integer>198</integer>
		<key>mitk</key>
		<integer>263</integer>
		<key>mitk.sst</key>
		<integer>290</integer>
		<key>mitk.st</key>
		<integer>290</integer>
		<key>mitkappa</key>
		<integer>322</integer>
		<key>mitkappa.sst</key>
		<integer>316</integer>
		<key>mitkappa.st</key>
		<integer>330</integer>
		<key>mitl</key>
		<integer>172</integer>
		<key>mitl.sst</key>
		<integer>168</integer>
		<key>mitl.st</key>
		<integer>168</integer>
		<key>mitlambda</key>
		<integer>368</integer>
		<key>mitlambda.sst</key>
		<integer>362</integer>
		<key>mitlambda.st</key>
		<integer>362</integer>
		<key>mitm</key>
		<integer>462</integer>
		<key>mitm.sst</key>
		<integer>491</integer>
		<key>mitm.st</key>
		<integer>491</integer>
		<key>mitmu</key>
		<integer>358</integer>
		<key>mitmu.sst</key>
		<integer>358</integer>
		<key>mitmu.st</key>
		<integer>368</integer>
		<key>mitn</key>
		<integer>322</integer>
		<key>mitn.sst</key>
		<integer>350</integer>
		<key>mitn.st</key>
		<integer>332</integer>
		<key>mitnabla</key>
		<integer>345</integer>
		<key>mitnabla.sst</key>
		<integer>359</integer>
		<key>mitnabla.st</key>
		<integer>359</integer>
		<key>mitnu</key>
		<integer>334</integer>
		<key>mitnu.sst</key>
		<integer>338</integer>
		<key>mitnu.st</key>
		<integer>341</integer>
		<key>mito</key>
		<integer>332</integer>
		<key>mito.sst</key>
		<integer>321</integer>
		<key>mito.st</key>
		<integer>321</integer>
		<key>mitomega</key>
		<integer>389</integer>
		<key>mitomega.sst</key>
		<integer>376</integer>
		<key>mitomega.st</key>
		<integer>386</integer>
		<key>mitomicron</key>
		<integer>324</integer>
		<key>mitomicron.sst</key>
		<integer>327</integer>
		<key>mitomicron.st</key>
		<integer>307</integer>
		<key>mitp</key>
		<integer>341</integer>
		<key>mitp.sst</key>
		<integer>326</integer>
		<key>mitp.st</key>
		<integer>354</integer>
		<key>mitpartial</key>
		<integer>388</integer>
		<key>mitpartial.sst</key>
		<integer>332</integer>
		<key>mitpartial.st</key>
		<integer>332</integer>
		<key>mitphi</key>
		<integer>414</integer>
		<key>mitphi.sst</key>
		<integer>412</integer>
		<key>mitphi.st</key>
		<integer>412</integer>
		<key>mitpi</key>
		<integer>386</integer>
		<key>mitpi.sst</key>
		<integer>358</integer>
		<key>mitpi.st</key>
		<integer>368</integer>
		<key>mitpsi</key>
		<integer>418</integer>
		<key>mitpsi.sst</key>
		<integer>426</integer>
		<key>mitpsi.st</key>
		<integer>438</integer>
		<key>mitq</key>
		<integer>328</integer>
		<key>mitq.sst</key>
		<integer>305</integer>
		<key>mitq.st</key>
		<integer>325</integer>
		<key>mitr</key>
		<integer>242</integer>
		<key>mitr.sst</key>
		<integer>234</integer>
		<key>mitr.st</key>
		<integer>244</integer>
		<key>mitrho</key>
		<integer>348</integer>
		<key>mitrho.sst</key>
		<integer>338</integer>
		<key>mitrho.st</key>
		<integer>348</integer>
		<key>mits</key>
		<integer>280</integer>
		<key>mits.sst</key>
		<integer>278</integer>
		<key>mits.st</key>
		<integer>278</integer>
		<key>mitsigma</key>
		<integer>333</integer>
		<key>mitsigma.sst</key>
		<integer>320</integer>
		<key>mitsigma.st</key>
		<integer>310</integer>
		<key>mitt</key>
		<integer>180</integer>
		<key>mitt.sst</key>
		<integer>198</integer>
		<key>mitt.st</key>
		<integer>208</integer>
		<key>mittau</key>
		<integer>297</integer>
		<key>mittau.sst</key>
		<integer>301</integer>
		<key>mittau.st</key>
		<integer>302</integer>
		<key>mittheta</key>
		<integer>376</integer>
		<key>mittheta.sst</key>
		<integer>384</integer>
		<key>mittheta.st</key>
		<integer>378</integer>
		<key>mitu</key>
		<integer>314</integer>
		<key>mitu.sst</key>
		<integer>318</integer>
		<key>mitu.st</key>
		<integer>328</integer>
		<key>mitupsilon</key>
		<integer>334</integer>
		<key>mitupsilon.sst</key>
		<integer>329</integer>
		<key>mitupsilon.st</key>
		<integer>339</integer>
		<key>mitv</key>
		<integer>266</integer>
		<key>mitv.sst</key>
		<integer>270</integer>
		<key>mitv.st</key>
		<integer>290</integer>
		<key>mitvarTheta</key>
		<integer>453</integer>
		<key>mitvarTheta.sst</key>
		<integer>468</integer>
		<key>mitvarTheta.st</key>
		<integer>470</integer>
		<key>mitvarepsilon</key>
		<integer>294</integer>
		<key>mitvarepsilon.sst</key>
		<integer>281</integer>
		<key>mitvarepsilon.st</key>
		<integer>291</integer>
		<key>mitvarphi</key>
		<integer>368</integer>
		<key>mitvarphi.sst</key>
		<integer>397</integer>
		<key>mitvarphi.st</key>
		<integer>399</integer>
		<key>mitvarpi</key>
		<integer>399</integer>
		<key>mitvarpi.sst</key>
		<integer>376</integer>
		<key>mitvarpi.st</key>
		<integer>386</integer>
		<key>mitvarrho</key>
		<integer>342</integer>
		<key>mitvarrho.sst</key>
		<integer>322</integer>
		<key>mitvarrho.st</key>
		<integer>332</integer>
		<key>mitvarsigma</key>
		<integer>324</integer>
		<key>mitvarsigma.sst</key>
		<integer>336</integer>
		<key>mitvarsigma.st</key>
		<integer>336</integer>
		<key>mitvartheta</key>
		<integer>356</integer>
		<key>mitvartheta.sst</key>
		<integer>365</integer>
		<key>mitvartheta.st</key>
		<integer>365</integer>
		<key>mitw</key>
		<integer>366</integer>
		<key>mitw.sst</key>
		<integer>371</integer>
		<key>mitw.st</key>
		<integer>381</integer>
		<key>mitx</key>
		<integer>316</integer>
		<key>mitx.sst</key>
		<integer>310</integer>
		<key>mitx.st</key>
		<integer>310</integer>
		<key>mitxi</key>
		<integer>365</integer>
		<key>mitxi.sst</key>
		<integer>340</integer>
		<key>mitxi.st</key>
		<integer>360</integer>
		<key>mity</key>
		<integer>296</integer>
		<key>mity.sst</key>
		<integer>324</integer>
		<key>mity.st</key>
		<integer>324</integer>
		<key>mitz</key>
		<integer>301</integer>
		<key>mitz.sst</key>
		<integer>294</integer>
		<key>mitz.st</key>
		<integer>294</integer>
		<key>mitzeta</key>
		<integer>319</integer>
		<key>mitzeta.sst</key>
		<integer>325</integer>
		<key>mitzeta.st</key>
		<integer>297</integer>
		<key>mscrA</key>
		<integer>734</integer>
		<key>mscrB</key>
		<integer>684</integer>
		<key>mscrC</key>
		<integer>600</integer>
		<key>mscrD</key>
		<integer>564</integer>
		<key>mscrE</key>
		<integer>452</integer>
		<key>mscrF</key>
		<integer>632</integer>
		<key>mscrG</key>
		<integer>526</integer>
		<key>mscrH</key>
		<integer>684</integer>
		<key>mscrI</key>
		<integer>764</integer>
		<key>mscrJ</key>
		<integer>710</integer>
		<key>mscrK</key>
		<integer>752</integer>
		<key>mscrL</key>
		<integer>708</integer>
		<key>mscrM</key>
		<integer>844</integer>
		<key>mscrN</key>
		<integer>805</integer>
		<key>mscrO</key>
		<integer>528</integer>
		<key>mscrP</key>
		<integer>678</integer>
		<key>mscrQ</key>
		<integer>528</integer>
		<key>mscrR</key>
		<integer>710</integer>
		<key>mscrS</key>
		<integer>778</integer>
		<key>mscrT</key>
		<integer>662</integer>
		<key>mscrU</key>
		<integer>594</integer>
		<key>mscrV</key>
		<integer>626</integer>
		<key>mscrW</key>
		<integer>646</integer>
		<key>mscrX</key>
		<integer>672</integer>
		<key>mscrY</key>
		<integer>578</integer>
		<key>mscrZ</key>
		<integer>658</integer>
		<key>mupBeta.sst</key>
		<integer>304</integer>
		<key>mupBeta.st</key>
		<integer>304</integer>
		<key>mupChi.sst</key>
		<integer>32749</integer>
		<key>mupGamma.sst</key>
		<integer>319</integer>
		<key>mupGamma.st</key>
		<integer>321</integer>
		<key>mupKappa.st</key>
		<integer>32747</integer>
		<key>mupLambda</key>
		<integer>391</integer>
		<key>mupLambda.sst</key>
		<integer>368</integer>
		<key>mupLambda.st</key>
		<integer>340</integer>
		<key>mupMu.sst</key>
		<integer>32747</integer>
		<key>mupOmega.sst</key>
		<integer>32717</integer>
		<key>mupOmicron.sst</key>
		<integer>32757</integer>
		<key>mupPhi.sst</key>
		<integer>360</integer>
		<key>mupPhi.st</key>
		<integer>354</integer>
		<key>mupPi.sst</key>
		<integer>354</integer>
		<key>mupPi.st</key>
		<integer>424</integer>
		<key>mupPsi.sst</key>
		<integer>378</integer>
		<key>mupPsi.st</key>
		<integer>380</integer>
		<key>mupRho.sst</key>
		<integer>32737</integer>
		<key>mupTau.sst</key>
		<integer>32748</integer>
		<key>mupTheta.sst</key>
		<integer>32757</integer>
		<key>mupUpsilon.st</key>
		<integer>385</integer>
		<key>mupXi.sst</key>
		<integer>32747</integer>
		<key>mupb.st</key>
		<integer>32737</integer>
		<key>mupc.st</key>
		<integer>32757</integer>
		<key>mupchi</key>
		<integer>328</integer>
		<key>mupchi.sst</key>
		<integer>32717</integer>
		<key>mupchi.st</key>
		<integer>32717</integer>
		<key>mupepsilon.sst</key>
		<integer>259</integer>
		<key>mupepsilon.st</key>
		<integer>241</integer>
		<key>mupeta.st</key>
		<integer>32735</integer>
		<key>mupf.st</key>
		<integer>32757</integer>
		<key>mupgamma</key>
		<integer>362</integer>
		<key>mupgamma.sst</key>
		<integer>388</integer>
		<key>mupgamma.st</key>
		<integer>388</integer>
		<key>mupiota.sst</key>
		<integer>32727</integer>
		<key>mupiota.st</key>
		<integer>32727</integer>
		<key>mupk.st</key>
		<integer>32747</integer>
		<key>mupkappa.sst</key>
		<integer>32727</integer>
		<key>mupkappa.st</key>
		<integer>32727</integer>
		<key>mupl.st</key>
		<integer>32757</integer>
		<key>muplambda</key>
		<integer>268</integer>
		<key>muplambda.sst</key>
		<integer>242</integer>
		<key>muplambda.st</key>
		<integer>242</integer>
		<key>mupmu.sst</key>
		<integer>32717</integer>
		<key>mupmu.st</key>
		<integer>32727</integer>
		<key>mupnu</key>
		<integer>304</integer>
		<key>mupnu.sst</key>
		<integer>32717</integer>
		<key>mupnu.st</key>
		<integer>32721</integer>
		<key>mupomega</key>
		<integer>360</integer>
		<key>mupomega.sst</key>
		<integer>32735</integer>
		<key>mupomega.st</key>
		<integer>336</integer>
		<key>mupp.st</key>
		<integer>32747</integer>
		<key>mupphi</key>
		<integer>338</integer>
		<key>mupphi.sst</key>
		<integer>32734</integer>
		<key>mupphi.st</key>
		<integer>324</integer>
		<key>muppi.sst</key>
		<integer>32737</integer>
		<key>muppi.st</key>
		<integer>32742</integer>
		<key>muppsi</key>
		<integer>364</integer>
		<key>muppsi.sst</key>
		<integer>32727</integer>
		<key>muppsi.st</key>
		<integer>363</integer>
		<key>mupr.st</key>
		<integer>32747</integer>
		<key>muprho.sst</key>
		<integer>32727</integer>
		<key>muprho.st</key>
		<integer>32737</integer>
		<key>mupsigma.sst</key>
		<integer>32725</integer>
		<key>mupsigma.st</key>
		<integer>32727</integer>
		<key>muptau.sst</key>
		<integer>32737</integer>
		<key>muptau.st</key>
		<integer>32737</integer>
		<key>muptheta</key>
		<integer>351</integer>
		<key>muptheta.sst</key>
		<integer>274</integer>
		<key>muptheta.st</key>
		<integer>282</integer>
		<key>mupupsilon.sst</key>
		<integer>32727</integer>
		<key>mupupsilon.st</key>
		<integer>32742</integer>
		<key>mupv.st</key>
		<integer>32757</integer>
		<key>mupvarTheta.sst</key>
		<integer>32757</integer>
		<key>mupvarepsilon</key>
		<integer>246</integer>
		<key>mupvarepsilon.sst</key>
		<integer>266</integer>
		<key>mupvarepsilon.st</key>
		<integer>208</integer>
		<key>mupvarphi.sst</key>
		<integer>32727</integer>
		<key>mupvarphi.st</key>
		<integer>32742</integer>
		<key>mupvarpi.sst</key>
		<integer>32728</integer>
		<key>mupvarpi.st</key>
		<integer>338</integer>
		<key>mupvarrho.sst</key>
		<integer>32737</integer>
		<key>mupvarrho.st</key>
		<integer>243</integer>
		<key>mupvarsigma.sst</key>
		<integer>32727</integer>
		<key>mupvarsigma.st</key>
		<integer>32737</integer>
		<key>mupvartheta.sst</key>
		<integer>32727</integer>
		<key>mupvartheta.st</key>
		<integer>278</integer>
		<key>mupw.st</key>
		<integer>32757</integer>
		<key>mupx.st</key>
		<integer>32747</integer>
		<key>mupxi.sst</key>
		<integer>32737</integer>
		<key>mupxi.st</key>
		<integer>32747</integer>
		<key>mupzeta.st</key>
		<integer>32737</integer>
		<key>ocirc</key>
		<integer>-220</integer>
		<key>overleftharpoon</key>
		<integer>-268</integer>
		<key>overleftrightarrow</key>
		<integer>-335</integer>
		<key>overrightarc</key>
		<integer>-275</integer>
		<key>overrightarrow</key>
		<integer>-268</integer>
		<key>overrightharpoon</key>
		<integer>-268</integer>
		<key>widebreve</key>
		<integer>-205</integer>
		<key>widecheck</key>
		<integer>-173</integer>
		<key>widecheck.h0</key>
		<integer>-120</integer>
		<key>widehat</key>
		<integer>-173</integer>
		<key>widehat.h0</key>
		<integer>-120</integer>
		<key>wideoverbar</key>
		<integer>-240</integer>
		<key>widetilde</key>
		<integer>-210</integer>
		<key>widetilde.h0</key>
		<integer>-120</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>480</integer>
		<key>AxisHeight</key>
		<integer>250</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1300</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1500</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>650</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>686</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>50</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>345</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>150</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>677</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>50</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>394</integer>
		<key>FractionRuleThickness</key>
		<integer>50</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>600</integer>
		<key>LowerLimitGapMin</key>
		<integer>167</integer>
		<key>MathLeading</key>
		<integer>160</integer>
		<key>MinConnectorOverlap</key>
		<integer>20</integer>
		<key>OverbarExtraAscender</key>
		<integer>50</integer>
		<key>OverbarRuleThickness</key>
		<integer>50</integer>
		<key>OverbarVerticalGap</key>
		<integer>150</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>60</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>170</integer>
		<key>RadicalExtraAscender</key>
		<integer>50</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-600</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>340</integer>
		<key>RadicalRuleThickness</key>
		<integer>50</integer>
		<key>RadicalVerticalGap</key>
		<integer>62</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>70</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>55</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>350</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>96</integer>
		<key>SpaceAfterScript</key>
		<integer>40</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>686</integer>
		<key>StackBottomShiftDown</key>
		<integer>345</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>350</integer>
		<key>StackGapMin</key>
		<integer>150</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>677</integer>
		<key>StackTopShiftUp</key>
		<integer>444</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>600</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>111</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>167</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>200</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>200</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>50</integer>
		<key>SubscriptShiftDown</key>
		<integer>210</integer>
		<key>SubscriptTopMax</key>
		<integer>384</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>386</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>374</integer>
		<key>SuperscriptBottomMin</key>
		<integer>117</integer>
		<key>SuperscriptShiftUp</key>
		<integer>450</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>289</integer>
		<key>UnderbarExtraDescender</key>
		<integer>50</integer>
		<key>UnderbarRuleThickness</key>
		<integer>50</integer>
		<key>UnderbarVerticalGap</key>
		<integer>150</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>200</integer>
		<key>UpperLimitGapMin</key>
		<integer>111</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>Leftarrow</key>
		<array>
			<string>Leftarrow</string>
			<string>Longleftarrow</string>
		</array>
		<key>Leftrightarrow</key>
		<array>
			<string>Leftrightarrow</string>
			<string>Longleftrightarrow</string>
		</array>
		<key>Mapsfrom</key>
		<array>
			<string>Mapsfrom</string>
			<string>Longmapsfrom</string>
		</array>
		<key>Mapsto</key>
		<array>
			<string>Mapsto</string>
			<string>Longmapsto</string>
		</array>
		<key>Rightarrow</key>
		<array>
			<string>Rightarrow</string>
			<string>Longrightarrow</string>
		</array>
		<key>equal</key>
		<array/>
		<key>equiv</key>
		<array/>
		<key>hookleftarrow</key>
		<array/>
		<key>hookrightarrow</key>
		<array/>
		<key>leftarrow</key>
		<array>
			<string>leftarrow</string>
			<string>longleftarrow</string>
		</array>
		<key>leftarrowtail</key>
		<array/>
		<key>leftharpoondown</key>
		<array/>
		<key>leftharpoonup</key>
		<array/>
		<key>leftrightarrow</key>
		<array>
			<string>leftrightarrow</string>
			<string>longleftrightarrow</string>
		</array>
		<key>leftrightarrows</key>
		<array/>
		<key>leftrightharpoons</key>
		<array/>
		<key>mapsfrom</key>
		<array>
			<string>mapsfrom</string>
			<string>longmapsfrom</string>
		</array>
		<key>mapsto</key>
		<array>
			<string>mapsto</string>
			<string>longmapsto</string>
		</array>
		<key>mathunderbar</key>
		<array/>
		<key>minus</key>
		<array/>
		<key>overbrace</key>
		<array>
			<string>overbrace</string>
			<string>overbrace.h1</string>
			<string>overbrace.h2</string>
			<string>overbrace.h3</string>
			<string>overbrace.h4</string>
		</array>
		<key>overbracket</key>
		<array>
			<string>overbracket</string>
			<string>overbracket.h1</string>
			<string>overbracket.h2</string>
			<string>overbracket.h3</string>
		</array>
		<key>overleftarrow</key>
		<array/>
		<key>overleftharpoon</key>
		<array/>
		<key>overleftrightarrow</key>
		<array>
			<string>overleftrightarrow.h1</string>
		</array>
		<key>overparen</key>
		<array>
			<string>overparen</string>
			<string>overparen.h1</string>
			<string>overparen.h2</string>
			<string>overparen.h3</string>
			<string>overparen.h4</string>
			<string>overparen.h5</string>
			<string>overparen.h6</string>
		</array>
		<key>overrightarc</key>
		<array>
			<string>overrightarc.h1</string>
			<string>overrightarc.h2</string>
			<string>overrightarc.h3</string>
			<string>overrightarc.h4</string>
			<string>overrightarc.h5</string>
			<string>overrightarc.h6</string>
		</array>
		<key>overrightarrow</key>
		<array/>
		<key>overrightharpoon</key>
		<array/>
		<key>rightarrow</key>
		<array>
			<string>rightarrow</string>
			<string>longrightarrow</string>
		</array>
		<key>rightarrowtail</key>
		<array/>
		<key>rightharpoondown</key>
		<array/>
		<key>rightharpoonup</key>
		<array/>
		<key>rightleftarrows</key>
		<array/>
		<key>rightleftharpoons</key>
		<array/>
		<key>twoheadleftarrow</key>
		<array/>
		<key>twoheadrightarrow</key>
		<array/>
		<key>underbrace</key>
		<array>
			<string>underbrace</string>
			<string>underbrace.h1</string>
			<string>underbrace.h2</string>
			<string>underbrace.h3</string>
			<string>underbrace.h4</string>
		</array>
		<key>underbracket</key>
		<array>
			<string>underbracket</string>
			<string>underbracket.h1</string>
			<string>underbracket.h2</string>
			<string>underbracket.h3</string>
		</array>
		<key>underleftarrow</key>
		<array/>
		<key>underleftharpoondown</key>
		<array/>
		<key>underleftrightarrow</key>
		<array>
			<string>underleftrightarrow</string>
			<string>underleftrightarrow.h1</string>
		</array>
		<key>underparen</key>
		<array>
			<string>underparen</string>
			<string>underparen.h1</string>
			<string>underparen.h2</string>
			<string>underparen.h3</string>
			<string>underparen.h4</string>
			<string>underparen.h5</string>
			<string>underparen.h6</string>
		</array>
		<key>underrightarrow</key>
		<array/>
		<key>underrightharpoondown</key>
		<array/>
		<key>widearc</key>
		<array>
			<string>widearc</string>
			<string>widearc.h1</string>
			<string>widearc.h2</string>
			<string>widearc.h3</string>
			<string>widearc.h4</string>
			<string>widearc.h5</string>
			<string>widearc.h6</string>
		</array>
		<key>widebreve</key>
		<array>
			<string>widebreve</string>
			<string>widebreve.h0</string>
			<string>widebreve.h1</string>
			<string>widebreve.h2</string>
			<string>widebreve.h3</string>
			<string>widebreve.h4</string>
			<string>widebreve.h5</string>
			<string>widebreve.h6</string>
		</array>
		<key>widecheck</key>
		<array>
			<string>widecheck</string>
			<string>widecheck.h0</string>
			<string>widecheck.h1</string>
			<string>widecheck.h2</string>
			<string>widecheck.h3</string>
			<string>widecheck.h4</string>
			<string>widecheck.h5</string>
			<string>widecheck.h6</string>
		</array>
		<key>widehat</key>
		<array>
			<string>widehat</string>
			<string>widehat.h0</string>
			<string>widehat.h1</string>
			<string>widehat.h2</string>
			<string>widehat.h3</string>
			<string>widehat.h4</string>
			<string>widehat.h5</string>
			<string>widehat.h6</string>
		</array>
		<key>wideoverbar</key>
		<array/>
		<key>widetilde</key>
		<array>
			<string>widetilde.h1</string>
			<string>widetilde.h0</string>
			<string>widetilde.h2</string>
			<string>widetilde.h3</string>
			<string>widetilde.h4</string>
			<string>widetilde.h5</string>
			<string>widetilde.h6</string>
		</array>
		<key>wideutilde</key>
		<array>
			<string>wideutilde</string>
			<string>wideutilde.h1</string>
			<string>wideutilde.h2</string>
			<string>wideutilde.h3</string>
			<string>wideutilde.h4</string>
			<string>wideutilde.h5</string>
			<string>wideutilde.h6</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>Planckconst</key>
		<integer>-12</integer>
		<key>Planckconst.sst</key>
		<integer>-16</integer>
		<key>Planckconst.st</key>
		<integer>-16</integer>
		<key>awint</key>
		<integer>100</integer>
		<key>awint.v1</key>
		<integer>300</integer>
		<key>ell</key>
		<integer>78</integer>
		<key>fint</key>
		<integer>200</integer>
		<key>fint.v1</key>
		<integer>350</integer>
		<key>hslash</key>
		<integer>-12</integer>
		<key>idotsint</key>
		<integer>160</integer>
		<key>idotsint.cnd</key>
		<integer>200</integer>
		<key>idotsint.v1</key>
		<integer>350</integer>
		<key>idotsint.v1.cnd</key>
		<integer>350</integer>
		<key>iiiint</key>
		<integer>150</integer>
		<key>iiiint.cnd</key>
		<integer>100</integer>
		<key>iiiint.v1</key>
		<integer>350</integer>
		<key>iiiint.v1.cnd</key>
		<integer>350</integer>
		<key>iiint</key>
		<integer>150</integer>
		<key>iiint.cnd</key>
		<integer>100</integer>
		<key>iiint.v1</key>
		<integer>350</integer>
		<key>iiint.v1.cnd</key>
		<integer>350</integer>
		<key>iint</key>
		<integer>150</integer>
		<key>iint.cnd</key>
		<integer>200</integer>
		<key>iint.v1</key>
		<integer>350</integer>
		<key>iint.v1.cnd</key>
		<integer>350</integer>
		<key>imath</key>
		<integer>34</integer>
		<key>int</key>
		<integer>200</integer>
		<key>int.v1</key>
		<integer>350</integer>
		<key>intclockwise</key>
		<integer>100</integer>
		<key>intclockwise.v1</key>
		<integer>300</integer>
		<key>jmath</key>
		<integer>36</integer>
		<key>mbfcalB</key>
		<integer>42</integer>
		<key>mbfcalC</key>
		<integer>42</integer>
		<key>mbfcalD</key>
		<integer>42</integer>
		<key>mbfcalE</key>
		<integer>42</integer>
		<key>mbfcalF</key>
		<integer>42</integer>
		<key>mbfcalG</key>
		<integer>42</integer>
		<key>mbfcalI</key>
		<integer>42</integer>
		<key>mbfcalJ</key>
		<integer>42</integer>
		<key>mbfcalN</key>
		<integer>42</integer>
		<key>mbfcalO</key>
		<integer>42</integer>
		<key>mbfcalP</key>
		<integer>42</integer>
		<key>mbfcalS</key>
		<integer>42</integer>
		<key>mbfcalT</key>
		<integer>42</integer>
		<key>mbfcalU</key>
		<integer>42</integer>
		<key>mbfcalV</key>
		<integer>42</integer>
		<key>mbfcalW</key>
		<integer>42</integer>
		<key>mbfcalX</key>
		<integer>42</integer>
		<key>mbfcalY</key>
		<integer>42</integer>
		<key>mbfcalZ</key>
		<integer>42</integer>
		<key>mbfitA</key>
		<integer>-12</integer>
		<key>mbfitAlpha</key>
		<integer>-12</integer>
		<key>mbfitB</key>
		<integer>16</integer>
		<key>mbfitBeta</key>
		<integer>16</integer>
		<key>mbfitC</key>
		<integer>46</integer>
		<key>mbfitChi</key>
		<integer>70</integer>
		<key>mbfitD</key>
		<integer>26</integer>
		<key>mbfitDelta</key>
		<integer>20</integer>
		<key>mbfitE</key>
		<integer>70</integer>
		<key>mbfitEpsilon</key>
		<integer>70</integer>
		<key>mbfitEta</key>
		<integer>56</integer>
		<key>mbfitF</key>
		<integer>70</integer>
		<key>mbfitG</key>
		<integer>46</integer>
		<key>mbfitGamma</key>
		<integer>70</integer>
		<key>mbfitH</key>
		<integer>56</integer>
		<key>mbfitI</key>
		<integer>56</integer>
		<key>mbfitIota</key>
		<integer>56</integer>
		<key>mbfitJ</key>
		<integer>56</integer>
		<key>mbfitK</key>
		<integer>66</integer>
		<key>mbfitKappa</key>
		<integer>66</integer>
		<key>mbfitL</key>
		<integer>-4</integer>
		<key>mbfitLambda</key>
		<integer>20</integer>
		<key>mbfitM</key>
		<integer>54</integer>
		<key>mbfitMu</key>
		<integer>54</integer>
		<key>mbfitN</key>
		<integer>58</integer>
		<key>mbfitNu</key>
		<integer>58</integer>
		<key>mbfitO</key>
		<integer>24</integer>
		<key>mbfitOmega</key>
		<integer>60</integer>
		<key>mbfitOmicron</key>
		<integer>24</integer>
		<key>mbfitP</key>
		<integer>46</integer>
		<key>mbfitPhi</key>
		<integer>60</integer>
		<key>mbfitPi</key>
		<integer>70</integer>
		<key>mbfitPsi</key>
		<integer>70</integer>
		<key>mbfitQ</key>
		<integer>44</integer>
		<key>mbfitR</key>
		<integer>10</integer>
		<key>mbfitRho</key>
		<integer>46</integer>
		<key>mbfitS</key>
		<integer>36</integer>
		<key>mbfitSigma</key>
		<integer>58</integer>
		<key>mbfitT</key>
		<integer>70</integer>
		<key>mbfitTau</key>
		<integer>70</integer>
		<key>mbfitTheta</key>
		<integer>70</integer>
		<key>mbfitU</key>
		<integer>56</integer>
		<key>mbfitUpsilon</key>
		<integer>70</integer>
		<key>mbfitV</key>
		<integer>80</integer>
		<key>mbfitW</key>
		<integer>80</integer>
		<key>mbfitX</key>
		<integer>80</integer>
		<key>mbfitXi</key>
		<integer>70</integer>
		<key>mbfitY</key>
		<integer>80</integer>
		<key>mbfitZ</key>
		<integer>80</integer>
		<key>mbfitZeta</key>
		<integer>70</integer>
		<key>mbfita</key>
		<integer>2</integer>
		<key>mbfitalpha</key>
		<integer>32</integer>
		<key>mbfitb</key>
		<integer>12</integer>
		<key>mbfitbeta</key>
		<integer>34</integer>
		<key>mbfitc</key>
		<integer>38</integer>
		<key>mbfitchi</key>
		<integer>28</integer>
		<key>mbfitd</key>
		<integer>46</integer>
		<key>mbfitdelta</key>
		<integer>30</integer>
		<key>mbfite</key>
		<integer>14</integer>
		<key>mbfitepsilon</key>
		<integer>36</integer>
		<key>mbfiteta</key>
		<integer>30</integer>
		<key>mbfitf</key>
		<integer>80</integer>
		<key>mbfitg</key>
		<integer>80</integer>
		<key>mbfitgamma</key>
		<integer>70</integer>
		<key>mbfith</key>
		<integer>-16</integer>
		<key>mbfiti</key>
		<integer>42</integer>
		<key>mbfitiota</key>
		<integer>32</integer>
		<key>mbfitj</key>
		<integer>70</integer>
		<key>mbfitk</key>
		<integer>52</integer>
		<key>mbfitkappa</key>
		<integer>36</integer>
		<key>mbfitl</key>
		<integer>14</integer>
		<key>mbfitlambda</key>
		<integer>34</integer>
		<key>mbfitm</key>
		<integer>8</integer>
		<key>mbfitmu</key>
		<integer>36</integer>
		<key>mbfitn</key>
		<integer>4</integer>
		<key>mbfitnabla</key>
		<integer>70</integer>
		<key>mbfitnu</key>
		<integer>54</integer>
		<key>mbfito</key>
		<integer>34</integer>
		<key>mbfitomega</key>
		<integer>30</integer>
		<key>mbfitomicron</key>
		<integer>34</integer>
		<key>mbfitp</key>
		<integer>46</integer>
		<key>mbfitpartial</key>
		<integer>30</integer>
		<key>mbfitphi</key>
		<integer>30</integer>
		<key>mbfitpi</key>
		<integer>34</integer>
		<key>mbfitpsi</key>
		<integer>50</integer>
		<key>mbfitq</key>
		<integer>66</integer>
		<key>mbfitr</key>
		<integer>80</integer>
		<key>mbfitrho</key>
		<integer>30</integer>
		<key>mbfits</key>
		<integer>32</integer>
		<key>mbfitsigma</key>
		<integer>76</integer>
		<key>mbfitt</key>
		<integer>60</integer>
		<key>mbfittau</key>
		<integer>50</integer>
		<key>mbfittheta</key>
		<integer>30</integer>
		<key>mbfitu</key>
		<integer>38</integer>
		<key>mbfitupsilon</key>
		<integer>28</integer>
		<key>mbfitv</key>
		<integer>80</integer>
		<key>mbfitvarTheta</key>
		<integer>60</integer>
		<key>mbfitvarepsilon</key>
		<integer>34</integer>
		<key>mbfitvarphi</key>
		<integer>50</integer>
		<key>mbfitvarpi</key>
		<integer>32</integer>
		<key>mbfitvarrho</key>
		<integer>30</integer>
		<key>mbfitvarsigma</key>
		<integer>32</integer>
		<key>mbfitvartheta</key>
		<integer>48</integer>
		<key>mbfitw</key>
		<integer>80</integer>
		<key>mbfitx</key>
		<integer>80</integer>
		<key>mbfitxi</key>
		<integer>34</integer>
		<key>mbfity</key>
		<integer>80</integer>
		<key>mbfitz</key>
		<integer>62</integer>
		<key>mbfitzeta</key>
		<integer>40</integer>
		<key>mbfscrA</key>
		<integer>42</integer>
		<key>mbfscrB</key>
		<integer>42</integer>
		<key>mbfscrC</key>
		<integer>42</integer>
		<key>mbfscrD</key>
		<integer>42</integer>
		<key>mbfscrE</key>
		<integer>42</integer>
		<key>mbfscrF</key>
		<integer>42</integer>
		<key>mbfscrG</key>
		<integer>42</integer>
		<key>mbfscrH</key>
		<integer>42</integer>
		<key>mbfscrI</key>
		<integer>42</integer>
		<key>mbfscrJ</key>
		<integer>42</integer>
		<key>mbfscrK</key>
		<integer>42</integer>
		<key>mbfscrL</key>
		<integer>42</integer>
		<key>mbfscrM</key>
		<integer>42</integer>
		<key>mbfscrN</key>
		<integer>42</integer>
		<key>mbfscrO</key>
		<integer>42</integer>
		<key>mbfscrP</key>
		<integer>42</integer>
		<key>mbfscrQ</key>
		<integer>42</integer>
		<key>mbfscrR</key>
		<integer>42</integer>
		<key>mbfscrS</key>
		<integer>42</integer>
		<key>mbfscrT</key>
		<integer>42</integer>
		<key>mbfscrU</key>
		<integer>42</integer>
		<key>mbfscrV</key>
		<integer>42</integer>
		<key>mbfscrW</key>
		<integer>42</integer>
		<key>mbfscrX</key>
		<integer>42</integer>
		<key>mbfscrY</key>
		<integer>42</integer>
		<key>mbfscrZ</key>
		<integer>42</integer>
		<key>mcalB</key>
		<integer>42</integer>
		<key>mcalC</key>
		<integer>42</integer>
		<key>mcalD</key>
		<integer>42</integer>
		<key>mcalE</key>
		<integer>42</integer>
		<key>mcalF</key>
		<integer>42</integer>
		<key>mcalG</key>
		<integer>42</integer>
		<key>mcalI</key>
		<integer>42</integer>
		<key>mcalJ</key>
		<integer>42</integer>
		<key>mcalN</key>
		<integer>42</integer>
		<key>mcalO</key>
		<integer>42</integer>
		<key>mcalP</key>
		<integer>42</integer>
		<key>mcalS</key>
		<integer>42</integer>
		<key>mcalT</key>
		<integer>42</integer>
		<key>mcalU</key>
		<integer>42</integer>
		<key>mcalV</key>
		<integer>42</integer>
		<key>mcalW</key>
		<integer>42</integer>
		<key>mcalX</key>
		<integer>42</integer>
		<key>mcalY</key>
		<integer>42</integer>
		<key>mcalZ</key>
		<integer>42</integer>
		<key>mitA</key>
		<integer>-14</integer>
		<key>mitA.sst</key>
		<integer>-12</integer>
		<key>mitA.st</key>
		<integer>-12</integer>
		<key>mitAlpha</key>
		<integer>-14</integer>
		<key>mitAlpha.sst</key>
		<integer>-12</integer>
		<key>mitAlpha.st</key>
		<integer>-12</integer>
		<key>mitB</key>
		<integer>24</integer>
		<key>mitB.sst</key>
		<integer>24</integer>
		<key>mitB.st</key>
		<integer>24</integer>
		<key>mitBeta</key>
		<integer>24</integer>
		<key>mitBeta.sst</key>
		<integer>24</integer>
		<key>mitBeta.st</key>
		<integer>24</integer>
		<key>mitC</key>
		<integer>68</integer>
		<key>mitC.sst</key>
		<integer>68</integer>
		<key>mitC.st</key>
		<integer>68</integer>
		<key>mitChi</key>
		<integer>70</integer>
		<key>mitChi.sst</key>
		<integer>86</integer>
		<key>mitChi.st</key>
		<integer>86</integer>
		<key>mitD</key>
		<integer>40</integer>
		<key>mitD.sst</key>
		<integer>40</integer>
		<key>mitD.st</key>
		<integer>40</integer>
		<key>mitDelta</key>
		<integer>8</integer>
		<key>mitDelta.sst</key>
		<integer>8</integer>
		<key>mitDelta.st</key>
		<integer>8</integer>
		<key>mitE</key>
		<integer>70</integer>
		<key>mitE.sst</key>
		<integer>70</integer>
		<key>mitE.st</key>
		<integer>70</integer>
		<key>mitEpsilon</key>
		<integer>70</integer>
		<key>mitEpsilon.sst</key>
		<integer>70</integer>
		<key>mitEpsilon.st</key>
		<integer>70</integer>
		<key>mitEta</key>
		<integer>54</integer>
		<key>mitEta.sst</key>
		<integer>54</integer>
		<key>mitEta.st</key>
		<integer>54</integer>
		<key>mitF</key>
		<integer>70</integer>
		<key>mitF.sst</key>
		<integer>116</integer>
		<key>mitF.st</key>
		<integer>116</integer>
		<key>mitG</key>
		<integer>66</integer>
		<key>mitG.sst</key>
		<integer>66</integer>
		<key>mitG.st</key>
		<integer>66</integer>
		<key>mitGamma</key>
		<integer>80</integer>
		<key>mitGamma.sst</key>
		<integer>134</integer>
		<key>mitGamma.st</key>
		<integer>134</integer>
		<key>mitH</key>
		<integer>54</integer>
		<key>mitH.sst</key>
		<integer>54</integer>
		<key>mitH.st</key>
		<integer>54</integer>
		<key>mitI</key>
		<integer>54</integer>
		<key>mitI.sst</key>
		<integer>54</integer>
		<key>mitI.st</key>
		<integer>54</integer>
		<key>mitIota</key>
		<integer>54</integer>
		<key>mitIota.sst</key>
		<integer>54</integer>
		<key>mitIota.st</key>
		<integer>54</integer>
		<key>mitJ</key>
		<integer>56</integer>
		<key>mitJ.sst</key>
		<integer>56</integer>
		<key>mitJ.st</key>
		<integer>56</integer>
		<key>mitK</key>
		<integer>60</integer>
		<key>mitK.sst</key>
		<integer>76</integer>
		<key>mitK.st</key>
		<integer>76</integer>
		<key>mitKappa</key>
		<integer>70</integer>
		<key>mitKappa.sst</key>
		<integer>76</integer>
		<key>mitKappa.st</key>
		<integer>76</integer>
		<key>mitL</key>
		<integer>-6</integer>
		<key>mitL.sst</key>
		<integer>-4</integer>
		<key>mitL.st</key>
		<integer>-4</integer>
		<key>mitLambda</key>
		<integer>50</integer>
		<key>mitLambda.sst</key>
		<integer>136</integer>
		<key>mitLambda.st</key>
		<integer>136</integer>
		<key>mitM</key>
		<integer>54</integer>
		<key>mitM.sst</key>
		<integer>54</integer>
		<key>mitM.st</key>
		<integer>54</integer>
		<key>mitMu</key>
		<integer>54</integer>
		<key>mitMu.sst</key>
		<integer>54</integer>
		<key>mitMu.st</key>
		<integer>54</integer>
		<key>mitN</key>
		<integer>54</integer>
		<key>mitN.sst</key>
		<integer>54</integer>
		<key>mitN.st</key>
		<integer>54</integer>
		<key>mitNu</key>
		<integer>54</integer>
		<key>mitNu.sst</key>
		<integer>54</integer>
		<key>mitNu.st</key>
		<integer>54</integer>
		<key>mitO</key>
		<integer>40</integer>
		<key>mitO.sst</key>
		<integer>40</integer>
		<key>mitO.st</key>
		<integer>40</integer>
		<key>mitOmega</key>
		<integer>70</integer>
		<key>mitOmega.sst</key>
		<integer>130</integer>
		<key>mitOmega.st</key>
		<integer>130</integer>
		<key>mitOmicron</key>
		<integer>40</integer>
		<key>mitOmicron.sst</key>
		<integer>40</integer>
		<key>mitOmicron.st</key>
		<integer>40</integer>
		<key>mitP</key>
		<integer>60</integer>
		<key>mitP.sst</key>
		<integer>70</integer>
		<key>mitP.st</key>
		<integer>70</integer>
		<key>mitPhi</key>
		<integer>70</integer>
		<key>mitPhi.sst</key>
		<integer>126</integer>
		<key>mitPhi.st</key>
		<integer>126</integer>
		<key>mitPi</key>
		<integer>70</integer>
		<key>mitPi.sst</key>
		<integer>138</integer>
		<key>mitPi.st</key>
		<integer>138</integer>
		<key>mitPsi</key>
		<integer>70</integer>
		<key>mitPsi.sst</key>
		<integer>220</integer>
		<key>mitPsi.st</key>
		<integer>220</integer>
		<key>mitQ</key>
		<integer>58</integer>
		<key>mitQ.sst</key>
		<integer>58</integer>
		<key>mitQ.st</key>
		<integer>58</integer>
		<key>mitR</key>
		<integer>30</integer>
		<key>mitR.sst</key>
		<integer>30</integer>
		<key>mitR.st</key>
		<integer>30</integer>
		<key>mitRho</key>
		<integer>70</integer>
		<key>mitRho.sst</key>
		<integer>70</integer>
		<key>mitRho.st</key>
		<integer>70</integer>
		<key>mitS</key>
		<integer>40</integer>
		<key>mitS.sst</key>
		<integer>40</integer>
		<key>mitS.st</key>
		<integer>40</integer>
		<key>mitSigma</key>
		<integer>46</integer>
		<key>mitSigma.sst</key>
		<integer>46</integer>
		<key>mitSigma.st</key>
		<integer>46</integer>
		<key>mitT</key>
		<integer>70</integer>
		<key>mitT.sst</key>
		<integer>112</integer>
		<key>mitT.st</key>
		<integer>112</integer>
		<key>mitTau</key>
		<integer>70</integer>
		<key>mitTau.sst</key>
		<integer>112</integer>
		<key>mitTau.st</key>
		<integer>112</integer>
		<key>mitTheta</key>
		<integer>68</integer>
		<key>mitTheta.sst</key>
		<integer>68</integer>
		<key>mitTheta.st</key>
		<integer>68</integer>
		<key>mitU</key>
		<integer>56</integer>
		<key>mitU.sst</key>
		<integer>56</integer>
		<key>mitU.st</key>
		<integer>56</integer>
		<key>mitUpsilon</key>
		<integer>70</integer>
		<key>mitUpsilon.sst</key>
		<integer>114</integer>
		<key>mitUpsilon.st</key>
		<integer>114</integer>
		<key>mitV</key>
		<integer>70</integer>
		<key>mitV.sst</key>
		<integer>112</integer>
		<key>mitV.st</key>
		<integer>112</integer>
		<key>mitW</key>
		<integer>70</integer>
		<key>mitW.sst</key>
		<integer>112</integer>
		<key>mitW.st</key>
		<integer>112</integer>
		<key>mitX</key>
		<integer>70</integer>
		<key>mitX.sst</key>
		<integer>86</integer>
		<key>mitX.st</key>
		<integer>86</integer>
		<key>mitXi</key>
		<integer>70</integer>
		<key>mitXi.sst</key>
		<integer>136</integer>
		<key>mitXi.st</key>
		<integer>136</integer>
		<key>mitY</key>
		<integer>70</integer>
		<key>mitY.sst</key>
		<integer>104</integer>
		<key>mitY.st</key>
		<integer>104</integer>
		<key>mitZ</key>
		<integer>70</integer>
		<key>mitZ.sst</key>
		<integer>92</integer>
		<key>mitZ.st</key>
		<integer>92</integer>
		<key>mitZeta</key>
		<integer>70</integer>
		<key>mitZeta.sst</key>
		<integer>92</integer>
		<key>mitZeta.st</key>
		<integer>92</integer>
		<key>mita</key>
		<integer>2</integer>
		<key>mita.sst</key>
		<integer>2</integer>
		<key>mita.st</key>
		<integer>2</integer>
		<key>mitalpha</key>
		<integer>36</integer>
		<key>mitalpha.sst</key>
		<integer>36</integer>
		<key>mitalpha.st</key>
		<integer>36</integer>
		<key>mitb</key>
		<integer>32</integer>
		<key>mitb.sst</key>
		<integer>32</integer>
		<key>mitb.st</key>
		<integer>32</integer>
		<key>mitbeta</key>
		<integer>40</integer>
		<key>mitbeta.sst</key>
		<integer>40</integer>
		<key>mitbeta.st</key>
		<integer>40</integer>
		<key>mitc</key>
		<integer>54</integer>
		<key>mitc.sst</key>
		<integer>54</integer>
		<key>mitc.st</key>
		<integer>54</integer>
		<key>mitchi</key>
		<integer>30</integer>
		<key>mitchi.sst</key>
		<integer>30</integer>
		<key>mitchi.st</key>
		<integer>30</integer>
		<key>mitd</key>
		<integer>72</integer>
		<key>mitd.sst</key>
		<integer>72</integer>
		<key>mitd.st</key>
		<integer>72</integer>
		<key>mitdelta</key>
		<integer>38</integer>
		<key>mitdelta.sst</key>
		<integer>38</integer>
		<key>mitdelta.st</key>
		<integer>38</integer>
		<key>mite</key>
		<integer>28</integer>
		<key>mite.sst</key>
		<integer>28</integer>
		<key>mite.st</key>
		<integer>28</integer>
		<key>mitepsilon</key>
		<integer>40</integer>
		<key>mitepsilon.sst</key>
		<integer>40</integer>
		<key>mitepsilon.st</key>
		<integer>40</integer>
		<key>miteta</key>
		<integer>40</integer>
		<key>miteta.sst</key>
		<integer>40</integer>
		<key>miteta.st</key>
		<integer>40</integer>
		<key>mitf</key>
		<integer>30</integer>
		<key>mitf.sst</key>
		<integer>30</integer>
		<key>mitf.st</key>
		<integer>30</integer>
		<key>mitg</key>
		<integer>80</integer>
		<key>mitg.sst</key>
		<integer>110</integer>
		<key>mitg.st</key>
		<integer>110</integer>
		<key>mitgamma</key>
		<integer>80</integer>
		<key>mitgamma.sst</key>
		<integer>86</integer>
		<key>mitgamma.st</key>
		<integer>86</integer>
		<key>miti</key>
		<integer>46</integer>
		<key>miti.sst</key>
		<integer>46</integer>
		<key>miti.st</key>
		<integer>46</integer>
		<key>mitiota</key>
		<integer>38</integer>
		<key>mitiota.sst</key>
		<integer>38</integer>
		<key>mitiota.st</key>
		<integer>38</integer>
		<key>mitj</key>
		<integer>58</integer>
		<key>mitj.sst</key>
		<integer>58</integer>
		<key>mitj.st</key>
		<integer>58</integer>
		<key>mitk</key>
		<integer>48</integer>
		<key>mitk.sst</key>
		<integer>48</integer>
		<key>mitk.st</key>
		<integer>48</integer>
		<key>mitkappa</key>
		<integer>42</integer>
		<key>mitkappa.sst</key>
		<integer>42</integer>
		<key>mitkappa.st</key>
		<integer>42</integer>
		<key>mitl</key>
		<integer>8</integer>
		<key>mitl.sst</key>
		<integer>8</integer>
		<key>mitl.st</key>
		<integer>8</integer>
		<key>mitlambda</key>
		<integer>40</integer>
		<key>mitlambda.sst</key>
		<integer>40</integer>
		<key>mitlambda.st</key>
		<integer>40</integer>
		<key>mitm</key>
		<integer>10</integer>
		<key>mitm.sst</key>
		<integer>10</integer>
		<key>mitm.st</key>
		<integer>10</integer>
		<key>mitmu</key>
		<integer>42</integer>
		<key>mitmu.sst</key>
		<integer>42</integer>
		<key>mitmu.st</key>
		<integer>42</integer>
		<key>mitn</key>
		<integer>8</integer>
		<key>mitn.sst</key>
		<integer>8</integer>
		<key>mitn.st</key>
		<integer>8</integer>
		<key>mitnabla</key>
		<integer>70</integer>
		<key>mitnabla.sst</key>
		<integer>126</integer>
		<key>mitnabla.st</key>
		<integer>126</integer>
		<key>mitnu</key>
		<integer>60</integer>
		<key>mitnu.sst</key>
		<integer>60</integer>
		<key>mitnu.st</key>
		<integer>60</integer>
		<key>mito</key>
		<integer>44</integer>
		<key>mito.sst</key>
		<integer>44</integer>
		<key>mito.st</key>
		<integer>44</integer>
		<key>mitomega</key>
		<integer>38</integer>
		<key>mitomega.sst</key>
		<integer>38</integer>
		<key>mitomega.st</key>
		<integer>38</integer>
		<key>mitomicron</key>
		<integer>44</integer>
		<key>mitomicron.sst</key>
		<integer>44</integer>
		<key>mitomicron.st</key>
		<integer>44</integer>
		<key>mitp</key>
		<integer>62</integer>
		<key>mitp.sst</key>
		<integer>62</integer>
		<key>mitp.st</key>
		<integer>62</integer>
		<key>mitpartial</key>
		<integer>38</integer>
		<key>mitpartial.sst</key>
		<integer>38</integer>
		<key>mitpartial.st</key>
		<integer>38</integer>
		<key>mitphi</key>
		<integer>40</integer>
		<key>mitphi.sst</key>
		<integer>40</integer>
		<key>mitphi.st</key>
		<integer>40</integer>
		<key>mitpi</key>
		<integer>36</integer>
		<key>mitpi.sst</key>
		<integer>36</integer>
		<key>mitpi.st</key>
		<integer>36</integer>
		<key>mitpsi</key>
		<integer>58</integer>
		<key>mitpsi.sst</key>
		<integer>58</integer>
		<key>mitpsi.st</key>
		<integer>58</integer>
		<key>mitq</key>
		<integer>74</integer>
		<key>mitq.sst</key>
		<integer>74</integer>
		<key>mitq.st</key>
		<integer>74</integer>
		<key>mitr</key>
		<integer>86</integer>
		<key>mitr.sst</key>
		<integer>86</integer>
		<key>mitr.st</key>
		<integer>86</integer>
		<key>mitrho</key>
		<integer>38</integer>
		<key>mitrho.sst</key>
		<integer>38</integer>
		<key>mitrho.st</key>
		<integer>38</integer>
		<key>mits</key>
		<integer>28</integer>
		<key>mits.sst</key>
		<integer>28</integer>
		<key>mits.st</key>
		<integer>28</integer>
		<key>mitsigma</key>
		<integer>76</integer>
		<key>mitsigma.sst</key>
		<integer>76</integer>
		<key>mitsigma.st</key>
		<integer>76</integer>
		<key>mitt</key>
		<integer>52</integer>
		<key>mitt.sst</key>
		<integer>52</integer>
		<key>mitt.st</key>
		<integer>52</integer>
		<key>mittau</key>
		<integer>58</integer>
		<key>mittau.sst</key>
		<integer>58</integer>
		<key>mittau.st</key>
		<integer>58</integer>
		<key>mittheta</key>
		<integer>38</integer>
		<key>mittheta.sst</key>
		<integer>38</integer>
		<key>mittheta.st</key>
		<integer>38</integer>
		<key>mitu</key>
		<integer>28</integer>
		<key>mitu.sst</key>
		<integer>28</integer>
		<key>mitu.st</key>
		<integer>28</integer>
		<key>mitupsilon</key>
		<integer>38</integer>
		<key>mitupsilon.sst</key>
		<integer>38</integer>
		<key>mitupsilon.st</key>
		<integer>38</integer>
		<key>mitv</key>
		<integer>82</integer>
		<key>mitv.sst</key>
		<integer>82</integer>
		<key>mitv.st</key>
		<integer>82</integer>
		<key>mitvarTheta</key>
		<integer>60</integer>
		<key>mitvarTheta.sst</key>
		<integer>68</integer>
		<key>mitvarTheta.st</key>
		<integer>68</integer>
		<key>mitvarepsilon</key>
		<integer>40</integer>
		<key>mitvarepsilon.sst</key>
		<integer>40</integer>
		<key>mitvarepsilon.st</key>
		<integer>40</integer>
		<key>mitvarphi</key>
		<integer>58</integer>
		<key>mitvarphi.sst</key>
		<integer>58</integer>
		<key>mitvarphi.st</key>
		<integer>58</integer>
		<key>mitvarpi</key>
		<integer>40</integer>
		<key>mitvarpi.sst</key>
		<integer>40</integer>
		<key>mitvarpi.st</key>
		<integer>40</integer>
		<key>mitvarrho</key>
		<integer>40</integer>
		<key>mitvarrho.sst</key>
		<integer>40</integer>
		<key>mitvarrho.st</key>
		<integer>40</integer>
		<key>mitvarsigma</key>
		<integer>38</integer>
		<key>mitvarsigma.sst</key>
		<integer>38</integer>
		<key>mitvarsigma.st</key>
		<integer>38</integer>
		<key>mitvartheta</key>
		<integer>40</integer>
		<key>mitvartheta.sst</key>
		<integer>40</integer>
		<key>mitvartheta.st</key>
		<integer>40</integer>
		<key>mitw</key>
		<integer>84</integer>
		<key>mitw.sst</key>
		<integer>84</integer>
		<key>mitw.st</key>
		<integer>84</integer>
		<key>mitx</key>
		<integer>78</integer>
		<key>mitx.sst</key>
		<integer>78</integer>
		<key>mitx.st</key>
		<integer>78</integer>
		<key>mitxi</key>
		<integer>40</integer>
		<key>mitxi.sst</key>
		<integer>40</integer>
		<key>mitxi.st</key>
		<integer>40</integer>
		<key>mity</key>
		<integer>100</integer>
		<key>mity.sst</key>
		<integer>100</integer>
		<key>mity.st</key>
		<integer>100</integer>
		<key>mitz</key>
		<integer>62</integer>
		<key>mitz.sst</key>
		<integer>62</integer>
		<key>mitz.st</key>
		<integer>62</integer>
		<key>mitzeta</key>
		<integer>42</integer>
		<key>mitzeta.sst</key>
		<integer>42</integer>
		<key>mitzeta.st</key>
		<integer>42</integer>
		<key>mscrA</key>
		<integer>42</integer>
		<key>mscrB</key>
		<integer>42</integer>
		<key>mscrC</key>
		<integer>42</integer>
		<key>mscrD</key>
		<integer>42</integer>
		<key>mscrE</key>
		<integer>42</integer>
		<key>mscrF</key>
		<integer>42</integer>
		<key>mscrG</key>
		<integer>42</integer>
		<key>mscrH</key>
		<integer>42</integer>
		<key>mscrI</key>
		<integer>42</integer>
		<key>mscrJ</key>
		<integer>42</integer>
		<key>mscrK</key>
		<integer>42</integer>
		<key>mscrL</key>
		<integer>42</integer>
		<key>mscrM</key>
		<integer>42</integer>
		<key>mscrN</key>
		<integer>42</integer>
		<key>mscrO</key>
		<integer>42</integer>
		<key>mscrP</key>
		<integer>42</integer>
		<key>mscrQ</key>
		<integer>42</integer>
		<key>mscrR</key>
		<integer>42</integer>
		<key>mscrS</key>
		<integer>42</integer>
		<key>mscrT</key>
		<integer>42</integer>
		<key>mscrU</key>
		<integer>42</integer>
		<key>mscrV</key>
		<integer>42</integer>
		<key>mscrW</key>
		<integer>42</integer>
		<key>mscrX</key>
		<integer>42</integer>
		<key>mscrY</key>
		<integer>42</integer>
		<key>mscrZ</key>
		<integer>42</integer>
		<key>oiiint</key>
		<integer>100</integer>
		<key>oiiint.cnd</key>
		<integer>100</integer>
		<key>oiiint.v1</key>
		<integer>340</integer>
		<key>oiiint.v1.cnd</key>
		<integer>340</integer>
		<key>oiiintacw</key>
		<integer>100</integer>
		<key>oiiintacw.cnd</key>
		<integer>100</integer>
		<key>oiiintacw.v1</key>
		<integer>350</integer>
		<key>oiiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>oiiintcw</key>
		<integer>100</integer>
		<key>oiiintcw.cnd</key>
		<integer>100</integer>
		<key>oiiintcw.v1</key>
		<integer>350</integer>
		<key>oiiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>oiint</key>
		<integer>100</integer>
		<key>oiint.cnd</key>
		<integer>100</integer>
		<key>oiint.v1</key>
		<integer>340</integer>
		<key>oiint.v1.cnd</key>
		<integer>340</integer>
		<key>oiintacw</key>
		<integer>100</integer>
		<key>oiintacw.cnd</key>
		<integer>100</integer>
		<key>oiintacw.v1</key>
		<integer>350</integer>
		<key>oiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>oiintcw</key>
		<integer>100</integer>
		<key>oiintcw.cnd</key>
		<integer>100</integer>
		<key>oiintcw.v1</key>
		<integer>350</integer>
		<key>oiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>oint</key>
		<integer>100</integer>
		<key>oint.v1</key>
		<integer>340</integer>
		<key>ointacw</key>
		<integer>150</integer>
		<key>ointacw.v1</key>
		<integer>300</integer>
		<key>ointcw</key>
		<integer>100</integer>
		<key>ointcw.v1</key>
		<integer>350</integer>
		<key>sqiiint</key>
		<integer>100</integer>
		<key>sqiiint.cnd</key>
		<integer>100</integer>
		<key>sqiiint.v1</key>
		<integer>350</integer>
		<key>sqiiint.v1.cnd</key>
		<integer>340</integer>
		<key>sqiint</key>
		<integer>100</integer>
		<key>sqiint.cnd</key>
		<integer>100</integer>
		<key>sqiint.v1</key>
		<integer>350</integer>
		<key>sqiint.v1.cnd</key>
		<integer>340</integer>
		<key>sqint</key>
		<integer>100</integer>
		<key>sqint.v1</key>
		<integer>300</integer>
		<key>varoiiintacw</key>
		<integer>100</integer>
		<key>varoiiintacw.cnd</key>
		<integer>100</integer>
		<key>varoiiintacw.v1</key>
		<integer>350</integer>
		<key>varoiiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>varoiiintcw</key>
		<integer>100</integer>
		<key>varoiiintcw.cnd</key>
		<integer>100</integer>
		<key>varoiiintcw.v1</key>
		<integer>350</integer>
		<key>varoiiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>varoiintacw</key>
		<integer>100</integer>
		<key>varoiintacw.cnd</key>
		<integer>100</integer>
		<key>varoiintacw.v1</key>
		<integer>350</integer>
		<key>varoiintacw.v1.cnd</key>
		<integer>340</integer>
		<key>varoiintcw</key>
		<integer>100</integer>
		<key>varoiintcw.cnd</key>
		<integer>100</integer>
		<key>varoiintcw.v1</key>
		<integer>350</integer>
		<key>varoiintcw.v1.cnd</key>
		<integer>340</integer>
		<key>varointacw</key>
		<integer>100</integer>
		<key>varointcw</key>
		<integer>150</integer>
		<key>varointcw.v1</key>
		<integer>300</integer>
		<key>wp</key>
		<integer>22</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>Downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>514</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown.bt</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>362</integer>
					<key>endConnector</key>
					<integer>121</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblvert.ex</string>
					<key>startConnector</key>
					<integer>121</integer>
				</dict>
			</array>
		</dict>
		<key>Uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>362</integer>
					<key>endConnector</key>
					<integer>121</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblvert.ex</string>
					<key>startConnector</key>
					<integer>121</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>Updownarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>514</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdbldown.bt</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>362</integer>
					<key>endConnector</key>
					<integer>121</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowdblvert.ex</string>
					<key>startConnector</key>
					<integer>121</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdblup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>Vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vert.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vert.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>Vvert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>Vvert.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>Vvert.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>downarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>504</integer>
					<key>endConnector</key>
					<integer>168</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown.bt</string>
					<key>startConnector</key>
					<integer>168</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>342</integer>
					<key>endConnector</key>
					<integer>114</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowvert.ex</string>
					<key>startConnector</key>
					<integer>114</integer>
				</dict>
			</array>
		</dict>
		<key>int</key>
		<dict>
			<key>italic</key>
			<integer>400</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1152</integer>
					<key>endConnector</key>
					<integer>384</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>integralbt</string>
					<key>startConnector</key>
					<integer>384</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>350</integer>
					<key>endConnector</key>
					<integer>117</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>integralex</string>
					<key>startConnector</key>
					<integer>117</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1152</integer>
					<key>endConnector</key>
					<integer>384</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>integraltp</string>
					<key>startConnector</key>
					<integer>384</integer>
				</dict>
			</array>
		</dict>
		<key>lBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lBrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lBrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.bt</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1500</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbracemid</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrace.tp</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
			</array>
		</dict>
		<key>lbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lbrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lbrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lceil.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lfloor.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>lparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>lparen.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>lparen.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>mid</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>mid</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>mid</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
			</array>
		</dict>
		<key>parallel</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>parallel</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>925</integer>
					<key>endConnector</key>
					<integer>308</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>parallel</string>
					<key>startConnector</key>
					<integer>308</integer>
				</dict>
			</array>
		</dict>
		<key>rBrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rBrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rBrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rbrace</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.bt</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1500</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbracemid</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>200</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vbrace.ex</string>
					<key>startConnector</key>
					<integer>67</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>800</integer>
					<key>endConnector</key>
					<integer>67</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrace.tp</string>
					<key>startConnector</key>
					<integer>267</integer>
				</dict>
			</array>
		</dict>
		<key>rbrack</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rbrack.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1550</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rbrack.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rceil</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rceil.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rfloor</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rfloor.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rceil.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>rparen</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.bt</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>rparen.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>rparen.tp</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
		<key>sqrt</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3127</integer>
					<key>endConnector</key>
					<integer>1042</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrtbottom</string>
					<key>startConnector</key>
					<integer>1042</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>316</integer>
					<key>endConnector</key>
					<integer>105</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>sqrt.ex</string>
					<key>startConnector</key>
					<integer>105</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>307</integer>
					<key>endConnector</key>
					<integer>102</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>sqrt.tp</string>
					<key>startConnector</key>
					<integer>102</integer>
				</dict>
			</array>
		</dict>
		<key>uparrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>342</integer>
					<key>endConnector</key>
					<integer>114</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowvert.ex</string>
					<key>startConnector</key>
					<integer>114</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>updownarrow</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>504</integer>
					<key>endConnector</key>
					<integer>168</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowdown.bt</string>
					<key>startConnector</key>
					<integer>168</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>342</integer>
					<key>endConnector</key>
					<integer>114</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>arrowvert.ex</string>
					<key>startConnector</key>
					<integer>114</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>513</integer>
					<key>endConnector</key>
					<integer>171</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>arrowup.tp</string>
					<key>startConnector</key>
					<integer>171</integer>
				</dict>
			</array>
		</dict>
		<key>vert</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2700</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>vert.v4</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>400</integer>
					<key>endConnector</key>
					<integer>133</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>vert.ex</string>
					<key>startConnector</key>
					<integer>133</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>Bbbsum</key>
		<array>
			<string>Bbbsum</string>
			<string>Bbbsum.v1</string>
		</array>
		<key>Downarrow</key>
		<array>
			<string>Downarrow</string>
		</array>
		<key>Uparrow</key>
		<array>
			<string>Uparrow</string>
		</array>
		<key>Updownarrow</key>
		<array/>
		<key>Vert</key>
		<array>
			<string>Vert</string>
			<string>Vert.v1</string>
			<string>Vert.v2</string>
			<string>Vert.v3</string>
			<string>Vert.v4</string>
		</array>
		<key>Vvert</key>
		<array>
			<string>Vvert</string>
			<string>Vvert.v1</string>
			<string>Vvert.v2</string>
			<string>Vvert.v3</string>
			<string>Vvert.v4</string>
		</array>
		<key>awint</key>
		<array>
			<string>awint</string>
			<string>awint.v1</string>
		</array>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.v1</string>
			<string>backslash.v2</string>
			<string>backslash.v3</string>
			<string>backslash.v4</string>
		</array>
		<key>bigcap</key>
		<array>
			<string>bigcap</string>
			<string>bigcap.v1</string>
		</array>
		<key>bigcapplus</key>
		<array>
			<string>bigcapplus</string>
			<string>bigcapplus.v1</string>
		</array>
		<key>bigcup</key>
		<array>
			<string>bigcup</string>
			<string>bigcup.v1</string>
		</array>
		<key>bigcupdot</key>
		<array>
			<string>bigcupdot</string>
			<string>bigcupdot.v1</string>
		</array>
		<key>bigodot</key>
		<array>
			<string>bigodot</string>
			<string>bigodot.v1</string>
		</array>
		<key>bigoplus</key>
		<array>
			<string>bigoplus</string>
			<string>bigoplus.v1</string>
		</array>
		<key>bigotimes</key>
		<array>
			<string>bigotimes</string>
			<string>bigotimes.v1</string>
		</array>
		<key>bigsqcap</key>
		<array>
			<string>bigsqcap</string>
			<string>bigsqcap.v1</string>
		</array>
		<key>bigsqcapplus</key>
		<array>
			<string>bigsqcapplus</string>
			<string>bigsqcapplus.v1</string>
		</array>
		<key>bigsqcup</key>
		<array>
			<string>bigsqcup</string>
			<string>bigsqcup.v1</string>
		</array>
		<key>bigsqcupplus</key>
		<array>
			<string>bigsqcupplus</string>
			<string>bigsqcupplus.v1</string>
		</array>
		<key>bigtimes</key>
		<array>
			<string>bigtimes</string>
			<string>bigtimes.v1</string>
		</array>
		<key>biguplus</key>
		<array>
			<string>biguplus</string>
			<string>biguplus.v1</string>
		</array>
		<key>bigvee</key>
		<array>
			<string>bigvee</string>
			<string>bigvee.v1</string>
		</array>
		<key>bigwedge</key>
		<array>
			<string>bigwedge</string>
			<string>bigwedge.v1</string>
		</array>
		<key>coprod</key>
		<array>
			<string>coprod</string>
			<string>coprod.v1</string>
		</array>
		<key>downarrow</key>
		<array>
			<string>downarrow</string>
		</array>
		<key>fint</key>
		<array>
			<string>fint</string>
			<string>fint.v1</string>
		</array>
		<key>idotsint</key>
		<array>
			<string>idotsint</string>
			<string>idotsint.v1</string>
		</array>
		<key>idotsint.cnd</key>
		<array>
			<string>idotsint.cnd</string>
			<string>idotsint.v1.cnd</string>
		</array>
		<key>iiiint</key>
		<array>
			<string>iiiint</string>
			<string>iiiint.v1</string>
		</array>
		<key>iiiint.cnd</key>
		<array>
			<string>iiiint.cnd</string>
			<string>iiiint.v1.cnd</string>
		</array>
		<key>iiint</key>
		<array>
			<string>iiint</string>
			<string>iiint.v1</string>
		</array>
		<key>iiint.cnd</key>
		<array>
			<string>iiint.cnd</string>
			<string>iiint.v1.cnd</string>
		</array>
		<key>iint</key>
		<array>
			<string>iint</string>
			<string>iint.v1</string>
		</array>
		<key>iint.cnd</key>
		<array>
			<string>iint.cnd</string>
			<string>iint.v1.cnd</string>
		</array>
		<key>int</key>
		<array>
			<string>int</string>
			<string>int.v1</string>
		</array>
		<key>intclockwise</key>
		<array>
			<string>intclockwise</string>
			<string>intclockwise.v1</string>
		</array>
		<key>lAngle</key>
		<array>
			<string>lAngle</string>
			<string>lAngle.v1</string>
			<string>lAngle.v2</string>
			<string>lAngle.v3</string>
			<string>lAngle.v4</string>
			<string>lAngle.v5</string>
			<string>lAngle.v6</string>
			<string>lAngle.v7</string>
		</array>
		<key>lBrack</key>
		<array>
			<string>lBrack</string>
			<string>lBrack.v1</string>
			<string>lBrack.v2</string>
			<string>lBrack.v3</string>
			<string>lBrack.v4</string>
		</array>
		<key>langle</key>
		<array>
			<string>langle</string>
			<string>langle.v1</string>
			<string>langle.v2</string>
			<string>langle.v3</string>
			<string>langle.v4</string>
			<string>langle.v5</string>
			<string>langle.v6</string>
			<string>langle.v7</string>
		</array>
		<key>lbag</key>
		<array>
			<string>lbag</string>
			<string>lbag.v1</string>
			<string>lbag.v2</string>
			<string>lbag.v3</string>
			<string>lbag.v4</string>
			<string>lbag.v5</string>
			<string>lbag.v6</string>
			<string>lbag.v7</string>
		</array>
		<key>lbrace</key>
		<array>
			<string>lbrace</string>
			<string>lbrace.v1</string>
			<string>lbrace.v2</string>
			<string>lbrace.v3</string>
			<string>lbrace.v4</string>
		</array>
		<key>lbrack</key>
		<array>
			<string>lbrack</string>
			<string>lbrack.v1</string>
			<string>lbrack.v2</string>
			<string>lbrack.v3</string>
			<string>lbrack.v4</string>
		</array>
		<key>lceil</key>
		<array>
			<string>lceil</string>
			<string>lceil.v1</string>
			<string>lceil.v2</string>
			<string>lceil.v3</string>
			<string>lceil.v4</string>
		</array>
		<key>lfloor</key>
		<array>
			<string>lfloor</string>
			<string>lfloor.v1</string>
			<string>lfloor.v2</string>
			<string>lfloor.v3</string>
			<string>lfloor.v4</string>
		</array>
		<key>lparen</key>
		<array>
			<string>lparen.v1</string>
			<string>lparen.v2</string>
			<string>lparen.v3</string>
			<string>lparen.v4</string>
		</array>
		<key>mathslash</key>
		<array>
			<string>mathslash</string>
			<string>slash.v1</string>
			<string>slash.v2</string>
			<string>slash.v3</string>
			<string>slash.v4</string>
		</array>
		<key>mid</key>
		<array>
			<string>mid</string>
		</array>
		<key>oiiint</key>
		<array>
			<string>oiiint</string>
			<string>oiiint.v1</string>
		</array>
		<key>oiiint.cnd</key>
		<array>
			<string>oiiint.cnd</string>
			<string>oiiint.v1.cnd</string>
		</array>
		<key>oiiintacw</key>
		<array>
			<string>oiiintacw</string>
			<string>oiiintacw.v1</string>
		</array>
		<key>oiiintacw.cnd</key>
		<array>
			<string>oiiintacw.cnd</string>
			<string>oiiintacw.v1.cnd</string>
		</array>
		<key>oiiintcw</key>
		<array>
			<string>oiiintcw</string>
			<string>oiiintcw.v1</string>
		</array>
		<key>oiiintcw.cnd</key>
		<array>
			<string>oiiintcw.cnd</string>
			<string>oiiintcw.v1.cnd</string>
		</array>
		<key>oiint</key>
		<array>
			<string>oiint</string>
			<string>oiint.v1</string>
		</array>
		<key>oiint.cnd</key>
		<array>
			<string>oiint.cnd</string>
			<string>oiint.v1.cnd</string>
		</array>
		<key>oiintacw</key>
		<array>
			<string>oiintacw</string>
			<string>oiintacw.v1</string>
		</array>
		<key>oiintacw.cnd</key>
		<array>
			<string>oiintacw.cnd</string>
			<string>oiintacw.v1.cnd</string>
		</array>
		<key>oiintcw</key>
		<array>
			<string>oiintcw</string>
			<string>oiintcw.v1</string>
		</array>
		<key>oiintcw.cnd</key>
		<array>
			<string>oiintcw.cnd</string>
			<string>oiintcw.v1.cnd</string>
		</array>
		<key>oint</key>
		<array>
			<string>oint</string>
			<string>oint.v1</string>
		</array>
		<key>ointacw</key>
		<array>
			<string>ointacw.v1</string>
		</array>
		<key>ointcw</key>
		<array>
			<string>ointcw</string>
			<string>ointcw.v1</string>
		</array>
		<key>parallel</key>
		<array>
			<string>parallel</string>
		</array>
		<key>prod</key>
		<array>
			<string>prod</string>
			<string>prod.v1</string>
		</array>
		<key>rAngle</key>
		<array>
			<string>rAngle</string>
			<string>rAngle.v1</string>
			<string>rAngle.v2</string>
			<string>rAngle.v3</string>
			<string>rAngle.v4</string>
			<string>rAngle.v5</string>
			<string>rAngle.v6</string>
			<string>rAngle.v7</string>
		</array>
		<key>rBrack</key>
		<array>
			<string>rBrack</string>
			<string>rBrack.v1</string>
			<string>rBrack.v2</string>
			<string>rBrack.v3</string>
			<string>rBrack.v4</string>
		</array>
		<key>rangle</key>
		<array>
			<string>rangle</string>
			<string>rangle.v1</string>
			<string>rangle.v2</string>
			<string>rangle.v3</string>
			<string>rangle.v4</string>
			<string>rangle.v5</string>
			<string>rangle.v6</string>
			<string>rangle.v7</string>
		</array>
		<key>rbag</key>
		<array>
			<string>rbag</string>
			<string>rbag.v1</string>
			<string>rbag.v2</string>
			<string>rbag.v3</string>
			<string>rbag.v4</string>
			<string>rbag.v5</string>
			<string>rbag.v6</string>
			<string>rbag.v7</string>
		</array>
		<key>rbrace</key>
		<array>
			<string>rbrace</string>
			<string>rbrace.v1</string>
			<string>rbrace.v2</string>
			<string>rbrace.v3</string>
			<string>rbrace.v4</string>
		</array>
		<key>rbrack</key>
		<array>
			<string>rbrack.v1</string>
			<string>rbrack.v2</string>
			<string>rbrack.v3</string>
			<string>rbrack.v4</string>
		</array>
		<key>rceil</key>
		<array>
			<string>rceil</string>
			<string>rceil.v1</string>
			<string>rceil.v2</string>
			<string>rceil.v3</string>
			<string>rceil.v4</string>
		</array>
		<key>rfloor</key>
		<array>
			<string>rfloor</string>
			<string>rfloor.v1</string>
			<string>rfloor.v2</string>
			<string>rfloor.v3</string>
			<string>rfloor.v4</string>
		</array>
		<key>rparen</key>
		<array>
			<string>rparen.v1</string>
			<string>rparen.v2</string>
			<string>rparen.v3</string>
			<string>rparen.v4</string>
		</array>
		<key>sqiiint</key>
		<array>
			<string>sqiiint</string>
			<string>sqiiint.v1</string>
		</array>
		<key>sqiiint.cnd</key>
		<array>
			<string>sqiiint.cnd</string>
			<string>sqiiint.v1.cnd</string>
		</array>
		<key>sqiint</key>
		<array>
			<string>sqiint</string>
			<string>sqiint.v1</string>
		</array>
		<key>sqiint.cnd</key>
		<array>
			<string>sqiint.cnd</string>
			<string>sqiint.v1.cnd</string>
		</array>
		<key>sqint</key>
		<array>
			<string>sqint</string>
			<string>sqint.v1</string>
		</array>
		<key>sqrt</key>
		<array>
			<string>sqrt.v1</string>
			<string>sqrt.v2</string>
			<string>sqrt.v3</string>
			<string>sqrt.v4</string>
		</array>
		<key>sum</key>
		<array>
			<string>sum</string>
			<string>sum.v1</string>
		</array>
		<key>uparrow</key>
		<array>
			<string>uparrow</string>
		</array>
		<key>updownarrow</key>
		<array/>
		<key>varidotsint</key>
		<array>
			<string>varidotsint</string>
			<string>varidotsint.v1</string>
		</array>
		<key>varidotsint.cnd</key>
		<array>
			<string>varidotsint.cnd</string>
			<string>varidotsint.v1.cnd</string>
		</array>
		<key>variiiint</key>
		<array>
			<string>variiiint</string>
			<string>variiiint.v1</string>
		</array>
		<key>variiiint.cnd</key>
		<array>
			<string>variiiint.cnd</string>
			<string>variiiint.v1.cnd</string>
		</array>
		<key>variiint</key>
		<array>
			<string>variiint</string>
			<string>variiint.v1</string>
		</array>
		<key>variiint.cnd</key>
		<array>
			<string>variiint.cnd</string>
			<string>variiint.v1.cnd</string>
		</array>
		<key>variint</key>
		<array>
			<string>variint</string>
			<string>variint.v1</string>
		</array>
		<key>variint.cnd</key>
		<array>
			<string>variint.cnd</string>
			<string>variint.v1.cnd</string>
		</array>
		<key>varint</key>
		<array>
			<string>varint</string>
			<string>varint.v1</string>
		</array>
		<key>varoiiintacw</key>
		<array>
			<string>varoiiintacw</string>
			<string>varoiiintacw.v1</string>
		</array>
		<key>varoiiintacw.cnd</key>
		<array>
			<string>varoiiintacw.cnd</string>
			<string>varoiiintacw.v1.cnd</string>
		</array>
		<key>varoiiintcw</key>
		<array>
			<string>varoiiintcw</string>
			<string>varoiiintcw.v1</string>
		</array>
		<key>varoiiintcw.cnd</key>
		<array>
			<string>varoiiintcw.cnd</string>
			<string>varoiiintcw.v1.cnd</string>
		</array>
		<key>varoiintacw</key>
		<array>
			<string>varoiintacw</string>
			<string>varoiintacw.v1</string>
		</array>
		<key>varoiintacw.cnd</key>
		<array>
			<string>varoiintacw.cnd</string>
			<string>varoiintacw.v1.cnd</string>
		</array>
		<key>varoiintcw</key>
		<array>
			<string>varoiintcw</string>
			<string>varoiintcw.v1</string>
		</array>
		<key>varoiintcw.cnd</key>
		<array>
			<string>varoiintcw.cnd</string>
			<string>varoiintcw.v1.cnd</string>
		</array>
		<key>varointacw</key>
		<array>
			<string>varointacw</string>
			<string>varointacw.v1</string>
		</array>
		<key>varointcw</key>
		<array>
			<string>varointcw.v1</string>
		</array>
		<key>vert</key>
		<array>
			<string>vert</string>
			<string>vert.v1</string>
			<string>vert.v2</string>
			<string>vert.v3</string>
			<string>vert.v4</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
