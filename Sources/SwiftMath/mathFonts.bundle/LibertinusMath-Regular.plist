<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>A</key>
		<integer>354</integer>
		<key>Alpha</key>
		<integer>354</integer>
		<key>B</key>
		<integer>267</integer>
		<key>Beta</key>
		<integer>267</integer>
		<key>C</key>
		<integer>340</integer>
		<key>Chi</key>
		<integer>332</integer>
		<key>D</key>
		<integer>306</integer>
		<key>E</key>
		<integer>281</integer>
		<key>Epsilon</key>
		<integer>281</integer>
		<key>Eta</key>
		<integer>361</integer>
		<key>F</key>
		<integer>284</integer>
		<key>G</key>
		<integer>365</integer>
		<key>Gamma</key>
		<integer>248</integer>
		<key>H</key>
		<integer>361</integer>
		<key>I</key>
		<integer>150</integer>
		<key>Ifraktur</key>
		<integer>400</integer>
		<key>Iota</key>
		<integer>150</integer>
		<key>J</key>
		<integer>262</integer>
		<key>K</key>
		<integer>336</integer>
		<key>Kappa</key>
		<integer>336</integer>
		<key>L</key>
		<integer>151</integer>
		<key>Lambda</key>
		<integer>329</integer>
		<key>M</key>
		<integer>431</integer>
		<key>Mu</key>
		<integer>431</integer>
		<key>N</key>
		<integer>346</integer>
		<key>Nu</key>
		<integer>346</integer>
		<key>O</key>
		<integer>335</integer>
		<key>Omicron</key>
		<integer>335</integer>
		<key>P</key>
		<integer>277</integer>
		<key>Phi</key>
		<integer>321</integer>
		<key>Pi</key>
		<integer>333</integer>
		<key>Psi</key>
		<integer>339</integer>
		<key>Q</key>
		<integer>348</integer>
		<key>R</key>
		<integer>269</integer>
		<key>Rfraktur</key>
		<integer>430</integer>
		<key>Rho</key>
		<integer>277</integer>
		<key>S</key>
		<integer>231</integer>
		<key>Sigma</key>
		<integer>287</integer>
		<key>T</key>
		<integer>307</integer>
		<key>Tau</key>
		<integer>307</integer>
		<key>Theta</key>
		<integer>351</integer>
		<key>U</key>
		<integer>346</integer>
		<key>Upsilon</key>
		<integer>295</integer>
		<key>Upsilon1</key>
		<integer>276</integer>
		<key>V</key>
		<integer>336</integer>
		<key>W</key>
		<integer>450</integer>
		<key>X</key>
		<integer>332</integer>
		<key>Xi</key>
		<integer>324</integer>
		<key>Y</key>
		<integer>295</integer>
		<key>Z</key>
		<integer>320</integer>
		<key>Zeta</key>
		<integer>320</integer>
		<key>a</key>
		<integer>209</integer>
		<key>acutecomb</key>
		<integer>-192</integer>
		<key>alpha</key>
		<integer>283</integer>
		<key>b</key>
		<integer>223</integer>
		<key>beta</key>
		<integer>258</integer>
		<key>c</key>
		<integer>242</integer>
		<key>chi</key>
		<integer>266</integer>
		<key>d</key>
		<integer>218</integer>
		<key>delta</key>
		<integer>245</integer>
		<key>dotbelowcomb</key>
		<integer>-216</integer>
		<key>e</key>
		<integer>242</integer>
		<key>epsilon</key>
		<integer>215</integer>
		<key>eta</key>
		<integer>264</integer>
		<key>f</key>
		<integer>230</integer>
		<key>g</key>
		<integer>231</integer>
		<key>gamma</key>
		<integer>279</integer>
		<key>gravecomb</key>
		<integer>-142</integer>
		<key>h</key>
		<integer>299</integer>
		<key>hookabovecomb</key>
		<integer>-160</integer>
		<key>i</key>
		<integer>137</integer>
		<key>iota</key>
		<integer>140</integer>
		<key>j</key>
		<integer>212</integer>
		<key>k</key>
		<integer>305</integer>
		<key>kappa</key>
		<integer>253</integer>
		<key>l</key>
		<integer>135</integer>
		<key>lambda</key>
		<integer>129</integer>
		<key>m</key>
		<integer>392</integer>
		<key>n</key>
		<integer>264</integer>
		<key>nu</key>
		<integer>271</integer>
		<key>o</key>
		<integer>238</integer>
		<key>omega</key>
		<integer>329</integer>
		<key>omega1</key>
		<integer>346</integer>
		<key>omicron</key>
		<integer>238</integer>
		<key>p</key>
		<integer>271</integer>
		<key>phi</key>
		<integer>294</integer>
		<key>phi1</key>
		<integer>289</integer>
		<key>pi</key>
		<integer>283</integer>
		<key>psi</key>
		<integer>342</integer>
		<key>q</key>
		<integer>238</integer>
		<key>r</key>
		<integer>191</integer>
		<key>rho</key>
		<integer>262</integer>
		<key>s</key>
		<integer>206</integer>
		<key>sigma</key>
		<integer>285</integer>
		<key>sigma1</key>
		<integer>220</integer>
		<key>t</key>
		<integer>131</integer>
		<key>tau</key>
		<integer>220</integer>
		<key>theta</key>
		<integer>244</integer>
		<key>theta1</key>
		<integer>233</integer>
		<key>tildecomb</key>
		<integer>-138</integer>
		<key>u</key>
		<integer>255</integer>
		<key>u1D400</key>
		<integer>354</integer>
		<key>u1D401</key>
		<integer>310</integer>
		<key>u1D402</key>
		<integer>374</integer>
		<key>u1D403</key>
		<integer>359</integer>
		<key>u1D404</key>
		<integer>281</integer>
		<key>u1D405</key>
		<integer>280</integer>
		<key>u1D406</key>
		<integer>363</integer>
		<key>u1D407</key>
		<integer>361</integer>
		<key>u1D408</key>
		<integer>159</integer>
		<key>u1D409</key>
		<integer>293</integer>
		<key>u1D40A</key>
		<integer>330</integer>
		<key>u1D40B</key>
		<integer>159</integer>
		<key>u1D40C</key>
		<integer>426</integer>
		<key>u1D40D</key>
		<integer>355</integer>
		<key>u1D40E</key>
		<integer>365</integer>
		<key>u1D40F</key>
		<integer>275</integer>
		<key>u1D410</key>
		<integer>346</integer>
		<key>u1D411</key>
		<integer>302</integer>
		<key>u1D412</key>
		<integer>227</integer>
		<key>u1D413</key>
		<integer>296</integer>
		<key>u1D414</key>
		<integer>339</integer>
		<key>u1D415</key>
		<integer>329</integer>
		<key>u1D416</key>
		<integer>491</integer>
		<key>u1D417</key>
		<integer>363</integer>
		<key>u1D418</key>
		<integer>315</integer>
		<key>u1D419</key>
		<integer>319</integer>
		<key>u1D41A</key>
		<integer>238</integer>
		<key>u1D41B</key>
		<integer>252</integer>
		<key>u1D41C</key>
		<integer>217</integer>
		<key>u1D41D</key>
		<integer>263</integer>
		<key>u1D41E</key>
		<integer>230</integer>
		<key>u1D41F</key>
		<integer>246</integer>
		<key>u1D420</key>
		<integer>254</integer>
		<key>u1D421</key>
		<integer>275</integer>
		<key>u1D422</key>
		<integer>143</integer>
		<key>u1D423</key>
		<integer>227</integer>
		<key>u1D424</key>
		<integer>261</integer>
		<key>u1D425</key>
		<integer>144</integer>
		<key>u1D426</key>
		<integer>397</integer>
		<key>u1D427</key>
		<integer>265</integer>
		<key>u1D428</key>
		<integer>259</integer>
		<key>u1D429</key>
		<integer>261</integer>
		<key>u1D42A</key>
		<integer>259</integer>
		<key>u1D42B</key>
		<integer>200</integer>
		<key>u1D42C</key>
		<integer>200</integer>
		<key>u1D42D</key>
		<integer>165</integer>
		<key>u1D42E</key>
		<integer>264</integer>
		<key>u1D42F</key>
		<integer>263</integer>
		<key>u1D430</key>
		<integer>389</integer>
		<key>u1D431</key>
		<integer>260</integer>
		<key>u1D432</key>
		<integer>266</integer>
		<key>u1D433</key>
		<integer>216</integer>
		<key>u1D434</key>
		<integer>492</integer>
		<key>u1D435</key>
		<integer>365</integer>
		<key>u1D436</key>
		<integer>401</integer>
		<key>u1D437</key>
		<integer>370</integer>
		<key>u1D438</key>
		<integer>392</integer>
		<key>u1D439</key>
		<integer>384</integer>
		<key>u1D43A</key>
		<integer>419</integer>
		<key>u1D43B</key>
		<integer>452</integer>
		<key>u1D43C</key>
		<integer>287</integer>
		<key>u1D43D</key>
		<integer>419</integer>
		<key>u1D43E</key>
		<integer>437</integer>
		<key>u1D43F</key>
		<integer>359</integer>
		<key>u1D440</key>
		<integer>531</integer>
		<key>u1D441</key>
		<integer>454</integer>
		<key>u1D442</key>
		<integer>418</integer>
		<key>u1D443</key>
		<integer>400</integer>
		<key>u1D444</key>
		<integer>401</integer>
		<key>u1D445</key>
		<integer>393</integer>
		<key>u1D446</key>
		<integer>341</integer>
		<key>u1D447</key>
		<integer>336</integer>
		<key>u1D448</key>
		<integer>354</integer>
		<key>u1D449</key>
		<integer>361</integer>
		<key>u1D44A</key>
		<integer>465</integer>
		<key>u1D44B</key>
		<integer>469</integer>
		<key>u1D44C</key>
		<integer>303</integer>
		<key>u1D44D</key>
		<integer>399</integer>
		<key>u1D44E</key>
		<integer>252</integer>
		<key>u1D44F</key>
		<integer>161</integer>
		<key>u1D450</key>
		<integer>250</integer>
		<key>u1D451</key>
		<integer>263</integer>
		<key>u1D452</key>
		<integer>239</integer>
		<key>u1D453</key>
		<integer>473</integer>
		<key>u1D454</key>
		<integer>290</integer>
		<key>u1D456</key>
		<integer>166</integer>
		<key>u1D457</key>
		<integer>267</integer>
		<key>u1D458</key>
		<integer>198</integer>
		<key>u1D459</key>
		<integer>179</integer>
		<key>u1D45A</key>
		<integer>400</integer>
		<key>u1D45B</key>
		<integer>295</integer>
		<key>u1D45C</key>
		<integer>271</integer>
		<key>u1D45D</key>
		<integer>420</integer>
		<key>u1D45E</key>
		<integer>278</integer>
		<key>u1D45F</key>
		<integer>213</integer>
		<key>u1D460</key>
		<integer>218</integer>
		<key>u1D461</key>
		<integer>176</integer>
		<key>u1D462</key>
		<integer>291</integer>
		<key>u1D463</key>
		<integer>285</integer>
		<key>u1D463.alt</key>
		<integer>400</integer>
		<key>u1D464</key>
		<integer>397</integer>
		<key>u1D465</key>
		<integer>321</integer>
		<key>u1D466</key>
		<integer>308</integer>
		<key>u1D467</key>
		<integer>296</integer>
		<key>u1D468</key>
		<integer>501</integer>
		<key>u1D469</key>
		<integer>422</integer>
		<key>u1D46A</key>
		<integer>418</integer>
		<key>u1D46B</key>
		<integer>445</integer>
		<key>u1D46C</key>
		<integer>410</integer>
		<key>u1D46D</key>
		<integer>413</integer>
		<key>u1D46E</key>
		<integer>448</integer>
		<key>u1D46F</key>
		<integer>472</integer>
		<key>u1D470</key>
		<integer>273</integer>
		<key>u1D471</key>
		<integer>378</integer>
		<key>u1D472</key>
		<integer>443</integer>
		<key>u1D473</key>
		<integer>260</integer>
		<key>u1D474</key>
		<integer>537</integer>
		<key>u1D475</key>
		<integer>453</integer>
		<key>u1D476</key>
		<integer>398</integer>
		<key>u1D477</key>
		<integer>416</integer>
		<key>u1D478</key>
		<integer>411</integer>
		<key>u1D479</key>
		<integer>408</integer>
		<key>u1D47A</key>
		<integer>341</integer>
		<key>u1D47B</key>
		<integer>306</integer>
		<key>u1D47C</key>
		<integer>373</integer>
		<key>u1D47D</key>
		<integer>387</integer>
		<key>u1D47E</key>
		<integer>501</integer>
		<key>u1D47F</key>
		<integer>487</integer>
		<key>u1D480</key>
		<integer>346</integer>
		<key>u1D481</key>
		<integer>459</integer>
		<key>u1D482</key>
		<integer>380</integer>
		<key>u1D483</key>
		<integer>214</integer>
		<key>u1D484</key>
		<integer>330</integer>
		<key>u1D485</key>
		<integer>308</integer>
		<key>u1D486</key>
		<integer>323</integer>
		<key>u1D487</key>
		<integer>509</integer>
		<key>u1D488</key>
		<integer>292</integer>
		<key>u1D489</key>
		<integer>207</integer>
		<key>u1D48A</key>
		<integer>187</integer>
		<key>u1D48B</key>
		<integer>384</integer>
		<key>u1D48C</key>
		<integer>262</integer>
		<key>u1D48D</key>
		<integer>190</integer>
		<key>u1D48E</key>
		<integer>463</integer>
		<key>u1D48F</key>
		<integer>335</integer>
		<key>u1D490</key>
		<integer>271</integer>
		<key>u1D491</key>
		<integer>470</integer>
		<key>u1D492</key>
		<integer>322</integer>
		<key>u1D493</key>
		<integer>262</integer>
		<key>u1D494</key>
		<integer>226</integer>
		<key>u1D495</key>
		<integer>197</integer>
		<key>u1D496</key>
		<integer>336</integer>
		<key>u1D497</key>
		<integer>324</integer>
		<key>u1D498</key>
		<integer>403</integer>
		<key>u1D499</key>
		<integer>345</integer>
		<key>u1D49A</key>
		<integer>407</integer>
		<key>u1D49B</key>
		<integer>334</integer>
		<key>u1D49C</key>
		<integer>800</integer>
		<key>u1D49E</key>
		<integer>697</integer>
		<key>u1D49F</key>
		<integer>725</integer>
		<key>u1D4A2</key>
		<integer>642</integer>
		<key>u1D4A5</key>
		<integer>662</integer>
		<key>u1D4A6</key>
		<integer>772</integer>
		<key>u1D4A9</key>
		<integer>740</integer>
		<key>u1D4AA</key>
		<integer>519</integer>
		<key>u1D4AB</key>
		<integer>710</integer>
		<key>u1D4AC</key>
		<integer>520</integer>
		<key>u1D4AE</key>
		<integer>562</integer>
		<key>u1D4AF</key>
		<integer>605</integer>
		<key>u1D4B0</key>
		<integer>552</integer>
		<key>u1D4B1</key>
		<integer>660</integer>
		<key>u1D4B2</key>
		<integer>756</integer>
		<key>u1D4B3</key>
		<integer>602</integer>
		<key>u1D4B4</key>
		<integer>648</integer>
		<key>u1D4B5</key>
		<integer>652</integer>
		<key>u1D4B6</key>
		<integer>499</integer>
		<key>u1D4B7</key>
		<integer>497</integer>
		<key>u1D4B8</key>
		<integer>487</integer>
		<key>u1D4B9</key>
		<integer>782</integer>
		<key>u1D4BB</key>
		<integer>600</integer>
		<key>u1D4BD</key>
		<integer>511</integer>
		<key>u1D4BE</key>
		<integer>443</integer>
		<key>u1D4BF</key>
		<integer>671</integer>
		<key>u1D4C0</key>
		<integer>513</integer>
		<key>u1D4C1</key>
		<integer>511</integer>
		<key>u1D4C2</key>
		<integer>603</integer>
		<key>u1D4C3</key>
		<integer>455</integer>
		<key>u1D4C5</key>
		<integer>545</integer>
		<key>u1D4C6</key>
		<integer>492</integer>
		<key>u1D4C7</key>
		<integer>404</integer>
		<key>u1D4C8</key>
		<integer>376</integer>
		<key>u1D4C9</key>
		<integer>422</integer>
		<key>u1D4CA</key>
		<integer>433</integer>
		<key>u1D4CB</key>
		<integer>393</integer>
		<key>u1D4CC</key>
		<integer>525</integer>
		<key>u1D4CD</key>
		<integer>454</integer>
		<key>u1D4CE</key>
		<integer>434</integer>
		<key>u1D4CF</key>
		<integer>399</integer>
		<key>u1D4D0</key>
		<integer>900</integer>
		<key>u1D4D1</key>
		<integer>856</integer>
		<key>u1D4D2</key>
		<integer>744</integer>
		<key>u1D4D3</key>
		<integer>799</integer>
		<key>u1D4D4</key>
		<integer>657</integer>
		<key>u1D4D5</key>
		<integer>719</integer>
		<key>u1D4D6</key>
		<integer>667</integer>
		<key>u1D4D7</key>
		<integer>787</integer>
		<key>u1D4D8</key>
		<integer>726</integer>
		<key>u1D4D9</key>
		<integer>631</integer>
		<key>u1D4DA</key>
		<integer>843</integer>
		<key>u1D4DB</key>
		<integer>906</integer>
		<key>u1D4DC</key>
		<integer>1006</integer>
		<key>u1D4DD</key>
		<integer>827</integer>
		<key>u1D4DE</key>
		<integer>562</integer>
		<key>u1D4DF</key>
		<integer>816</integer>
		<key>u1D4E0</key>
		<integer>562</integer>
		<key>u1D4E1</key>
		<integer>816</integer>
		<key>u1D4E2</key>
		<integer>606</integer>
		<key>u1D4E3</key>
		<integer>715</integer>
		<key>u1D4E4</key>
		<integer>624</integer>
		<key>u1D4E5</key>
		<integer>722</integer>
		<key>u1D4E6</key>
		<integer>936</integer>
		<key>u1D4E7</key>
		<integer>666</integer>
		<key>u1D4E8</key>
		<integer>634</integer>
		<key>u1D4E9</key>
		<integer>676</integer>
		<key>u1D4EA</key>
		<integer>585</integer>
		<key>u1D4EB</key>
		<integer>525</integer>
		<key>u1D4EC</key>
		<integer>560</integer>
		<key>u1D4ED</key>
		<integer>848</integer>
		<key>u1D4EE</key>
		<integer>523</integer>
		<key>u1D4EF</key>
		<integer>642</integer>
		<key>u1D4F0</key>
		<integer>557</integer>
		<key>u1D4F1</key>
		<integer>557</integer>
		<key>u1D4F2</key>
		<integer>469</integer>
		<key>u1D4F3</key>
		<integer>758</integer>
		<key>u1D4F4</key>
		<integer>555</integer>
		<key>u1D4F5</key>
		<integer>525</integer>
		<key>u1D4F6</key>
		<integer>648</integer>
		<key>u1D4F7</key>
		<integer>481</integer>
		<key>u1D4F8</key>
		<integer>576</integer>
		<key>u1D4F9</key>
		<integer>636</integer>
		<key>u1D4FA</key>
		<integer>562</integer>
		<key>u1D4FB</key>
		<integer>452</integer>
		<key>u1D4FC</key>
		<integer>375</integer>
		<key>u1D4FD</key>
		<integer>475</integer>
		<key>u1D4FE</key>
		<integer>475</integer>
		<key>u1D4FF</key>
		<integer>464</integer>
		<key>u1D500</key>
		<integer>622</integer>
		<key>u1D501</key>
		<integer>488</integer>
		<key>u1D502</key>
		<integer>500</integer>
		<key>u1D503</key>
		<integer>391</integer>
		<key>u1D504</key>
		<integer>420</integer>
		<key>u1D505</key>
		<integer>430</integer>
		<key>u1D507</key>
		<integer>420</integer>
		<key>u1D508</key>
		<integer>360</integer>
		<key>u1D509</key>
		<integer>340</integer>
		<key>u1D50A</key>
		<integer>380</integer>
		<key>u1D50D</key>
		<integer>370</integer>
		<key>u1D50E</key>
		<integer>410</integer>
		<key>u1D50F</key>
		<integer>330</integer>
		<key>u1D510</key>
		<integer>530</integer>
		<key>u1D511</key>
		<integer>430</integer>
		<key>u1D512</key>
		<integer>390</integer>
		<key>u1D513</key>
		<integer>420</integer>
		<key>u1D514</key>
		<integer>390</integer>
		<key>u1D516</key>
		<integer>420</integer>
		<key>u1D517</key>
		<integer>380</integer>
		<key>u1D518</key>
		<integer>370</integer>
		<key>u1D519</key>
		<integer>440</integer>
		<key>u1D51A</key>
		<integer>550</integer>
		<key>u1D51B</key>
		<integer>370</integer>
		<key>u1D51C</key>
		<integer>380</integer>
		<key>u1D51E</key>
		<integer>260</integer>
		<key>u1D51F</key>
		<integer>250</integer>
		<key>u1D520</key>
		<integer>210</integer>
		<key>u1D521</key>
		<integer>270</integer>
		<key>u1D522</key>
		<integer>210</integer>
		<key>u1D523</key>
		<integer>190</integer>
		<key>u1D524</key>
		<integer>280</integer>
		<key>u1D525</key>
		<integer>270</integer>
		<key>u1D526</key>
		<integer>185</integer>
		<key>u1D527</key>
		<integer>200</integer>
		<key>u1D528</key>
		<integer>210</integer>
		<key>u1D529</key>
		<integer>200</integer>
		<key>u1D52A</key>
		<integer>450</integer>
		<key>u1D52B</key>
		<integer>330</integer>
		<key>u1D52C</key>
		<integer>260</integer>
		<key>u1D52D</key>
		<integer>300</integer>
		<key>u1D52E</key>
		<integer>270</integer>
		<key>u1D52F</key>
		<integer>250</integer>
		<key>u1D530</key>
		<integer>260</integer>
		<key>u1D531</key>
		<integer>190</integer>
		<key>u1D532</key>
		<integer>320</integer>
		<key>u1D533</key>
		<integer>260</integer>
		<key>u1D534</key>
		<integer>390</integer>
		<key>u1D535</key>
		<integer>220</integer>
		<key>u1D536</key>
		<integer>260</integer>
		<key>u1D537</key>
		<integer>240</integer>
		<key>u1D56C</key>
		<integer>460</integer>
		<key>u1D56D</key>
		<integer>430</integer>
		<key>u1D56E</key>
		<integer>390</integer>
		<key>u1D56F</key>
		<integer>412</integer>
		<key>u1D570</key>
		<integer>400</integer>
		<key>u1D571</key>
		<integer>380</integer>
		<key>u1D572</key>
		<integer>390</integer>
		<key>u1D573</key>
		<integer>420</integer>
		<key>u1D574</key>
		<integer>400</integer>
		<key>u1D575</key>
		<integer>400</integer>
		<key>u1D576</key>
		<integer>420</integer>
		<key>u1D577</key>
		<integer>330</integer>
		<key>u1D578</key>
		<integer>530</integer>
		<key>u1D579</key>
		<integer>430</integer>
		<key>u1D57A</key>
		<integer>410</integer>
		<key>u1D57B</key>
		<integer>420</integer>
		<key>u1D57C</key>
		<integer>410</integer>
		<key>u1D57D</key>
		<integer>430</integer>
		<key>u1D57E</key>
		<integer>400</integer>
		<key>u1D57F</key>
		<integer>380</integer>
		<key>u1D580</key>
		<integer>415</integer>
		<key>u1D581</key>
		<integer>430</integer>
		<key>u1D582</key>
		<integer>580</integer>
		<key>u1D583</key>
		<integer>400</integer>
		<key>u1D584</key>
		<integer>430</integer>
		<key>u1D585</key>
		<integer>380</integer>
		<key>u1D586</key>
		<integer>295</integer>
		<key>u1D587</key>
		<integer>270</integer>
		<key>u1D588</key>
		<integer>250</integer>
		<key>u1D589</key>
		<integer>300</integer>
		<key>u1D58A</key>
		<integer>240</integer>
		<key>u1D58B</key>
		<integer>220</integer>
		<key>u1D58C</key>
		<integer>320</integer>
		<key>u1D58D</key>
		<integer>300</integer>
		<key>u1D58E</key>
		<integer>210</integer>
		<key>u1D58F</key>
		<integer>230</integer>
		<key>u1D590</key>
		<integer>230</integer>
		<key>u1D591</key>
		<integer>220</integer>
		<key>u1D592</key>
		<integer>480</integer>
		<key>u1D593</key>
		<integer>350</integer>
		<key>u1D594</key>
		<integer>280</integer>
		<key>u1D595</key>
		<integer>360</integer>
		<key>u1D596</key>
		<integer>300</integer>
		<key>u1D597</key>
		<integer>280</integer>
		<key>u1D598</key>
		<integer>300</integer>
		<key>u1D599</key>
		<integer>200</integer>
		<key>u1D59A</key>
		<integer>340</integer>
		<key>u1D59B</key>
		<integer>300</integer>
		<key>u1D59C</key>
		<integer>450</integer>
		<key>u1D59D</key>
		<integer>270</integer>
		<key>u1D59E</key>
		<integer>320</integer>
		<key>u1D59F</key>
		<integer>240</integer>
		<key>u1D5A0</key>
		<integer>322</integer>
		<key>u1D5A1</key>
		<integer>289</integer>
		<key>u1D5A2</key>
		<integer>371</integer>
		<key>u1D5A3</key>
		<integer>309</integer>
		<key>u1D5A4</key>
		<integer>284</integer>
		<key>u1D5A5</key>
		<integer>293</integer>
		<key>u1D5A6</key>
		<integer>379</integer>
		<key>u1D5A7</key>
		<integer>354</integer>
		<key>u1D5A8</key>
		<integer>152</integer>
		<key>u1D5A9</key>
		<integer>242</integer>
		<key>u1D5AA</key>
		<integer>335</integer>
		<key>u1D5AB</key>
		<integer>150</integer>
		<key>u1D5AC</key>
		<integer>457</integer>
		<key>u1D5AD</key>
		<integer>353</integer>
		<key>u1D5AE</key>
		<integer>375</integer>
		<key>u1D5AF</key>
		<integer>277</integer>
		<key>u1D5B0</key>
		<integer>346</integer>
		<key>u1D5B1</key>
		<integer>269</integer>
		<key>u1D5B2</key>
		<integer>261</integer>
		<key>u1D5B3</key>
		<integer>268</integer>
		<key>u1D5B4</key>
		<integer>353</integer>
		<key>u1D5B5</key>
		<integer>329</integer>
		<key>u1D5B6</key>
		<integer>429</integer>
		<key>u1D5B7</key>
		<integer>331</integer>
		<key>u1D5B8</key>
		<integer>293</integer>
		<key>u1D5B9</key>
		<integer>339</integer>
		<key>u1D5BA</key>
		<integer>222</integer>
		<key>u1D5BB</key>
		<integer>283</integer>
		<key>u1D5BC</key>
		<integer>227</integer>
		<key>u1D5BD</key>
		<integer>240</integer>
		<key>u1D5BE</key>
		<integer>245</integer>
		<key>u1D5BF</key>
		<integer>239</integer>
		<key>u1D5C0</key>
		<integer>220</integer>
		<key>u1D5C1</key>
		<integer>307</integer>
		<key>u1D5C2</key>
		<integer>131</integer>
		<key>u1D5C3</key>
		<integer>201</integer>
		<key>u1D5C4</key>
		<integer>281</integer>
		<key>u1D5C5</key>
		<integer>130</integer>
		<key>u1D5C6</key>
		<integer>388</integer>
		<key>u1D5C7</key>
		<integer>253</integer>
		<key>u1D5C8</key>
		<integer>245</integer>
		<key>u1D5C9</key>
		<integer>279</integer>
		<key>u1D5CA</key>
		<integer>249</integer>
		<key>u1D5CB</key>
		<integer>185</integer>
		<key>u1D5CC</key>
		<integer>208</integer>
		<key>u1D5CD</key>
		<integer>146</integer>
		<key>u1D5CE</key>
		<integer>262</integer>
		<key>u1D5CF</key>
		<integer>263</integer>
		<key>u1D5D0</key>
		<integer>346</integer>
		<key>u1D5D1</key>
		<integer>257</integer>
		<key>u1D5D2</key>
		<integer>294</integer>
		<key>u1D5D3</key>
		<integer>221</integer>
		<key>u1D5D4</key>
		<integer>344</integer>
		<key>u1D5D5</key>
		<integer>302</integer>
		<key>u1D5D6</key>
		<integer>401</integer>
		<key>u1D5D7</key>
		<integer>385</integer>
		<key>u1D5D8</key>
		<integer>300</integer>
		<key>u1D5D9</key>
		<integer>307</integer>
		<key>u1D5DA</key>
		<integer>372</integer>
		<key>u1D5DB</key>
		<integer>384</integer>
		<key>u1D5DC</key>
		<integer>169</integer>
		<key>u1D5DD</key>
		<integer>289</integer>
		<key>u1D5DE</key>
		<integer>355</integer>
		<key>u1D5DF</key>
		<integer>177</integer>
		<key>u1D5E0</key>
		<integer>416</integer>
		<key>u1D5E1</key>
		<integer>365</integer>
		<key>u1D5E2</key>
		<integer>385</integer>
		<key>u1D5E3</key>
		<integer>296</integer>
		<key>u1D5E4</key>
		<integer>346</integer>
		<key>u1D5E5</key>
		<integer>288</integer>
		<key>u1D5E6</key>
		<integer>264</integer>
		<key>u1D5E7</key>
		<integer>288</integer>
		<key>u1D5E8</key>
		<integer>365</integer>
		<key>u1D5E9</key>
		<integer>349</integer>
		<key>u1D5EA</key>
		<integer>459</integer>
		<key>u1D5EB</key>
		<integer>351</integer>
		<key>u1D5EC</key>
		<integer>313</integer>
		<key>u1D5ED</key>
		<integer>339</integer>
		<key>u1D5EE</key>
		<integer>249</integer>
		<key>u1D5EF</key>
		<integer>318</integer>
		<key>u1D5F0</key>
		<integer>301</integer>
		<key>u1D5F1</key>
		<integer>282</integer>
		<key>u1D5F2</key>
		<integer>277</integer>
		<key>u1D5F3</key>
		<integer>300</integer>
		<key>u1D5F4</key>
		<integer>240</integer>
		<key>u1D5F5</key>
		<integer>355</integer>
		<key>u1D5F6</key>
		<integer>156</integer>
		<key>u1D5F7</key>
		<integer>246</integer>
		<key>u1D5F8</key>
		<integer>266</integer>
		<key>u1D5F9</key>
		<integer>152</integer>
		<key>u1D5FA</key>
		<integer>450</integer>
		<key>u1D5FB</key>
		<integer>289</integer>
		<key>u1D5FC</key>
		<integer>297</integer>
		<key>u1D5FD</key>
		<integer>291</integer>
		<key>u1D5FE</key>
		<integer>263</integer>
		<key>u1D5FF</key>
		<integer>249</integer>
		<key>u1D600</key>
		<integer>220</integer>
		<key>u1D601</key>
		<integer>164</integer>
		<key>u1D602</key>
		<integer>324</integer>
		<key>u1D603</key>
		<integer>326</integer>
		<key>u1D604</key>
		<integer>413</integer>
		<key>u1D605</key>
		<integer>263</integer>
		<key>u1D606</key>
		<integer>267</integer>
		<key>u1D607</key>
		<integer>221</integer>
		<key>u1D608</key>
		<integer>473</integer>
		<key>u1D609</key>
		<integer>357</integer>
		<key>u1D60A</key>
		<integer>438</integer>
		<key>u1D60B</key>
		<integer>383</integer>
		<key>u1D60C</key>
		<integer>362</integer>
		<key>u1D60D</key>
		<integer>367</integer>
		<key>u1D60E</key>
		<integer>437</integer>
		<key>u1D60F</key>
		<integer>426</integer>
		<key>u1D610</key>
		<integer>218</integer>
		<key>u1D611</key>
		<integer>426</integer>
		<key>u1D612</key>
		<integer>411</integer>
		<key>u1D613</key>
		<integer>228</integer>
		<key>u1D614</key>
		<integer>538</integer>
		<key>u1D615</key>
		<integer>447</integer>
		<key>u1D616</key>
		<integer>439</integer>
		<key>u1D617</key>
		<integer>356</integer>
		<key>u1D618</key>
		<integer>420</integer>
		<key>u1D619</key>
		<integer>349</integer>
		<key>u1D61A</key>
		<integer>382</integer>
		<key>u1D61B</key>
		<integer>303</integer>
		<key>u1D61C</key>
		<integer>391</integer>
		<key>u1D61D</key>
		<integer>348</integer>
		<key>u1D61E</key>
		<integer>446</integer>
		<key>u1D61F</key>
		<integer>400</integer>
		<key>u1D620</key>
		<integer>312</integer>
		<key>u1D621</key>
		<integer>467</integer>
		<key>u1D622</key>
		<integer>273</integer>
		<key>u1D623</key>
		<integer>248</integer>
		<key>u1D624</key>
		<integer>175</integer>
		<key>u1D625</key>
		<integer>302</integer>
		<key>u1D626</key>
		<integer>275</integer>
		<key>u1D627</key>
		<integer>505</integer>
		<key>u1D628</key>
		<integer>362</integer>
		<key>u1D629</key>
		<integer>285</integer>
		<key>u1D62A</key>
		<integer>191</integer>
		<key>u1D62B</key>
		<integer>359</integer>
		<key>u1D62C</key>
		<integer>336</integer>
		<key>u1D62D</key>
		<integer>191</integer>
		<key>u1D62E</key>
		<integer>401</integer>
		<key>u1D62F</key>
		<integer>300</integer>
		<key>u1D630</key>
		<integer>267</integer>
		<key>u1D631</key>
		<integer>324</integer>
		<key>u1D632</key>
		<integer>301</integer>
		<key>u1D633</key>
		<integer>258</integer>
		<key>u1D634</key>
		<integer>246</integer>
		<key>u1D635</key>
		<integer>167</integer>
		<key>u1D636</key>
		<integer>284</integer>
		<key>u1D637</key>
		<integer>199</integer>
		<key>u1D638</key>
		<integer>322</integer>
		<key>u1D639</key>
		<integer>308</integer>
		<key>u1D63A</key>
		<integer>285</integer>
		<key>u1D63B</key>
		<integer>338</integer>
		<key>u1D6A4</key>
		<integer>121</integer>
		<key>u1D6A5</key>
		<integer>219</integer>
		<key>u1D6A8</key>
		<integer>354</integer>
		<key>u1D6A9</key>
		<integer>310</integer>
		<key>u1D6AA</key>
		<integer>255</integer>
		<key>u1D6AB</key>
		<integer>346</integer>
		<key>u1D6AC</key>
		<integer>281</integer>
		<key>u1D6AD</key>
		<integer>319</integer>
		<key>u1D6AE</key>
		<integer>361</integer>
		<key>u1D6AF</key>
		<integer>365</integer>
		<key>u1D6B0</key>
		<integer>159</integer>
		<key>u1D6B1</key>
		<integer>330</integer>
		<key>u1D6B2</key>
		<integer>318</integer>
		<key>u1D6B3</key>
		<integer>426</integer>
		<key>u1D6B4</key>
		<integer>355</integer>
		<key>u1D6B5</key>
		<integer>324</integer>
		<key>u1D6B6</key>
		<integer>365</integer>
		<key>u1D6B7</key>
		<integer>375</integer>
		<key>u1D6B8</key>
		<integer>275</integer>
		<key>u1D6B9</key>
		<integer>365</integer>
		<key>u1D6BA</key>
		<integer>307</integer>
		<key>u1D6BB</key>
		<integer>296</integer>
		<key>u1D6BC</key>
		<integer>315</integer>
		<key>u1D6BD</key>
		<integer>354</integer>
		<key>u1D6BE</key>
		<integer>363</integer>
		<key>u1D6BF</key>
		<integer>374</integer>
		<key>u1D6C0</key>
		<integer>364</integer>
		<key>u1D6C2</key>
		<integer>319</integer>
		<key>u1D6C3</key>
		<integer>271</integer>
		<key>u1D6C4</key>
		<integer>319</integer>
		<key>u1D6C5</key>
		<integer>270</integer>
		<key>u1D6C6</key>
		<integer>237</integer>
		<key>u1D6C7</key>
		<integer>248</integer>
		<key>u1D6C8</key>
		<integer>280</integer>
		<key>u1D6C9</key>
		<integer>249</integer>
		<key>u1D6CA</key>
		<integer>164</integer>
		<key>u1D6CB</key>
		<integer>265</integer>
		<key>u1D6CC</key>
		<integer>161</integer>
		<key>u1D6CD</key>
		<integer>264</integer>
		<key>u1D6CE</key>
		<integer>258</integer>
		<key>u1D6CF</key>
		<integer>230</integer>
		<key>u1D6D0</key>
		<integer>259</integer>
		<key>u1D6D1</key>
		<integer>303</integer>
		<key>u1D6D2</key>
		<integer>280</integer>
		<key>u1D6D3</key>
		<integer>239</integer>
		<key>u1D6D4</key>
		<integer>297</integer>
		<key>u1D6D5</key>
		<integer>231</integer>
		<key>u1D6D6</key>
		<integer>265</integer>
		<key>u1D6D7</key>
		<integer>323</integer>
		<key>u1D6D8</key>
		<integer>274</integer>
		<key>u1D6D9</key>
		<integer>343</integer>
		<key>u1D6DA</key>
		<integer>368</integer>
		<key>u1D6DC</key>
		<integer>223</integer>
		<key>u1D6DD</key>
		<integer>242</integer>
		<key>u1D6DE</key>
		<integer>266</integer>
		<key>u1D6DF</key>
		<integer>302</integer>
		<key>u1D6E0</key>
		<integer>244</integer>
		<key>u1D6E1</key>
		<integer>394</integer>
		<key>u1D6E2</key>
		<integer>492</integer>
		<key>u1D6E3</key>
		<integer>421</integer>
		<key>u1D6E4</key>
		<integer>423</integer>
		<key>u1D6E5</key>
		<integer>433</integer>
		<key>u1D6E6</key>
		<integer>392</integer>
		<key>u1D6E7</key>
		<integer>399</integer>
		<key>u1D6E8</key>
		<integer>452</integer>
		<key>u1D6E9</key>
		<integer>388</integer>
		<key>u1D6EA</key>
		<integer>287</integer>
		<key>u1D6EB</key>
		<integer>437</integer>
		<key>u1D6EC</key>
		<integer>449</integer>
		<key>u1D6ED</key>
		<integer>531</integer>
		<key>u1D6EE</key>
		<integer>454</integer>
		<key>u1D6EF</key>
		<integer>389</integer>
		<key>u1D6F0</key>
		<integer>418</integer>
		<key>u1D6F1</key>
		<integer>468</integer>
		<key>u1D6F2</key>
		<integer>400</integer>
		<key>u1D6F3</key>
		<integer>399</integer>
		<key>u1D6F4</key>
		<integer>413</integer>
		<key>u1D6F5</key>
		<integer>336</integer>
		<key>u1D6F6</key>
		<integer>303</integer>
		<key>u1D6F7</key>
		<integer>383</integer>
		<key>u1D6F8</key>
		<integer>469</integer>
		<key>u1D6F9</key>
		<integer>355</integer>
		<key>u1D6FA</key>
		<integer>439</integer>
		<key>u1D6FB</key>
		<integer>285</integer>
		<key>u1D6FC</key>
		<integer>287</integer>
		<key>u1D6FD</key>
		<integer>427</integer>
		<key>u1D6FE</key>
		<integer>254</integer>
		<key>u1D6FF</key>
		<integer>297</integer>
		<key>u1D700</key>
		<integer>260</integer>
		<key>u1D701</key>
		<integer>294</integer>
		<key>u1D702</key>
		<integer>270</integer>
		<key>u1D703</key>
		<integer>315</integer>
		<key>u1D704</key>
		<integer>141</integer>
		<key>u1D705</key>
		<integer>273</integer>
		<key>u1D706</key>
		<integer>286</integer>
		<key>u1D707</key>
		<integer>338</integer>
		<key>u1D708</key>
		<integer>245</integer>
		<key>u1D709</key>
		<integer>288</integer>
		<key>u1D70A</key>
		<integer>271</integer>
		<key>u1D70B</key>
		<integer>326</integer>
		<key>u1D70C</key>
		<integer>377</integer>
		<key>u1D70D</key>
		<integer>262</integer>
		<key>u1D70E</key>
		<integer>264</integer>
		<key>u1D70F</key>
		<integer>225</integer>
		<key>u1D710</key>
		<integer>241</integer>
		<key>u1D711</key>
		<integer>324</integer>
		<key>u1D712</key>
		<integer>394</integer>
		<key>u1D713</key>
		<integer>383</integer>
		<key>u1D714</key>
		<integer>379</integer>
		<key>u1D715</key>
		<integer>307</integer>
		<key>u1D716</key>
		<integer>287</integer>
		<key>u1D717</key>
		<integer>313</integer>
		<key>u1D718</key>
		<integer>305</integer>
		<key>u1D719</key>
		<integer>385</integer>
		<key>u1D71A</key>
		<integer>297</integer>
		<key>u1D71B</key>
		<integer>371</integer>
		<key>u1D71C</key>
		<integer>501</integer>
		<key>u1D71D</key>
		<integer>410</integer>
		<key>u1D71E</key>
		<integer>390</integer>
		<key>u1D71F</key>
		<integer>447</integer>
		<key>u1D720</key>
		<integer>398</integer>
		<key>u1D721</key>
		<integer>447</integer>
		<key>u1D722</key>
		<integer>460</integer>
		<key>u1D723</key>
		<integer>416</integer>
		<key>u1D724</key>
		<integer>273</integer>
		<key>u1D725</key>
		<integer>443</integer>
		<key>u1D726</key>
		<integer>448</integer>
		<key>u1D727</key>
		<integer>576</integer>
		<key>u1D728</key>
		<integer>453</integer>
		<key>u1D729</key>
		<integer>404</integer>
		<key>u1D72A</key>
		<integer>398</integer>
		<key>u1D72B</key>
		<integer>466</integer>
		<key>u1D72C</key>
		<integer>416</integer>
		<key>u1D72D</key>
		<integer>416</integer>
		<key>u1D72E</key>
		<integer>447</integer>
		<key>u1D72F</key>
		<integer>310</integer>
		<key>u1D730</key>
		<integer>346</integer>
		<key>u1D731</key>
		<integer>400</integer>
		<key>u1D732</key>
		<integer>487</integer>
		<key>u1D733</key>
		<integer>376</integer>
		<key>u1D734</key>
		<integer>457</integer>
		<key>u1D736</key>
		<integer>312</integer>
		<key>u1D737</key>
		<integer>435</integer>
		<key>u1D738</key>
		<integer>346</integer>
		<key>u1D739</key>
		<integer>362</integer>
		<key>u1D73A</key>
		<integer>292</integer>
		<key>u1D73B</key>
		<integer>311</integer>
		<key>u1D73C</key>
		<integer>323</integer>
		<key>u1D73D</key>
		<integer>339</integer>
		<key>u1D73E</key>
		<integer>154</integer>
		<key>u1D73F</key>
		<integer>320</integer>
		<key>u1D740</key>
		<integer>328</integer>
		<key>u1D741</key>
		<integer>350</integer>
		<key>u1D742</key>
		<integer>312</integer>
		<key>u1D743</key>
		<integer>312</integer>
		<key>u1D744</key>
		<integer>292</integer>
		<key>u1D745</key>
		<integer>382</integer>
		<key>u1D746</key>
		<integer>424</integer>
		<key>u1D747</key>
		<integer>347</integer>
		<key>u1D748</key>
		<integer>360</integer>
		<key>u1D749</key>
		<integer>261</integer>
		<key>u1D74A</key>
		<integer>265</integer>
		<key>u1D74B</key>
		<integer>365</integer>
		<key>u1D74C</key>
		<integer>405</integer>
		<key>u1D74D</key>
		<integer>389</integer>
		<key>u1D74E</key>
		<integer>413</integer>
		<key>u1D74F</key>
		<integer>318</integer>
		<key>u1D750</key>
		<integer>275</integer>
		<key>u1D751</key>
		<integer>378</integer>
		<key>u1D752</key>
		<integer>319</integer>
		<key>u1D753</key>
		<integer>398</integer>
		<key>u1D754</key>
		<integer>320</integer>
		<key>u1D755</key>
		<integer>390</integer>
		<key>u1D756</key>
		<integer>344</integer>
		<key>u1D757</key>
		<integer>302</integer>
		<key>u1D758</key>
		<integer>349</integer>
		<key>u1D759</key>
		<integer>348</integer>
		<key>u1D75A</key>
		<integer>300</integer>
		<key>u1D75B</key>
		<integer>339</integer>
		<key>u1D75C</key>
		<integer>384</integer>
		<key>u1D75D</key>
		<integer>399</integer>
		<key>u1D75E</key>
		<integer>169</integer>
		<key>u1D75F</key>
		<integer>355</integer>
		<key>u1D760</key>
		<integer>297</integer>
		<key>u1D761</key>
		<integer>416</integer>
		<key>u1D762</key>
		<integer>365</integer>
		<key>u1D763</key>
		<integer>324</integer>
		<key>u1D764</key>
		<integer>385</integer>
		<key>u1D765</key>
		<integer>392</integer>
		<key>u1D766</key>
		<integer>296</integer>
		<key>u1D767</key>
		<integer>399</integer>
		<key>u1D768</key>
		<integer>295</integer>
		<key>u1D769</key>
		<integer>302</integer>
		<key>u1D76A</key>
		<integer>347</integer>
		<key>u1D76B</key>
		<integer>364</integer>
		<key>u1D76C</key>
		<integer>351</integer>
		<key>u1D76D</key>
		<integer>365</integer>
		<key>u1D76E</key>
		<integer>383</integer>
		<key>u1D770</key>
		<integer>324</integer>
		<key>u1D771</key>
		<integer>291</integer>
		<key>u1D772</key>
		<integer>314</integer>
		<key>u1D773</key>
		<integer>270</integer>
		<key>u1D774</key>
		<integer>233</integer>
		<key>u1D775</key>
		<integer>248</integer>
		<key>u1D776</key>
		<integer>310</integer>
		<key>u1D777</key>
		<integer>284</integer>
		<key>u1D778</key>
		<integer>167</integer>
		<key>u1D779</key>
		<integer>297</integer>
		<key>u1D77A</key>
		<integer>132</integer>
		<key>u1D77B</key>
		<integer>299</integer>
		<key>u1D77C</key>
		<integer>262</integer>
		<key>u1D77D</key>
		<integer>242</integer>
		<key>u1D77E</key>
		<integer>283</integer>
		<key>u1D77F</key>
		<integer>296</integer>
		<key>u1D780</key>
		<integer>280</integer>
		<key>u1D781</key>
		<integer>220</integer>
		<key>u1D782</key>
		<integer>285</integer>
		<key>u1D783</key>
		<integer>220</integer>
		<key>u1D784</key>
		<integer>261</integer>
		<key>u1D785</key>
		<integer>338</integer>
		<key>u1D786</key>
		<integer>282</integer>
		<key>u1D787</key>
		<integer>364</integer>
		<key>u1D788</key>
		<integer>380</integer>
		<key>u1D78B</key>
		<integer>309</integer>
		<key>u1D7CA</key>
		<integer>320</integer>
		<key>u1D7CB</key>
		<integer>234</integer>
		<key>uni0302</key>
		<integer>-138</integer>
		<key>uni0302.size1</key>
		<integer>286</integer>
		<key>uni0302.size2</key>
		<integer>501</integer>
		<key>uni0302.size3</key>
		<integer>748</integer>
		<key>uni0302.size4</key>
		<integer>966</integer>
		<key>uni0302.size5</key>
		<integer>1192</integer>
		<key>uni0304</key>
		<integer>-206</integer>
		<key>uni0305</key>
		<integer>-205</integer>
		<key>uni0306</key>
		<integer>-161</integer>
		<key>uni0307</key>
		<integer>-180</integer>
		<key>uni0308</key>
		<integer>-210</integer>
		<key>uni030A</key>
		<integer>-184</integer>
		<key>uni030B</key>
		<integer>-219</integer>
		<key>uni030C</key>
		<integer>-205</integer>
		<key>uni030C.size1</key>
		<integer>286</integer>
		<key>uni030C.size2</key>
		<integer>501</integer>
		<key>uni030C.size3</key>
		<integer>748</integer>
		<key>uni030C.size4</key>
		<integer>966</integer>
		<key>uni030C.size5</key>
		<integer>1192</integer>
		<key>uni030D</key>
		<integer>-155</integer>
		<key>uni030E</key>
		<integer>-245</integer>
		<key>uni030F</key>
		<integer>-140</integer>
		<key>uni0310</key>
		<integer>-251</integer>
		<key>uni0311</key>
		<integer>-205</integer>
		<key>uni0312</key>
		<integer>-110</integer>
		<key>uni0313</key>
		<integer>-96</integer>
		<key>uni0314</key>
		<integer>-76</integer>
		<key>uni0315</key>
		<integer>-116</integer>
		<key>uni0316</key>
		<integer>-219</integer>
		<key>uni0317</key>
		<integer>-210</integer>
		<key>uni0318</key>
		<integer>-224</integer>
		<key>uni0319</key>
		<integer>-285</integer>
		<key>uni031A</key>
		<integer>18</integer>
		<key>uni031B</key>
		<integer>-130</integer>
		<key>uni031C</key>
		<integer>-216</integer>
		<key>uni031D</key>
		<integer>-249</integer>
		<key>uni031E</key>
		<integer>-247</integer>
		<key>uni031F</key>
		<integer>-249</integer>
		<key>uni0320</key>
		<integer>-253</integer>
		<key>uni0321</key>
		<integer>-56</integer>
		<key>uni0322</key>
		<integer>-30</integer>
		<key>uni0324</key>
		<integer>-218</integer>
		<key>uni0325</key>
		<integer>-218</integer>
		<key>uni0326</key>
		<integer>-255</integer>
		<key>uni0327</key>
		<integer>-182</integer>
		<key>uni0328</key>
		<integer>-182</integer>
		<key>uni0329</key>
		<integer>-257</integer>
		<key>uni032A</key>
		<integer>-253</integer>
		<key>uni032B</key>
		<integer>-251</integer>
		<key>uni032C</key>
		<integer>-204</integer>
		<key>uni032D</key>
		<integer>-195</integer>
		<key>uni032E</key>
		<integer>-251</integer>
		<key>uni032F</key>
		<integer>-250</integer>
		<key>uni0330</key>
		<integer>-213</integer>
		<key>uni0331</key>
		<integer>-244</integer>
		<key>uni0332</key>
		<integer>-244</integer>
		<key>uni0333</key>
		<integer>-244</integer>
		<key>uni0334</key>
		<integer>-200</integer>
		<key>uni0335</key>
		<integer>-200</integer>
		<key>uni0336</key>
		<integer>-240</integer>
		<key>uni0337</key>
		<integer>-243</integer>
		<key>uni0338</key>
		<integer>-379</integer>
		<key>uni0339</key>
		<integer>-216</integer>
		<key>uni033A</key>
		<integer>-253</integer>
		<key>uni033B</key>
		<integer>-246</integer>
		<key>uni033C</key>
		<integer>-251</integer>
		<key>uni033D</key>
		<integer>-265</integer>
		<key>uni033E</key>
		<integer>-265</integer>
		<key>uni033F</key>
		<integer>-235</integer>
		<key>uni0340</key>
		<integer>-142</integer>
		<key>uni0341</key>
		<integer>-191</integer>
		<key>uni0342</key>
		<integer>-229</integer>
		<key>uni0343</key>
		<integer>-96</integer>
		<key>uni0345</key>
		<integer>-182</integer>
		<key>uni0346</key>
		<integer>-251</integer>
		<key>uni0347</key>
		<integer>-244</integer>
		<key>uni0348</key>
		<integer>-197</integer>
		<key>uni0349</key>
		<integer>-192</integer>
		<key>uni034A</key>
		<integer>-215</integer>
		<key>uni034B</key>
		<integer>-205</integer>
		<key>uni034C</key>
		<integer>-235</integer>
		<key>uni034D</key>
		<integer>320</integer>
		<key>uni034E</key>
		<integer>-227</integer>
		<key>uni0350</key>
		<integer>-201</integer>
		<key>uni0351</key>
		<integer>-171</integer>
		<key>uni0352</key>
		<integer>-251</integer>
		<key>uni0353</key>
		<integer>-260</integer>
		<key>uni0354</key>
		<integer>-172</integer>
		<key>uni0355</key>
		<integer>-196</integer>
		<key>uni0356</key>
		<integer>-316</integer>
		<key>uni0357</key>
		<integer>-171</integer>
		<key>uni0358</key>
		<integer>-66</integer>
		<key>uni0359</key>
		<integer>-125</integer>
		<key>uni035A</key>
		<integer>-169</integer>
		<key>uni035B</key>
		<integer>-164</integer>
		<key>uni0362</key>
		<integer>-232</integer>
		<key>uni0363</key>
		<integer>-171</integer>
		<key>uni0364</key>
		<integer>-244</integer>
		<key>uni0365</key>
		<integer>-225</integer>
		<key>uni0366</key>
		<integer>-161</integer>
		<key>uni0367</key>
		<integer>-153</integer>
		<key>uni0368</key>
		<integer>-160</integer>
		<key>uni0369</key>
		<integer>-191</integer>
		<key>uni036A</key>
		<integer>-221</integer>
		<key>uni036B</key>
		<integer>-194</integer>
		<key>uni036C</key>
		<integer>-201</integer>
		<key>uni036D</key>
		<integer>-197</integer>
		<key>uni036E</key>
		<integer>-235</integer>
		<key>uni036F</key>
		<integer>-228</integer>
		<key>uni0394</key>
		<integer>318</integer>
		<key>uni03A9</key>
		<integer>348</integer>
		<key>uni03BC</key>
		<integer>245</integer>
		<key>uni03D0</key>
		<integer>334</integer>
		<key>uni03DC</key>
		<integer>249</integer>
		<key>uni03DD</key>
		<integer>222</integer>
		<key>uni03F0</key>
		<integer>249</integer>
		<key>uni03F1</key>
		<integer>236</integer>
		<key>uni03F4</key>
		<integer>341</integer>
		<key>uni03F5</key>
		<integer>240</integer>
		<key>uni03F6</key>
		<integer>190</integer>
		<key>uni20D6</key>
		<integer>210</integer>
		<key>uni20D7</key>
		<integer>210</integer>
		<key>uni20DB</key>
		<integer>-294</integer>
		<key>uni20DC</key>
		<integer>-378</integer>
		<key>uni20E1</key>
		<integer>320</integer>
		<key>uni20EE</key>
		<integer>210</integer>
		<key>uni20EF</key>
		<integer>210</integer>
		<key>uni210A</key>
		<integer>491</integer>
		<key>uni210B</key>
		<integer>700</integer>
		<key>uni210C</key>
		<integer>440</integer>
		<key>uni2110</key>
		<integer>647</integer>
		<key>uni2112</key>
		<integer>850</integer>
		<key>uni211B</key>
		<integer>770</integer>
		<key>uni2128</key>
		<integer>360</integer>
		<key>uni212C</key>
		<integer>808</integer>
		<key>uni212D</key>
		<integer>350</integer>
		<key>uni212F</key>
		<integer>463</integer>
		<key>uni2130</key>
		<integer>633</integer>
		<key>uni2131</key>
		<integer>658</integer>
		<key>uni2133</key>
		<integer>867</integer>
		<key>uni23DE</key>
		<integer>310</integer>
		<key>uni23DF</key>
		<integer>310</integer>
		<key>upsilon</key>
		<integer>261</integer>
		<key>v</key>
		<integer>256</integer>
		<key>w</key>
		<integer>345</integer>
		<key>x</key>
		<integer>256</integer>
		<key>xi</key>
		<integer>213</integer>
		<key>y</key>
		<integer>262</integer>
		<key>z</key>
		<integer>220</integer>
		<key>zeta</key>
		<integer>220</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>440</integer>
		<key>AxisHeight</key>
		<integer>260</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1500</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1250</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>640</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>198</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>700</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>65</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>480</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>198</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>580</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>65</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>480</integer>
		<key>FractionRuleThickness</key>
		<integer>65</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>620</integer>
		<key>LowerLimitGapMin</key>
		<integer>150</integer>
		<key>MathLeading</key>
		<integer>150</integer>
		<key>MinConnectorOverlap</key>
		<integer>100</integer>
		<key>OverbarExtraAscender</key>
		<integer>40</integer>
		<key>OverbarRuleThickness</key>
		<integer>40</integer>
		<key>OverbarVerticalGap</key>
		<integer>120</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>50</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>170</integer>
		<key>RadicalExtraAscender</key>
		<integer>65</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-380</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>85</integer>
		<key>RadicalRuleThickness</key>
		<integer>40</integer>
		<key>RadicalVerticalGap</key>
		<integer>90</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>80</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>60</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>300</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>65</integer>
		<key>SpaceAfterScript</key>
		<integer>43</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>700</integer>
		<key>StackBottomShiftDown</key>
		<integer>380</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>330</integer>
		<key>StackGapMin</key>
		<integer>200</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>750</integer>
		<key>StackTopShiftUp</key>
		<integer>470</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>600</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>65</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>65</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>800</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>150</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>45</integer>
		<key>SubscriptShiftDown</key>
		<integer>210</integer>
		<key>SubscriptTopMax</key>
		<integer>360</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>230</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>380</integer>
		<key>SuperscriptBottomMin</key>
		<integer>120</integer>
		<key>SuperscriptShiftUp</key>
		<integer>375</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>310</integer>
		<key>UnderbarExtraDescender</key>
		<integer>40</integer>
		<key>UnderbarRuleThickness</key>
		<integer>40</integer>
		<key>UnderbarVerticalGap</key>
		<integer>120</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>250</integer>
		<key>UpperLimitGapMin</key>
		<integer>150</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>arrowboth</key>
		<array>
			<string>arrowboth</string>
		</array>
		<key>arrowleft</key>
		<array>
			<string>arrowleft</string>
		</array>
		<key>arrowright</key>
		<array>
			<string>arrowright</string>
		</array>
		<key>tildecomb</key>
		<array>
			<string>tildecomb</string>
			<string>tildecomb.size1</string>
			<string>tildecomb.size2</string>
			<string>tildecomb.size3</string>
			<string>tildecomb.size4</string>
			<string>tildecomb.size5</string>
		</array>
		<key>uni0302</key>
		<array>
			<string>uni0302</string>
			<string>uni0302.size1</string>
			<string>uni0302.size2</string>
			<string>uni0302.size3</string>
			<string>uni0302.size4</string>
			<string>uni0302.size5</string>
		</array>
		<key>uni0305</key>
		<array>
			<string>uni0305</string>
			<string>uni0305.size1</string>
			<string>uni0305.size2</string>
		</array>
		<key>uni0306</key>
		<array>
			<string>uni0306</string>
			<string>uni0306.size1</string>
			<string>uni0306.size2</string>
			<string>uni0306.size3</string>
			<string>uni0306.size4</string>
			<string>uni0306.size5</string>
		</array>
		<key>uni030C</key>
		<array>
			<string>uni030C</string>
			<string>uni030C.size1</string>
			<string>uni030C.size2</string>
			<string>uni030C.size3</string>
			<string>uni030C.size4</string>
			<string>uni030C.size5</string>
		</array>
		<key>uni0330</key>
		<array>
			<string>uni0330</string>
			<string>uni0330.size1</string>
			<string>uni0330.size2</string>
			<string>uni0330.size3</string>
			<string>uni0330.size3</string>
			<string>uni0330.size4</string>
		</array>
		<key>uni0332</key>
		<array>
			<string>uni0332</string>
			<string>uni0332.size1</string>
			<string>uni0332.size2</string>
		</array>
		<key>uni034D</key>
		<array>
			<string>uni034D</string>
		</array>
		<key>uni20D0</key>
		<array>
			<string>uni20D0</string>
		</array>
		<key>uni20D1</key>
		<array>
			<string>uni20D1</string>
		</array>
		<key>uni20D6</key>
		<array>
			<string>uni20D6</string>
		</array>
		<key>uni20D7</key>
		<array>
			<string>uni20D7</string>
		</array>
		<key>uni20E1</key>
		<array>
			<string>uni20E1</string>
		</array>
		<key>uni20EE</key>
		<array>
			<string>uni20EE</string>
		</array>
		<key>uni20EF</key>
		<array>
			<string>uni20EF</string>
		</array>
		<key>uni21A6</key>
		<array>
			<string>uni21A6</string>
		</array>
		<key>uni21A9</key>
		<array>
			<string>uni21A9</string>
		</array>
		<key>uni21AA</key>
		<array>
			<string>uni21AA</string>
		</array>
		<key>uni23B4</key>
		<array>
			<string>uni23B4</string>
			<string>uni23B4.size1</string>
			<string>uni23B4.size2</string>
			<string>uni23B4.size3</string>
			<string>uni23B4.size4</string>
			<string>uni23B4.size5</string>
		</array>
		<key>uni23B5</key>
		<array>
			<string>uni23B5</string>
			<string>uni23B5.size1</string>
			<string>uni23B5.size2</string>
			<string>uni23B5.size3</string>
			<string>uni23B5.size4</string>
			<string>uni23B5.size5</string>
		</array>
		<key>uni23DC</key>
		<array>
			<string>uni23DC</string>
			<string>uni23DC.size1</string>
			<string>uni23DC.size2</string>
			<string>uni23DC.size3</string>
			<string>uni23DC.size4</string>
			<string>uni23DC.size5</string>
		</array>
		<key>uni23DD</key>
		<array>
			<string>uni23DD</string>
			<string>uni23DD.size1</string>
			<string>uni23DD.size2</string>
			<string>uni23DD.size3</string>
			<string>uni23DD.size4</string>
			<string>uni23DD.size5</string>
		</array>
		<key>uni23DE</key>
		<array>
			<string>uni23DE</string>
			<string>uni23DE.size1</string>
			<string>uni23DE.size2</string>
			<string>uni23DE.size3</string>
			<string>uni23DE.size4</string>
			<string>uni23DE.size5</string>
		</array>
		<key>uni23DF</key>
		<array>
			<string>uni23DF</string>
			<string>uni23DF.size1</string>
			<string>uni23DF.size2</string>
			<string>uni23DF.size3</string>
			<string>uni23DF.size4</string>
			<string>uni23DF.size5</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>f</key>
		<integer>42</integer>
		<key>integral</key>
		<integer>150</integer>
		<key>integral.size1</key>
		<integer>250</integer>
		<key>integral.sl</key>
		<integer>300</integer>
		<key>integral.slsize1</key>
		<integer>650</integer>
		<key>u1D411</key>
		<integer>29</integer>
		<key>u1D41F</key>
		<integer>71</integer>
		<key>u1D435</key>
		<integer>24</integer>
		<key>u1D436</key>
		<integer>74</integer>
		<key>u1D437</key>
		<integer>46</integer>
		<key>u1D438</key>
		<integer>47</integer>
		<key>u1D439</key>
		<integer>116</integer>
		<key>u1D43A</key>
		<integer>62</integer>
		<key>u1D43B</key>
		<integer>124</integer>
		<key>u1D43C</key>
		<integer>127</integer>
		<key>u1D43D</key>
		<integer>126</integer>
		<key>u1D43E</key>
		<integer>105</integer>
		<key>u1D440</key>
		<integer>90</integer>
		<key>u1D441</key>
		<integer>142</integer>
		<key>u1D442</key>
		<integer>58</integer>
		<key>u1D443</key>
		<integer>95</integer>
		<key>u1D444</key>
		<integer>58</integer>
		<key>u1D445</key>
		<integer>10</integer>
		<key>u1D446</key>
		<integer>59</integer>
		<key>u1D447</key>
		<integer>132</integer>
		<key>u1D448</key>
		<integer>146</integer>
		<key>u1D449</key>
		<integer>148</integer>
		<key>u1D44A</key>
		<integer>151</integer>
		<key>u1D44B</key>
		<integer>124</integer>
		<key>u1D44C</key>
		<integer>131</integer>
		<key>u1D44D</key>
		<integer>115</integer>
		<key>u1D44E</key>
		<integer>26</integer>
		<key>u1D44F</key>
		<integer>34</integer>
		<key>u1D450</key>
		<integer>54</integer>
		<key>u1D451</key>
		<integer>78</integer>
		<key>u1D452</key>
		<integer>43</integer>
		<key>u1D453</key>
		<integer>168</integer>
		<key>u1D454</key>
		<integer>59</integer>
		<key>u1D457</key>
		<integer>63</integer>
		<key>u1D458</key>
		<integer>54</integer>
		<key>u1D459</key>
		<integer>65</integer>
		<key>u1D45A</key>
		<integer>23</integer>
		<key>u1D45B</key>
		<integer>22</integer>
		<key>u1D45C</key>
		<integer>38</integer>
		<key>u1D45D</key>
		<integer>40</integer>
		<key>u1D45E</key>
		<integer>36</integer>
		<key>u1D45F</key>
		<integer>92</integer>
		<key>u1D460</key>
		<integer>44</integer>
		<key>u1D461</key>
		<integer>76</integer>
		<key>u1D462</key>
		<integer>36</integer>
		<key>u1D463</key>
		<integer>97</integer>
		<key>u1D463.alt</key>
		<integer>51</integer>
		<key>u1D464</key>
		<integer>97</integer>
		<key>u1D465</key>
		<integer>81</integer>
		<key>u1D466</key>
		<integer>96</integer>
		<key>u1D467</key>
		<integer>51</integer>
		<key>u1D46A</key>
		<integer>57</integer>
		<key>u1D46B</key>
		<integer>45</integer>
		<key>u1D46C</key>
		<integer>69</integer>
		<key>u1D46D</key>
		<integer>114</integer>
		<key>u1D46E</key>
		<integer>57</integer>
		<key>u1D46F</key>
		<integer>116</integer>
		<key>u1D470</key>
		<integer>128</integer>
		<key>u1D471</key>
		<integer>139</integer>
		<key>u1D472</key>
		<integer>117</integer>
		<key>u1D473</key>
		<integer>20</integer>
		<key>u1D474</key>
		<integer>67</integer>
		<key>u1D475</key>
		<integer>130</integer>
		<key>u1D476</key>
		<integer>59</integer>
		<key>u1D477</key>
		<integer>92</integer>
		<key>u1D478</key>
		<integer>59</integer>
		<key>u1D479</key>
		<integer>10</integer>
		<key>u1D47A</key>
		<integer>43</integer>
		<key>u1D47B</key>
		<integer>112</integer>
		<key>u1D47C</key>
		<integer>144</integer>
		<key>u1D47D</key>
		<integer>135</integer>
		<key>u1D47E</key>
		<integer>116</integer>
		<key>u1D47F</key>
		<integer>111</integer>
		<key>u1D480</key>
		<integer>134</integer>
		<key>u1D481</key>
		<integer>102</integer>
		<key>u1D482</key>
		<integer>37</integer>
		<key>u1D483</key>
		<integer>45</integer>
		<key>u1D484</key>
		<integer>59</integer>
		<key>u1D485</key>
		<integer>66</integer>
		<key>u1D486</key>
		<integer>21</integer>
		<key>u1D487</key>
		<integer>140</integer>
		<key>u1D488</key>
		<integer>75</integer>
		<key>u1D48A</key>
		<integer>27</integer>
		<key>u1D48B</key>
		<integer>20</integer>
		<key>u1D48C</key>
		<integer>46</integer>
		<key>u1D48D</key>
		<integer>52</integer>
		<key>u1D48E</key>
		<integer>26</integer>
		<key>u1D490</key>
		<integer>30</integer>
		<key>u1D491</key>
		<integer>37</integer>
		<key>u1D493</key>
		<integer>78</integer>
		<key>u1D494</key>
		<integer>29</integer>
		<key>u1D495</key>
		<integer>68</integer>
		<key>u1D496</key>
		<integer>29</integer>
		<key>u1D497</key>
		<integer>72</integer>
		<key>u1D498</key>
		<integer>52</integer>
		<key>u1D499</key>
		<integer>70</integer>
		<key>u1D49A</key>
		<integer>59</integer>
		<key>u1D49B</key>
		<integer>52</integer>
		<key>u1D49C</key>
		<integer>125</integer>
		<key>u1D49E</key>
		<integer>145</integer>
		<key>u1D49F</key>
		<integer>125</integer>
		<key>u1D4A2</key>
		<integer>145</integer>
		<key>u1D4A5</key>
		<integer>185</integer>
		<key>u1D4A6</key>
		<integer>125</integer>
		<key>u1D4A9</key>
		<integer>245</integer>
		<key>u1D4AA</key>
		<integer>95</integer>
		<key>u1D4AB</key>
		<integer>245</integer>
		<key>u1D4AC</key>
		<integer>45</integer>
		<key>u1D4AE</key>
		<integer>145</integer>
		<key>u1D4AF</key>
		<integer>245</integer>
		<key>u1D4B0</key>
		<integer>95</integer>
		<key>u1D4B1</key>
		<integer>245</integer>
		<key>u1D4B2</key>
		<integer>245</integer>
		<key>u1D4B3</key>
		<integer>125</integer>
		<key>u1D4B4</key>
		<integer>125</integer>
		<key>u1D4B5</key>
		<integer>125</integer>
		<key>u1D4BB</key>
		<integer>40</integer>
		<key>u1D4BE</key>
		<integer>40</integer>
		<key>u1D4BF</key>
		<integer>40</integer>
		<key>u1D4C1</key>
		<integer>40</integer>
		<key>u1D4C9</key>
		<integer>20</integer>
		<key>u1D4EF</key>
		<integer>40</integer>
		<key>u1D4F2</key>
		<integer>40</integer>
		<key>u1D4F3</key>
		<integer>40</integer>
		<key>u1D4F5</key>
		<integer>40</integer>
		<key>u1D4FD</key>
		<integer>40</integer>
		<key>u1D5B0</key>
		<integer>89</integer>
		<key>u1D5CA.ssty</key>
		<integer>57</integer>
		<key>u1D5E4</key>
		<integer>31</integer>
		<key>u1D5F3</key>
		<integer>70</integer>
		<key>u1D5F3.ssty</key>
		<integer>289</integer>
		<key>u1D60A</key>
		<integer>64</integer>
		<key>u1D60B</key>
		<integer>50</integer>
		<key>u1D60C</key>
		<integer>99</integer>
		<key>u1D60D</key>
		<integer>146</integer>
		<key>u1D60E</key>
		<integer>49</integer>
		<key>u1D60F</key>
		<integer>64</integer>
		<key>u1D610</key>
		<integer>66</integer>
		<key>u1D611</key>
		<integer>70</integer>
		<key>u1D612</key>
		<integer>125</integer>
		<key>u1D614</key>
		<integer>22</integer>
		<key>u1D615</key>
		<integer>107</integer>
		<key>u1D616</key>
		<integer>57</integer>
		<key>u1D617</key>
		<integer>101</integer>
		<key>u1D618</key>
		<integer>82</integer>
		<key>u1D619</key>
		<integer>10</integer>
		<key>u1D61A</key>
		<integer>68</integer>
		<key>u1D61B</key>
		<integer>142</integer>
		<key>u1D61C</key>
		<integer>80</integer>
		<key>u1D61D</key>
		<integer>139</integer>
		<key>u1D61E</key>
		<integer>139</integer>
		<key>u1D61F</key>
		<integer>124</integer>
		<key>u1D620</key>
		<integer>130</integer>
		<key>u1D621</key>
		<integer>130</integer>
		<key>u1D622</key>
		<integer>25</integer>
		<key>u1D623</key>
		<integer>36</integer>
		<key>u1D624</key>
		<integer>64</integer>
		<key>u1D625</key>
		<integer>100</integer>
		<key>u1D626</key>
		<integer>37</integer>
		<key>u1D627</key>
		<integer>207</integer>
		<key>u1D628</key>
		<integer>65</integer>
		<key>u1D62A</key>
		<integer>60</integer>
		<key>u1D62B</key>
		<integer>59</integer>
		<key>u1D62C</key>
		<integer>81</integer>
		<key>u1D62D</key>
		<integer>66</integer>
		<key>u1D630</key>
		<integer>36</integer>
		<key>u1D631</key>
		<integer>26</integer>
		<key>u1D632</key>
		<integer>43</integer>
		<key>u1D633</key>
		<integer>95</integer>
		<key>u1D634</key>
		<integer>47</integer>
		<key>u1D635</key>
		<integer>90</integer>
		<key>u1D636</key>
		<integer>34</integer>
		<key>u1D637</key>
		<integer>95</integer>
		<key>u1D638</key>
		<integer>94</integer>
		<key>u1D639</key>
		<integer>97</integer>
		<key>u1D63A</key>
		<integer>93</integer>
		<key>u1D63B</key>
		<integer>90</integer>
		<key>u1D63D</key>
		<integer>37</integer>
		<key>u1D63E</key>
		<integer>95</integer>
		<key>u1D63F</key>
		<integer>50</integer>
		<key>u1D640</key>
		<integer>110</integer>
		<key>u1D641</key>
		<integer>147</integer>
		<key>u1D642</key>
		<integer>53</integer>
		<key>u1D643</key>
		<integer>87</integer>
		<key>u1D644</key>
		<integer>87</integer>
		<key>u1D645</key>
		<integer>87</integer>
		<key>u1D646</key>
		<integer>154</integer>
		<key>u1D648</key>
		<integer>51</integer>
		<key>u1D649</key>
		<integer>97</integer>
		<key>u1D64A</key>
		<integer>54</integer>
		<key>u1D64B</key>
		<integer>111</integer>
		<key>u1D64C</key>
		<integer>54</integer>
		<key>u1D64D</key>
		<integer>47</integer>
		<key>u1D64E</key>
		<integer>87</integer>
		<key>u1D64F</key>
		<integer>153</integer>
		<key>u1D650</key>
		<integer>91</integer>
		<key>u1D651</key>
		<integer>151</integer>
		<key>u1D652</key>
		<integer>151</integer>
		<key>u1D653</key>
		<integer>129</integer>
		<key>u1D654</key>
		<integer>142</integer>
		<key>u1D655</key>
		<integer>142</integer>
		<key>u1D656</key>
		<integer>51</integer>
		<key>u1D657</key>
		<integer>44</integer>
		<key>u1D658</key>
		<integer>80</integer>
		<key>u1D659</key>
		<integer>105</integer>
		<key>u1D65A</key>
		<integer>33</integer>
		<key>u1D65B</key>
		<integer>127</integer>
		<key>u1D65C</key>
		<integer>71</integer>
		<key>u1D65E</key>
		<integer>76</integer>
		<key>u1D65F</key>
		<integer>75</integer>
		<key>u1D660</key>
		<integer>91</integer>
		<key>u1D661</key>
		<integer>105</integer>
		<key>u1D664</key>
		<integer>36</integer>
		<key>u1D665</key>
		<integer>40</integer>
		<key>u1D666</key>
		<integer>50</integer>
		<key>u1D667</key>
		<integer>98</integer>
		<key>u1D668</key>
		<integer>58</integer>
		<key>u1D669</key>
		<integer>104</integer>
		<key>u1D66A</key>
		<integer>41</integer>
		<key>u1D66B</key>
		<integer>99</integer>
		<key>u1D66C</key>
		<integer>101</integer>
		<key>u1D66D</key>
		<integer>81</integer>
		<key>u1D66E</key>
		<integer>99</integer>
		<key>u1D66F</key>
		<integer>88</integer>
		<key>u1D6A5</key>
		<integer>63</integer>
		<key>u1D6E3</key>
		<integer>24</integer>
		<key>u1D6E4</key>
		<integer>118</integer>
		<key>u1D6E6</key>
		<integer>47</integer>
		<key>u1D6E7</key>
		<integer>115</integer>
		<key>u1D6E8</key>
		<integer>124</integer>
		<key>u1D6E9</key>
		<integer>58</integer>
		<key>u1D6EA</key>
		<integer>127</integer>
		<key>u1D6EB</key>
		<integer>105</integer>
		<key>u1D6ED</key>
		<integer>90</integer>
		<key>u1D6EE</key>
		<integer>142</integer>
		<key>u1D6EF</key>
		<integer>80</integer>
		<key>u1D6F0</key>
		<integer>58</integer>
		<key>u1D6F1</key>
		<integer>118</integer>
		<key>u1D6F2</key>
		<integer>95</integer>
		<key>u1D6F3</key>
		<integer>53</integer>
		<key>u1D6F4</key>
		<integer>55</integer>
		<key>u1D6F5</key>
		<integer>132</integer>
		<key>u1D6F6</key>
		<integer>131</integer>
		<key>u1D6F7</key>
		<integer>44</integer>
		<key>u1D6F8</key>
		<integer>124</integer>
		<key>u1D6F9</key>
		<integer>105</integer>
		<key>u1D6FA</key>
		<integer>48</integer>
		<key>u1D6FB</key>
		<integer>127</integer>
		<key>u1D6FC</key>
		<integer>86</integer>
		<key>u1D6FD</key>
		<integer>74</integer>
		<key>u1D6FE</key>
		<integer>115</integer>
		<key>u1D6FF</key>
		<integer>87</integer>
		<key>u1D700</key>
		<integer>64</integer>
		<key>u1D701</key>
		<integer>141</integer>
		<key>u1D703</key>
		<integer>83</integer>
		<key>u1D704</key>
		<integer>30</integer>
		<key>u1D705</key>
		<integer>48</integer>
		<key>u1D706</key>
		<integer>25</integer>
		<key>u1D707</key>
		<integer>44</integer>
		<key>u1D708</key>
		<integer>80</integer>
		<key>u1D709</key>
		<integer>124</integer>
		<key>u1D70A</key>
		<integer>38</integer>
		<key>u1D70B</key>
		<integer>94</integer>
		<key>u1D70C</key>
		<integer>44</integer>
		<key>u1D70D</key>
		<integer>71</integer>
		<key>u1D70E</key>
		<integer>100</integer>
		<key>u1D70F</key>
		<integer>108</integer>
		<key>u1D710</key>
		<integer>47</integer>
		<key>u1D711</key>
		<integer>43</integer>
		<key>u1D712</key>
		<integer>105</integer>
		<key>u1D713</key>
		<integer>122</integer>
		<key>u1D714</key>
		<integer>42</integer>
		<key>u1D715</key>
		<integer>55</integer>
		<key>u1D716</key>
		<integer>62</integer>
		<key>u1D717</key>
		<integer>106</integer>
		<key>u1D718</key>
		<integer>46</integer>
		<key>u1D719</key>
		<integer>47</integer>
		<key>u1D71A</key>
		<integer>50</integer>
		<key>u1D71B</key>
		<integer>46</integer>
		<key>u1D71E</key>
		<integer>74</integer>
		<key>u1D720</key>
		<integer>41</integer>
		<key>u1D722</key>
		<integer>69</integer>
		<key>u1D723</key>
		<integer>59</integer>
		<key>u1D724</key>
		<integer>133</integer>
		<key>u1D725</key>
		<integer>146</integer>
		<key>u1D728</key>
		<integer>63</integer>
		<key>u1D729</key>
		<integer>71</integer>
		<key>u1D72A</key>
		<integer>59</integer>
		<key>u1D72B</key>
		<integer>122</integer>
		<key>u1D72D</key>
		<integer>59</integer>
		<key>u1D72E</key>
		<integer>76</integer>
		<key>u1D72F</key>
		<integer>78</integer>
		<key>u1D730</key>
		<integer>109</integer>
		<key>u1D731</key>
		<integer>50</integer>
		<key>u1D732</key>
		<integer>21</integer>
		<key>u1D733</key>
		<integer>111</integer>
		<key>u1D734</key>
		<integer>54</integer>
		<key>u1D736</key>
		<integer>112</integer>
		<key>u1D737</key>
		<integer>74</integer>
		<key>u1D738</key>
		<integer>82</integer>
		<key>u1D739</key>
		<integer>70</integer>
		<key>u1D73A</key>
		<integer>63</integer>
		<key>u1D73B</key>
		<integer>101</integer>
		<key>u1D73D</key>
		<integer>85</integer>
		<key>u1D73E</key>
		<integer>39</integer>
		<key>u1D73F</key>
		<integer>79</integer>
		<key>u1D740</key>
		<integer>32</integer>
		<key>u1D741</key>
		<integer>22</integer>
		<key>u1D742</key>
		<integer>84</integer>
		<key>u1D743</key>
		<integer>120</integer>
		<key>u1D744</key>
		<integer>30</integer>
		<key>u1D745</key>
		<integer>88</integer>
		<key>u1D746</key>
		<integer>48</integer>
		<key>u1D747</key>
		<integer>77</integer>
		<key>u1D748</key>
		<integer>98</integer>
		<key>u1D749</key>
		<integer>89</integer>
		<key>u1D74A</key>
		<integer>58</integer>
		<key>u1D74B</key>
		<integer>49</integer>
		<key>u1D74C</key>
		<integer>113</integer>
		<key>u1D74D</key>
		<integer>44</integer>
		<key>u1D74E</key>
		<integer>37</integer>
		<key>u1D74F</key>
		<integer>61</integer>
		<key>u1D750</key>
		<integer>66</integer>
		<key>u1D751</key>
		<integer>70</integer>
		<key>u1D752</key>
		<integer>53</integer>
		<key>u1D753</key>
		<integer>47</integer>
		<key>u1D754</key>
		<integer>51</integer>
		<key>u1D755</key>
		<integer>112</integer>
		<key>u1D76D</key>
		<integer>78</integer>
		<key>u1D791</key>
		<integer>29</integer>
		<key>u1D792</key>
		<integer>138</integer>
		<key>u1D794</key>
		<integer>100</integer>
		<key>u1D795</key>
		<integer>131</integer>
		<key>u1D796</key>
		<integer>77</integer>
		<key>u1D797</key>
		<integer>48</integer>
		<key>u1D798</key>
		<integer>77</integer>
		<key>u1D799</key>
		<integer>144</integer>
		<key>u1D79A</key>
		<integer>2</integer>
		<key>u1D79B</key>
		<integer>41</integer>
		<key>u1D79C</key>
		<integer>87</integer>
		<key>u1D79D</key>
		<integer>116</integer>
		<key>u1D79E</key>
		<integer>48</integer>
		<key>u1D79F</key>
		<integer>67</integer>
		<key>u1D7A0</key>
		<integer>103</integer>
		<key>u1D7A1</key>
		<integer>48</integer>
		<key>u1D7A2</key>
		<integer>90</integer>
		<key>u1D7A3</key>
		<integer>143</integer>
		<key>u1D7A4</key>
		<integer>132</integer>
		<key>u1D7A5</key>
		<integer>61</integer>
		<key>u1D7A6</key>
		<integer>119</integer>
		<key>u1D7A7</key>
		<integer>205</integer>
		<key>u1D7A8</key>
		<integer>58</integer>
		<key>u1D7A9</key>
		<integer>123</integer>
		<key>u1D7AA</key>
		<integer>133</integer>
		<key>u1D7AB</key>
		<integer>66</integer>
		<key>u1D7AC</key>
		<integer>80</integer>
		<key>u1D7AD</key>
		<integer>56</integer>
		<key>u1D7AE</key>
		<integer>73</integer>
		<key>u1D7AF</key>
		<integer>117</integer>
		<key>u1D7B0</key>
		<integer>40</integer>
		<key>u1D7B1</key>
		<integer>65</integer>
		<key>u1D7B2</key>
		<integer>44</integer>
		<key>u1D7B3</key>
		<integer>86</integer>
		<key>u1D7B4</key>
		<integer>26</integer>
		<key>u1D7B5</key>
		<integer>74</integer>
		<key>u1D7B6</key>
		<integer>103</integer>
		<key>u1D7B7</key>
		<integer>140</integer>
		<key>u1D7B8</key>
		<integer>55</integer>
		<key>u1D7B9</key>
		<integer>115</integer>
		<key>u1D7BA</key>
		<integer>81</integer>
		<key>u1D7BB</key>
		<integer>107</integer>
		<key>u1D7BC</key>
		<integer>116</integer>
		<key>u1D7BD</key>
		<integer>126</integer>
		<key>u1D7BE</key>
		<integer>77</integer>
		<key>u1D7BF</key>
		<integer>85</integer>
		<key>u1D7C0</key>
		<integer>146</integer>
		<key>u1D7C1</key>
		<integer>76</integer>
		<key>u1D7C2</key>
		<integer>50</integer>
		<key>u1D7C5</key>
		<integer>77</integer>
		<key>u1D7CA</key>
		<integer>28</integer>
		<key>uni210B</key>
		<integer>145</integer>
		<key>uni2110</key>
		<integer>195</integer>
		<key>uni2112</key>
		<integer>125</integer>
		<key>uni211B</key>
		<integer>45</integer>
		<key>uni212C</key>
		<integer>45</integer>
		<key>uni2130</key>
		<integer>105</integer>
		<key>uni2131</key>
		<integer>195</integer>
		<key>uni2133</key>
		<integer>45</integer>
		<key>uni222C</key>
		<integer>150</integer>
		<key>uni222C.size1</key>
		<integer>250</integer>
		<key>uni222C.sl</key>
		<integer>300</integer>
		<key>uni222C.slsize1</key>
		<integer>650</integer>
		<key>uni222D</key>
		<integer>150</integer>
		<key>uni222D.size1</key>
		<integer>250</integer>
		<key>uni222D.sl</key>
		<integer>300</integer>
		<key>uni222D.slsize1</key>
		<integer>650</integer>
		<key>uni222E.size1</key>
		<integer>250</integer>
		<key>uni222E.sl</key>
		<integer>100</integer>
		<key>uni222E.slsize1</key>
		<integer>650</integer>
		<key>uni222F.size1</key>
		<integer>250</integer>
		<key>uni222F.sl</key>
		<integer>100</integer>
		<key>uni222F.slsize1</key>
		<integer>650</integer>
		<key>uni2230.size1</key>
		<integer>250</integer>
		<key>uni2230.sl</key>
		<integer>100</integer>
		<key>uni2230.slsize1</key>
		<integer>650</integer>
		<key>uni2231.size1</key>
		<integer>250</integer>
		<key>uni2231.sl</key>
		<integer>100</integer>
		<key>uni2231.slsize1</key>
		<integer>650</integer>
		<key>uni2232.size1</key>
		<integer>250</integer>
		<key>uni2232.sl</key>
		<integer>100</integer>
		<key>uni2232.slsize1</key>
		<integer>650</integer>
		<key>uni2233.size1</key>
		<integer>250</integer>
		<key>uni2233.sl</key>
		<integer>100</integer>
		<key>uni2233.slsize1</key>
		<integer>650</integer>
		<key>uni2A0C</key>
		<integer>150</integer>
		<key>uni2A0C.size1</key>
		<integer>250</integer>
		<key>uni2A0C.sl</key>
		<integer>300</integer>
		<key>uni2A0C.slsize1</key>
		<integer>650</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>bar</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>930</integer>
					<key>endConnector</key>
					<integer>460</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>bar</string>
					<key>startConnector</key>
					<integer>460</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>930</integer>
					<key>endConnector</key>
					<integer>460</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>bar</string>
					<key>startConnector</key>
					<integer>460</integer>
				</dict>
			</array>
		</dict>
		<key>braceleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1001</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1947</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A8</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1001</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>600</integer>
				</dict>
			</array>
		</dict>
		<key>braceright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1001</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1947</integer>
					<key>endConnector</key>
					<integer>500</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AC</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1001</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>500</integer>
				</dict>
			</array>
		</dict>
		<key>bracketleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>1200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1001</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>1200</integer>
				</dict>
			</array>
		</dict>
		<key>bracketright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>1200</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1001</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1275</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>1200</integer>
				</dict>
			</array>
		</dict>
		<key>parenleft</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1273</integer>
					<key>endConnector</key>
					<integer>250</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1252</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1273</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>250</integer>
				</dict>
			</array>
		</dict>
		<key>parenright</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1273</integer>
					<key>endConnector</key>
					<integer>250</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1252</integer>
					<key>endConnector</key>
					<integer>1000</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>1000</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1273</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>250</integer>
				</dict>
			</array>
		</dict>
		<key>radical</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>903</integer>
					<key>endConnector</key>
					<integer>301</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>radical.bt</string>
					<key>startConnector</key>
					<integer>301</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>602</integer>
					<key>endConnector</key>
					<integer>201</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>radical.md</string>
					<key>startConnector</key>
					<integer>201</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>159</integer>
					<key>endConnector</key>
					<integer>53</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>radical.tp</string>
					<key>startConnector</key>
					<integer>53</integer>
				</dict>
			</array>
		</dict>
		<key>uni2016</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>930</integer>
					<key>endConnector</key>
					<integer>460</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2016</string>
					<key>startConnector</key>
					<integer>460</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>930</integer>
					<key>endConnector</key>
					<integer>460</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2016</string>
					<key>startConnector</key>
					<integer>460</integer>
				</dict>
			</array>
		</dict>
		<key>uni2308</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
			</array>
		</dict>
		<key>uni2309</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
			</array>
		</dict>
		<key>uni230A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
			</array>
		</dict>
		<key>uni230B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EE</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EF</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>893</integer>
					<key>endConnector</key>
					<integer>298</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>298</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>backslash</key>
		<array>
			<string>backslash</string>
			<string>backslash.size1</string>
			<string>backslash.size2</string>
			<string>backslash.size3</string>
			<string>backslash.size4</string>
		</array>
		<key>bar</key>
		<array>
			<string>bar</string>
			<string>bar.size1</string>
			<string>bar.size2</string>
			<string>bar.size3</string>
			<string>bar.size4</string>
			<string>bar.size5</string>
			<string>bar.size6</string>
			<string>bar.size7</string>
			<string>bar.size8</string>
			<string>bar.size9</string>
			<string>bar.size10</string>
			<string>bar.size11</string>
			<string>bar.size12</string>
		</array>
		<key>braceleft</key>
		<array>
			<string>braceleft</string>
			<string>braceleft.size1</string>
			<string>braceleft.size2</string>
			<string>braceleft.size3</string>
			<string>braceleft.size4</string>
			<string>braceleft.size5</string>
			<string>braceleft.size6</string>
			<string>braceleft.size7</string>
			<string>braceleft.size8</string>
			<string>braceleft.size9</string>
			<string>braceleft.size10</string>
			<string>braceleft.size11</string>
			<string>braceleft.size12</string>
		</array>
		<key>braceright</key>
		<array>
			<string>braceright</string>
			<string>braceright.size1</string>
			<string>braceright.size2</string>
			<string>braceright.size3</string>
			<string>braceright.size4</string>
			<string>braceright.size5</string>
			<string>braceright.size6</string>
			<string>braceright.size7</string>
			<string>braceright.size8</string>
			<string>braceright.size9</string>
			<string>braceright.size10</string>
			<string>braceright.size11</string>
			<string>braceright.size12</string>
		</array>
		<key>bracketleft</key>
		<array>
			<string>bracketleft</string>
			<string>bracketleft.size1</string>
			<string>bracketleft.size2</string>
			<string>bracketleft.size3</string>
			<string>bracketleft.size4</string>
			<string>bracketleft.size5</string>
			<string>bracketleft.size6</string>
			<string>bracketleft.size7</string>
			<string>bracketleft.size8</string>
			<string>bracketleft.size9</string>
			<string>bracketleft.size10</string>
			<string>bracketleft.size11</string>
			<string>bracketleft.size12</string>
		</array>
		<key>bracketright</key>
		<array>
			<string>bracketright</string>
			<string>bracketright.size1</string>
			<string>bracketright.size2</string>
			<string>bracketright.size3</string>
			<string>bracketright.size4</string>
			<string>bracketright.size5</string>
			<string>bracketright.size6</string>
			<string>bracketright.size7</string>
			<string>bracketright.size8</string>
			<string>bracketright.size9</string>
			<string>bracketright.size10</string>
			<string>bracketright.size11</string>
			<string>bracketright.size12</string>
		</array>
		<key>integral</key>
		<array>
			<string>integral</string>
			<string>integral.size1</string>
		</array>
		<key>integral.sl</key>
		<array>
			<string>integral.sl</string>
			<string>integral.slsize1</string>
		</array>
		<key>parenleft</key>
		<array>
			<string>parenleft</string>
			<string>parenleft.size1</string>
			<string>parenleft.size2</string>
			<string>parenleft.size3</string>
			<string>parenleft.size4</string>
			<string>parenleft.size5</string>
			<string>parenleft.size6</string>
			<string>parenleft.size7</string>
			<string>parenleft.size8</string>
			<string>parenleft.size9</string>
			<string>parenleft.size10</string>
			<string>parenleft.size11</string>
			<string>parenleft.size12</string>
		</array>
		<key>parenright</key>
		<array>
			<string>parenright</string>
			<string>parenright.size1</string>
			<string>parenright.size2</string>
			<string>parenright.size3</string>
			<string>parenright.size4</string>
			<string>parenright.size5</string>
			<string>parenright.size6</string>
			<string>parenright.size7</string>
			<string>parenright.size8</string>
			<string>parenright.size9</string>
			<string>parenright.size10</string>
			<string>parenright.size11</string>
			<string>parenright.size12</string>
		</array>
		<key>product</key>
		<array>
			<string>product</string>
			<string>product.size1</string>
		</array>
		<key>radical</key>
		<array>
			<string>radical</string>
			<string>radical.size1</string>
			<string>radical.size2</string>
			<string>radical.size3</string>
			<string>radical.size4</string>
			<string>radical.size5</string>
			<string>radical.size6</string>
			<string>radical.size7</string>
		</array>
		<key>slash</key>
		<array>
			<string>slash</string>
			<string>slash.size1</string>
			<string>slash.size2</string>
			<string>slash.size3</string>
			<string>slash.size4</string>
		</array>
		<key>summation</key>
		<array>
			<string>summation</string>
			<string>summation.size1</string>
		</array>
		<key>uni2016</key>
		<array>
			<string>uni2016</string>
			<string>uni2016.size1</string>
			<string>uni2016.size2</string>
			<string>uni2016.size3</string>
			<string>uni2016.size4</string>
			<string>uni2016.size5</string>
			<string>uni2016.size6</string>
			<string>uni2016.size7</string>
			<string>uni2016.size8</string>
			<string>uni2016.size9</string>
			<string>uni2016.size10</string>
			<string>uni2016.size11</string>
			<string>uni2016.size12</string>
		</array>
		<key>uni2210</key>
		<array>
			<string>uni2210</string>
			<string>uni2210.size1</string>
		</array>
		<key>uni222C</key>
		<array>
			<string>uni222C</string>
			<string>uni222C.size1</string>
		</array>
		<key>uni222C.sl</key>
		<array>
			<string>uni222C.sl</string>
			<string>uni222C.slsize1</string>
		</array>
		<key>uni222D</key>
		<array>
			<string>uni222D</string>
			<string>uni222D.size1</string>
		</array>
		<key>uni222D.sl</key>
		<array>
			<string>uni222D.sl</string>
			<string>uni222D.slsize1</string>
		</array>
		<key>uni222E</key>
		<array>
			<string>uni222E</string>
			<string>uni222E.size1</string>
		</array>
		<key>uni222E.sl</key>
		<array>
			<string>uni222E.sl</string>
			<string>uni222E.slsize1</string>
		</array>
		<key>uni222F</key>
		<array>
			<string>uni222F</string>
			<string>uni222F.size1</string>
		</array>
		<key>uni222F.sl</key>
		<array>
			<string>uni222F.sl</string>
			<string>uni222F.slsize1</string>
		</array>
		<key>uni2230</key>
		<array>
			<string>uni2230</string>
			<string>uni2230.size1</string>
		</array>
		<key>uni2230.sl</key>
		<array>
			<string>uni2230.sl</string>
			<string>uni2230.slsize1</string>
		</array>
		<key>uni2231</key>
		<array>
			<string>uni2231</string>
			<string>uni2231.size1</string>
		</array>
		<key>uni2231.sl</key>
		<array>
			<string>uni2231.sl</string>
			<string>uni2231.slsize1</string>
		</array>
		<key>uni2232</key>
		<array>
			<string>uni2232</string>
			<string>uni2232.size1</string>
		</array>
		<key>uni2232.sl</key>
		<array>
			<string>uni2232.sl</string>
			<string>uni2232.slsize1</string>
		</array>
		<key>uni2233</key>
		<array>
			<string>uni2233</string>
			<string>uni2233.size1</string>
		</array>
		<key>uni2233.sl</key>
		<array>
			<string>uni2233.sl</string>
			<string>uni2233.slsize1</string>
		</array>
		<key>uni22C0</key>
		<array>
			<string>uni22C0</string>
			<string>uni22C0.size1</string>
		</array>
		<key>uni22C1</key>
		<array>
			<string>uni22C1</string>
			<string>uni22C1.size1</string>
		</array>
		<key>uni22C2</key>
		<array>
			<string>uni22C2</string>
			<string>uni22C2.size1</string>
		</array>
		<key>uni22C3</key>
		<array>
			<string>uni22C3</string>
			<string>uni22C3.size1</string>
		</array>
		<key>uni2308</key>
		<array>
			<string>uni2308</string>
			<string>uni2308.size1</string>
			<string>uni2308.size2</string>
			<string>uni2308.size3</string>
			<string>uni2308.size4</string>
			<string>uni2308.size5</string>
			<string>uni2308.size6</string>
			<string>uni2308.size7</string>
			<string>uni2308.size8</string>
			<string>uni2308.size9</string>
			<string>uni2308.size10</string>
			<string>uni2308.size11</string>
			<string>uni2308.size12</string>
		</array>
		<key>uni2309</key>
		<array>
			<string>uni2309</string>
			<string>uni2309.size1</string>
			<string>uni2309.size2</string>
			<string>uni2309.size3</string>
			<string>uni2309.size4</string>
			<string>uni2309.size5</string>
			<string>uni2309.size6</string>
			<string>uni2309.size7</string>
			<string>uni2309.size8</string>
			<string>uni2309.size9</string>
			<string>uni2309.size10</string>
			<string>uni2309.size11</string>
			<string>uni2309.size12</string>
		</array>
		<key>uni230A</key>
		<array>
			<string>uni230A</string>
			<string>uni230A.size1</string>
			<string>uni230A.size2</string>
			<string>uni230A.size3</string>
			<string>uni230A.size4</string>
			<string>uni230A.size5</string>
			<string>uni230A.size6</string>
			<string>uni230A.size7</string>
			<string>uni230A.size8</string>
			<string>uni230A.size9</string>
			<string>uni230A.size10</string>
			<string>uni230A.size11</string>
			<string>uni230A.size12</string>
		</array>
		<key>uni230B</key>
		<array>
			<string>uni230B</string>
			<string>uni230B.size1</string>
			<string>uni230B.size2</string>
			<string>uni230B.size3</string>
			<string>uni230B.size4</string>
			<string>uni230B.size5</string>
			<string>uni230B.size6</string>
			<string>uni230B.size7</string>
			<string>uni230B.size8</string>
			<string>uni230B.size9</string>
			<string>uni230B.size10</string>
			<string>uni230B.size11</string>
			<string>uni230B.size12</string>
		</array>
		<key>uni27E6</key>
		<array>
			<string>uni27E6</string>
			<string>uni27E6.size1</string>
			<string>uni27E6.size2</string>
			<string>uni27E6.size3</string>
			<string>uni27E6.size4</string>
			<string>uni27E6.size5</string>
			<string>uni27E6.size6</string>
			<string>uni27E6.size7</string>
			<string>uni27E6.size8</string>
			<string>uni27E6.size9</string>
			<string>uni27E6.size10</string>
			<string>uni27E6.size11</string>
			<string>uni27E6.size12</string>
		</array>
		<key>uni27E7</key>
		<array>
			<string>uni27E7</string>
			<string>uni27E7.size1</string>
			<string>uni27E7.size2</string>
			<string>uni27E7.size3</string>
			<string>uni27E7.size4</string>
			<string>uni27E7.size5</string>
			<string>uni27E7.size6</string>
			<string>uni27E7.size7</string>
			<string>uni27E7.size8</string>
			<string>uni27E7.size9</string>
			<string>uni27E7.size10</string>
			<string>uni27E7.size11</string>
			<string>uni27E7.size12</string>
		</array>
		<key>uni27E8</key>
		<array>
			<string>uni27E8</string>
			<string>uni27E8.size1</string>
			<string>uni27E8.size2</string>
			<string>uni27E8.size3</string>
			<string>uni27E8.size4</string>
			<string>uni27E8.size5</string>
			<string>uni27E8.size6</string>
			<string>uni27E8.size7</string>
			<string>uni27E8.size8</string>
			<string>uni27E8.size9</string>
			<string>uni27E8.size10</string>
			<string>uni27E8.size11</string>
			<string>uni27E8.size12</string>
		</array>
		<key>uni27E9</key>
		<array>
			<string>uni27E9</string>
			<string>uni27E9.size1</string>
			<string>uni27E9.size2</string>
			<string>uni27E9.size3</string>
			<string>uni27E9.size4</string>
			<string>uni27E9.size5</string>
			<string>uni27E9.size6</string>
			<string>uni27E9.size7</string>
			<string>uni27E9.size8</string>
			<string>uni27E9.size9</string>
			<string>uni27E9.size10</string>
			<string>uni27E9.size11</string>
			<string>uni27E9.size12</string>
		</array>
		<key>uni27EA</key>
		<array>
			<string>uni27EA</string>
			<string>uni27EA.size1</string>
			<string>uni27EA.size2</string>
			<string>uni27EA.size3</string>
			<string>uni27EA.size4</string>
			<string>uni27EA.size5</string>
			<string>uni27EA.size6</string>
			<string>uni27EA.size7</string>
			<string>uni27EA.size8</string>
			<string>uni27EA.size9</string>
			<string>uni27EA.size10</string>
			<string>uni27EA.size11</string>
			<string>uni27EA.size12</string>
		</array>
		<key>uni27EB</key>
		<array>
			<string>uni27EB</string>
			<string>uni27EB.size1</string>
			<string>uni27EB.size2</string>
			<string>uni27EB.size3</string>
			<string>uni27EB.size4</string>
			<string>uni27EB.size5</string>
			<string>uni27EB.size6</string>
			<string>uni27EB.size7</string>
			<string>uni27EB.size8</string>
			<string>uni27EB.size9</string>
			<string>uni27EB.size10</string>
			<string>uni27EB.size11</string>
			<string>uni27EB.size12</string>
		</array>
		<key>uni27EE</key>
		<array>
			<string>uni27EE</string>
		</array>
		<key>uni27EF</key>
		<array>
			<string>uni27EF</string>
		</array>
		<key>uni2987</key>
		<array>
			<string>uni2987</string>
			<string>uni2987.size1</string>
			<string>uni2987.size2</string>
			<string>uni2987.size3</string>
			<string>uni2987.size4</string>
			<string>uni2987.size5</string>
			<string>uni2987.size6</string>
			<string>uni2987.size7</string>
			<string>uni2987.size8</string>
			<string>uni2987.size9</string>
			<string>uni2987.size10</string>
			<string>uni2987.size11</string>
			<string>uni2987.size12</string>
		</array>
		<key>uni2988</key>
		<array>
			<string>uni2988</string>
			<string>uni2988.size1</string>
			<string>uni2988.size2</string>
			<string>uni2988.size3</string>
			<string>uni2988.size4</string>
			<string>uni2988.size5</string>
			<string>uni2988.size6</string>
			<string>uni2988.size7</string>
			<string>uni2988.size8</string>
			<string>uni2988.size9</string>
			<string>uni2988.size10</string>
			<string>uni2988.size11</string>
			<string>uni2988.size12</string>
		</array>
		<key>uni2A00</key>
		<array>
			<string>uni2A00</string>
			<string>uni2A00.size1</string>
		</array>
		<key>uni2A01</key>
		<array>
			<string>uni2A01</string>
			<string>uni2A01.size1</string>
		</array>
		<key>uni2A02</key>
		<array>
			<string>uni2A02</string>
			<string>uni2A02.size1</string>
		</array>
		<key>uni2A03</key>
		<array>
			<string>uni2A03</string>
			<string>uni2A03.size1</string>
		</array>
		<key>uni2A04</key>
		<array>
			<string>uni2A04</string>
			<string>uni2A04.size1</string>
		</array>
		<key>uni2A05</key>
		<array>
			<string>uni2A05</string>
			<string>uni2A05.size1</string>
		</array>
		<key>uni2A06</key>
		<array>
			<string>uni2A06</string>
			<string>uni2A06.size1</string>
		</array>
		<key>uni2A0C</key>
		<array>
			<string>uni2A0C</string>
			<string>uni2A0C.size1</string>
		</array>
		<key>uni2A0C.sl</key>
		<array>
			<string>uni2A0C.sl</string>
			<string>uni2A0C.slsize1</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
