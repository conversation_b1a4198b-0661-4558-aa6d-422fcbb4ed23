<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>accents</key>
	<dict>
		<key>u1D400</key>
		<integer>296</integer>
		<key>u1D401</key>
		<integer>278</integer>
		<key>u1D402</key>
		<integer>348</integer>
		<key>u1D403</key>
		<integer>302</integer>
		<key>u1D404</key>
		<integer>278</integer>
		<key>u1D405</key>
		<integer>272</integer>
		<key>u1D406</key>
		<integer>365</integer>
		<key>u1D407</key>
		<integer>333</integer>
		<key>u1D408</key>
		<integer>147</integer>
		<key>u1D409</key>
		<integer>174</integer>
		<key>u1D40A</key>
		<integer>325</integer>
		<key>u1D40B</key>
		<integer>147</integer>
		<key>u1D40C</key>
		<integer>393</integer>
		<key>u1D40D</key>
		<integer>332</integer>
		<key>u1D40E</key>
		<integer>346</integer>
		<key>u1D40F</key>
		<integer>322</integer>
		<key>u1D410</key>
		<integer>345</integer>
		<key>u1D411</key>
		<integer>292</integer>
		<key>u1D412</key>
		<integer>288</integer>
		<key>u1D413</key>
		<integer>274</integer>
		<key>u1D414</key>
		<integer>326</integer>
		<key>u1D415</key>
		<integer>286</integer>
		<key>u1D416</key>
		<integer>423</integer>
		<key>u1D417</key>
		<integer>290</integer>
		<key>u1D418</key>
		<integer>297</integer>
		<key>u1D419</key>
		<integer>280</integer>
		<key>u1D41A</key>
		<integer>264</integer>
		<key>u1D41B</key>
		<integer>141</integer>
		<key>u1D41C</key>
		<integer>262</integer>
		<key>u1D41D</key>
		<integer>459</integer>
		<key>u1D41E</key>
		<integer>283</integer>
		<key>u1D41F</key>
		<integer>286</integer>
		<key>u1D420</key>
		<integer>278</integer>
		<key>u1D421</key>
		<integer>141</integer>
		<key>u1D422</key>
		<integer>140</integer>
		<key>u1D423</key>
		<integer>140</integer>
		<key>u1D424</key>
		<integer>141</integer>
		<key>u1D425</key>
		<integer>136</integer>
		<key>u1D426</key>
		<integer>425</integer>
		<key>u1D427</key>
		<integer>291</integer>
		<key>u1D428</key>
		<integer>292</integer>
		<key>u1D429</key>
		<integer>311</integer>
		<key>u1D42A</key>
		<integer>288</integer>
		<key>u1D42B</key>
		<integer>232</integer>
		<key>u1D42C</key>
		<integer>249</integer>
		<key>u1D42D</key>
		<integer>156</integer>
		<key>u1D42E</key>
		<integer>284</integer>
		<key>u1D42F</key>
		<integer>256</integer>
		<key>u1D430</key>
		<integer>374</integer>
		<key>u1D431</key>
		<integer>256</integer>
		<key>u1D432</key>
		<integer>259</integer>
		<key>u1D433</key>
		<integer>235</integer>
		<key>u1D434</key>
		<integer>343</integer>
		<key>u1D435</key>
		<integer>329</integer>
		<key>u1D436</key>
		<integer>394</integer>
		<key>u1D437</key>
		<integer>349</integer>
		<key>u1D438</key>
		<integer>343</integer>
		<key>u1D439</key>
		<integer>340</integer>
		<key>u1D43A</key>
		<integer>405</integer>
		<key>u1D43B</key>
		<integer>417</integer>
		<key>u1D43C</key>
		<integer>226</integer>
		<key>u1D43D</key>
		<integer>239</integer>
		<key>u1D43E</key>
		<integer>384</integer>
		<key>u1D43F</key>
		<integer>226</integer>
		<key>u1D440</key>
		<integer>439</integer>
		<key>u1D441</key>
		<integer>399</integer>
		<key>u1D442</key>
		<integer>417</integer>
		<key>u1D443</key>
		<integer>336</integer>
		<key>u1D444</key>
		<integer>417</integer>
		<key>u1D445</key>
		<integer>332</integer>
		<key>u1D446</key>
		<integer>339</integer>
		<key>u1D447</key>
		<integer>333</integer>
		<key>u1D448</key>
		<integer>388</integer>
		<key>u1D449</key>
		<integer>334</integer>
		<key>u1D44A</key>
		<integer>470</integer>
		<key>u1D44B</key>
		<integer>325</integer>
		<key>u1D44C</key>
		<integer>331</integer>
		<key>u1D44D</key>
		<integer>329</integer>
		<key>u1D44E</key>
		<integer>346</integer>
		<key>u1D44F</key>
		<integer>227</integer>
		<key>u1D450</key>
		<integer>334</integer>
		<key>u1D451</key>
		<integer>530</integer>
		<key>u1D452</key>
		<integer>323</integer>
		<key>u1D453</key>
		<integer>429</integer>
		<key>u1D454</key>
		<integer>352</integer>
		<key>u1D456</key>
		<integer>208</integer>
		<key>u1D457</key>
		<integer>209</integer>
		<key>u1D458</key>
		<integer>207</integer>
		<key>u1D459</key>
		<integer>213</integer>
		<key>u1D45A</key>
		<integer>447</integer>
		<key>u1D45B</key>
		<integer>324</integer>
		<key>u1D45C</key>
		<integer>325</integer>
		<key>u1D45D</key>
		<integer>318</integer>
		<key>u1D45E</key>
		<integer>351</integer>
		<key>u1D45F</key>
		<integer>273</integer>
		<key>u1D460</key>
		<integer>283</integer>
		<key>u1D461</key>
		<integer>229</integer>
		<key>u1D462</key>
		<integer>323</integer>
		<key>u1D463</key>
		<integer>280</integer>
		<key>u1D464</key>
		<integer>394</integer>
		<key>u1D465</key>
		<integer>328</integer>
		<key>u1D466</key>
		<integer>311</integer>
		<key>u1D467</key>
		<integer>300</integer>
		<key>u1D468</key>
		<integer>350</integer>
		<key>u1D469</key>
		<integer>327</integer>
		<key>u1D46A</key>
		<integer>395</integer>
		<key>u1D46B</key>
		<integer>327</integer>
		<key>u1D46C</key>
		<integer>343</integer>
		<key>u1D46D</key>
		<integer>327</integer>
		<key>u1D46E</key>
		<integer>407</integer>
		<key>u1D46F</key>
		<integer>387</integer>
		<key>u1D470</key>
		<integer>225</integer>
		<key>u1D471</key>
		<integer>248</integer>
		<key>u1D472</key>
		<integer>395</integer>
		<key>u1D473</key>
		<integer>224</integer>
		<key>u1D474</key>
		<integer>440</integer>
		<key>u1D475</key>
		<integer>386</integer>
		<key>u1D476</key>
		<integer>419</integer>
		<key>u1D477</key>
		<integer>344</integer>
		<key>u1D478</key>
		<integer>419</integer>
		<key>u1D479</key>
		<integer>347</integer>
		<key>u1D47A</key>
		<integer>348</integer>
		<key>u1D47B</key>
		<integer>329</integer>
		<key>u1D47C</key>
		<integer>380</integer>
		<key>u1D47D</key>
		<integer>341</integer>
		<key>u1D47E</key>
		<integer>498</integer>
		<key>u1D47F</key>
		<integer>344</integer>
		<key>u1D480</key>
		<integer>352</integer>
		<key>u1D481</key>
		<integer>335</integer>
		<key>u1D482</key>
		<integer>345</integer>
		<key>u1D483</key>
		<integer>225</integer>
		<key>u1D484</key>
		<integer>334</integer>
		<key>u1D485</key>
		<integer>535</integer>
		<key>u1D486</key>
		<integer>328</integer>
		<key>u1D487</key>
		<integer>452</integer>
		<key>u1D488</key>
		<integer>356</integer>
		<key>u1D489</key>
		<integer>225</integer>
		<key>u1D48A</key>
		<integer>212</integer>
		<key>u1D48B</key>
		<integer>212</integer>
		<key>u1D48C</key>
		<integer>211</integer>
		<key>u1D48D</key>
		<integer>221</integer>
		<key>u1D48E</key>
		<integer>439</integer>
		<key>u1D48F</key>
		<integer>327</integer>
		<key>u1D490</key>
		<integer>324</integer>
		<key>u1D491</key>
		<integer>334</integer>
		<key>u1D492</key>
		<integer>352</integer>
		<key>u1D493</key>
		<integer>266</integer>
		<key>u1D494</key>
		<integer>290</integer>
		<key>u1D495</key>
		<integer>238</integer>
		<key>u1D496</key>
		<integer>315</integer>
		<key>u1D497</key>
		<integer>290</integer>
		<key>u1D498</key>
		<integer>418</integer>
		<key>u1D499</key>
		<integer>284</integer>
		<key>u1D49A</key>
		<integer>291</integer>
		<key>u1D49B</key>
		<integer>267</integer>
		<key>u1D6A4</key>
		<integer>196</integer>
		<key>u1D6A5</key>
		<integer>196</integer>
		<key>u1D6A8</key>
		<integer>296</integer>
		<key>u1D6A9</key>
		<integer>278</integer>
		<key>u1D6AA</key>
		<integer>287</integer>
		<key>u1D6AB</key>
		<integer>300</integer>
		<key>u1D6AC</key>
		<integer>278</integer>
		<key>u1D6AD</key>
		<integer>280</integer>
		<key>u1D6AE</key>
		<integer>333</integer>
		<key>u1D6AF</key>
		<integer>346</integer>
		<key>u1D6B0</key>
		<integer>147</integer>
		<key>u1D6B1</key>
		<integer>325</integer>
		<key>u1D6B2</key>
		<integer>295</integer>
		<key>u1D6B3</key>
		<integer>393</integer>
		<key>u1D6B4</key>
		<integer>332</integer>
		<key>u1D6B5</key>
		<integer>262</integer>
		<key>u1D6B6</key>
		<integer>346</integer>
		<key>u1D6B7</key>
		<integer>332</integer>
		<key>u1D6B8</key>
		<integer>322</integer>
		<key>u1D6B9</key>
		<integer>346</integer>
		<key>u1D6BA</key>
		<integer>251</integer>
		<key>u1D6BB</key>
		<integer>274</integer>
		<key>u1D6BC</key>
		<integer>297</integer>
		<key>u1D6BD</key>
		<integer>421</integer>
		<key>u1D6BE</key>
		<integer>290</integer>
		<key>u1D6BF</key>
		<integer>364</integer>
		<key>u1D6C0</key>
		<integer>345</integer>
		<key>u1D6C1</key>
		<integer>342</integer>
		<key>u1D6C2</key>
		<integer>303</integer>
		<key>u1D6C3</key>
		<integer>292</integer>
		<key>u1D6C4</key>
		<integer>248</integer>
		<key>u1D6C5</key>
		<integer>300</integer>
		<key>u1D6C6</key>
		<integer>264</integer>
		<key>u1D6C7</key>
		<integer>232</integer>
		<key>u1D6C8</key>
		<integer>284</integer>
		<key>u1D6C9</key>
		<integer>282</integer>
		<key>u1D6CA</key>
		<integer>136</integer>
		<key>u1D6CB</key>
		<integer>303</integer>
		<key>u1D6CC</key>
		<integer>238</integer>
		<key>u1D6CD</key>
		<integer>300</integer>
		<key>u1D6CE</key>
		<integer>248</integer>
		<key>u1D6CF</key>
		<integer>248</integer>
		<key>u1D6D0</key>
		<integer>292</integer>
		<key>u1D6D1</key>
		<integer>294</integer>
		<key>u1D6D2</key>
		<integer>298</integer>
		<key>u1D6D3</key>
		<integer>253</integer>
		<key>u1D6D4</key>
		<integer>312</integer>
		<key>u1D6D5</key>
		<integer>241</integer>
		<key>u1D6D6</key>
		<integer>290</integer>
		<key>u1D6D7</key>
		<integer>377</integer>
		<key>u1D6D8</key>
		<integer>242</integer>
		<key>u1D6D9</key>
		<integer>395</integer>
		<key>u1D6DA</key>
		<integer>387</integer>
		<key>u1D6DB</key>
		<integer>264</integer>
		<key>u1D6DC</key>
		<integer>254</integer>
		<key>u1D6DD</key>
		<integer>255</integer>
		<key>u1D6DE</key>
		<integer>285</integer>
		<key>u1D6DF</key>
		<integer>332</integer>
		<key>u1D6E0</key>
		<integer>301</integer>
		<key>u1D6E1</key>
		<integer>371</integer>
		<key>u1D6E2</key>
		<integer>343</integer>
		<key>u1D6E3</key>
		<integer>329</integer>
		<key>u1D6E4</key>
		<integer>378</integer>
		<key>u1D6E5</key>
		<integer>368</integer>
		<key>u1D6E6</key>
		<integer>343</integer>
		<key>u1D6E7</key>
		<integer>329</integer>
		<key>u1D6E8</key>
		<integer>417</integer>
		<key>u1D6E9</key>
		<integer>417</integer>
		<key>u1D6EA</key>
		<integer>226</integer>
		<key>u1D6EB</key>
		<integer>384</integer>
		<key>u1D6EC</key>
		<integer>363</integer>
		<key>u1D6ED</key>
		<integer>439</integer>
		<key>u1D6EE</key>
		<integer>399</integer>
		<key>u1D6EF</key>
		<integer>318</integer>
		<key>u1D6F0</key>
		<integer>417</integer>
		<key>u1D6F1</key>
		<integer>404</integer>
		<key>u1D6F2</key>
		<integer>356</integer>
		<key>u1D6F3</key>
		<integer>417</integer>
		<key>u1D6F4</key>
		<integer>320</integer>
		<key>u1D6F5</key>
		<integer>333</integer>
		<key>u1D6F6</key>
		<integer>331</integer>
		<key>u1D6F7</key>
		<integer>477</integer>
		<key>u1D6F8</key>
		<integer>325</integer>
		<key>u1D6F9</key>
		<integer>418</integer>
		<key>u1D6FA</key>
		<integer>416</integer>
		<key>u1D6FB</key>
		<integer>320</integer>
		<key>u1D6FC</key>
		<integer>318</integer>
		<key>u1D6FD</key>
		<integer>365</integer>
		<key>u1D6FE</key>
		<integer>266</integer>
		<key>u1D6FF</key>
		<integer>356</integer>
		<key>u1D700</key>
		<integer>285</integer>
		<key>u1D701</key>
		<integer>313</integer>
		<key>u1D702</key>
		<integer>324</integer>
		<key>u1D703</key>
		<integer>361</integer>
		<key>u1D704</key>
		<integer>191</integer>
		<key>u1D705</key>
		<integer>308</integer>
		<key>u1D706</key>
		<integer>254</integer>
		<key>u1D707</key>
		<integer>330</integer>
		<key>u1D708</key>
		<integer>266</integer>
		<key>u1D709</key>
		<integer>335</integer>
		<key>u1D70A</key>
		<integer>325</integer>
		<key>u1D70B</key>
		<integer>343</integer>
		<key>u1D70C</key>
		<integer>329</integer>
		<key>u1D70D</key>
		<integer>320</integer>
		<key>u1D70E</key>
		<integer>348</integer>
		<key>u1D70F</key>
		<integer>263</integer>
		<key>u1D710</key>
		<integer>316</integer>
		<key>u1D711</key>
		<integer>385</integer>
		<key>u1D712</key>
		<integer>245</integer>
		<key>u1D713</key>
		<integer>441</integer>
		<key>u1D714</key>
		<integer>412</integer>
		<key>u1D715</key>
		<integer>280</integer>
		<key>u1D716</key>
		<integer>329</integer>
		<key>u1D717</key>
		<integer>300</integer>
		<key>u1D718</key>
		<integer>300</integer>
		<key>u1D719</key>
		<integer>388</integer>
		<key>u1D71A</key>
		<integer>392</integer>
		<key>u1D71B</key>
		<integer>398</integer>
		<key>u1D71C</key>
		<integer>350</integer>
		<key>u1D71D</key>
		<integer>327</integer>
		<key>u1D71E</key>
		<integer>354</integer>
		<key>u1D71F</key>
		<integer>374</integer>
		<key>u1D720</key>
		<integer>343</integer>
		<key>u1D721</key>
		<integer>335</integer>
		<key>u1D722</key>
		<integer>387</integer>
		<key>u1D723</key>
		<integer>419</integer>
		<key>u1D724</key>
		<integer>225</integer>
		<key>u1D725</key>
		<integer>395</integer>
		<key>u1D726</key>
		<integer>350</integer>
		<key>u1D727</key>
		<integer>440</integer>
		<key>u1D728</key>
		<integer>386</integer>
		<key>u1D729</key>
		<integer>324</integer>
		<key>u1D72A</key>
		<integer>419</integer>
		<key>u1D72B</key>
		<integer>397</integer>
		<key>u1D72C</key>
		<integer>344</integer>
		<key>u1D72D</key>
		<integer>419</integer>
		<key>u1D72E</key>
		<integer>317</integer>
		<key>u1D72F</key>
		<integer>329</integer>
		<key>u1D730</key>
		<integer>352</integer>
		<key>u1D731</key>
		<integer>496</integer>
		<key>u1D732</key>
		<integer>344</integer>
		<key>u1D733</key>
		<integer>438</integer>
		<key>u1D734</key>
		<integer>415</integer>
		<key>u1D735</key>
		<integer>358</integer>
		<key>u1D736</key>
		<integer>308</integer>
		<key>u1D737</key>
		<integer>359</integer>
		<key>u1D738</key>
		<integer>286</integer>
		<key>u1D739</key>
		<integer>350</integer>
		<key>u1D73A</key>
		<integer>279</integer>
		<key>u1D73B</key>
		<integer>318</integer>
		<key>u1D73C</key>
		<integer>311</integer>
		<key>u1D73D</key>
		<integer>354</integer>
		<key>u1D73E</key>
		<integer>190</integer>
		<key>u1D73F</key>
		<integer>316</integer>
		<key>u1D740</key>
		<integer>273</integer>
		<key>u1D741</key>
		<integer>319</integer>
		<key>u1D742</key>
		<integer>286</integer>
		<key>u1D743</key>
		<integer>336</integer>
		<key>u1D744</key>
		<integer>324</integer>
		<key>u1D745</key>
		<integer>346</integer>
		<key>u1D746</key>
		<integer>323</integer>
		<key>u1D747</key>
		<integer>322</integer>
		<key>u1D748</key>
		<integer>346</integer>
		<key>u1D749</key>
		<integer>268</integer>
		<key>u1D74A</key>
		<integer>304</integer>
		<key>u1D74B</key>
		<integer>390</integer>
		<key>u1D74C</key>
		<integer>254</integer>
		<key>u1D74D</key>
		<integer>448</integer>
		<key>u1D74E</key>
		<integer>418</integer>
		<key>u1D74F</key>
		<integer>278</integer>
		<key>u1D750</key>
		<integer>312</integer>
		<key>u1D751</key>
		<integer>301</integer>
		<key>u1D752</key>
		<integer>303</integer>
		<key>u1D753</key>
		<integer>413</integer>
		<key>u1D754</key>
		<integer>323</integer>
		<key>u1D755</key>
		<integer>416</integer>
		<key>uni0041</key>
		<integer>287</integer>
		<key>uni0042</key>
		<integer>292</integer>
		<key>uni0043</key>
		<integer>298</integer>
		<key>uni0044</key>
		<integer>314</integer>
		<key>uni0045</key>
		<integer>290</integer>
		<key>uni0046</key>
		<integer>283</integer>
		<key>uni0047</key>
		<integer>312</integer>
		<key>uni0048</key>
		<integer>340</integer>
		<key>uni0049</key>
		<integer>148</integer>
		<key>uni004A</key>
		<integer>162</integer>
		<key>uni004B</key>
		<integer>324</integer>
		<key>uni004C</key>
		<integer>148</integer>
		<key>uni004D</key>
		<integer>389</integer>
		<key>uni004E</key>
		<integer>342</integer>
		<key>uni004F</key>
		<integer>346</integer>
		<key>uni0050</key>
		<integer>294</integer>
		<key>uni0051</key>
		<integer>346</integer>
		<key>uni0052</key>
		<integer>290</integer>
		<key>uni0053</key>
		<integer>270</integer>
		<key>uni0054</key>
		<integer>261</integer>
		<key>uni0055</key>
		<integer>331</integer>
		<key>uni0056</key>
		<integer>278</integer>
		<key>uni0057</key>
		<integer>413</integer>
		<key>uni0058</key>
		<integer>270</integer>
		<key>uni0059</key>
		<integer>275</integer>
		<key>uni005A</key>
		<integer>271</integer>
		<key>uni0061</key>
		<integer>265</integer>
		<key>uni0062</key>
		<integer>141</integer>
		<key>uni0063</key>
		<integer>287</integer>
		<key>uni0064</key>
		<integer>457</integer>
		<key>uni0065</key>
		<integer>276</integer>
		<key>uni0066</key>
		<integer>269</integer>
		<key>uni0067</key>
		<integer>272</integer>
		<key>uni0068</key>
		<integer>141</integer>
		<key>uni0069</key>
		<integer>140</integer>
		<key>uni006A</key>
		<integer>139</integer>
		<key>uni006B</key>
		<integer>141</integer>
		<key>uni006C</key>
		<integer>136</integer>
		<key>uni006D</key>
		<integer>431</integer>
		<key>uni006E</key>
		<integer>296</integer>
		<key>uni006F</key>
		<integer>292</integer>
		<key>uni0070</key>
		<integer>314</integer>
		<key>uni0071</key>
		<integer>282</integer>
		<key>uni0072</key>
		<integer>236</integer>
		<key>uni0073</key>
		<integer>228</integer>
		<key>uni0074</key>
		<integer>147</integer>
		<key>uni0075</key>
		<integer>288</integer>
		<key>uni0076</key>
		<integer>246</integer>
		<key>uni0077</key>
		<integer>358</integer>
		<key>uni0078</key>
		<integer>242</integer>
		<key>uni0079</key>
		<integer>246</integer>
		<key>uni007A</key>
		<integer>216</integer>
		<key>uni00B4</key>
		<integer>150</integer>
		<key>uni0131</key>
		<integer>141</integer>
		<key>uni0300</key>
		<integer>-150</integer>
		<key>uni0301</key>
		<integer>-150</integer>
		<key>uni0302</key>
		<integer>-196</integer>
		<key>uni0303</key>
		<integer>-197</integer>
		<key>uni0304</key>
		<integer>-166</integer>
		<key>uni0305</key>
		<integer>-270</integer>
		<key>uni0306</key>
		<integer>-182</integer>
		<key>uni0307</key>
		<integer>-94</integer>
		<key>uni0308</key>
		<integer>-192</integer>
		<key>uni0309</key>
		<integer>-131</integer>
		<key>uni030A</key>
		<integer>-144</integer>
		<key>uni030B</key>
		<integer>-183</integer>
		<key>uni030C</key>
		<integer>-196</integer>
		<key>uni030D</key>
		<integer>-74</integer>
		<key>uni030F</key>
		<integer>-183</integer>
		<key>uni0311</key>
		<integer>-182</integer>
		<key>uni0312</key>
		<integer>-90</integer>
		<key>uni0313</key>
		<integer>-90</integer>
		<key>uni0316</key>
		<integer>-150</integer>
		<key>uni0317</key>
		<integer>-150</integer>
		<key>uni0318</key>
		<integer>-132</integer>
		<key>uni0319</key>
		<integer>-133</integer>
		<key>uni031A</key>
		<integer>-93</integer>
		<key>uni031B</key>
		<integer>-97</integer>
		<key>uni031C</key>
		<integer>-87</integer>
		<key>uni031D</key>
		<integer>-180</integer>
		<key>uni031E</key>
		<integer>-180</integer>
		<key>uni031F</key>
		<integer>-156</integer>
		<key>uni0320</key>
		<integer>-157</integer>
		<key>uni0321</key>
		<integer>-163</integer>
		<key>uni0323</key>
		<integer>-94</integer>
		<key>uni0324</key>
		<integer>-192</integer>
		<key>uni0325</key>
		<integer>-144</integer>
		<key>uni0326</key>
		<integer>-90</integer>
		<key>uni0327</key>
		<integer>-138</integer>
		<key>uni0328</key>
		<integer>-132</integer>
		<key>uni0329</key>
		<integer>-86</integer>
		<key>uni032A</key>
		<integer>-170</integer>
		<key>uni032B</key>
		<integer>-221</integer>
		<key>uni032C</key>
		<integer>-196</integer>
		<key>uni032D</key>
		<integer>-196</integer>
		<key>uni032F</key>
		<integer>-182</integer>
		<key>uni0330</key>
		<integer>-197</integer>
		<key>uni0331</key>
		<integer>-166</integer>
		<key>uni0332</key>
		<integer>-235</integer>
		<key>uni0334</key>
		<integer>-239</integer>
		<key>uni0335</key>
		<integer>-202</integer>
		<key>uni0336</key>
		<integer>-393</integer>
		<key>uni0337</key>
		<integer>-236</integer>
		<key>uni0338</key>
		<integer>-311</integer>
		<key>uni0339</key>
		<integer>-86</integer>
		<key>uni033A</key>
		<integer>-170</integer>
		<key>uni033B</key>
		<integer>-170</integer>
		<key>uni033C</key>
		<integer>-221</integer>
		<key>uni033D</key>
		<integer>-174</integer>
		<key>uni0342</key>
		<integer>-197</integer>
		<key>uni0345</key>
		<integer>-136</integer>
		<key>uni035C</key>
		<integer>0</integer>
		<key>uni0361</key>
		<integer>-1</integer>
		<key>uni0391</key>
		<integer>287</integer>
		<key>uni0392</key>
		<integer>292</integer>
		<key>uni0393</key>
		<integer>279</integer>
		<key>uni0394</key>
		<integer>292</integer>
		<key>uni0395</key>
		<integer>290</integer>
		<key>uni0396</key>
		<integer>271</integer>
		<key>uni0397</key>
		<integer>340</integer>
		<key>uni0398</key>
		<integer>346</integer>
		<key>uni0399</key>
		<integer>148</integer>
		<key>uni039A</key>
		<integer>324</integer>
		<key>uni039B</key>
		<integer>287</integer>
		<key>uni039C</key>
		<integer>389</integer>
		<key>uni039D</key>
		<integer>342</integer>
		<key>uni039E</key>
		<integer>251</integer>
		<key>uni039F</key>
		<integer>346</integer>
		<key>uni03A0</key>
		<integer>337</integer>
		<key>uni03A1</key>
		<integer>294</integer>
		<key>uni03A3</key>
		<integer>251</integer>
		<key>uni03A4</key>
		<integer>261</integer>
		<key>uni03A5</key>
		<integer>275</integer>
		<key>uni03A6</key>
		<integer>405</integer>
		<key>uni03A7</key>
		<integer>270</integer>
		<key>uni03A8</key>
		<integer>347</integer>
		<key>uni03A9</key>
		<integer>346</integer>
		<key>uni03B1</key>
		<integer>302</integer>
		<key>uni03B2</key>
		<integer>291</integer>
		<key>uni03B3</key>
		<integer>234</integer>
		<key>uni03B4</key>
		<integer>288</integer>
		<key>uni03B5</key>
		<integer>263</integer>
		<key>uni03B6</key>
		<integer>230</integer>
		<key>uni03B7</key>
		<integer>288</integer>
		<key>uni03B8</key>
		<integer>288</integer>
		<key>uni03B9</key>
		<integer>136</integer>
		<key>uni03BA</key>
		<integer>285</integer>
		<key>uni03BB</key>
		<integer>222</integer>
		<key>uni03BC</key>
		<integer>304</integer>
		<key>uni03BD</key>
		<integer>234</integer>
		<key>uni03BE</key>
		<integer>248</integer>
		<key>uni03BF</key>
		<integer>293</integer>
		<key>uni03C0</key>
		<integer>290</integer>
		<key>uni03C1</key>
		<integer>300</integer>
		<key>uni03C2</key>
		<integer>252</integer>
		<key>uni03C3</key>
		<integer>308</integer>
		<key>uni03C4</key>
		<integer>225</integer>
		<key>uni03C5</key>
		<integer>296</integer>
		<key>uni03C6</key>
		<integer>368</integer>
		<key>uni03C7</key>
		<integer>227</integer>
		<key>uni03C8</key>
		<integer>382</integer>
		<key>uni03C9</key>
		<integer>389</integer>
		<key>uni03D0</key>
		<integer>289</integer>
		<key>uni03D1</key>
		<integer>241</integer>
		<key>uni03D2</key>
		<integer>303</integer>
		<key>uni03D5</key>
		<integer>314</integer>
		<key>uni03D6</key>
		<integer>359</integer>
		<key>uni03D7</key>
		<integer>300</integer>
		<key>uni03F0</key>
		<integer>285</integer>
		<key>uni03F1</key>
		<integer>304</integer>
		<key>uni03F2</key>
		<integer>287</integer>
		<key>uni03F4</key>
		<integer>346</integer>
		<key>uni03F5</key>
		<integer>269</integer>
		<key>uni03F6</key>
		<integer>231</integer>
		<key>uni20D0</key>
		<integer>-216</integer>
		<key>uni20D1</key>
		<integer>-216</integer>
		<key>uni20D2</key>
		<integer>-200</integer>
		<key>uni20D3</key>
		<integer>-200</integer>
		<key>uni20D6</key>
		<integer>-210</integer>
		<key>uni20D7</key>
		<integer>-210</integer>
		<key>uni20DB</key>
		<integer>-287</integer>
		<key>uni20DC</key>
		<integer>-372</integer>
		<key>uni20E1</key>
		<integer>-250</integer>
		<key>uni20E7</key>
		<integer>-430</integer>
		<key>uni20E8</key>
		<integer>-288</integer>
		<key>uni20E9</key>
		<integer>-245</integer>
		<key>uni20EC</key>
		<integer>-216</integer>
		<key>uni20ED</key>
		<integer>-216</integer>
		<key>uni20EE</key>
		<integer>-210</integer>
		<key>uni20EF</key>
		<integer>-210</integer>
		<key>uni20F0</key>
		<integer>-180</integer>
		<key>uni210A</key>
		<integer>282</integer>
		<key>uni210E</key>
		<integer>207</integer>
		<key>uni210F</key>
		<integer>207</integer>
		<key>uni2113</key>
		<integer>250</integer>
		<key>uni2202</key>
		<integer>245</integer>
		<key>uni2206</key>
		<integer>292</integer>
		<key>uni2207</key>
		<integer>306</integer>
	</dict>
	<key>constants</key>
	<dict>
		<key>AccentBaseHeight</key>
		<integer>527</integer>
		<key>AxisHeight</key>
		<integer>280</integer>
		<key>DelimitedSubFormulaMinHeight</key>
		<integer>1500</integer>
		<key>DisplayOperatorMinHeight</key>
		<integer>1500</integer>
		<key>FlattenedAccentBaseHeight</key>
		<integer>689</integer>
		<key>FractionDenomDisplayStyleGapMin</key>
		<integer>200</integer>
		<key>FractionDenominatorDisplayStyleShiftDown</key>
		<integer>700</integer>
		<key>FractionDenominatorGapMin</key>
		<integer>80</integer>
		<key>FractionDenominatorShiftDown</key>
		<integer>480</integer>
		<key>FractionNumDisplayStyleGapMin</key>
		<integer>200</integer>
		<key>FractionNumeratorDisplayStyleShiftUp</key>
		<integer>580</integer>
		<key>FractionNumeratorGapMin</key>
		<integer>80</integer>
		<key>FractionNumeratorShiftUp</key>
		<integer>450</integer>
		<key>FractionRuleThickness</key>
		<integer>76</integer>
		<key>LowerLimitBaselineDropMin</key>
		<integer>600</integer>
		<key>LowerLimitGapMin</key>
		<integer>150</integer>
		<key>MathLeading</key>
		<integer>150</integer>
		<key>MinConnectorOverlap</key>
		<integer>20</integer>
		<key>OverbarExtraAscender</key>
		<integer>50</integer>
		<key>OverbarRuleThickness</key>
		<integer>66</integer>
		<key>OverbarVerticalGap</key>
		<integer>150</integer>
		<key>RadicalDegreeBottomRaisePercent</key>
		<integer>64</integer>
		<key>RadicalDisplayStyleVerticalGap</key>
		<integer>142</integer>
		<key>RadicalExtraAscender</key>
		<integer>76</integer>
		<key>RadicalKernAfterDegree</key>
		<integer>-400</integer>
		<key>RadicalKernBeforeDegree</key>
		<integer>276</integer>
		<key>RadicalRuleThickness</key>
		<integer>76</integer>
		<key>RadicalVerticalGap</key>
		<integer>96</integer>
		<key>ScriptPercentScaleDown</key>
		<integer>72</integer>
		<key>ScriptScriptPercentScaleDown</key>
		<integer>58</integer>
		<key>SkewedFractionHorizontalGap</key>
		<integer>0</integer>
		<key>SkewedFractionVerticalGap</key>
		<integer>0</integer>
		<key>SpaceAfterScript</key>
		<integer>41</integer>
		<key>StackBottomDisplayStyleShiftDown</key>
		<integer>700</integer>
		<key>StackBottomShiftDown</key>
		<integer>480</integer>
		<key>StackDisplayStyleGapMin</key>
		<integer>500</integer>
		<key>StackGapMin</key>
		<integer>200</integer>
		<key>StackTopDisplayStyleShiftUp</key>
		<integer>580</integer>
		<key>StackTopShiftUp</key>
		<integer>450</integer>
		<key>StretchStackBottomShiftDown</key>
		<integer>600</integer>
		<key>StretchStackGapAboveMin</key>
		<integer>150</integer>
		<key>StretchStackGapBelowMin</key>
		<integer>150</integer>
		<key>StretchStackTopShiftUp</key>
		<integer>300</integer>
		<key>SubSuperscriptGapMin</key>
		<integer>200</integer>
		<key>SubscriptBaselineDropMin</key>
		<integer>250</integer>
		<key>SubscriptShiftDown</key>
		<integer>350</integer>
		<key>SubscriptTopMax</key>
		<integer>527</integer>
		<key>SuperscriptBaselineDropMax</key>
		<integer>360</integer>
		<key>SuperscriptBottomMaxWithSubscript</key>
		<integer>527</integer>
		<key>SuperscriptBottomMin</key>
		<integer>130</integer>
		<key>SuperscriptShiftUp</key>
		<integer>400</integer>
		<key>SuperscriptShiftUpCramped</key>
		<integer>270</integer>
		<key>UnderbarExtraDescender</key>
		<integer>50</integer>
		<key>UnderbarRuleThickness</key>
		<integer>66</integer>
		<key>UnderbarVerticalGap</key>
		<integer>150</integer>
		<key>UpperLimitBaselineRiseMin</key>
		<integer>150</integer>
		<key>UpperLimitGapMin</key>
		<integer>150</integer>
	</dict>
	<key>h_variants</key>
	<dict>
		<key>uni23B4</key>
		<array>
			<string>uni23B4</string>
			<string>uni23B4.size1</string>
			<string>uni23B4.size2</string>
			<string>uni23B4.size3</string>
			<string>uni23B4.size4</string>
			<string>uni23B4.size5</string>
			<string>uni23B4.size6</string>
			<string>uni23B4.size7</string>
			<string>uni23B4.size8</string>
			<string>uni23B4.size9</string>
			<string>uni23B4.size10</string>
			<string>uni23B4.size11</string>
			<string>uni23B4.size12</string>
			<string>uni23B4.size13</string>
			<string>uni23B4.size14</string>
			<string>uni23B4.size15</string>
		</array>
		<key>uni23B5</key>
		<array>
			<string>uni23B5</string>
			<string>uni23B5.size1</string>
			<string>uni23B5.size2</string>
			<string>uni23B5.size3</string>
			<string>uni23B5.size4</string>
			<string>uni23B5.size5</string>
			<string>uni23B5.size6</string>
			<string>uni23B5.size7</string>
			<string>uni23B5.size8</string>
			<string>uni23B5.size9</string>
			<string>uni23B5.size10</string>
			<string>uni23B5.size11</string>
			<string>uni23B5.size12</string>
			<string>uni23B5.size13</string>
			<string>uni23B5.size14</string>
			<string>uni23B5.size15</string>
		</array>
		<key>uni23DC</key>
		<array>
			<string>uni23DC</string>
			<string>uni23DC.size1</string>
			<string>uni23DC.size2</string>
			<string>uni23DC.size3</string>
			<string>uni23DC.size4</string>
			<string>uni23DC.size5</string>
			<string>uni23DC.size6</string>
			<string>uni23DC.size7</string>
			<string>uni23DC.size8</string>
			<string>uni23DC.size9</string>
			<string>uni23DC.size10</string>
			<string>uni23DC.size11</string>
			<string>uni23DC.size12</string>
			<string>uni23DC.size13</string>
			<string>uni23DC.size14</string>
			<string>uni23DC.size15</string>
		</array>
		<key>uni23DD</key>
		<array>
			<string>uni23DD</string>
			<string>uni23DD.size1</string>
			<string>uni23DD.size2</string>
			<string>uni23DD.size3</string>
			<string>uni23DD.size4</string>
			<string>uni23DD.size5</string>
			<string>uni23DD.size6</string>
			<string>uni23DD.size7</string>
			<string>uni23DD.size8</string>
			<string>uni23DD.size9</string>
			<string>uni23DD.size10</string>
			<string>uni23DD.size11</string>
			<string>uni23DD.size12</string>
			<string>uni23DD.size13</string>
			<string>uni23DD.size14</string>
			<string>uni23DD.size15</string>
		</array>
		<key>uni23DE</key>
		<array>
			<string>uni23DE</string>
			<string>uni23DE.size1</string>
			<string>uni23DE.size2</string>
			<string>uni23DE.size3</string>
			<string>uni23DE.size4</string>
			<string>uni23DE.size5</string>
			<string>uni23DE.size6</string>
			<string>uni23DE.size7</string>
			<string>uni23DE.size8</string>
			<string>uni23DE.size9</string>
			<string>uni23DE.size10</string>
			<string>uni23DE.size11</string>
			<string>uni23DE.size12</string>
			<string>uni23DE.size13</string>
			<string>uni23DE.size14</string>
			<string>uni23DE.size15</string>
		</array>
		<key>uni23DF</key>
		<array>
			<string>uni23DF</string>
			<string>uni23DF.size1</string>
			<string>uni23DF.size2</string>
			<string>uni23DF.size3</string>
			<string>uni23DF.size4</string>
			<string>uni23DF.size5</string>
			<string>uni23DF.size6</string>
			<string>uni23DF.size7</string>
			<string>uni23DF.size8</string>
			<string>uni23DF.size9</string>
			<string>uni23DF.size10</string>
			<string>uni23DF.size11</string>
			<string>uni23DF.size12</string>
			<string>uni23DF.size13</string>
			<string>uni23DF.size14</string>
			<string>uni23DF.size15</string>
		</array>
	</dict>
	<key>italic</key>
	<dict>
		<key>u1D41F</key>
		<integer>100</integer>
		<key>u1D420</key>
		<integer>15</integer>
		<key>u1D42B</key>
		<integer>30</integer>
		<key>u1D42D</key>
		<integer>30</integer>
		<key>u1D42F</key>
		<integer>30</integer>
		<key>u1D430</key>
		<integer>30</integer>
		<key>u1D431</key>
		<integer>20</integer>
		<key>u1D432</key>
		<integer>30</integer>
		<key>u1D435</key>
		<integer>30</integer>
		<key>u1D436</key>
		<integer>60</integer>
		<key>u1D437</key>
		<integer>15</integer>
		<key>u1D438</key>
		<integer>60</integer>
		<key>u1D439</key>
		<integer>80</integer>
		<key>u1D43A</key>
		<integer>20</integer>
		<key>u1D43B</key>
		<integer>20</integer>
		<key>u1D43C</key>
		<integer>20</integer>
		<key>u1D43D</key>
		<integer>20</integer>
		<key>u1D43E</key>
		<integer>80</integer>
		<key>u1D440</key>
		<integer>20</integer>
		<key>u1D441</key>
		<integer>20</integer>
		<key>u1D442</key>
		<integer>15</integer>
		<key>u1D443</key>
		<integer>40</integer>
		<key>u1D444</key>
		<integer>20</integer>
		<key>u1D445</key>
		<integer>20</integer>
		<key>u1D446</key>
		<integer>30</integer>
		<key>u1D447</key>
		<integer>90</integer>
		<key>u1D448</key>
		<integer>30</integer>
		<key>u1D449</key>
		<integer>100</integer>
		<key>u1D44A</key>
		<integer>80</integer>
		<key>u1D44B</key>
		<integer>70</integer>
		<key>u1D44C</key>
		<integer>90</integer>
		<key>u1D44D</key>
		<integer>50</integer>
		<key>u1D450</key>
		<integer>30</integer>
		<key>u1D451</key>
		<integer>50</integer>
		<key>u1D453</key>
		<integer>80</integer>
		<key>u1D454</key>
		<integer>20</integer>
		<key>u1D456</key>
		<integer>15</integer>
		<key>u1D457</key>
		<integer>15</integer>
		<key>u1D458</key>
		<integer>15</integer>
		<key>u1D459</key>
		<integer>20</integer>
		<key>u1D45A</key>
		<integer>20</integer>
		<key>u1D45B</key>
		<integer>20</integer>
		<key>u1D45E</key>
		<integer>20</integer>
		<key>u1D45F</key>
		<integer>40</integer>
		<key>u1D460</key>
		<integer>25</integer>
		<key>u1D461</key>
		<integer>15</integer>
		<key>u1D462</key>
		<integer>20</integer>
		<key>u1D463</key>
		<integer>50</integer>
		<key>u1D464</key>
		<integer>40</integer>
		<key>u1D465</key>
		<integer>40</integer>
		<key>u1D466</key>
		<integer>40</integer>
		<key>u1D467</key>
		<integer>20</integer>
		<key>u1D469</key>
		<integer>30</integer>
		<key>u1D46A</key>
		<integer>70</integer>
		<key>u1D46B</key>
		<integer>25</integer>
		<key>u1D46C</key>
		<integer>50</integer>
		<key>u1D46D</key>
		<integer>80</integer>
		<key>u1D46E</key>
		<integer>30</integer>
		<key>u1D46F</key>
		<integer>30</integer>
		<key>u1D470</key>
		<integer>30</integer>
		<key>u1D471</key>
		<integer>30</integer>
		<key>u1D472</key>
		<integer>90</integer>
		<key>u1D474</key>
		<integer>40</integer>
		<key>u1D475</key>
		<integer>30</integer>
		<key>u1D476</key>
		<integer>30</integer>
		<key>u1D477</key>
		<integer>45</integer>
		<key>u1D478</key>
		<integer>30</integer>
		<key>u1D479</key>
		<integer>30</integer>
		<key>u1D47A</key>
		<integer>40</integer>
		<key>u1D47B</key>
		<integer>95</integer>
		<key>u1D47C</key>
		<integer>40</integer>
		<key>u1D47D</key>
		<integer>110</integer>
		<key>u1D47E</key>
		<integer>90</integer>
		<key>u1D47F</key>
		<integer>90</integer>
		<key>u1D480</key>
		<integer>110</integer>
		<key>u1D481</key>
		<integer>60</integer>
		<key>u1D482</key>
		<integer>10</integer>
		<key>u1D484</key>
		<integer>40</integer>
		<key>u1D485</key>
		<integer>60</integer>
		<key>u1D486</key>
		<integer>20</integer>
		<key>u1D487</key>
		<integer>100</integer>
		<key>u1D488</key>
		<integer>30</integer>
		<key>u1D489</key>
		<integer>10</integer>
		<key>u1D48A</key>
		<integer>30</integer>
		<key>u1D48B</key>
		<integer>30</integer>
		<key>u1D48C</key>
		<integer>30</integer>
		<key>u1D48D</key>
		<integer>25</integer>
		<key>u1D48E</key>
		<integer>20</integer>
		<key>u1D48F</key>
		<integer>20</integer>
		<key>u1D492</key>
		<integer>30</integer>
		<key>u1D493</key>
		<integer>60</integer>
		<key>u1D494</key>
		<integer>30</integer>
		<key>u1D495</key>
		<integer>25</integer>
		<key>u1D496</key>
		<integer>30</integer>
		<key>u1D497</key>
		<integer>70</integer>
		<key>u1D498</key>
		<integer>60</integer>
		<key>u1D499</key>
		<integer>50</integer>
		<key>u1D49A</key>
		<integer>70</integer>
		<key>u1D49B</key>
		<integer>30</integer>
		<key>u1D6A4</key>
		<integer>15</integer>
		<key>u1D6A5</key>
		<integer>15</integer>
		<key>u1D6E3</key>
		<integer>30</integer>
		<key>u1D6E4</key>
		<integer>90</integer>
		<key>u1D6E6</key>
		<integer>60</integer>
		<key>u1D6E7</key>
		<integer>50</integer>
		<key>u1D6E8</key>
		<integer>20</integer>
		<key>u1D6E9</key>
		<integer>15</integer>
		<key>u1D6EA</key>
		<integer>20</integer>
		<key>u1D6EB</key>
		<integer>80</integer>
		<key>u1D6ED</key>
		<integer>20</integer>
		<key>u1D6EE</key>
		<integer>20</integer>
		<key>u1D6EF</key>
		<integer>20</integer>
		<key>u1D6F0</key>
		<integer>15</integer>
		<key>u1D6F1</key>
		<integer>20</integer>
		<key>u1D6F2</key>
		<integer>40</integer>
		<key>u1D6F3</key>
		<integer>15</integer>
		<key>u1D6F4</key>
		<integer>40</integer>
		<key>u1D6F5</key>
		<integer>90</integer>
		<key>u1D6F6</key>
		<integer>90</integer>
		<key>u1D6F7</key>
		<integer>20</integer>
		<key>u1D6F8</key>
		<integer>70</integer>
		<key>u1D6F9</key>
		<integer>50</integer>
		<key>u1D6FA</key>
		<integer>20</integer>
		<key>u1D6FB</key>
		<integer>20</integer>
		<key>u1D6FC</key>
		<integer>15</integer>
		<key>u1D6FD</key>
		<integer>20</integer>
		<key>u1D6FE</key>
		<integer>50</integer>
		<key>u1D6FF</key>
		<integer>15</integer>
		<key>u1D700</key>
		<integer>15</integer>
		<key>u1D701</key>
		<integer>110</integer>
		<key>u1D702</key>
		<integer>20</integer>
		<key>u1D703</key>
		<integer>20</integer>
		<key>u1D705</key>
		<integer>20</integer>
		<key>u1D707</key>
		<integer>15</integer>
		<key>u1D708</key>
		<integer>50</integer>
		<key>u1D709</key>
		<integer>90</integer>
		<key>u1D70A</key>
		<integer>10</integer>
		<key>u1D70B</key>
		<integer>40</integer>
		<key>u1D70C</key>
		<integer>15</integer>
		<key>u1D70D</key>
		<integer>50</integer>
		<key>u1D70E</key>
		<integer>40</integer>
		<key>u1D70F</key>
		<integer>60</integer>
		<key>u1D710</key>
		<integer>15</integer>
		<key>u1D711</key>
		<integer>15</integer>
		<key>u1D712</key>
		<integer>40</integer>
		<key>u1D713</key>
		<integer>15</integer>
		<key>u1D714</key>
		<integer>15</integer>
		<key>u1D715</key>
		<integer>10</integer>
		<key>u1D716</key>
		<integer>15</integer>
		<key>u1D717</key>
		<integer>25</integer>
		<key>u1D718</key>
		<integer>15</integer>
		<key>u1D719</key>
		<integer>20</integer>
		<key>u1D71A</key>
		<integer>15</integer>
		<key>u1D71B</key>
		<integer>30</integer>
		<key>u1D71D</key>
		<integer>30</integer>
		<key>u1D71E</key>
		<integer>100</integer>
		<key>u1D720</key>
		<integer>50</integer>
		<key>u1D721</key>
		<integer>60</integer>
		<key>u1D722</key>
		<integer>30</integer>
		<key>u1D723</key>
		<integer>30</integer>
		<key>u1D724</key>
		<integer>30</integer>
		<key>u1D725</key>
		<integer>90</integer>
		<key>u1D727</key>
		<integer>40</integer>
		<key>u1D728</key>
		<integer>30</integer>
		<key>u1D729</key>
		<integer>50</integer>
		<key>u1D72A</key>
		<integer>20</integer>
		<key>u1D72B</key>
		<integer>30</integer>
		<key>u1D72C</key>
		<integer>45</integer>
		<key>u1D72D</key>
		<integer>30</integer>
		<key>u1D72E</key>
		<integer>60</integer>
		<key>u1D72F</key>
		<integer>95</integer>
		<key>u1D730</key>
		<integer>110</integer>
		<key>u1D731</key>
		<integer>30</integer>
		<key>u1D732</key>
		<integer>90</integer>
		<key>u1D733</key>
		<integer>60</integer>
		<key>u1D734</key>
		<integer>30</integer>
		<key>u1D735</key>
		<integer>25</integer>
		<key>u1D736</key>
		<integer>20</integer>
		<key>u1D737</key>
		<integer>30</integer>
		<key>u1D738</key>
		<integer>60</integer>
		<key>u1D739</key>
		<integer>25</integer>
		<key>u1D73A</key>
		<integer>20</integer>
		<key>u1D73B</key>
		<integer>110</integer>
		<key>u1D73C</key>
		<integer>20</integer>
		<key>u1D73D</key>
		<integer>30</integer>
		<key>u1D73E</key>
		<integer>10</integer>
		<key>u1D73F</key>
		<integer>30</integer>
		<key>u1D741</key>
		<integer>20</integer>
		<key>u1D742</key>
		<integer>60</integer>
		<key>u1D743</key>
		<integer>100</integer>
		<key>u1D744</key>
		<integer>15</integer>
		<key>u1D745</key>
		<integer>45</integer>
		<key>u1D746</key>
		<integer>20</integer>
		<key>u1D747</key>
		<integer>55</integer>
		<key>u1D748</key>
		<integer>50</integer>
		<key>u1D749</key>
		<integer>70</integer>
		<key>u1D74A</key>
		<integer>15</integer>
		<key>u1D74B</key>
		<integer>20</integer>
		<key>u1D74C</key>
		<integer>50</integer>
		<key>u1D74D</key>
		<integer>20</integer>
		<key>u1D74E</key>
		<integer>15</integer>
		<key>u1D74F</key>
		<integer>15</integer>
		<key>u1D750</key>
		<integer>30</integer>
		<key>u1D751</key>
		<integer>35</integer>
		<key>u1D752</key>
		<integer>20</integer>
		<key>u1D753</key>
		<integer>20</integer>
		<key>u1D754</key>
		<integer>15</integer>
		<key>u1D755</key>
		<integer>40</integer>
		<key>uni0066</key>
		<integer>80</integer>
		<key>uni0072</key>
		<integer>20</integer>
		<key>uni0074</key>
		<integer>20</integer>
		<key>uni0076</key>
		<integer>30</integer>
		<key>uni0077</key>
		<integer>20</integer>
		<key>uni0079</key>
		<integer>20</integer>
		<key>uni210E</key>
		<integer>20</integer>
		<key>uni210F</key>
		<integer>20</integer>
		<key>uni222B</key>
		<integer>240</integer>
		<key>uni222B.display</key>
		<integer>580</integer>
		<key>uni222B.display.up</key>
		<integer>280</integer>
		<key>uni222B.up</key>
		<integer>180</integer>
		<key>uni222C</key>
		<integer>240</integer>
		<key>uni222C.display</key>
		<integer>580</integer>
		<key>uni222C.display.up</key>
		<integer>280</integer>
		<key>uni222C.up</key>
		<integer>180</integer>
		<key>uni222D</key>
		<integer>240</integer>
		<key>uni222D.display</key>
		<integer>580</integer>
		<key>uni222D.display.up</key>
		<integer>280</integer>
		<key>uni222D.up</key>
		<integer>180</integer>
		<key>uni222E</key>
		<integer>240</integer>
		<key>uni222E.display</key>
		<integer>580</integer>
		<key>uni222E.display.up</key>
		<integer>280</integer>
		<key>uni222E.up</key>
		<integer>180</integer>
		<key>uni222F</key>
		<integer>240</integer>
		<key>uni222F.display</key>
		<integer>580</integer>
		<key>uni222F.display.up</key>
		<integer>280</integer>
		<key>uni222F.up</key>
		<integer>180</integer>
		<key>uni2230</key>
		<integer>240</integer>
		<key>uni2230.display</key>
		<integer>580</integer>
		<key>uni2230.display.up</key>
		<integer>280</integer>
		<key>uni2230.up</key>
		<integer>180</integer>
		<key>uni2A0C</key>
		<integer>240</integer>
		<key>uni2A0C.display</key>
		<integer>580</integer>
		<key>uni2A0C.display.up</key>
		<integer>280</integer>
		<key>uni2A0C.up</key>
		<integer>180</integer>
	</dict>
	<key>v_assembly</key>
	<dict>
		<key>uni0028</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239D</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239C</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239B</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni0029</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A0</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni239F</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni239E</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni005B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A3</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A2</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A1</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni005D</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A6</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23A5</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A4</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni007B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1400</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A9</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A8</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1400</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23A7</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni007C</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni007C.ext</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni007C.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni007D</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1400</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AD</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2000</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AC</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni23AA</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1400</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni23AB</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni2016</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2016.ext</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2016.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni221A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3800</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221A.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221A.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni221B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3800</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221B.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221B.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221B.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni221C</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>3800</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221C.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni221C.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni221C.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni2308</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2308.ext</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2308.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni2309</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2309.ext</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2309.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni230A</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni230A.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni230A.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni230B</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>1600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni230B.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni230B.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EE</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EE.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27EE.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EE.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni27EF</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EF.bot</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni27EF.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>2956</integer>
					<key>endConnector</key>
					<integer>0</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni27EF.top</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
		<key>uni2980</key>
		<dict>
			<key>italic</key>
			<integer>0</integer>
			<key>parts</key>
			<array>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<false/>
					<key>glyph</key>
					<string>uni2980.ext</string>
					<key>startConnector</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>advance</key>
					<integer>600</integer>
					<key>endConnector</key>
					<integer>150</integer>
					<key>extender</key>
					<true/>
					<key>glyph</key>
					<string>uni2980.ext</string>
					<key>startConnector</key>
					<integer>150</integer>
				</dict>
			</array>
		</dict>
	</dict>
	<key>v_variants</key>
	<dict>
		<key>uni0028</key>
		<array>
			<string>uni0028</string>
			<string>uni0028.size1</string>
			<string>uni0028.size2</string>
			<string>uni0028.size3</string>
			<string>uni0028.size4</string>
			<string>uni0028.size5</string>
			<string>uni0028.size6</string>
			<string>uni0028.size7</string>
			<string>uni0028.size8</string>
			<string>uni0028.size9</string>
			<string>uni0028.size10</string>
			<string>uni0028.size11</string>
			<string>uni0028.size12</string>
			<string>uni0028.size13</string>
			<string>uni0028.size14</string>
			<string>uni0028.size15</string>
		</array>
		<key>uni0029</key>
		<array>
			<string>uni0029</string>
			<string>uni0029.size1</string>
			<string>uni0029.size2</string>
			<string>uni0029.size3</string>
			<string>uni0029.size4</string>
			<string>uni0029.size5</string>
			<string>uni0029.size6</string>
			<string>uni0029.size7</string>
			<string>uni0029.size8</string>
			<string>uni0029.size9</string>
			<string>uni0029.size10</string>
			<string>uni0029.size11</string>
			<string>uni0029.size12</string>
			<string>uni0029.size13</string>
			<string>uni0029.size14</string>
			<string>uni0029.size15</string>
		</array>
		<key>uni005B</key>
		<array>
			<string>uni005B</string>
			<string>uni005B.size1</string>
			<string>uni005B.size2</string>
			<string>uni005B.size3</string>
			<string>uni005B.size4</string>
			<string>uni005B.size5</string>
			<string>uni005B.size6</string>
			<string>uni005B.size7</string>
			<string>uni005B.size8</string>
			<string>uni005B.size9</string>
			<string>uni005B.size10</string>
			<string>uni005B.size11</string>
			<string>uni005B.size12</string>
			<string>uni005B.size13</string>
			<string>uni005B.size14</string>
			<string>uni005B.size15</string>
		</array>
		<key>uni005D</key>
		<array>
			<string>uni005D</string>
			<string>uni005D.size1</string>
			<string>uni005D.size2</string>
			<string>uni005D.size3</string>
			<string>uni005D.size4</string>
			<string>uni005D.size5</string>
			<string>uni005D.size6</string>
			<string>uni005D.size7</string>
			<string>uni005D.size8</string>
			<string>uni005D.size9</string>
			<string>uni005D.size10</string>
			<string>uni005D.size11</string>
			<string>uni005D.size12</string>
			<string>uni005D.size13</string>
			<string>uni005D.size14</string>
			<string>uni005D.size15</string>
		</array>
		<key>uni007B</key>
		<array>
			<string>uni007B</string>
			<string>uni007B.size1</string>
			<string>uni007B.size2</string>
			<string>uni007B.size3</string>
			<string>uni007B.size4</string>
			<string>uni007B.size5</string>
			<string>uni007B.size6</string>
			<string>uni007B.size7</string>
			<string>uni007B.size8</string>
			<string>uni007B.size9</string>
			<string>uni007B.size10</string>
			<string>uni007B.size11</string>
			<string>uni007B.size12</string>
			<string>uni007B.size13</string>
			<string>uni007B.size14</string>
			<string>uni007B.size15</string>
		</array>
		<key>uni007C</key>
		<array>
			<string>uni007C</string>
			<string>uni007C.size1</string>
			<string>uni007C.size2</string>
			<string>uni007C.size3</string>
			<string>uni007C.size4</string>
			<string>uni007C.size5</string>
			<string>uni007C.size6</string>
			<string>uni007C.size7</string>
			<string>uni007C.size8</string>
			<string>uni007C.size9</string>
			<string>uni007C.size10</string>
			<string>uni007C.size11</string>
			<string>uni007C.size12</string>
			<string>uni007C.size13</string>
			<string>uni007C.size14</string>
			<string>uni007C.size15</string>
		</array>
		<key>uni007D</key>
		<array>
			<string>uni007D</string>
			<string>uni007D.size1</string>
			<string>uni007D.size2</string>
			<string>uni007D.size3</string>
			<string>uni007D.size4</string>
			<string>uni007D.size5</string>
			<string>uni007D.size6</string>
			<string>uni007D.size7</string>
			<string>uni007D.size8</string>
			<string>uni007D.size9</string>
			<string>uni007D.size10</string>
			<string>uni007D.size11</string>
			<string>uni007D.size12</string>
			<string>uni007D.size13</string>
			<string>uni007D.size14</string>
			<string>uni007D.size15</string>
		</array>
		<key>uni2016</key>
		<array>
			<string>uni2016</string>
			<string>uni2016.size1</string>
			<string>uni2016.size2</string>
			<string>uni2016.size3</string>
			<string>uni2016.size4</string>
			<string>uni2016.size5</string>
			<string>uni2016.size6</string>
			<string>uni2016.size7</string>
			<string>uni2016.size8</string>
			<string>uni2016.size9</string>
			<string>uni2016.size10</string>
			<string>uni2016.size11</string>
			<string>uni2016.size12</string>
			<string>uni2016.size13</string>
			<string>uni2016.size14</string>
			<string>uni2016.size15</string>
		</array>
		<key>uni2140</key>
		<array>
			<string>uni2140</string>
			<string>uni2140.display</string>
		</array>
		<key>uni220F</key>
		<array>
			<string>uni220F</string>
			<string>uni220F.display</string>
		</array>
		<key>uni2210</key>
		<array>
			<string>uni2210</string>
			<string>uni2210.display</string>
		</array>
		<key>uni2211</key>
		<array>
			<string>uni2211</string>
			<string>uni2211.display</string>
		</array>
		<key>uni221A</key>
		<array>
			<string>uni221A</string>
			<string>uni221A.size1</string>
			<string>uni221A.size2</string>
			<string>uni221A.size3</string>
			<string>uni221A.size4</string>
			<string>uni221A.size5</string>
			<string>uni221A.size6</string>
			<string>uni221A.size7</string>
			<string>uni221A.size8</string>
			<string>uni221A.size9</string>
			<string>uni221A.size10</string>
			<string>uni221A.size11</string>
			<string>uni221A.size12</string>
			<string>uni221A.size13</string>
			<string>uni221A.size14</string>
			<string>uni221A.size15</string>
		</array>
		<key>uni221B</key>
		<array>
			<string>uni221B</string>
			<string>uni221B.size1</string>
			<string>uni221B.size2</string>
			<string>uni221B.size3</string>
			<string>uni221B.size4</string>
			<string>uni221B.size5</string>
			<string>uni221B.size6</string>
			<string>uni221B.size7</string>
			<string>uni221B.size8</string>
			<string>uni221B.size9</string>
			<string>uni221B.size10</string>
			<string>uni221B.size11</string>
			<string>uni221B.size12</string>
			<string>uni221B.size13</string>
			<string>uni221B.size14</string>
			<string>uni221B.size15</string>
		</array>
		<key>uni221C</key>
		<array>
			<string>uni221C</string>
			<string>uni221C.size1</string>
			<string>uni221C.size2</string>
			<string>uni221C.size3</string>
			<string>uni221C.size4</string>
			<string>uni221C.size5</string>
			<string>uni221C.size6</string>
			<string>uni221C.size7</string>
			<string>uni221C.size8</string>
			<string>uni221C.size9</string>
			<string>uni221C.size10</string>
			<string>uni221C.size11</string>
			<string>uni221C.size12</string>
			<string>uni221C.size13</string>
			<string>uni221C.size14</string>
			<string>uni221C.size15</string>
		</array>
		<key>uni222B</key>
		<array>
			<string>uni222B</string>
			<string>uni222B.display</string>
		</array>
		<key>uni222B.up</key>
		<array>
			<string>uni222B.up</string>
			<string>uni222B.display.up</string>
		</array>
		<key>uni222C</key>
		<array>
			<string>uni222C</string>
			<string>uni222C.display</string>
		</array>
		<key>uni222C.up</key>
		<array>
			<string>uni222C.up</string>
			<string>uni222C.display.up</string>
		</array>
		<key>uni222D</key>
		<array>
			<string>uni222D</string>
			<string>uni222D.display</string>
		</array>
		<key>uni222D.up</key>
		<array>
			<string>uni222D.up</string>
			<string>uni222D.display.up</string>
		</array>
		<key>uni222E</key>
		<array>
			<string>uni222E</string>
			<string>uni222E.display</string>
		</array>
		<key>uni222E.up</key>
		<array>
			<string>uni222E.up</string>
			<string>uni222E.display.up</string>
		</array>
		<key>uni222F</key>
		<array>
			<string>uni222F</string>
			<string>uni222F.display</string>
		</array>
		<key>uni222F.up</key>
		<array>
			<string>uni222F.up</string>
			<string>uni222F.display.up</string>
		</array>
		<key>uni2230</key>
		<array>
			<string>uni2230</string>
			<string>uni2230.display</string>
		</array>
		<key>uni2230.up</key>
		<array>
			<string>uni2230.up</string>
			<string>uni2230.display.up</string>
		</array>
		<key>uni2308</key>
		<array>
			<string>uni2308</string>
			<string>uni2308.size1</string>
			<string>uni2308.size2</string>
			<string>uni2308.size3</string>
			<string>uni2308.size4</string>
			<string>uni2308.size5</string>
			<string>uni2308.size6</string>
			<string>uni2308.size7</string>
			<string>uni2308.size8</string>
			<string>uni2308.size9</string>
			<string>uni2308.size10</string>
			<string>uni2308.size11</string>
			<string>uni2308.size12</string>
			<string>uni2308.size13</string>
			<string>uni2308.size14</string>
			<string>uni2308.size15</string>
		</array>
		<key>uni2309</key>
		<array>
			<string>uni2309</string>
			<string>uni2309.size1</string>
			<string>uni2309.size2</string>
			<string>uni2309.size3</string>
			<string>uni2309.size4</string>
			<string>uni2309.size5</string>
			<string>uni2309.size6</string>
			<string>uni2309.size7</string>
			<string>uni2309.size8</string>
			<string>uni2309.size9</string>
			<string>uni2309.size10</string>
			<string>uni2309.size11</string>
			<string>uni2309.size12</string>
			<string>uni2309.size13</string>
			<string>uni2309.size14</string>
			<string>uni2309.size15</string>
		</array>
		<key>uni230A</key>
		<array>
			<string>uni230A</string>
			<string>uni230A.size1</string>
			<string>uni230A.size2</string>
			<string>uni230A.size3</string>
			<string>uni230A.size4</string>
			<string>uni230A.size5</string>
			<string>uni230A.size6</string>
			<string>uni230A.size7</string>
			<string>uni230A.size8</string>
			<string>uni230A.size9</string>
			<string>uni230A.size10</string>
			<string>uni230A.size11</string>
			<string>uni230A.size12</string>
			<string>uni230A.size13</string>
			<string>uni230A.size14</string>
			<string>uni230A.size15</string>
		</array>
		<key>uni230B</key>
		<array>
			<string>uni230B</string>
			<string>uni230B.size1</string>
			<string>uni230B.size2</string>
			<string>uni230B.size3</string>
			<string>uni230B.size4</string>
			<string>uni230B.size5</string>
			<string>uni230B.size6</string>
			<string>uni230B.size7</string>
			<string>uni230B.size8</string>
			<string>uni230B.size9</string>
			<string>uni230B.size10</string>
			<string>uni230B.size11</string>
			<string>uni230B.size12</string>
			<string>uni230B.size13</string>
			<string>uni230B.size14</string>
			<string>uni230B.size15</string>
		</array>
		<key>uni27E8</key>
		<array>
			<string>uni27E8</string>
			<string>uni27E8.size1</string>
			<string>uni27E8.size2</string>
			<string>uni27E8.size3</string>
			<string>uni27E8.size4</string>
			<string>uni27E8.size5</string>
			<string>uni27E8.size6</string>
			<string>uni27E8.size7</string>
			<string>uni27E8.size8</string>
			<string>uni27E8.size9</string>
			<string>uni27E8.size10</string>
			<string>uni27E8.size11</string>
			<string>uni27E8.size12</string>
			<string>uni27E8.size13</string>
			<string>uni27E8.size14</string>
			<string>uni27E8.size15</string>
		</array>
		<key>uni27E9</key>
		<array>
			<string>uni27E9</string>
			<string>uni27E9.size1</string>
			<string>uni27E9.size2</string>
			<string>uni27E9.size3</string>
			<string>uni27E9.size4</string>
			<string>uni27E9.size5</string>
			<string>uni27E9.size6</string>
			<string>uni27E9.size7</string>
			<string>uni27E9.size8</string>
			<string>uni27E9.size9</string>
			<string>uni27E9.size10</string>
			<string>uni27E9.size11</string>
			<string>uni27E9.size12</string>
			<string>uni27E9.size13</string>
			<string>uni27E9.size14</string>
			<string>uni27E9.size15</string>
		</array>
		<key>uni27EA</key>
		<array>
			<string>uni27EA</string>
			<string>uni27EA.size1</string>
			<string>uni27EA.size2</string>
			<string>uni27EA.size3</string>
			<string>uni27EA.size4</string>
			<string>uni27EA.size5</string>
			<string>uni27EA.size6</string>
			<string>uni27EA.size7</string>
			<string>uni27EA.size8</string>
			<string>uni27EA.size9</string>
			<string>uni27EA.size10</string>
			<string>uni27EA.size11</string>
			<string>uni27EA.size12</string>
			<string>uni27EA.size13</string>
			<string>uni27EA.size14</string>
			<string>uni27EA.size15</string>
		</array>
		<key>uni27EB</key>
		<array>
			<string>uni27EB</string>
			<string>uni27EB.size1</string>
			<string>uni27EB.size2</string>
			<string>uni27EB.size3</string>
			<string>uni27EB.size4</string>
			<string>uni27EB.size5</string>
			<string>uni27EB.size6</string>
			<string>uni27EB.size7</string>
			<string>uni27EB.size8</string>
			<string>uni27EB.size9</string>
			<string>uni27EB.size10</string>
			<string>uni27EB.size11</string>
			<string>uni27EB.size12</string>
			<string>uni27EB.size13</string>
			<string>uni27EB.size14</string>
			<string>uni27EB.size15</string>
		</array>
		<key>uni27EE</key>
		<array>
			<string>uni27EE</string>
			<string>uni27EE.size1</string>
			<string>uni27EE.size2</string>
			<string>uni27EE.size3</string>
			<string>uni27EE.size4</string>
			<string>uni27EE.size5</string>
			<string>uni27EE.size6</string>
			<string>uni27EE.size7</string>
			<string>uni27EE.size8</string>
			<string>uni27EE.size9</string>
			<string>uni27EE.size10</string>
			<string>uni27EE.size11</string>
			<string>uni27EE.size12</string>
			<string>uni27EE.size13</string>
			<string>uni27EE.size14</string>
			<string>uni27EE.size15</string>
		</array>
		<key>uni27EF</key>
		<array>
			<string>uni27EF</string>
			<string>uni27EF.size1</string>
			<string>uni27EF.size2</string>
			<string>uni27EF.size3</string>
			<string>uni27EF.size4</string>
			<string>uni27EF.size5</string>
			<string>uni27EF.size6</string>
			<string>uni27EF.size7</string>
			<string>uni27EF.size8</string>
			<string>uni27EF.size9</string>
			<string>uni27EF.size10</string>
			<string>uni27EF.size11</string>
			<string>uni27EF.size12</string>
			<string>uni27EF.size13</string>
			<string>uni27EF.size14</string>
			<string>uni27EF.size15</string>
		</array>
		<key>uni2980</key>
		<array>
			<string>uni2980</string>
			<string>uni2980.size1</string>
			<string>uni2980.size2</string>
			<string>uni2980.size3</string>
			<string>uni2980.size4</string>
			<string>uni2980.size5</string>
			<string>uni2980.size6</string>
			<string>uni2980.size7</string>
			<string>uni2980.size8</string>
			<string>uni2980.size9</string>
			<string>uni2980.size10</string>
			<string>uni2980.size11</string>
			<string>uni2980.size12</string>
			<string>uni2980.size13</string>
			<string>uni2980.size14</string>
			<string>uni2980.size15</string>
		</array>
		<key>uni2A0C</key>
		<array>
			<string>uni2A0C</string>
			<string>uni2A0C.display</string>
		</array>
		<key>uni2A0C.up</key>
		<array>
			<string>uni2A0C.up</string>
			<string>uni2A0C.display.up</string>
		</array>
	</dict>
	<key>version</key>
	<string>1.3</string>
</dict>
</plist>
