    switch (c) {
      case 0x0041:
        bufpush(0x0061);
        break;
      case 0x0042:
        bufpush(0x0062);
        break;
      case 0x0043:
        bufpush(0x0063);
        break;
      case 0x0044:
        bufpush(0x0064);
        break;
      case 0x0045:
        bufpush(0x0065);
        break;
      case 0x0046:
        bufpush(0x0066);
        break;
      case 0x0047:
        bufpush(0x0067);
        break;
      case 0x0048:
        bufpush(0x0068);
        break;
      case 0x0049:
        bufpush(0x0069);
        break;
      case 0x004A:
        bufpush(0x006A);
        break;
      case 0x004B:
        bufpush(0x006B);
        break;
      case 0x004C:
        bufpush(0x006C);
        break;
      case 0x004D:
        bufpush(0x006D);
        break;
      case 0x004E:
        bufpush(0x006E);
        break;
      case 0x004F:
        bufpush(0x006F);
        break;
      case 0x0050:
        bufpush(0x0070);
        break;
      case 0x0051:
        bufpush(0x0071);
        break;
      case 0x0052:
        bufpush(0x0072);
        break;
      case 0x0053:
        bufpush(0x0073);
        break;
      case 0x0054:
        bufpush(0x0074);
        break;
      case 0x0055:
        bufpush(0x0075);
        break;
      case 0x0056:
        bufpush(0x0076);
        break;
      case 0x0057:
        bufpush(0x0077);
        break;
      case 0x0058:
        bufpush(0x0078);
        break;
      case 0x0059:
        bufpush(0x0079);
        break;
      case 0x005A:
        bufpush(0x007A);
        break;
      case 0x00B5:
        bufpush(0x03BC);
        break;
      case 0x00C0:
        bufpush(0x00E0);
        break;
      case 0x00C1:
        bufpush(0x00E1);
        break;
      case 0x00C2:
        bufpush(0x00E2);
        break;
      case 0x00C3:
        bufpush(0x00E3);
        break;
      case 0x00C4:
        bufpush(0x00E4);
        break;
      case 0x00C5:
        bufpush(0x00E5);
        break;
      case 0x00C6:
        bufpush(0x00E6);
        break;
      case 0x00C7:
        bufpush(0x00E7);
        break;
      case 0x00C8:
        bufpush(0x00E8);
        break;
      case 0x00C9:
        bufpush(0x00E9);
        break;
      case 0x00CA:
        bufpush(0x00EA);
        break;
      case 0x00CB:
        bufpush(0x00EB);
        break;
      case 0x00CC:
        bufpush(0x00EC);
        break;
      case 0x00CD:
        bufpush(0x00ED);
        break;
      case 0x00CE:
        bufpush(0x00EE);
        break;
      case 0x00CF:
        bufpush(0x00EF);
        break;
      case 0x00D0:
        bufpush(0x00F0);
        break;
      case 0x00D1:
        bufpush(0x00F1);
        break;
      case 0x00D2:
        bufpush(0x00F2);
        break;
      case 0x00D3:
        bufpush(0x00F3);
        break;
      case 0x00D4:
        bufpush(0x00F4);
        break;
      case 0x00D5:
        bufpush(0x00F5);
        break;
      case 0x00D6:
        bufpush(0x00F6);
        break;
      case 0x00D8:
        bufpush(0x00F8);
        break;
      case 0x00D9:
        bufpush(0x00F9);
        break;
      case 0x00DA:
        bufpush(0x00FA);
        break;
      case 0x00DB:
        bufpush(0x00FB);
        break;
      case 0x00DC:
        bufpush(0x00FC);
        break;
      case 0x00DD:
        bufpush(0x00FD);
        break;
      case 0x00DE:
        bufpush(0x00FE);
        break;
      case 0x00DF:
        bufpush(0x0073);
        bufpush(0x0073);
        break;
      case 0x0100:
        bufpush(0x0101);
        break;
      case 0x0102:
        bufpush(0x0103);
        break;
      case 0x0104:
        bufpush(0x0105);
        break;
      case 0x0106:
        bufpush(0x0107);
        break;
      case 0x0108:
        bufpush(0x0109);
        break;
      case 0x010A:
        bufpush(0x010B);
        break;
      case 0x010C:
        bufpush(0x010D);
        break;
      case 0x010E:
        bufpush(0x010F);
        break;
      case 0x0110:
        bufpush(0x0111);
        break;
      case 0x0112:
        bufpush(0x0113);
        break;
      case 0x0114:
        bufpush(0x0115);
        break;
      case 0x0116:
        bufpush(0x0117);
        break;
      case 0x0118:
        bufpush(0x0119);
        break;
      case 0x011A:
        bufpush(0x011B);
        break;
      case 0x011C:
        bufpush(0x011D);
        break;
      case 0x011E:
        bufpush(0x011F);
        break;
      case 0x0120:
        bufpush(0x0121);
        break;
      case 0x0122:
        bufpush(0x0123);
        break;
      case 0x0124:
        bufpush(0x0125);
        break;
      case 0x0126:
        bufpush(0x0127);
        break;
      case 0x0128:
        bufpush(0x0129);
        break;
      case 0x012A:
        bufpush(0x012B);
        break;
      case 0x012C:
        bufpush(0x012D);
        break;
      case 0x012E:
        bufpush(0x012F);
        break;
      case 0x0130:
        bufpush(0x0069);
        bufpush(0x0307);
        break;
      case 0x0132:
        bufpush(0x0133);
        break;
      case 0x0134:
        bufpush(0x0135);
        break;
      case 0x0136:
        bufpush(0x0137);
        break;
      case 0x0139:
        bufpush(0x013A);
        break;
      case 0x013B:
        bufpush(0x013C);
        break;
      case 0x013D:
        bufpush(0x013E);
        break;
      case 0x013F:
        bufpush(0x0140);
        break;
      case 0x0141:
        bufpush(0x0142);
        break;
      case 0x0143:
        bufpush(0x0144);
        break;
      case 0x0145:
        bufpush(0x0146);
        break;
      case 0x0147:
        bufpush(0x0148);
        break;
      case 0x0149:
        bufpush(0x02BC);
        bufpush(0x006E);
        break;
      case 0x014A:
        bufpush(0x014B);
        break;
      case 0x014C:
        bufpush(0x014D);
        break;
      case 0x014E:
        bufpush(0x014F);
        break;
      case 0x0150:
        bufpush(0x0151);
        break;
      case 0x0152:
        bufpush(0x0153);
        break;
      case 0x0154:
        bufpush(0x0155);
        break;
      case 0x0156:
        bufpush(0x0157);
        break;
      case 0x0158:
        bufpush(0x0159);
        break;
      case 0x015A:
        bufpush(0x015B);
        break;
      case 0x015C:
        bufpush(0x015D);
        break;
      case 0x015E:
        bufpush(0x015F);
        break;
      case 0x0160:
        bufpush(0x0161);
        break;
      case 0x0162:
        bufpush(0x0163);
        break;
      case 0x0164:
        bufpush(0x0165);
        break;
      case 0x0166:
        bufpush(0x0167);
        break;
      case 0x0168:
        bufpush(0x0169);
        break;
      case 0x016A:
        bufpush(0x016B);
        break;
      case 0x016C:
        bufpush(0x016D);
        break;
      case 0x016E:
        bufpush(0x016F);
        break;
      case 0x0170:
        bufpush(0x0171);
        break;
      case 0x0172:
        bufpush(0x0173);
        break;
      case 0x0174:
        bufpush(0x0175);
        break;
      case 0x0176:
        bufpush(0x0177);
        break;
      case 0x0178:
        bufpush(0x00FF);
        break;
      case 0x0179:
        bufpush(0x017A);
        break;
      case 0x017B:
        bufpush(0x017C);
        break;
      case 0x017D:
        bufpush(0x017E);
        break;
      case 0x017F:
        bufpush(0x0073);
        break;
      case 0x0181:
        bufpush(0x0253);
        break;
      case 0x0182:
        bufpush(0x0183);
        break;
      case 0x0184:
        bufpush(0x0185);
        break;
      case 0x0186:
        bufpush(0x0254);
        break;
      case 0x0187:
        bufpush(0x0188);
        break;
      case 0x0189:
        bufpush(0x0256);
        break;
      case 0x018A:
        bufpush(0x0257);
        break;
      case 0x018B:
        bufpush(0x018C);
        break;
      case 0x018E:
        bufpush(0x01DD);
        break;
      case 0x018F:
        bufpush(0x0259);
        break;
      case 0x0190:
        bufpush(0x025B);
        break;
      case 0x0191:
        bufpush(0x0192);
        break;
      case 0x0193:
        bufpush(0x0260);
        break;
      case 0x0194:
        bufpush(0x0263);
        break;
      case 0x0196:
        bufpush(0x0269);
        break;
      case 0x0197:
        bufpush(0x0268);
        break;
      case 0x0198:
        bufpush(0x0199);
        break;
      case 0x019C:
        bufpush(0x026F);
        break;
      case 0x019D:
        bufpush(0x0272);
        break;
      case 0x019F:
        bufpush(0x0275);
        break;
      case 0x01A0:
        bufpush(0x01A1);
        break;
      case 0x01A2:
        bufpush(0x01A3);
        break;
      case 0x01A4:
        bufpush(0x01A5);
        break;
      case 0x01A6:
        bufpush(0x0280);
        break;
      case 0x01A7:
        bufpush(0x01A8);
        break;
      case 0x01A9:
        bufpush(0x0283);
        break;
      case 0x01AC:
        bufpush(0x01AD);
        break;
      case 0x01AE:
        bufpush(0x0288);
        break;
      case 0x01AF:
        bufpush(0x01B0);
        break;
      case 0x01B1:
        bufpush(0x028A);
        break;
      case 0x01B2:
        bufpush(0x028B);
        break;
      case 0x01B3:
        bufpush(0x01B4);
        break;
      case 0x01B5:
        bufpush(0x01B6);
        break;
      case 0x01B7:
        bufpush(0x0292);
        break;
      case 0x01B8:
        bufpush(0x01B9);
        break;
      case 0x01BC:
        bufpush(0x01BD);
        break;
      case 0x01C4:
        bufpush(0x01C6);
        break;
      case 0x01C5:
        bufpush(0x01C6);
        break;
      case 0x01C7:
        bufpush(0x01C9);
        break;
      case 0x01C8:
        bufpush(0x01C9);
        break;
      case 0x01CA:
        bufpush(0x01CC);
        break;
      case 0x01CB:
        bufpush(0x01CC);
        break;
      case 0x01CD:
        bufpush(0x01CE);
        break;
      case 0x01CF:
        bufpush(0x01D0);
        break;
      case 0x01D1:
        bufpush(0x01D2);
        break;
      case 0x01D3:
        bufpush(0x01D4);
        break;
      case 0x01D5:
        bufpush(0x01D6);
        break;
      case 0x01D7:
        bufpush(0x01D8);
        break;
      case 0x01D9:
        bufpush(0x01DA);
        break;
      case 0x01DB:
        bufpush(0x01DC);
        break;
      case 0x01DE:
        bufpush(0x01DF);
        break;
      case 0x01E0:
        bufpush(0x01E1);
        break;
      case 0x01E2:
        bufpush(0x01E3);
        break;
      case 0x01E4:
        bufpush(0x01E5);
        break;
      case 0x01E6:
        bufpush(0x01E7);
        break;
      case 0x01E8:
        bufpush(0x01E9);
        break;
      case 0x01EA:
        bufpush(0x01EB);
        break;
      case 0x01EC:
        bufpush(0x01ED);
        break;
      case 0x01EE:
        bufpush(0x01EF);
        break;
      case 0x01F0:
        bufpush(0x006A);
        bufpush(0x030C);
        break;
      case 0x01F1:
        bufpush(0x01F3);
        break;
      case 0x01F2:
        bufpush(0x01F3);
        break;
      case 0x01F4:
        bufpush(0x01F5);
        break;
      case 0x01F6:
        bufpush(0x0195);
        break;
      case 0x01F7:
        bufpush(0x01BF);
        break;
      case 0x01F8:
        bufpush(0x01F9);
        break;
      case 0x01FA:
        bufpush(0x01FB);
        break;
      case 0x01FC:
        bufpush(0x01FD);
        break;
      case 0x01FE:
        bufpush(0x01FF);
        break;
      case 0x0200:
        bufpush(0x0201);
        break;
      case 0x0202:
        bufpush(0x0203);
        break;
      case 0x0204:
        bufpush(0x0205);
        break;
      case 0x0206:
        bufpush(0x0207);
        break;
      case 0x0208:
        bufpush(0x0209);
        break;
      case 0x020A:
        bufpush(0x020B);
        break;
      case 0x020C:
        bufpush(0x020D);
        break;
      case 0x020E:
        bufpush(0x020F);
        break;
      case 0x0210:
        bufpush(0x0211);
        break;
      case 0x0212:
        bufpush(0x0213);
        break;
      case 0x0214:
        bufpush(0x0215);
        break;
      case 0x0216:
        bufpush(0x0217);
        break;
      case 0x0218:
        bufpush(0x0219);
        break;
      case 0x021A:
        bufpush(0x021B);
        break;
      case 0x021C:
        bufpush(0x021D);
        break;
      case 0x021E:
        bufpush(0x021F);
        break;
      case 0x0220:
        bufpush(0x019E);
        break;
      case 0x0222:
        bufpush(0x0223);
        break;
      case 0x0224:
        bufpush(0x0225);
        break;
      case 0x0226:
        bufpush(0x0227);
        break;
      case 0x0228:
        bufpush(0x0229);
        break;
      case 0x022A:
        bufpush(0x022B);
        break;
      case 0x022C:
        bufpush(0x022D);
        break;
      case 0x022E:
        bufpush(0x022F);
        break;
      case 0x0230:
        bufpush(0x0231);
        break;
      case 0x0232:
        bufpush(0x0233);
        break;
      case 0x023A:
        bufpush(0x2C65);
        break;
      case 0x023B:
        bufpush(0x023C);
        break;
      case 0x023D:
        bufpush(0x019A);
        break;
      case 0x023E:
        bufpush(0x2C66);
        break;
      case 0x0241:
        bufpush(0x0242);
        break;
      case 0x0243:
        bufpush(0x0180);
        break;
      case 0x0244:
        bufpush(0x0289);
        break;
      case 0x0245:
        bufpush(0x028C);
        break;
      case 0x0246:
        bufpush(0x0247);
        break;
      case 0x0248:
        bufpush(0x0249);
        break;
      case 0x024A:
        bufpush(0x024B);
        break;
      case 0x024C:
        bufpush(0x024D);
        break;
      case 0x024E:
        bufpush(0x024F);
        break;
      case 0x0345:
        bufpush(0x03B9);
        break;
      case 0x0370:
        bufpush(0x0371);
        break;
      case 0x0372:
        bufpush(0x0373);
        break;
      case 0x0376:
        bufpush(0x0377);
        break;
      case 0x037F:
        bufpush(0x03F3);
        break;
      case 0x0386:
        bufpush(0x03AC);
        break;
      case 0x0388:
        bufpush(0x03AD);
        break;
      case 0x0389:
        bufpush(0x03AE);
        break;
      case 0x038A:
        bufpush(0x03AF);
        break;
      case 0x038C:
        bufpush(0x03CC);
        break;
      case 0x038E:
        bufpush(0x03CD);
        break;
      case 0x038F:
        bufpush(0x03CE);
        break;
      case 0x0390:
        bufpush(0x03B9);
        bufpush(0x0308);
        bufpush(0x0301);
        break;
      case 0x0391:
        bufpush(0x03B1);
        break;
      case 0x0392:
        bufpush(0x03B2);
        break;
      case 0x0393:
        bufpush(0x03B3);
        break;
      case 0x0394:
        bufpush(0x03B4);
        break;
      case 0x0395:
        bufpush(0x03B5);
        break;
      case 0x0396:
        bufpush(0x03B6);
        break;
      case 0x0397:
        bufpush(0x03B7);
        break;
      case 0x0398:
        bufpush(0x03B8);
        break;
      case 0x0399:
        bufpush(0x03B9);
        break;
      case 0x039A:
        bufpush(0x03BA);
        break;
      case 0x039B:
        bufpush(0x03BB);
        break;
      case 0x039C:
        bufpush(0x03BC);
        break;
      case 0x039D:
        bufpush(0x03BD);
        break;
      case 0x039E:
        bufpush(0x03BE);
        break;
      case 0x039F:
        bufpush(0x03BF);
        break;
      case 0x03A0:
        bufpush(0x03C0);
        break;
      case 0x03A1:
        bufpush(0x03C1);
        break;
      case 0x03A3:
        bufpush(0x03C3);
        break;
      case 0x03A4:
        bufpush(0x03C4);
        break;
      case 0x03A5:
        bufpush(0x03C5);
        break;
      case 0x03A6:
        bufpush(0x03C6);
        break;
      case 0x03A7:
        bufpush(0x03C7);
        break;
      case 0x03A8:
        bufpush(0x03C8);
        break;
      case 0x03A9:
        bufpush(0x03C9);
        break;
      case 0x03AA:
        bufpush(0x03CA);
        break;
      case 0x03AB:
        bufpush(0x03CB);
        break;
      case 0x03B0:
        bufpush(0x03C5);
        bufpush(0x0308);
        bufpush(0x0301);
        break;
      case 0x03C2:
        bufpush(0x03C3);
        break;
      case 0x03CF:
        bufpush(0x03D7);
        break;
      case 0x03D0:
        bufpush(0x03B2);
        break;
      case 0x03D1:
        bufpush(0x03B8);
        break;
      case 0x03D5:
        bufpush(0x03C6);
        break;
      case 0x03D6:
        bufpush(0x03C0);
        break;
      case 0x03D8:
        bufpush(0x03D9);
        break;
      case 0x03DA:
        bufpush(0x03DB);
        break;
      case 0x03DC:
        bufpush(0x03DD);
        break;
      case 0x03DE:
        bufpush(0x03DF);
        break;
      case 0x03E0:
        bufpush(0x03E1);
        break;
      case 0x03E2:
        bufpush(0x03E3);
        break;
      case 0x03E4:
        bufpush(0x03E5);
        break;
      case 0x03E6:
        bufpush(0x03E7);
        break;
      case 0x03E8:
        bufpush(0x03E9);
        break;
      case 0x03EA:
        bufpush(0x03EB);
        break;
      case 0x03EC:
        bufpush(0x03ED);
        break;
      case 0x03EE:
        bufpush(0x03EF);
        break;
      case 0x03F0:
        bufpush(0x03BA);
        break;
      case 0x03F1:
        bufpush(0x03C1);
        break;
      case 0x03F4:
        bufpush(0x03B8);
        break;
      case 0x03F5:
        bufpush(0x03B5);
        break;
      case 0x03F7:
        bufpush(0x03F8);
        break;
      case 0x03F9:
        bufpush(0x03F2);
        break;
      case 0x03FA:
        bufpush(0x03FB);
        break;
      case 0x03FD:
        bufpush(0x037B);
        break;
      case 0x03FE:
        bufpush(0x037C);
        break;
      case 0x03FF:
        bufpush(0x037D);
        break;
      case 0x0400:
        bufpush(0x0450);
        break;
      case 0x0401:
        bufpush(0x0451);
        break;
      case 0x0402:
        bufpush(0x0452);
        break;
      case 0x0403:
        bufpush(0x0453);
        break;
      case 0x0404:
        bufpush(0x0454);
        break;
      case 0x0405:
        bufpush(0x0455);
        break;
      case 0x0406:
        bufpush(0x0456);
        break;
      case 0x0407:
        bufpush(0x0457);
        break;
      case 0x0408:
        bufpush(0x0458);
        break;
      case 0x0409:
        bufpush(0x0459);
        break;
      case 0x040A:
        bufpush(0x045A);
        break;
      case 0x040B:
        bufpush(0x045B);
        break;
      case 0x040C:
        bufpush(0x045C);
        break;
      case 0x040D:
        bufpush(0x045D);
        break;
      case 0x040E:
        bufpush(0x045E);
        break;
      case 0x040F:
        bufpush(0x045F);
        break;
      case 0x0410:
        bufpush(0x0430);
        break;
      case 0x0411:
        bufpush(0x0431);
        break;
      case 0x0412:
        bufpush(0x0432);
        break;
      case 0x0413:
        bufpush(0x0433);
        break;
      case 0x0414:
        bufpush(0x0434);
        break;
      case 0x0415:
        bufpush(0x0435);
        break;
      case 0x0416:
        bufpush(0x0436);
        break;
      case 0x0417:
        bufpush(0x0437);
        break;
      case 0x0418:
        bufpush(0x0438);
        break;
      case 0x0419:
        bufpush(0x0439);
        break;
      case 0x041A:
        bufpush(0x043A);
        break;
      case 0x041B:
        bufpush(0x043B);
        break;
      case 0x041C:
        bufpush(0x043C);
        break;
      case 0x041D:
        bufpush(0x043D);
        break;
      case 0x041E:
        bufpush(0x043E);
        break;
      case 0x041F:
        bufpush(0x043F);
        break;
      case 0x0420:
        bufpush(0x0440);
        break;
      case 0x0421:
        bufpush(0x0441);
        break;
      case 0x0422:
        bufpush(0x0442);
        break;
      case 0x0423:
        bufpush(0x0443);
        break;
      case 0x0424:
        bufpush(0x0444);
        break;
      case 0x0425:
        bufpush(0x0445);
        break;
      case 0x0426:
        bufpush(0x0446);
        break;
      case 0x0427:
        bufpush(0x0447);
        break;
      case 0x0428:
        bufpush(0x0448);
        break;
      case 0x0429:
        bufpush(0x0449);
        break;
      case 0x042A:
        bufpush(0x044A);
        break;
      case 0x042B:
        bufpush(0x044B);
        break;
      case 0x042C:
        bufpush(0x044C);
        break;
      case 0x042D:
        bufpush(0x044D);
        break;
      case 0x042E:
        bufpush(0x044E);
        break;
      case 0x042F:
        bufpush(0x044F);
        break;
      case 0x0460:
        bufpush(0x0461);
        break;
      case 0x0462:
        bufpush(0x0463);
        break;
      case 0x0464:
        bufpush(0x0465);
        break;
      case 0x0466:
        bufpush(0x0467);
        break;
      case 0x0468:
        bufpush(0x0469);
        break;
      case 0x046A:
        bufpush(0x046B);
        break;
      case 0x046C:
        bufpush(0x046D);
        break;
      case 0x046E:
        bufpush(0x046F);
        break;
      case 0x0470:
        bufpush(0x0471);
        break;
      case 0x0472:
        bufpush(0x0473);
        break;
      case 0x0474:
        bufpush(0x0475);
        break;
      case 0x0476:
        bufpush(0x0477);
        break;
      case 0x0478:
        bufpush(0x0479);
        break;
      case 0x047A:
        bufpush(0x047B);
        break;
      case 0x047C:
        bufpush(0x047D);
        break;
      case 0x047E:
        bufpush(0x047F);
        break;
      case 0x0480:
        bufpush(0x0481);
        break;
      case 0x048A:
        bufpush(0x048B);
        break;
      case 0x048C:
        bufpush(0x048D);
        break;
      case 0x048E:
        bufpush(0x048F);
        break;
      case 0x0490:
        bufpush(0x0491);
        break;
      case 0x0492:
        bufpush(0x0493);
        break;
      case 0x0494:
        bufpush(0x0495);
        break;
      case 0x0496:
        bufpush(0x0497);
        break;
      case 0x0498:
        bufpush(0x0499);
        break;
      case 0x049A:
        bufpush(0x049B);
        break;
      case 0x049C:
        bufpush(0x049D);
        break;
      case 0x049E:
        bufpush(0x049F);
        break;
      case 0x04A0:
        bufpush(0x04A1);
        break;
      case 0x04A2:
        bufpush(0x04A3);
        break;
      case 0x04A4:
        bufpush(0x04A5);
        break;
      case 0x04A6:
        bufpush(0x04A7);
        break;
      case 0x04A8:
        bufpush(0x04A9);
        break;
      case 0x04AA:
        bufpush(0x04AB);
        break;
      case 0x04AC:
        bufpush(0x04AD);
        break;
      case 0x04AE:
        bufpush(0x04AF);
        break;
      case 0x04B0:
        bufpush(0x04B1);
        break;
      case 0x04B2:
        bufpush(0x04B3);
        break;
      case 0x04B4:
        bufpush(0x04B5);
        break;
      case 0x04B6:
        bufpush(0x04B7);
        break;
      case 0x04B8:
        bufpush(0x04B9);
        break;
      case 0x04BA:
        bufpush(0x04BB);
        break;
      case 0x04BC:
        bufpush(0x04BD);
        break;
      case 0x04BE:
        bufpush(0x04BF);
        break;
      case 0x04C0:
        bufpush(0x04CF);
        break;
      case 0x04C1:
        bufpush(0x04C2);
        break;
      case 0x04C3:
        bufpush(0x04C4);
        break;
      case 0x04C5:
        bufpush(0x04C6);
        break;
      case 0x04C7:
        bufpush(0x04C8);
        break;
      case 0x04C9:
        bufpush(0x04CA);
        break;
      case 0x04CB:
        bufpush(0x04CC);
        break;
      case 0x04CD:
        bufpush(0x04CE);
        break;
      case 0x04D0:
        bufpush(0x04D1);
        break;
      case 0x04D2:
        bufpush(0x04D3);
        break;
      case 0x04D4:
        bufpush(0x04D5);
        break;
      case 0x04D6:
        bufpush(0x04D7);
        break;
      case 0x04D8:
        bufpush(0x04D9);
        break;
      case 0x04DA:
        bufpush(0x04DB);
        break;
      case 0x04DC:
        bufpush(0x04DD);
        break;
      case 0x04DE:
        bufpush(0x04DF);
        break;
      case 0x04E0:
        bufpush(0x04E1);
        break;
      case 0x04E2:
        bufpush(0x04E3);
        break;
      case 0x04E4:
        bufpush(0x04E5);
        break;
      case 0x04E6:
        bufpush(0x04E7);
        break;
      case 0x04E8:
        bufpush(0x04E9);
        break;
      case 0x04EA:
        bufpush(0x04EB);
        break;
      case 0x04EC:
        bufpush(0x04ED);
        break;
      case 0x04EE:
        bufpush(0x04EF);
        break;
      case 0x04F0:
        bufpush(0x04F1);
        break;
      case 0x04F2:
        bufpush(0x04F3);
        break;
      case 0x04F4:
        bufpush(0x04F5);
        break;
      case 0x04F6:
        bufpush(0x04F7);
        break;
      case 0x04F8:
        bufpush(0x04F9);
        break;
      case 0x04FA:
        bufpush(0x04FB);
        break;
      case 0x04FC:
        bufpush(0x04FD);
        break;
      case 0x04FE:
        bufpush(0x04FF);
        break;
      case 0x0500:
        bufpush(0x0501);
        break;
      case 0x0502:
        bufpush(0x0503);
        break;
      case 0x0504:
        bufpush(0x0505);
        break;
      case 0x0506:
        bufpush(0x0507);
        break;
      case 0x0508:
        bufpush(0x0509);
        break;
      case 0x050A:
        bufpush(0x050B);
        break;
      case 0x050C:
        bufpush(0x050D);
        break;
      case 0x050E:
        bufpush(0x050F);
        break;
      case 0x0510:
        bufpush(0x0511);
        break;
      case 0x0512:
        bufpush(0x0513);
        break;
      case 0x0514:
        bufpush(0x0515);
        break;
      case 0x0516:
        bufpush(0x0517);
        break;
      case 0x0518:
        bufpush(0x0519);
        break;
      case 0x051A:
        bufpush(0x051B);
        break;
      case 0x051C:
        bufpush(0x051D);
        break;
      case 0x051E:
        bufpush(0x051F);
        break;
      case 0x0520:
        bufpush(0x0521);
        break;
      case 0x0522:
        bufpush(0x0523);
        break;
      case 0x0524:
        bufpush(0x0525);
        break;
      case 0x0526:
        bufpush(0x0527);
        break;
      case 0x0528:
        bufpush(0x0529);
        break;
      case 0x052A:
        bufpush(0x052B);
        break;
      case 0x052C:
        bufpush(0x052D);
        break;
      case 0x052E:
        bufpush(0x052F);
        break;
      case 0x0531:
        bufpush(0x0561);
        break;
      case 0x0532:
        bufpush(0x0562);
        break;
      case 0x0533:
        bufpush(0x0563);
        break;
      case 0x0534:
        bufpush(0x0564);
        break;
      case 0x0535:
        bufpush(0x0565);
        break;
      case 0x0536:
        bufpush(0x0566);
        break;
      case 0x0537:
        bufpush(0x0567);
        break;
      case 0x0538:
        bufpush(0x0568);
        break;
      case 0x0539:
        bufpush(0x0569);
        break;
      case 0x053A:
        bufpush(0x056A);
        break;
      case 0x053B:
        bufpush(0x056B);
        break;
      case 0x053C:
        bufpush(0x056C);
        break;
      case 0x053D:
        bufpush(0x056D);
        break;
      case 0x053E:
        bufpush(0x056E);
        break;
      case 0x053F:
        bufpush(0x056F);
        break;
      case 0x0540:
        bufpush(0x0570);
        break;
      case 0x0541:
        bufpush(0x0571);
        break;
      case 0x0542:
        bufpush(0x0572);
        break;
      case 0x0543:
        bufpush(0x0573);
        break;
      case 0x0544:
        bufpush(0x0574);
        break;
      case 0x0545:
        bufpush(0x0575);
        break;
      case 0x0546:
        bufpush(0x0576);
        break;
      case 0x0547:
        bufpush(0x0577);
        break;
      case 0x0548:
        bufpush(0x0578);
        break;
      case 0x0549:
        bufpush(0x0579);
        break;
      case 0x054A:
        bufpush(0x057A);
        break;
      case 0x054B:
        bufpush(0x057B);
        break;
      case 0x054C:
        bufpush(0x057C);
        break;
      case 0x054D:
        bufpush(0x057D);
        break;
      case 0x054E:
        bufpush(0x057E);
        break;
      case 0x054F:
        bufpush(0x057F);
        break;
      case 0x0550:
        bufpush(0x0580);
        break;
      case 0x0551:
        bufpush(0x0581);
        break;
      case 0x0552:
        bufpush(0x0582);
        break;
      case 0x0553:
        bufpush(0x0583);
        break;
      case 0x0554:
        bufpush(0x0584);
        break;
      case 0x0555:
        bufpush(0x0585);
        break;
      case 0x0556:
        bufpush(0x0586);
        break;
      case 0x0587:
        bufpush(0x0565);
        bufpush(0x0582);
        break;
      case 0x10A0:
        bufpush(0x2D00);
        break;
      case 0x10A1:
        bufpush(0x2D01);
        break;
      case 0x10A2:
        bufpush(0x2D02);
        break;
      case 0x10A3:
        bufpush(0x2D03);
        break;
      case 0x10A4:
        bufpush(0x2D04);
        break;
      case 0x10A5:
        bufpush(0x2D05);
        break;
      case 0x10A6:
        bufpush(0x2D06);
        break;
      case 0x10A7:
        bufpush(0x2D07);
        break;
      case 0x10A8:
        bufpush(0x2D08);
        break;
      case 0x10A9:
        bufpush(0x2D09);
        break;
      case 0x10AA:
        bufpush(0x2D0A);
        break;
      case 0x10AB:
        bufpush(0x2D0B);
        break;
      case 0x10AC:
        bufpush(0x2D0C);
        break;
      case 0x10AD:
        bufpush(0x2D0D);
        break;
      case 0x10AE:
        bufpush(0x2D0E);
        break;
      case 0x10AF:
        bufpush(0x2D0F);
        break;
      case 0x10B0:
        bufpush(0x2D10);
        break;
      case 0x10B1:
        bufpush(0x2D11);
        break;
      case 0x10B2:
        bufpush(0x2D12);
        break;
      case 0x10B3:
        bufpush(0x2D13);
        break;
      case 0x10B4:
        bufpush(0x2D14);
        break;
      case 0x10B5:
        bufpush(0x2D15);
        break;
      case 0x10B6:
        bufpush(0x2D16);
        break;
      case 0x10B7:
        bufpush(0x2D17);
        break;
      case 0x10B8:
        bufpush(0x2D18);
        break;
      case 0x10B9:
        bufpush(0x2D19);
        break;
      case 0x10BA:
        bufpush(0x2D1A);
        break;
      case 0x10BB:
        bufpush(0x2D1B);
        break;
      case 0x10BC:
        bufpush(0x2D1C);
        break;
      case 0x10BD:
        bufpush(0x2D1D);
        break;
      case 0x10BE:
        bufpush(0x2D1E);
        break;
      case 0x10BF:
        bufpush(0x2D1F);
        break;
      case 0x10C0:
        bufpush(0x2D20);
        break;
      case 0x10C1:
        bufpush(0x2D21);
        break;
      case 0x10C2:
        bufpush(0x2D22);
        break;
      case 0x10C3:
        bufpush(0x2D23);
        break;
      case 0x10C4:
        bufpush(0x2D24);
        break;
      case 0x10C5:
        bufpush(0x2D25);
        break;
      case 0x10C7:
        bufpush(0x2D27);
        break;
      case 0x10CD:
        bufpush(0x2D2D);
        break;
      case 0x13F8:
        bufpush(0x13F0);
        break;
      case 0x13F9:
        bufpush(0x13F1);
        break;
      case 0x13FA:
        bufpush(0x13F2);
        break;
      case 0x13FB:
        bufpush(0x13F3);
        break;
      case 0x13FC:
        bufpush(0x13F4);
        break;
      case 0x13FD:
        bufpush(0x13F5);
        break;
      case 0x1C80:
        bufpush(0x0432);
        break;
      case 0x1C81:
        bufpush(0x0434);
        break;
      case 0x1C82:
        bufpush(0x043E);
        break;
      case 0x1C83:
        bufpush(0x0441);
        break;
      case 0x1C84:
        bufpush(0x0442);
        break;
      case 0x1C85:
        bufpush(0x0442);
        break;
      case 0x1C86:
        bufpush(0x044A);
        break;
      case 0x1C87:
        bufpush(0x0463);
        break;
      case 0x1C88:
        bufpush(0xA64B);
        break;
      case 0x1E00:
        bufpush(0x1E01);
        break;
      case 0x1E02:
        bufpush(0x1E03);
        break;
      case 0x1E04:
        bufpush(0x1E05);
        break;
      case 0x1E06:
        bufpush(0x1E07);
        break;
      case 0x1E08:
        bufpush(0x1E09);
        break;
      case 0x1E0A:
        bufpush(0x1E0B);
        break;
      case 0x1E0C:
        bufpush(0x1E0D);
        break;
      case 0x1E0E:
        bufpush(0x1E0F);
        break;
      case 0x1E10:
        bufpush(0x1E11);
        break;
      case 0x1E12:
        bufpush(0x1E13);
        break;
      case 0x1E14:
        bufpush(0x1E15);
        break;
      case 0x1E16:
        bufpush(0x1E17);
        break;
      case 0x1E18:
        bufpush(0x1E19);
        break;
      case 0x1E1A:
        bufpush(0x1E1B);
        break;
      case 0x1E1C:
        bufpush(0x1E1D);
        break;
      case 0x1E1E:
        bufpush(0x1E1F);
        break;
      case 0x1E20:
        bufpush(0x1E21);
        break;
      case 0x1E22:
        bufpush(0x1E23);
        break;
      case 0x1E24:
        bufpush(0x1E25);
        break;
      case 0x1E26:
        bufpush(0x1E27);
        break;
      case 0x1E28:
        bufpush(0x1E29);
        break;
      case 0x1E2A:
        bufpush(0x1E2B);
        break;
      case 0x1E2C:
        bufpush(0x1E2D);
        break;
      case 0x1E2E:
        bufpush(0x1E2F);
        break;
      case 0x1E30:
        bufpush(0x1E31);
        break;
      case 0x1E32:
        bufpush(0x1E33);
        break;
      case 0x1E34:
        bufpush(0x1E35);
        break;
      case 0x1E36:
        bufpush(0x1E37);
        break;
      case 0x1E38:
        bufpush(0x1E39);
        break;
      case 0x1E3A:
        bufpush(0x1E3B);
        break;
      case 0x1E3C:
        bufpush(0x1E3D);
        break;
      case 0x1E3E:
        bufpush(0x1E3F);
        break;
      case 0x1E40:
        bufpush(0x1E41);
        break;
      case 0x1E42:
        bufpush(0x1E43);
        break;
      case 0x1E44:
        bufpush(0x1E45);
        break;
      case 0x1E46:
        bufpush(0x1E47);
        break;
      case 0x1E48:
        bufpush(0x1E49);
        break;
      case 0x1E4A:
        bufpush(0x1E4B);
        break;
      case 0x1E4C:
        bufpush(0x1E4D);
        break;
      case 0x1E4E:
        bufpush(0x1E4F);
        break;
      case 0x1E50:
        bufpush(0x1E51);
        break;
      case 0x1E52:
        bufpush(0x1E53);
        break;
      case 0x1E54:
        bufpush(0x1E55);
        break;
      case 0x1E56:
        bufpush(0x1E57);
        break;
      case 0x1E58:
        bufpush(0x1E59);
        break;
      case 0x1E5A:
        bufpush(0x1E5B);
        break;
      case 0x1E5C:
        bufpush(0x1E5D);
        break;
      case 0x1E5E:
        bufpush(0x1E5F);
        break;
      case 0x1E60:
        bufpush(0x1E61);
        break;
      case 0x1E62:
        bufpush(0x1E63);
        break;
      case 0x1E64:
        bufpush(0x1E65);
        break;
      case 0x1E66:
        bufpush(0x1E67);
        break;
      case 0x1E68:
        bufpush(0x1E69);
        break;
      case 0x1E6A:
        bufpush(0x1E6B);
        break;
      case 0x1E6C:
        bufpush(0x1E6D);
        break;
      case 0x1E6E:
        bufpush(0x1E6F);
        break;
      case 0x1E70:
        bufpush(0x1E71);
        break;
      case 0x1E72:
        bufpush(0x1E73);
        break;
      case 0x1E74:
        bufpush(0x1E75);
        break;
      case 0x1E76:
        bufpush(0x1E77);
        break;
      case 0x1E78:
        bufpush(0x1E79);
        break;
      case 0x1E7A:
        bufpush(0x1E7B);
        break;
      case 0x1E7C:
        bufpush(0x1E7D);
        break;
      case 0x1E7E:
        bufpush(0x1E7F);
        break;
      case 0x1E80:
        bufpush(0x1E81);
        break;
      case 0x1E82:
        bufpush(0x1E83);
        break;
      case 0x1E84:
        bufpush(0x1E85);
        break;
      case 0x1E86:
        bufpush(0x1E87);
        break;
      case 0x1E88:
        bufpush(0x1E89);
        break;
      case 0x1E8A:
        bufpush(0x1E8B);
        break;
      case 0x1E8C:
        bufpush(0x1E8D);
        break;
      case 0x1E8E:
        bufpush(0x1E8F);
        break;
      case 0x1E90:
        bufpush(0x1E91);
        break;
      case 0x1E92:
        bufpush(0x1E93);
        break;
      case 0x1E94:
        bufpush(0x1E95);
        break;
      case 0x1E96:
        bufpush(0x0068);
        bufpush(0x0331);
        break;
      case 0x1E97:
        bufpush(0x0074);
        bufpush(0x0308);
        break;
      case 0x1E98:
        bufpush(0x0077);
        bufpush(0x030A);
        break;
      case 0x1E99:
        bufpush(0x0079);
        bufpush(0x030A);
        break;
      case 0x1E9A:
        bufpush(0x0061);
        bufpush(0x02BE);
        break;
      case 0x1E9B:
        bufpush(0x1E61);
        break;
      case 0x1E9E:
        bufpush(0x0073);
        bufpush(0x0073);
        break;
      case 0x1EA0:
        bufpush(0x1EA1);
        break;
      case 0x1EA2:
        bufpush(0x1EA3);
        break;
      case 0x1EA4:
        bufpush(0x1EA5);
        break;
      case 0x1EA6:
        bufpush(0x1EA7);
        break;
      case 0x1EA8:
        bufpush(0x1EA9);
        break;
      case 0x1EAA:
        bufpush(0x1EAB);
        break;
      case 0x1EAC:
        bufpush(0x1EAD);
        break;
      case 0x1EAE:
        bufpush(0x1EAF);
        break;
      case 0x1EB0:
        bufpush(0x1EB1);
        break;
      case 0x1EB2:
        bufpush(0x1EB3);
        break;
      case 0x1EB4:
        bufpush(0x1EB5);
        break;
      case 0x1EB6:
        bufpush(0x1EB7);
        break;
      case 0x1EB8:
        bufpush(0x1EB9);
        break;
      case 0x1EBA:
        bufpush(0x1EBB);
        break;
      case 0x1EBC:
        bufpush(0x1EBD);
        break;
      case 0x1EBE:
        bufpush(0x1EBF);
        break;
      case 0x1EC0:
        bufpush(0x1EC1);
        break;
      case 0x1EC2:
        bufpush(0x1EC3);
        break;
      case 0x1EC4:
        bufpush(0x1EC5);
        break;
      case 0x1EC6:
        bufpush(0x1EC7);
        break;
      case 0x1EC8:
        bufpush(0x1EC9);
        break;
      case 0x1ECA:
        bufpush(0x1ECB);
        break;
      case 0x1ECC:
        bufpush(0x1ECD);
        break;
      case 0x1ECE:
        bufpush(0x1ECF);
        break;
      case 0x1ED0:
        bufpush(0x1ED1);
        break;
      case 0x1ED2:
        bufpush(0x1ED3);
        break;
      case 0x1ED4:
        bufpush(0x1ED5);
        break;
      case 0x1ED6:
        bufpush(0x1ED7);
        break;
      case 0x1ED8:
        bufpush(0x1ED9);
        break;
      case 0x1EDA:
        bufpush(0x1EDB);
        break;
      case 0x1EDC:
        bufpush(0x1EDD);
        break;
      case 0x1EDE:
        bufpush(0x1EDF);
        break;
      case 0x1EE0:
        bufpush(0x1EE1);
        break;
      case 0x1EE2:
        bufpush(0x1EE3);
        break;
      case 0x1EE4:
        bufpush(0x1EE5);
        break;
      case 0x1EE6:
        bufpush(0x1EE7);
        break;
      case 0x1EE8:
        bufpush(0x1EE9);
        break;
      case 0x1EEA:
        bufpush(0x1EEB);
        break;
      case 0x1EEC:
        bufpush(0x1EED);
        break;
      case 0x1EEE:
        bufpush(0x1EEF);
        break;
      case 0x1EF0:
        bufpush(0x1EF1);
        break;
      case 0x1EF2:
        bufpush(0x1EF3);
        break;
      case 0x1EF4:
        bufpush(0x1EF5);
        break;
      case 0x1EF6:
        bufpush(0x1EF7);
        break;
      case 0x1EF8:
        bufpush(0x1EF9);
        break;
      case 0x1EFA:
        bufpush(0x1EFB);
        break;
      case 0x1EFC:
        bufpush(0x1EFD);
        break;
      case 0x1EFE:
        bufpush(0x1EFF);
        break;
      case 0x1F08:
        bufpush(0x1F00);
        break;
      case 0x1F09:
        bufpush(0x1F01);
        break;
      case 0x1F0A:
        bufpush(0x1F02);
        break;
      case 0x1F0B:
        bufpush(0x1F03);
        break;
      case 0x1F0C:
        bufpush(0x1F04);
        break;
      case 0x1F0D:
        bufpush(0x1F05);
        break;
      case 0x1F0E:
        bufpush(0x1F06);
        break;
      case 0x1F0F:
        bufpush(0x1F07);
        break;
      case 0x1F18:
        bufpush(0x1F10);
        break;
      case 0x1F19:
        bufpush(0x1F11);
        break;
      case 0x1F1A:
        bufpush(0x1F12);
        break;
      case 0x1F1B:
        bufpush(0x1F13);
        break;
      case 0x1F1C:
        bufpush(0x1F14);
        break;
      case 0x1F1D:
        bufpush(0x1F15);
        break;
      case 0x1F28:
        bufpush(0x1F20);
        break;
      case 0x1F29:
        bufpush(0x1F21);
        break;
      case 0x1F2A:
        bufpush(0x1F22);
        break;
      case 0x1F2B:
        bufpush(0x1F23);
        break;
      case 0x1F2C:
        bufpush(0x1F24);
        break;
      case 0x1F2D:
        bufpush(0x1F25);
        break;
      case 0x1F2E:
        bufpush(0x1F26);
        break;
      case 0x1F2F:
        bufpush(0x1F27);
        break;
      case 0x1F38:
        bufpush(0x1F30);
        break;
      case 0x1F39:
        bufpush(0x1F31);
        break;
      case 0x1F3A:
        bufpush(0x1F32);
        break;
      case 0x1F3B:
        bufpush(0x1F33);
        break;
      case 0x1F3C:
        bufpush(0x1F34);
        break;
      case 0x1F3D:
        bufpush(0x1F35);
        break;
      case 0x1F3E:
        bufpush(0x1F36);
        break;
      case 0x1F3F:
        bufpush(0x1F37);
        break;
      case 0x1F48:
        bufpush(0x1F40);
        break;
      case 0x1F49:
        bufpush(0x1F41);
        break;
      case 0x1F4A:
        bufpush(0x1F42);
        break;
      case 0x1F4B:
        bufpush(0x1F43);
        break;
      case 0x1F4C:
        bufpush(0x1F44);
        break;
      case 0x1F4D:
        bufpush(0x1F45);
        break;
      case 0x1F50:
        bufpush(0x03C5);
        bufpush(0x0313);
        break;
      case 0x1F52:
        bufpush(0x03C5);
        bufpush(0x0313);
        bufpush(0x0300);
        break;
      case 0x1F54:
        bufpush(0x03C5);
        bufpush(0x0313);
        bufpush(0x0301);
        break;
      case 0x1F56:
        bufpush(0x03C5);
        bufpush(0x0313);
        bufpush(0x0342);
        break;
      case 0x1F59:
        bufpush(0x1F51);
        break;
      case 0x1F5B:
        bufpush(0x1F53);
        break;
      case 0x1F5D:
        bufpush(0x1F55);
        break;
      case 0x1F5F:
        bufpush(0x1F57);
        break;
      case 0x1F68:
        bufpush(0x1F60);
        break;
      case 0x1F69:
        bufpush(0x1F61);
        break;
      case 0x1F6A:
        bufpush(0x1F62);
        break;
      case 0x1F6B:
        bufpush(0x1F63);
        break;
      case 0x1F6C:
        bufpush(0x1F64);
        break;
      case 0x1F6D:
        bufpush(0x1F65);
        break;
      case 0x1F6E:
        bufpush(0x1F66);
        break;
      case 0x1F6F:
        bufpush(0x1F67);
        break;
      case 0x1F80:
        bufpush(0x1F00);
        bufpush(0x03B9);
        break;
      case 0x1F81:
        bufpush(0x1F01);
        bufpush(0x03B9);
        break;
      case 0x1F82:
        bufpush(0x1F02);
        bufpush(0x03B9);
        break;
      case 0x1F83:
        bufpush(0x1F03);
        bufpush(0x03B9);
        break;
      case 0x1F84:
        bufpush(0x1F04);
        bufpush(0x03B9);
        break;
      case 0x1F85:
        bufpush(0x1F05);
        bufpush(0x03B9);
        break;
      case 0x1F86:
        bufpush(0x1F06);
        bufpush(0x03B9);
        break;
      case 0x1F87:
        bufpush(0x1F07);
        bufpush(0x03B9);
        break;
      case 0x1F88:
        bufpush(0x1F00);
        bufpush(0x03B9);
        break;
      case 0x1F89:
        bufpush(0x1F01);
        bufpush(0x03B9);
        break;
      case 0x1F8A:
        bufpush(0x1F02);
        bufpush(0x03B9);
        break;
      case 0x1F8B:
        bufpush(0x1F03);
        bufpush(0x03B9);
        break;
      case 0x1F8C:
        bufpush(0x1F04);
        bufpush(0x03B9);
        break;
      case 0x1F8D:
        bufpush(0x1F05);
        bufpush(0x03B9);
        break;
      case 0x1F8E:
        bufpush(0x1F06);
        bufpush(0x03B9);
        break;
      case 0x1F8F:
        bufpush(0x1F07);
        bufpush(0x03B9);
        break;
      case 0x1F90:
        bufpush(0x1F20);
        bufpush(0x03B9);
        break;
      case 0x1F91:
        bufpush(0x1F21);
        bufpush(0x03B9);
        break;
      case 0x1F92:
        bufpush(0x1F22);
        bufpush(0x03B9);
        break;
      case 0x1F93:
        bufpush(0x1F23);
        bufpush(0x03B9);
        break;
      case 0x1F94:
        bufpush(0x1F24);
        bufpush(0x03B9);
        break;
      case 0x1F95:
        bufpush(0x1F25);
        bufpush(0x03B9);
        break;
      case 0x1F96:
        bufpush(0x1F26);
        bufpush(0x03B9);
        break;
      case 0x1F97:
        bufpush(0x1F27);
        bufpush(0x03B9);
        break;
      case 0x1F98:
        bufpush(0x1F20);
        bufpush(0x03B9);
        break;
      case 0x1F99:
        bufpush(0x1F21);
        bufpush(0x03B9);
        break;
      case 0x1F9A:
        bufpush(0x1F22);
        bufpush(0x03B9);
        break;
      case 0x1F9B:
        bufpush(0x1F23);
        bufpush(0x03B9);
        break;
      case 0x1F9C:
        bufpush(0x1F24);
        bufpush(0x03B9);
        break;
      case 0x1F9D:
        bufpush(0x1F25);
        bufpush(0x03B9);
        break;
      case 0x1F9E:
        bufpush(0x1F26);
        bufpush(0x03B9);
        break;
      case 0x1F9F:
        bufpush(0x1F27);
        bufpush(0x03B9);
        break;
      case 0x1FA0:
        bufpush(0x1F60);
        bufpush(0x03B9);
        break;
      case 0x1FA1:
        bufpush(0x1F61);
        bufpush(0x03B9);
        break;
      case 0x1FA2:
        bufpush(0x1F62);
        bufpush(0x03B9);
        break;
      case 0x1FA3:
        bufpush(0x1F63);
        bufpush(0x03B9);
        break;
      case 0x1FA4:
        bufpush(0x1F64);
        bufpush(0x03B9);
        break;
      case 0x1FA5:
        bufpush(0x1F65);
        bufpush(0x03B9);
        break;
      case 0x1FA6:
        bufpush(0x1F66);
        bufpush(0x03B9);
        break;
      case 0x1FA7:
        bufpush(0x1F67);
        bufpush(0x03B9);
        break;
      case 0x1FA8:
        bufpush(0x1F60);
        bufpush(0x03B9);
        break;
      case 0x1FA9:
        bufpush(0x1F61);
        bufpush(0x03B9);
        break;
      case 0x1FAA:
        bufpush(0x1F62);
        bufpush(0x03B9);
        break;
      case 0x1FAB:
        bufpush(0x1F63);
        bufpush(0x03B9);
        break;
      case 0x1FAC:
        bufpush(0x1F64);
        bufpush(0x03B9);
        break;
      case 0x1FAD:
        bufpush(0x1F65);
        bufpush(0x03B9);
        break;
      case 0x1FAE:
        bufpush(0x1F66);
        bufpush(0x03B9);
        break;
      case 0x1FAF:
        bufpush(0x1F67);
        bufpush(0x03B9);
        break;
      case 0x1FB2:
        bufpush(0x1F70);
        bufpush(0x03B9);
        break;
      case 0x1FB3:
        bufpush(0x03B1);
        bufpush(0x03B9);
        break;
      case 0x1FB4:
        bufpush(0x03AC);
        bufpush(0x03B9);
        break;
      case 0x1FB6:
        bufpush(0x03B1);
        bufpush(0x0342);
        break;
      case 0x1FB7:
        bufpush(0x03B1);
        bufpush(0x0342);
        bufpush(0x03B9);
        break;
      case 0x1FB8:
        bufpush(0x1FB0);
        break;
      case 0x1FB9:
        bufpush(0x1FB1);
        break;
      case 0x1FBA:
        bufpush(0x1F70);
        break;
      case 0x1FBB:
        bufpush(0x1F71);
        break;
      case 0x1FBC:
        bufpush(0x03B1);
        bufpush(0x03B9);
        break;
      case 0x1FBE:
        bufpush(0x03B9);
        break;
      case 0x1FC2:
        bufpush(0x1F74);
        bufpush(0x03B9);
        break;
      case 0x1FC3:
        bufpush(0x03B7);
        bufpush(0x03B9);
        break;
      case 0x1FC4:
        bufpush(0x03AE);
        bufpush(0x03B9);
        break;
      case 0x1FC6:
        bufpush(0x03B7);
        bufpush(0x0342);
        break;
      case 0x1FC7:
        bufpush(0x03B7);
        bufpush(0x0342);
        bufpush(0x03B9);
        break;
      case 0x1FC8:
        bufpush(0x1F72);
        break;
      case 0x1FC9:
        bufpush(0x1F73);
        break;
      case 0x1FCA:
        bufpush(0x1F74);
        break;
      case 0x1FCB:
        bufpush(0x1F75);
        break;
      case 0x1FCC:
        bufpush(0x03B7);
        bufpush(0x03B9);
        break;
      case 0x1FD2:
        bufpush(0x03B9);
        bufpush(0x0308);
        bufpush(0x0300);
        break;
      case 0x1FD3:
        bufpush(0x03B9);
        bufpush(0x0308);
        bufpush(0x0301);
        break;
      case 0x1FD6:
        bufpush(0x03B9);
        bufpush(0x0342);
        break;
      case 0x1FD7:
        bufpush(0x03B9);
        bufpush(0x0308);
        bufpush(0x0342);
        break;
      case 0x1FD8:
        bufpush(0x1FD0);
        break;
      case 0x1FD9:
        bufpush(0x1FD1);
        break;
      case 0x1FDA:
        bufpush(0x1F76);
        break;
      case 0x1FDB:
        bufpush(0x1F77);
        break;
      case 0x1FE2:
        bufpush(0x03C5);
        bufpush(0x0308);
        bufpush(0x0300);
        break;
      case 0x1FE3:
        bufpush(0x03C5);
        bufpush(0x0308);
        bufpush(0x0301);
        break;
      case 0x1FE4:
        bufpush(0x03C1);
        bufpush(0x0313);
        break;
      case 0x1FE6:
        bufpush(0x03C5);
        bufpush(0x0342);
        break;
      case 0x1FE7:
        bufpush(0x03C5);
        bufpush(0x0308);
        bufpush(0x0342);
        break;
      case 0x1FE8:
        bufpush(0x1FE0);
        break;
      case 0x1FE9:
        bufpush(0x1FE1);
        break;
      case 0x1FEA:
        bufpush(0x1F7A);
        break;
      case 0x1FEB:
        bufpush(0x1F7B);
        break;
      case 0x1FEC:
        bufpush(0x1FE5);
        break;
      case 0x1FF2:
        bufpush(0x1F7C);
        bufpush(0x03B9);
        break;
      case 0x1FF3:
        bufpush(0x03C9);
        bufpush(0x03B9);
        break;
      case 0x1FF4:
        bufpush(0x03CE);
        bufpush(0x03B9);
        break;
      case 0x1FF6:
        bufpush(0x03C9);
        bufpush(0x0342);
        break;
      case 0x1FF7:
        bufpush(0x03C9);
        bufpush(0x0342);
        bufpush(0x03B9);
        break;
      case 0x1FF8:
        bufpush(0x1F78);
        break;
      case 0x1FF9:
        bufpush(0x1F79);
        break;
      case 0x1FFA:
        bufpush(0x1F7C);
        break;
      case 0x1FFB:
        bufpush(0x1F7D);
        break;
      case 0x1FFC:
        bufpush(0x03C9);
        bufpush(0x03B9);
        break;
      case 0x2126:
        bufpush(0x03C9);
        break;
      case 0x212A:
        bufpush(0x006B);
        break;
      case 0x212B:
        bufpush(0x00E5);
        break;
      case 0x2132:
        bufpush(0x214E);
        break;
      case 0x2160:
        bufpush(0x2170);
        break;
      case 0x2161:
        bufpush(0x2171);
        break;
      case 0x2162:
        bufpush(0x2172);
        break;
      case 0x2163:
        bufpush(0x2173);
        break;
      case 0x2164:
        bufpush(0x2174);
        break;
      case 0x2165:
        bufpush(0x2175);
        break;
      case 0x2166:
        bufpush(0x2176);
        break;
      case 0x2167:
        bufpush(0x2177);
        break;
      case 0x2168:
        bufpush(0x2178);
        break;
      case 0x2169:
        bufpush(0x2179);
        break;
      case 0x216A:
        bufpush(0x217A);
        break;
      case 0x216B:
        bufpush(0x217B);
        break;
      case 0x216C:
        bufpush(0x217C);
        break;
      case 0x216D:
        bufpush(0x217D);
        break;
      case 0x216E:
        bufpush(0x217E);
        break;
      case 0x216F:
        bufpush(0x217F);
        break;
      case 0x2183:
        bufpush(0x2184);
        break;
      case 0x24B6:
        bufpush(0x24D0);
        break;
      case 0x24B7:
        bufpush(0x24D1);
        break;
      case 0x24B8:
        bufpush(0x24D2);
        break;
      case 0x24B9:
        bufpush(0x24D3);
        break;
      case 0x24BA:
        bufpush(0x24D4);
        break;
      case 0x24BB:
        bufpush(0x24D5);
        break;
      case 0x24BC:
        bufpush(0x24D6);
        break;
      case 0x24BD:
        bufpush(0x24D7);
        break;
      case 0x24BE:
        bufpush(0x24D8);
        break;
      case 0x24BF:
        bufpush(0x24D9);
        break;
      case 0x24C0:
        bufpush(0x24DA);
        break;
      case 0x24C1:
        bufpush(0x24DB);
        break;
      case 0x24C2:
        bufpush(0x24DC);
        break;
      case 0x24C3:
        bufpush(0x24DD);
        break;
      case 0x24C4:
        bufpush(0x24DE);
        break;
      case 0x24C5:
        bufpush(0x24DF);
        break;
      case 0x24C6:
        bufpush(0x24E0);
        break;
      case 0x24C7:
        bufpush(0x24E1);
        break;
      case 0x24C8:
        bufpush(0x24E2);
        break;
      case 0x24C9:
        bufpush(0x24E3);
        break;
      case 0x24CA:
        bufpush(0x24E4);
        break;
      case 0x24CB:
        bufpush(0x24E5);
        break;
      case 0x24CC:
        bufpush(0x24E6);
        break;
      case 0x24CD:
        bufpush(0x24E7);
        break;
      case 0x24CE:
        bufpush(0x24E8);
        break;
      case 0x24CF:
        bufpush(0x24E9);
        break;
      case 0x2C00:
        bufpush(0x2C30);
        break;
      case 0x2C01:
        bufpush(0x2C31);
        break;
      case 0x2C02:
        bufpush(0x2C32);
        break;
      case 0x2C03:
        bufpush(0x2C33);
        break;
      case 0x2C04:
        bufpush(0x2C34);
        break;
      case 0x2C05:
        bufpush(0x2C35);
        break;
      case 0x2C06:
        bufpush(0x2C36);
        break;
      case 0x2C07:
        bufpush(0x2C37);
        break;
      case 0x2C08:
        bufpush(0x2C38);
        break;
      case 0x2C09:
        bufpush(0x2C39);
        break;
      case 0x2C0A:
        bufpush(0x2C3A);
        break;
      case 0x2C0B:
        bufpush(0x2C3B);
        break;
      case 0x2C0C:
        bufpush(0x2C3C);
        break;
      case 0x2C0D:
        bufpush(0x2C3D);
        break;
      case 0x2C0E:
        bufpush(0x2C3E);
        break;
      case 0x2C0F:
        bufpush(0x2C3F);
        break;
      case 0x2C10:
        bufpush(0x2C40);
        break;
      case 0x2C11:
        bufpush(0x2C41);
        break;
      case 0x2C12:
        bufpush(0x2C42);
        break;
      case 0x2C13:
        bufpush(0x2C43);
        break;
      case 0x2C14:
        bufpush(0x2C44);
        break;
      case 0x2C15:
        bufpush(0x2C45);
        break;
      case 0x2C16:
        bufpush(0x2C46);
        break;
      case 0x2C17:
        bufpush(0x2C47);
        break;
      case 0x2C18:
        bufpush(0x2C48);
        break;
      case 0x2C19:
        bufpush(0x2C49);
        break;
      case 0x2C1A:
        bufpush(0x2C4A);
        break;
      case 0x2C1B:
        bufpush(0x2C4B);
        break;
      case 0x2C1C:
        bufpush(0x2C4C);
        break;
      case 0x2C1D:
        bufpush(0x2C4D);
        break;
      case 0x2C1E:
        bufpush(0x2C4E);
        break;
      case 0x2C1F:
        bufpush(0x2C4F);
        break;
      case 0x2C20:
        bufpush(0x2C50);
        break;
      case 0x2C21:
        bufpush(0x2C51);
        break;
      case 0x2C22:
        bufpush(0x2C52);
        break;
      case 0x2C23:
        bufpush(0x2C53);
        break;
      case 0x2C24:
        bufpush(0x2C54);
        break;
      case 0x2C25:
        bufpush(0x2C55);
        break;
      case 0x2C26:
        bufpush(0x2C56);
        break;
      case 0x2C27:
        bufpush(0x2C57);
        break;
      case 0x2C28:
        bufpush(0x2C58);
        break;
      case 0x2C29:
        bufpush(0x2C59);
        break;
      case 0x2C2A:
        bufpush(0x2C5A);
        break;
      case 0x2C2B:
        bufpush(0x2C5B);
        break;
      case 0x2C2C:
        bufpush(0x2C5C);
        break;
      case 0x2C2D:
        bufpush(0x2C5D);
        break;
      case 0x2C2E:
        bufpush(0x2C5E);
        break;
      case 0x2C60:
        bufpush(0x2C61);
        break;
      case 0x2C62:
        bufpush(0x026B);
        break;
      case 0x2C63:
        bufpush(0x1D7D);
        break;
      case 0x2C64:
        bufpush(0x027D);
        break;
      case 0x2C67:
        bufpush(0x2C68);
        break;
      case 0x2C69:
        bufpush(0x2C6A);
        break;
      case 0x2C6B:
        bufpush(0x2C6C);
        break;
      case 0x2C6D:
        bufpush(0x0251);
        break;
      case 0x2C6E:
        bufpush(0x0271);
        break;
      case 0x2C6F:
        bufpush(0x0250);
        break;
      case 0x2C70:
        bufpush(0x0252);
        break;
      case 0x2C72:
        bufpush(0x2C73);
        break;
      case 0x2C75:
        bufpush(0x2C76);
        break;
      case 0x2C7E:
        bufpush(0x023F);
        break;
      case 0x2C7F:
        bufpush(0x0240);
        break;
      case 0x2C80:
        bufpush(0x2C81);
        break;
      case 0x2C82:
        bufpush(0x2C83);
        break;
      case 0x2C84:
        bufpush(0x2C85);
        break;
      case 0x2C86:
        bufpush(0x2C87);
        break;
      case 0x2C88:
        bufpush(0x2C89);
        break;
      case 0x2C8A:
        bufpush(0x2C8B);
        break;
      case 0x2C8C:
        bufpush(0x2C8D);
        break;
      case 0x2C8E:
        bufpush(0x2C8F);
        break;
      case 0x2C90:
        bufpush(0x2C91);
        break;
      case 0x2C92:
        bufpush(0x2C93);
        break;
      case 0x2C94:
        bufpush(0x2C95);
        break;
      case 0x2C96:
        bufpush(0x2C97);
        break;
      case 0x2C98:
        bufpush(0x2C99);
        break;
      case 0x2C9A:
        bufpush(0x2C9B);
        break;
      case 0x2C9C:
        bufpush(0x2C9D);
        break;
      case 0x2C9E:
        bufpush(0x2C9F);
        break;
      case 0x2CA0:
        bufpush(0x2CA1);
        break;
      case 0x2CA2:
        bufpush(0x2CA3);
        break;
      case 0x2CA4:
        bufpush(0x2CA5);
        break;
      case 0x2CA6:
        bufpush(0x2CA7);
        break;
      case 0x2CA8:
        bufpush(0x2CA9);
        break;
      case 0x2CAA:
        bufpush(0x2CAB);
        break;
      case 0x2CAC:
        bufpush(0x2CAD);
        break;
      case 0x2CAE:
        bufpush(0x2CAF);
        break;
      case 0x2CB0:
        bufpush(0x2CB1);
        break;
      case 0x2CB2:
        bufpush(0x2CB3);
        break;
      case 0x2CB4:
        bufpush(0x2CB5);
        break;
      case 0x2CB6:
        bufpush(0x2CB7);
        break;
      case 0x2CB8:
        bufpush(0x2CB9);
        break;
      case 0x2CBA:
        bufpush(0x2CBB);
        break;
      case 0x2CBC:
        bufpush(0x2CBD);
        break;
      case 0x2CBE:
        bufpush(0x2CBF);
        break;
      case 0x2CC0:
        bufpush(0x2CC1);
        break;
      case 0x2CC2:
        bufpush(0x2CC3);
        break;
      case 0x2CC4:
        bufpush(0x2CC5);
        break;
      case 0x2CC6:
        bufpush(0x2CC7);
        break;
      case 0x2CC8:
        bufpush(0x2CC9);
        break;
      case 0x2CCA:
        bufpush(0x2CCB);
        break;
      case 0x2CCC:
        bufpush(0x2CCD);
        break;
      case 0x2CCE:
        bufpush(0x2CCF);
        break;
      case 0x2CD0:
        bufpush(0x2CD1);
        break;
      case 0x2CD2:
        bufpush(0x2CD3);
        break;
      case 0x2CD4:
        bufpush(0x2CD5);
        break;
      case 0x2CD6:
        bufpush(0x2CD7);
        break;
      case 0x2CD8:
        bufpush(0x2CD9);
        break;
      case 0x2CDA:
        bufpush(0x2CDB);
        break;
      case 0x2CDC:
        bufpush(0x2CDD);
        break;
      case 0x2CDE:
        bufpush(0x2CDF);
        break;
      case 0x2CE0:
        bufpush(0x2CE1);
        break;
      case 0x2CE2:
        bufpush(0x2CE3);
        break;
      case 0x2CEB:
        bufpush(0x2CEC);
        break;
      case 0x2CED:
        bufpush(0x2CEE);
        break;
      case 0x2CF2:
        bufpush(0x2CF3);
        break;
      case 0xA640:
        bufpush(0xA641);
        break;
      case 0xA642:
        bufpush(0xA643);
        break;
      case 0xA644:
        bufpush(0xA645);
        break;
      case 0xA646:
        bufpush(0xA647);
        break;
      case 0xA648:
        bufpush(0xA649);
        break;
      case 0xA64A:
        bufpush(0xA64B);
        break;
      case 0xA64C:
        bufpush(0xA64D);
        break;
      case 0xA64E:
        bufpush(0xA64F);
        break;
      case 0xA650:
        bufpush(0xA651);
        break;
      case 0xA652:
        bufpush(0xA653);
        break;
      case 0xA654:
        bufpush(0xA655);
        break;
      case 0xA656:
        bufpush(0xA657);
        break;
      case 0xA658:
        bufpush(0xA659);
        break;
      case 0xA65A:
        bufpush(0xA65B);
        break;
      case 0xA65C:
        bufpush(0xA65D);
        break;
      case 0xA65E:
        bufpush(0xA65F);
        break;
      case 0xA660:
        bufpush(0xA661);
        break;
      case 0xA662:
        bufpush(0xA663);
        break;
      case 0xA664:
        bufpush(0xA665);
        break;
      case 0xA666:
        bufpush(0xA667);
        break;
      case 0xA668:
        bufpush(0xA669);
        break;
      case 0xA66A:
        bufpush(0xA66B);
        break;
      case 0xA66C:
        bufpush(0xA66D);
        break;
      case 0xA680:
        bufpush(0xA681);
        break;
      case 0xA682:
        bufpush(0xA683);
        break;
      case 0xA684:
        bufpush(0xA685);
        break;
      case 0xA686:
        bufpush(0xA687);
        break;
      case 0xA688:
        bufpush(0xA689);
        break;
      case 0xA68A:
        bufpush(0xA68B);
        break;
      case 0xA68C:
        bufpush(0xA68D);
        break;
      case 0xA68E:
        bufpush(0xA68F);
        break;
      case 0xA690:
        bufpush(0xA691);
        break;
      case 0xA692:
        bufpush(0xA693);
        break;
      case 0xA694:
        bufpush(0xA695);
        break;
      case 0xA696:
        bufpush(0xA697);
        break;
      case 0xA698:
        bufpush(0xA699);
        break;
      case 0xA69A:
        bufpush(0xA69B);
        break;
      case 0xA722:
        bufpush(0xA723);
        break;
      case 0xA724:
        bufpush(0xA725);
        break;
      case 0xA726:
        bufpush(0xA727);
        break;
      case 0xA728:
        bufpush(0xA729);
        break;
      case 0xA72A:
        bufpush(0xA72B);
        break;
      case 0xA72C:
        bufpush(0xA72D);
        break;
      case 0xA72E:
        bufpush(0xA72F);
        break;
      case 0xA732:
        bufpush(0xA733);
        break;
      case 0xA734:
        bufpush(0xA735);
        break;
      case 0xA736:
        bufpush(0xA737);
        break;
      case 0xA738:
        bufpush(0xA739);
        break;
      case 0xA73A:
        bufpush(0xA73B);
        break;
      case 0xA73C:
        bufpush(0xA73D);
        break;
      case 0xA73E:
        bufpush(0xA73F);
        break;
      case 0xA740:
        bufpush(0xA741);
        break;
      case 0xA742:
        bufpush(0xA743);
        break;
      case 0xA744:
        bufpush(0xA745);
        break;
      case 0xA746:
        bufpush(0xA747);
        break;
      case 0xA748:
        bufpush(0xA749);
        break;
      case 0xA74A:
        bufpush(0xA74B);
        break;
      case 0xA74C:
        bufpush(0xA74D);
        break;
      case 0xA74E:
        bufpush(0xA74F);
        break;
      case 0xA750:
        bufpush(0xA751);
        break;
      case 0xA752:
        bufpush(0xA753);
        break;
      case 0xA754:
        bufpush(0xA755);
        break;
      case 0xA756:
        bufpush(0xA757);
        break;
      case 0xA758:
        bufpush(0xA759);
        break;
      case 0xA75A:
        bufpush(0xA75B);
        break;
      case 0xA75C:
        bufpush(0xA75D);
        break;
      case 0xA75E:
        bufpush(0xA75F);
        break;
      case 0xA760:
        bufpush(0xA761);
        break;
      case 0xA762:
        bufpush(0xA763);
        break;
      case 0xA764:
        bufpush(0xA765);
        break;
      case 0xA766:
        bufpush(0xA767);
        break;
      case 0xA768:
        bufpush(0xA769);
        break;
      case 0xA76A:
        bufpush(0xA76B);
        break;
      case 0xA76C:
        bufpush(0xA76D);
        break;
      case 0xA76E:
        bufpush(0xA76F);
        break;
      case 0xA779:
        bufpush(0xA77A);
        break;
      case 0xA77B:
        bufpush(0xA77C);
        break;
      case 0xA77D:
        bufpush(0x1D79);
        break;
      case 0xA77E:
        bufpush(0xA77F);
        break;
      case 0xA780:
        bufpush(0xA781);
        break;
      case 0xA782:
        bufpush(0xA783);
        break;
      case 0xA784:
        bufpush(0xA785);
        break;
      case 0xA786:
        bufpush(0xA787);
        break;
      case 0xA78B:
        bufpush(0xA78C);
        break;
      case 0xA78D:
        bufpush(0x0265);
        break;
      case 0xA790:
        bufpush(0xA791);
        break;
      case 0xA792:
        bufpush(0xA793);
        break;
      case 0xA796:
        bufpush(0xA797);
        break;
      case 0xA798:
        bufpush(0xA799);
        break;
      case 0xA79A:
        bufpush(0xA79B);
        break;
      case 0xA79C:
        bufpush(0xA79D);
        break;
      case 0xA79E:
        bufpush(0xA79F);
        break;
      case 0xA7A0:
        bufpush(0xA7A1);
        break;
      case 0xA7A2:
        bufpush(0xA7A3);
        break;
      case 0xA7A4:
        bufpush(0xA7A5);
        break;
      case 0xA7A6:
        bufpush(0xA7A7);
        break;
      case 0xA7A8:
        bufpush(0xA7A9);
        break;
      case 0xA7AA:
        bufpush(0x0266);
        break;
      case 0xA7AB:
        bufpush(0x025C);
        break;
      case 0xA7AC:
        bufpush(0x0261);
        break;
      case 0xA7AD:
        bufpush(0x026C);
        break;
      case 0xA7AE:
        bufpush(0x026A);
        break;
      case 0xA7B0:
        bufpush(0x029E);
        break;
      case 0xA7B1:
        bufpush(0x0287);
        break;
      case 0xA7B2:
        bufpush(0x029D);
        break;
      case 0xA7B3:
        bufpush(0xAB53);
        break;
      case 0xA7B4:
        bufpush(0xA7B5);
        break;
      case 0xA7B6:
        bufpush(0xA7B7);
        break;
      case 0xAB70:
        bufpush(0x13A0);
        break;
      case 0xAB71:
        bufpush(0x13A1);
        break;
      case 0xAB72:
        bufpush(0x13A2);
        break;
      case 0xAB73:
        bufpush(0x13A3);
        break;
      case 0xAB74:
        bufpush(0x13A4);
        break;
      case 0xAB75:
        bufpush(0x13A5);
        break;
      case 0xAB76:
        bufpush(0x13A6);
        break;
      case 0xAB77:
        bufpush(0x13A7);
        break;
      case 0xAB78:
        bufpush(0x13A8);
        break;
      case 0xAB79:
        bufpush(0x13A9);
        break;
      case 0xAB7A:
        bufpush(0x13AA);
        break;
      case 0xAB7B:
        bufpush(0x13AB);
        break;
      case 0xAB7C:
        bufpush(0x13AC);
        break;
      case 0xAB7D:
        bufpush(0x13AD);
        break;
      case 0xAB7E:
        bufpush(0x13AE);
        break;
      case 0xAB7F:
        bufpush(0x13AF);
        break;
      case 0xAB80:
        bufpush(0x13B0);
        break;
      case 0xAB81:
        bufpush(0x13B1);
        break;
      case 0xAB82:
        bufpush(0x13B2);
        break;
      case 0xAB83:
        bufpush(0x13B3);
        break;
      case 0xAB84:
        bufpush(0x13B4);
        break;
      case 0xAB85:
        bufpush(0x13B5);
        break;
      case 0xAB86:
        bufpush(0x13B6);
        break;
      case 0xAB87:
        bufpush(0x13B7);
        break;
      case 0xAB88:
        bufpush(0x13B8);
        break;
      case 0xAB89:
        bufpush(0x13B9);
        break;
      case 0xAB8A:
        bufpush(0x13BA);
        break;
      case 0xAB8B:
        bufpush(0x13BB);
        break;
      case 0xAB8C:
        bufpush(0x13BC);
        break;
      case 0xAB8D:
        bufpush(0x13BD);
        break;
      case 0xAB8E:
        bufpush(0x13BE);
        break;
      case 0xAB8F:
        bufpush(0x13BF);
        break;
      case 0xAB90:
        bufpush(0x13C0);
        break;
      case 0xAB91:
        bufpush(0x13C1);
        break;
      case 0xAB92:
        bufpush(0x13C2);
        break;
      case 0xAB93:
        bufpush(0x13C3);
        break;
      case 0xAB94:
        bufpush(0x13C4);
        break;
      case 0xAB95:
        bufpush(0x13C5);
        break;
      case 0xAB96:
        bufpush(0x13C6);
        break;
      case 0xAB97:
        bufpush(0x13C7);
        break;
      case 0xAB98:
        bufpush(0x13C8);
        break;
      case 0xAB99:
        bufpush(0x13C9);
        break;
      case 0xAB9A:
        bufpush(0x13CA);
        break;
      case 0xAB9B:
        bufpush(0x13CB);
        break;
      case 0xAB9C:
        bufpush(0x13CC);
        break;
      case 0xAB9D:
        bufpush(0x13CD);
        break;
      case 0xAB9E:
        bufpush(0x13CE);
        break;
      case 0xAB9F:
        bufpush(0x13CF);
        break;
      case 0xABA0:
        bufpush(0x13D0);
        break;
      case 0xABA1:
        bufpush(0x13D1);
        break;
      case 0xABA2:
        bufpush(0x13D2);
        break;
      case 0xABA3:
        bufpush(0x13D3);
        break;
      case 0xABA4:
        bufpush(0x13D4);
        break;
      case 0xABA5:
        bufpush(0x13D5);
        break;
      case 0xABA6:
        bufpush(0x13D6);
        break;
      case 0xABA7:
        bufpush(0x13D7);
        break;
      case 0xABA8:
        bufpush(0x13D8);
        break;
      case 0xABA9:
        bufpush(0x13D9);
        break;
      case 0xABAA:
        bufpush(0x13DA);
        break;
      case 0xABAB:
        bufpush(0x13DB);
        break;
      case 0xABAC:
        bufpush(0x13DC);
        break;
      case 0xABAD:
        bufpush(0x13DD);
        break;
      case 0xABAE:
        bufpush(0x13DE);
        break;
      case 0xABAF:
        bufpush(0x13DF);
        break;
      case 0xABB0:
        bufpush(0x13E0);
        break;
      case 0xABB1:
        bufpush(0x13E1);
        break;
      case 0xABB2:
        bufpush(0x13E2);
        break;
      case 0xABB3:
        bufpush(0x13E3);
        break;
      case 0xABB4:
        bufpush(0x13E4);
        break;
      case 0xABB5:
        bufpush(0x13E5);
        break;
      case 0xABB6:
        bufpush(0x13E6);
        break;
      case 0xABB7:
        bufpush(0x13E7);
        break;
      case 0xABB8:
        bufpush(0x13E8);
        break;
      case 0xABB9:
        bufpush(0x13E9);
        break;
      case 0xABBA:
        bufpush(0x13EA);
        break;
      case 0xABBB:
        bufpush(0x13EB);
        break;
      case 0xABBC:
        bufpush(0x13EC);
        break;
      case 0xABBD:
        bufpush(0x13ED);
        break;
      case 0xABBE:
        bufpush(0x13EE);
        break;
      case 0xABBF:
        bufpush(0x13EF);
        break;
      case 0xFB00:
        bufpush(0x0066);
        bufpush(0x0066);
        break;
      case 0xFB01:
        bufpush(0x0066);
        bufpush(0x0069);
        break;
      case 0xFB02:
        bufpush(0x0066);
        bufpush(0x006C);
        break;
      case 0xFB03:
        bufpush(0x0066);
        bufpush(0x0066);
        bufpush(0x0069);
        break;
      case 0xFB04:
        bufpush(0x0066);
        bufpush(0x0066);
        bufpush(0x006C);
        break;
      case 0xFB05:
        bufpush(0x0073);
        bufpush(0x0074);
        break;
      case 0xFB06:
        bufpush(0x0073);
        bufpush(0x0074);
        break;
      case 0xFB13:
        bufpush(0x0574);
        bufpush(0x0576);
        break;
      case 0xFB14:
        bufpush(0x0574);
        bufpush(0x0565);
        break;
      case 0xFB15:
        bufpush(0x0574);
        bufpush(0x056B);
        break;
      case 0xFB16:
        bufpush(0x057E);
        bufpush(0x0576);
        break;
      case 0xFB17:
        bufpush(0x0574);
        bufpush(0x056D);
        break;
      case 0xFF21:
        bufpush(0xFF41);
        break;
      case 0xFF22:
        bufpush(0xFF42);
        break;
      case 0xFF23:
        bufpush(0xFF43);
        break;
      case 0xFF24:
        bufpush(0xFF44);
        break;
      case 0xFF25:
        bufpush(0xFF45);
        break;
      case 0xFF26:
        bufpush(0xFF46);
        break;
      case 0xFF27:
        bufpush(0xFF47);
        break;
      case 0xFF28:
        bufpush(0xFF48);
        break;
      case 0xFF29:
        bufpush(0xFF49);
        break;
      case 0xFF2A:
        bufpush(0xFF4A);
        break;
      case 0xFF2B:
        bufpush(0xFF4B);
        break;
      case 0xFF2C:
        bufpush(0xFF4C);
        break;
      case 0xFF2D:
        bufpush(0xFF4D);
        break;
      case 0xFF2E:
        bufpush(0xFF4E);
        break;
      case 0xFF2F:
        bufpush(0xFF4F);
        break;
      case 0xFF30:
        bufpush(0xFF50);
        break;
      case 0xFF31:
        bufpush(0xFF51);
        break;
      case 0xFF32:
        bufpush(0xFF52);
        break;
      case 0xFF33:
        bufpush(0xFF53);
        break;
      case 0xFF34:
        bufpush(0xFF54);
        break;
      case 0xFF35:
        bufpush(0xFF55);
        break;
      case 0xFF36:
        bufpush(0xFF56);
        break;
      case 0xFF37:
        bufpush(0xFF57);
        break;
      case 0xFF38:
        bufpush(0xFF58);
        break;
      case 0xFF39:
        bufpush(0xFF59);
        break;
      case 0xFF3A:
        bufpush(0xFF5A);
        break;
      case 0x10400:
        bufpush(0x10428);
        break;
      case 0x10401:
        bufpush(0x10429);
        break;
      case 0x10402:
        bufpush(0x1042A);
        break;
      case 0x10403:
        bufpush(0x1042B);
        break;
      case 0x10404:
        bufpush(0x1042C);
        break;
      case 0x10405:
        bufpush(0x1042D);
        break;
      case 0x10406:
        bufpush(0x1042E);
        break;
      case 0x10407:
        bufpush(0x1042F);
        break;
      case 0x10408:
        bufpush(0x10430);
        break;
      case 0x10409:
        bufpush(0x10431);
        break;
      case 0x1040A:
        bufpush(0x10432);
        break;
      case 0x1040B:
        bufpush(0x10433);
        break;
      case 0x1040C:
        bufpush(0x10434);
        break;
      case 0x1040D:
        bufpush(0x10435);
        break;
      case 0x1040E:
        bufpush(0x10436);
        break;
      case 0x1040F:
        bufpush(0x10437);
        break;
      case 0x10410:
        bufpush(0x10438);
        break;
      case 0x10411:
        bufpush(0x10439);
        break;
      case 0x10412:
        bufpush(0x1043A);
        break;
      case 0x10413:
        bufpush(0x1043B);
        break;
      case 0x10414:
        bufpush(0x1043C);
        break;
      case 0x10415:
        bufpush(0x1043D);
        break;
      case 0x10416:
        bufpush(0x1043E);
        break;
      case 0x10417:
        bufpush(0x1043F);
        break;
      case 0x10418:
        bufpush(0x10440);
        break;
      case 0x10419:
        bufpush(0x10441);
        break;
      case 0x1041A:
        bufpush(0x10442);
        break;
      case 0x1041B:
        bufpush(0x10443);
        break;
      case 0x1041C:
        bufpush(0x10444);
        break;
      case 0x1041D:
        bufpush(0x10445);
        break;
      case 0x1041E:
        bufpush(0x10446);
        break;
      case 0x1041F:
        bufpush(0x10447);
        break;
      case 0x10420:
        bufpush(0x10448);
        break;
      case 0x10421:
        bufpush(0x10449);
        break;
      case 0x10422:
        bufpush(0x1044A);
        break;
      case 0x10423:
        bufpush(0x1044B);
        break;
      case 0x10424:
        bufpush(0x1044C);
        break;
      case 0x10425:
        bufpush(0x1044D);
        break;
      case 0x10426:
        bufpush(0x1044E);
        break;
      case 0x10427:
        bufpush(0x1044F);
        break;
      case 0x104B0:
        bufpush(0x104D8);
        break;
      case 0x104B1:
        bufpush(0x104D9);
        break;
      case 0x104B2:
        bufpush(0x104DA);
        break;
      case 0x104B3:
        bufpush(0x104DB);
        break;
      case 0x104B4:
        bufpush(0x104DC);
        break;
      case 0x104B5:
        bufpush(0x104DD);
        break;
      case 0x104B6:
        bufpush(0x104DE);
        break;
      case 0x104B7:
        bufpush(0x104DF);
        break;
      case 0x104B8:
        bufpush(0x104E0);
        break;
      case 0x104B9:
        bufpush(0x104E1);
        break;
      case 0x104BA:
        bufpush(0x104E2);
        break;
      case 0x104BB:
        bufpush(0x104E3);
        break;
      case 0x104BC:
        bufpush(0x104E4);
        break;
      case 0x104BD:
        bufpush(0x104E5);
        break;
      case 0x104BE:
        bufpush(0x104E6);
        break;
      case 0x104BF:
        bufpush(0x104E7);
        break;
      case 0x104C0:
        bufpush(0x104E8);
        break;
      case 0x104C1:
        bufpush(0x104E9);
        break;
      case 0x104C2:
        bufpush(0x104EA);
        break;
      case 0x104C3:
        bufpush(0x104EB);
        break;
      case 0x104C4:
        bufpush(0x104EC);
        break;
      case 0x104C5:
        bufpush(0x104ED);
        break;
      case 0x104C6:
        bufpush(0x104EE);
        break;
      case 0x104C7:
        bufpush(0x104EF);
        break;
      case 0x104C8:
        bufpush(0x104F0);
        break;
      case 0x104C9:
        bufpush(0x104F1);
        break;
      case 0x104CA:
        bufpush(0x104F2);
        break;
      case 0x104CB:
        bufpush(0x104F3);
        break;
      case 0x104CC:
        bufpush(0x104F4);
        break;
      case 0x104CD:
        bufpush(0x104F5);
        break;
      case 0x104CE:
        bufpush(0x104F6);
        break;
      case 0x104CF:
        bufpush(0x104F7);
        break;
      case 0x104D0:
        bufpush(0x104F8);
        break;
      case 0x104D1:
        bufpush(0x104F9);
        break;
      case 0x104D2:
        bufpush(0x104FA);
        break;
      case 0x104D3:
        bufpush(0x104FB);
        break;
      case 0x10C80:
        bufpush(0x10CC0);
        break;
      case 0x10C81:
        bufpush(0x10CC1);
        break;
      case 0x10C82:
        bufpush(0x10CC2);
        break;
      case 0x10C83:
        bufpush(0x10CC3);
        break;
      case 0x10C84:
        bufpush(0x10CC4);
        break;
      case 0x10C85:
        bufpush(0x10CC5);
        break;
      case 0x10C86:
        bufpush(0x10CC6);
        break;
      case 0x10C87:
        bufpush(0x10CC7);
        break;
      case 0x10C88:
        bufpush(0x10CC8);
        break;
      case 0x10C89:
        bufpush(0x10CC9);
        break;
      case 0x10C8A:
        bufpush(0x10CCA);
        break;
      case 0x10C8B:
        bufpush(0x10CCB);
        break;
      case 0x10C8C:
        bufpush(0x10CCC);
        break;
      case 0x10C8D:
        bufpush(0x10CCD);
        break;
      case 0x10C8E:
        bufpush(0x10CCE);
        break;
      case 0x10C8F:
        bufpush(0x10CCF);
        break;
      case 0x10C90:
        bufpush(0x10CD0);
        break;
      case 0x10C91:
        bufpush(0x10CD1);
        break;
      case 0x10C92:
        bufpush(0x10CD2);
        break;
      case 0x10C93:
        bufpush(0x10CD3);
        break;
      case 0x10C94:
        bufpush(0x10CD4);
        break;
      case 0x10C95:
        bufpush(0x10CD5);
        break;
      case 0x10C96:
        bufpush(0x10CD6);
        break;
      case 0x10C97:
        bufpush(0x10CD7);
        break;
      case 0x10C98:
        bufpush(0x10CD8);
        break;
      case 0x10C99:
        bufpush(0x10CD9);
        break;
      case 0x10C9A:
        bufpush(0x10CDA);
        break;
      case 0x10C9B:
        bufpush(0x10CDB);
        break;
      case 0x10C9C:
        bufpush(0x10CDC);
        break;
      case 0x10C9D:
        bufpush(0x10CDD);
        break;
      case 0x10C9E:
        bufpush(0x10CDE);
        break;
      case 0x10C9F:
        bufpush(0x10CDF);
        break;
      case 0x10CA0:
        bufpush(0x10CE0);
        break;
      case 0x10CA1:
        bufpush(0x10CE1);
        break;
      case 0x10CA2:
        bufpush(0x10CE2);
        break;
      case 0x10CA3:
        bufpush(0x10CE3);
        break;
      case 0x10CA4:
        bufpush(0x10CE4);
        break;
      case 0x10CA5:
        bufpush(0x10CE5);
        break;
      case 0x10CA6:
        bufpush(0x10CE6);
        break;
      case 0x10CA7:
        bufpush(0x10CE7);
        break;
      case 0x10CA8:
        bufpush(0x10CE8);
        break;
      case 0x10CA9:
        bufpush(0x10CE9);
        break;
      case 0x10CAA:
        bufpush(0x10CEA);
        break;
      case 0x10CAB:
        bufpush(0x10CEB);
        break;
      case 0x10CAC:
        bufpush(0x10CEC);
        break;
      case 0x10CAD:
        bufpush(0x10CED);
        break;
      case 0x10CAE:
        bufpush(0x10CEE);
        break;
      case 0x10CAF:
        bufpush(0x10CEF);
        break;
      case 0x10CB0:
        bufpush(0x10CF0);
        break;
      case 0x10CB1:
        bufpush(0x10CF1);
        break;
      case 0x10CB2:
        bufpush(0x10CF2);
        break;
      case 0x118A0:
        bufpush(0x118C0);
        break;
      case 0x118A1:
        bufpush(0x118C1);
        break;
      case 0x118A2:
        bufpush(0x118C2);
        break;
      case 0x118A3:
        bufpush(0x118C3);
        break;
      case 0x118A4:
        bufpush(0x118C4);
        break;
      case 0x118A5:
        bufpush(0x118C5);
        break;
      case 0x118A6:
        bufpush(0x118C6);
        break;
      case 0x118A7:
        bufpush(0x118C7);
        break;
      case 0x118A8:
        bufpush(0x118C8);
        break;
      case 0x118A9:
        bufpush(0x118C9);
        break;
      case 0x118AA:
        bufpush(0x118CA);
        break;
      case 0x118AB:
        bufpush(0x118CB);
        break;
      case 0x118AC:
        bufpush(0x118CC);
        break;
      case 0x118AD:
        bufpush(0x118CD);
        break;
      case 0x118AE:
        bufpush(0x118CE);
        break;
      case 0x118AF:
        bufpush(0x118CF);
        break;
      case 0x118B0:
        bufpush(0x118D0);
        break;
      case 0x118B1:
        bufpush(0x118D1);
        break;
      case 0x118B2:
        bufpush(0x118D2);
        break;
      case 0x118B3:
        bufpush(0x118D3);
        break;
      case 0x118B4:
        bufpush(0x118D4);
        break;
      case 0x118B5:
        bufpush(0x118D5);
        break;
      case 0x118B6:
        bufpush(0x118D6);
        break;
      case 0x118B7:
        bufpush(0x118D7);
        break;
      case 0x118B8:
        bufpush(0x118D8);
        break;
      case 0x118B9:
        bufpush(0x118D9);
        break;
      case 0x118BA:
        bufpush(0x118DA);
        break;
      case 0x118BB:
        bufpush(0x118DB);
        break;
      case 0x118BC:
        bufpush(0x118DC);
        break;
      case 0x118BD:
        bufpush(0x118DD);
        break;
      case 0x118BE:
        bufpush(0x118DE);
        break;
      case 0x118BF:
        bufpush(0x118DF);
        break;
      case 0x1E900:
        bufpush(0x1E922);
        break;
      case 0x1E901:
        bufpush(0x1E923);
        break;
      case 0x1E902:
        bufpush(0x1E924);
        break;
      case 0x1E903:
        bufpush(0x1E925);
        break;
      case 0x1E904:
        bufpush(0x1E926);
        break;
      case 0x1E905:
        bufpush(0x1E927);
        break;
      case 0x1E906:
        bufpush(0x1E928);
        break;
      case 0x1E907:
        bufpush(0x1E929);
        break;
      case 0x1E908:
        bufpush(0x1E92A);
        break;
      case 0x1E909:
        bufpush(0x1E92B);
        break;
      case 0x1E90A:
        bufpush(0x1E92C);
        break;
      case 0x1E90B:
        bufpush(0x1E92D);
        break;
      case 0x1E90C:
        bufpush(0x1E92E);
        break;
      case 0x1E90D:
        bufpush(0x1E92F);
        break;
      case 0x1E90E:
        bufpush(0x1E930);
        break;
      case 0x1E90F:
        bufpush(0x1E931);
        break;
      case 0x1E910:
        bufpush(0x1E932);
        break;
      case 0x1E911:
        bufpush(0x1E933);
        break;
      case 0x1E912:
        bufpush(0x1E934);
        break;
      case 0x1E913:
        bufpush(0x1E935);
        break;
      case 0x1E914:
        bufpush(0x1E936);
        break;
      case 0x1E915:
        bufpush(0x1E937);
        break;
      case 0x1E916:
        bufpush(0x1E938);
        break;
      case 0x1E917:
        bufpush(0x1E939);
        break;
      case 0x1E918:
        bufpush(0x1E93A);
        break;
      case 0x1E919:
        bufpush(0x1E93B);
        break;
      case 0x1E91A:
        bufpush(0x1E93C);
        break;
      case 0x1E91B:
        bufpush(0x1E93D);
        break;
      case 0x1E91C:
        bufpush(0x1E93E);
        break;
      case 0x1E91D:
        bufpush(0x1E93F);
        break;
      case 0x1E91E:
        bufpush(0x1E940);
        break;
      case 0x1E91F:
        bufpush(0x1E941);
        break;
      case 0x1E920:
        bufpush(0x1E942);
        break;
      case 0x1E921:
        bufpush(0x1E943);
        break;
      default:
        bufpush(c);
    }
