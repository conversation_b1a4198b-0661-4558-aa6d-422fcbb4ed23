/* Generated by re2c 3.0 */
#include "scanners.h"
#include "chunk.h"
#include <stdlib.h>

bufsize_t _scan_at(bufsize_t (*scanner)(const unsigned char *), cmark_chunk *c,
                   bufsize_t offset) {
  bufsize_t res;
  unsigned char *ptr = (unsigned char *)c->data;

  if (ptr == NULL || offset > c->len) {
    return 0;
  } else {
    unsigned char lim = ptr[c->len];

    ptr[c->len] = '\0';
    res = scanner(ptr + offset);
    ptr[c->len] = lim;
  }

  return res;
}

// Try to match a scheme including colon.
bufsize_t _scan_scheme(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    yych = *p;
    if (yych <= '@')
      goto yy1;
    if (yych <= 'Z')
      goto yy3;
    if (yych <= '`')
      goto yy1;
    if (yych <= 'z')
      goto yy3;
  yy1:
    ++p;
  yy2 : { return 0; }
  yy3:
    yych = *(marker = ++p);
    if (yych <= '/') {
      if (yych <= '+') {
        if (yych <= '*')
          goto yy2;
      } else {
        if (yych <= ',')
          goto yy2;
        if (yych >= '/')
          goto yy2;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '9')
          goto yy4;
        if (yych <= '@')
          goto yy2;
      } else {
        if (yych <= '`')
          goto yy2;
        if (yych >= '{')
          goto yy2;
      }
    }
  yy4:
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych == '+')
          goto yy6;
      } else {
        if (yych != '/')
          goto yy6;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych >= 'A')
          goto yy6;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych <= 'z')
          goto yy6;
      }
    }
  yy5:
    p = marker;
    goto yy2;
  yy6:
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych == '+')
          goto yy8;
        goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
        goto yy8;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
        goto yy8;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych <= 'z')
          goto yy8;
        goto yy5;
      }
    }
  yy7:
    ++p;
    { return (bufsize_t)(p - start); }
  yy8:
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy5;
      } else {
        if (yych == '/')
          goto yy5;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy7;
        if (yych <= '@')
          goto yy5;
      } else {
        if (yych <= '`')
          goto yy5;
        if (yych >= '{')
          goto yy5;
      }
    }
    yych = *++p;
    if (yych == ':')
      goto yy7;
    goto yy5;
  }
}

// Try to match URI autolink after first <, returning number of chars matched.
bufsize_t _scan_autolink_uri(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 0,   128, 0,   128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych <= '@')
      goto yy10;
    if (yych <= 'Z')
      goto yy12;
    if (yych <= '`')
      goto yy10;
    if (yych <= 'z')
      goto yy12;
  yy10:
    ++p;
  yy11 : { return 0; }
  yy12:
    yych = *(marker = ++p);
    if (yych <= '/') {
      if (yych <= '+') {
        if (yych <= '*')
          goto yy11;
      } else {
        if (yych <= ',')
          goto yy11;
        if (yych >= '/')
          goto yy11;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '9')
          goto yy13;
        if (yych <= '@')
          goto yy11;
      } else {
        if (yych <= '`')
          goto yy11;
        if (yych >= '{')
          goto yy11;
      }
    }
  yy13:
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych == '+')
          goto yy15;
      } else {
        if (yych != '/')
          goto yy15;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych >= 'A')
          goto yy15;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych <= 'z')
          goto yy15;
      }
    }
  yy14:
    p = marker;
    goto yy11;
  yy15:
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych == '+')
          goto yy17;
        goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
        goto yy17;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
        goto yy17;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych <= 'z')
          goto yy17;
        goto yy14;
      }
    }
  yy16:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy16;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '<')
          goto yy14;
        if (yych <= '>')
          goto yy18;
        goto yy14;
      } else {
        if (yych <= 0xDF)
          goto yy19;
        if (yych <= 0xE0)
          goto yy20;
        goto yy21;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy22;
        if (yych <= 0xEF)
          goto yy21;
        goto yy23;
      } else {
        if (yych <= 0xF3)
          goto yy24;
        if (yych <= 0xF4)
          goto yy25;
        goto yy14;
      }
    }
  yy17:
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych == '+')
          goto yy26;
        goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
        goto yy26;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
        goto yy26;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych <= 'z')
          goto yy26;
        goto yy14;
      }
    }
  yy18:
    ++p;
    { return (bufsize_t)(p - start); }
  yy19:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy14;
    if (yych <= 0xBF)
      goto yy16;
    goto yy14;
  yy20:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy14;
    if (yych <= 0xBF)
      goto yy19;
    goto yy14;
  yy21:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy14;
    if (yych <= 0xBF)
      goto yy19;
    goto yy14;
  yy22:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy14;
    if (yych <= 0x9F)
      goto yy19;
    goto yy14;
  yy23:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy14;
    if (yych <= 0xBF)
      goto yy21;
    goto yy14;
  yy24:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy14;
    if (yych <= 0xBF)
      goto yy21;
    goto yy14;
  yy25:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy14;
    if (yych <= 0x8F)
      goto yy21;
    goto yy14;
  yy26:
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych <= ',') {
        if (yych != '+')
          goto yy14;
      } else {
        if (yych == '/')
          goto yy14;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= ':')
          goto yy16;
        if (yych <= '@')
          goto yy14;
      } else {
        if (yych <= '`')
          goto yy14;
        if (yych >= '{')
          goto yy14;
      }
    }
    yych = *++p;
    if (yych == ':')
      goto yy16;
    goto yy14;
  }
}

// Try to match email autolink after first <, returning num of chars matched.
bufsize_t _scan_autolink_email(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   128, 0,   128, 128, 128, 128, 128, 0,   0,
        128, 128, 0,   128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 0,   0,   0,   128, 0,   128, 0,   128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 0,   0,   0,   128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych <= '9') {
      if (yych <= '\'') {
        if (yych == '!')
          goto yy30;
        if (yych >= '#')
          goto yy30;
      } else {
        if (yych <= ')')
          goto yy28;
        if (yych != ',')
          goto yy30;
      }
    } else {
      if (yych <= '?') {
        if (yych == '=')
          goto yy30;
        if (yych >= '?')
          goto yy30;
      } else {
        if (yych <= 'Z') {
          if (yych >= 'A')
            goto yy30;
        } else {
          if (yych <= ']')
            goto yy28;
          if (yych <= '~')
            goto yy30;
        }
      }
    }
  yy28:
    ++p;
  yy29 : { return 0; }
  yy30:
    yych = *(marker = ++p);
    if (yych <= ',') {
      if (yych <= '"') {
        if (yych == '!')
          goto yy32;
        goto yy29;
      } else {
        if (yych <= '\'')
          goto yy32;
        if (yych <= ')')
          goto yy29;
        if (yych <= '+')
          goto yy32;
        goto yy29;
      }
    } else {
      if (yych <= '>') {
        if (yych <= '9')
          goto yy32;
        if (yych == '=')
          goto yy32;
        goto yy29;
      } else {
        if (yych <= 'Z')
          goto yy32;
        if (yych <= ']')
          goto yy29;
        if (yych <= '~')
          goto yy32;
        goto yy29;
      }
    }
  yy31:
    yych = *++p;
  yy32:
    if (yybm[0 + yych] & 128) {
      goto yy31;
    }
    if (yych <= '>')
      goto yy33;
    if (yych <= '@')
      goto yy34;
  yy33:
    p = marker;
    goto yy29;
  yy34:
    yych = *++p;
    if (yych <= '@') {
      if (yych <= '/')
        goto yy33;
      if (yych >= ':')
        goto yy33;
    } else {
      if (yych <= 'Z')
        goto yy35;
      if (yych <= '`')
        goto yy33;
      if (yych >= '{')
        goto yy33;
    }
  yy35:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy36;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy36;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy36;
        goto yy33;
      }
    }
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy38;
      if (yych <= '/')
        goto yy33;
      goto yy39;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy39;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy39;
        goto yy33;
      }
    }
  yy36:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych <= '-')
          goto yy38;
        goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy39;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy39;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy39;
        goto yy33;
      }
    }
  yy37:
    ++p;
    { return (bufsize_t)(p - start); }
  yy38:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy40;
      if (yych <= '/')
        goto yy33;
      goto yy41;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy41;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy41;
        goto yy33;
      }
    }
  yy39:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy41;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy41;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy41;
        goto yy33;
      }
    }
  yy40:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy42;
      if (yych <= '/')
        goto yy33;
      goto yy43;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy43;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy43;
        goto yy33;
      }
    }
  yy41:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy43;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy43;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy43;
        goto yy33;
      }
    }
  yy42:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy44;
      if (yych <= '/')
        goto yy33;
      goto yy45;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy45;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy45;
        goto yy33;
      }
    }
  yy43:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy45;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy45;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy45;
        goto yy33;
      }
    }
  yy44:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy46;
      if (yych <= '/')
        goto yy33;
      goto yy47;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy47;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy47;
        goto yy33;
      }
    }
  yy45:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy47;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy47;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy47;
        goto yy33;
      }
    }
  yy46:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy48;
      if (yych <= '/')
        goto yy33;
      goto yy49;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy49;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy49;
        goto yy33;
      }
    }
  yy47:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy49;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy49;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy49;
        goto yy33;
      }
    }
  yy48:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy50;
      if (yych <= '/')
        goto yy33;
      goto yy51;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy51;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy51;
        goto yy33;
      }
    }
  yy49:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy51;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy51;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy51;
        goto yy33;
      }
    }
  yy50:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy52;
      if (yych <= '/')
        goto yy33;
      goto yy53;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy53;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy53;
        goto yy33;
      }
    }
  yy51:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy53;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy53;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy53;
        goto yy33;
      }
    }
  yy52:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy54;
      if (yych <= '/')
        goto yy33;
      goto yy55;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy55;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy55;
        goto yy33;
      }
    }
  yy53:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy55;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy55;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy55;
        goto yy33;
      }
    }
  yy54:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy56;
      if (yych <= '/')
        goto yy33;
      goto yy57;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy57;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy57;
        goto yy33;
      }
    }
  yy55:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy57;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy57;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy57;
        goto yy33;
      }
    }
  yy56:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy58;
      if (yych <= '/')
        goto yy33;
      goto yy59;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy59;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy59;
        goto yy33;
      }
    }
  yy57:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy59;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy59;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy59;
        goto yy33;
      }
    }
  yy58:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy60;
      if (yych <= '/')
        goto yy33;
      goto yy61;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy61;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy61;
        goto yy33;
      }
    }
  yy59:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy61;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy61;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy61;
        goto yy33;
      }
    }
  yy60:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy62;
      if (yych <= '/')
        goto yy33;
      goto yy63;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy63;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy63;
        goto yy33;
      }
    }
  yy61:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy63;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy63;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy63;
        goto yy33;
      }
    }
  yy62:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy64;
      if (yych <= '/')
        goto yy33;
      goto yy65;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy65;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy65;
        goto yy33;
      }
    }
  yy63:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy65;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy65;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy65;
        goto yy33;
      }
    }
  yy64:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy66;
      if (yych <= '/')
        goto yy33;
      goto yy67;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy67;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy67;
        goto yy33;
      }
    }
  yy65:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy67;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy67;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy67;
        goto yy33;
      }
    }
  yy66:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy68;
      if (yych <= '/')
        goto yy33;
      goto yy69;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy69;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy69;
        goto yy33;
      }
    }
  yy67:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy69;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy69;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy69;
        goto yy33;
      }
    }
  yy68:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy70;
      if (yych <= '/')
        goto yy33;
      goto yy71;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy71;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy71;
        goto yy33;
      }
    }
  yy69:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy71;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy71;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy71;
        goto yy33;
      }
    }
  yy70:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy72;
      if (yych <= '/')
        goto yy33;
      goto yy73;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy73;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy73;
        goto yy33;
      }
    }
  yy71:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy73;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy73;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy73;
        goto yy33;
      }
    }
  yy72:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy74;
      if (yych <= '/')
        goto yy33;
      goto yy75;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy75;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy75;
        goto yy33;
      }
    }
  yy73:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy75;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy75;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy75;
        goto yy33;
      }
    }
  yy74:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy76;
      if (yych <= '/')
        goto yy33;
      goto yy77;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy77;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy77;
        goto yy33;
      }
    }
  yy75:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy77;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy77;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy77;
        goto yy33;
      }
    }
  yy76:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy78;
      if (yych <= '/')
        goto yy33;
      goto yy79;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy79;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy79;
        goto yy33;
      }
    }
  yy77:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy79;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy79;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy79;
        goto yy33;
      }
    }
  yy78:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy80;
      if (yych <= '/')
        goto yy33;
      goto yy81;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy81;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy81;
        goto yy33;
      }
    }
  yy79:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy81;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy81;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy81;
        goto yy33;
      }
    }
  yy80:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy82;
      if (yych <= '/')
        goto yy33;
      goto yy83;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy83;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy83;
        goto yy33;
      }
    }
  yy81:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy83;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy83;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy83;
        goto yy33;
      }
    }
  yy82:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy84;
      if (yych <= '/')
        goto yy33;
      goto yy85;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy85;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy85;
        goto yy33;
      }
    }
  yy83:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy85;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy85;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy85;
        goto yy33;
      }
    }
  yy84:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy86;
      if (yych <= '/')
        goto yy33;
      goto yy87;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy87;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy87;
        goto yy33;
      }
    }
  yy85:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy87;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy87;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy87;
        goto yy33;
      }
    }
  yy86:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy88;
      if (yych <= '/')
        goto yy33;
      goto yy89;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy89;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy89;
        goto yy33;
      }
    }
  yy87:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy89;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy89;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy89;
        goto yy33;
      }
    }
  yy88:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy90;
      if (yych <= '/')
        goto yy33;
      goto yy91;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy91;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy91;
        goto yy33;
      }
    }
  yy89:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy91;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy91;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy91;
        goto yy33;
      }
    }
  yy90:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy92;
      if (yych <= '/')
        goto yy33;
      goto yy93;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy93;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy93;
        goto yy33;
      }
    }
  yy91:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy93;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy93;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy93;
        goto yy33;
      }
    }
  yy92:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy94;
      if (yych <= '/')
        goto yy33;
      goto yy95;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy95;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy95;
        goto yy33;
      }
    }
  yy93:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy95;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy95;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy95;
        goto yy33;
      }
    }
  yy94:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy96;
      if (yych <= '/')
        goto yy33;
      goto yy97;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy97;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy97;
        goto yy33;
      }
    }
  yy95:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy97;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy97;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy97;
        goto yy33;
      }
    }
  yy96:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy98;
      if (yych <= '/')
        goto yy33;
      goto yy99;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy99;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy99;
        goto yy33;
      }
    }
  yy97:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy99;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy99;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy99;
        goto yy33;
      }
    }
  yy98:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy100;
      if (yych <= '/')
        goto yy33;
      goto yy101;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy101;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy101;
        goto yy33;
      }
    }
  yy99:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy101;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy101;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy101;
        goto yy33;
      }
    }
  yy100:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy102;
      if (yych <= '/')
        goto yy33;
      goto yy103;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy103;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy103;
        goto yy33;
      }
    }
  yy101:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy103;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy103;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy103;
        goto yy33;
      }
    }
  yy102:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy104;
      if (yych <= '/')
        goto yy33;
      goto yy105;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy105;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy105;
        goto yy33;
      }
    }
  yy103:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy105;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy105;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy105;
        goto yy33;
      }
    }
  yy104:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy106;
      if (yych <= '/')
        goto yy33;
      goto yy107;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy107;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy107;
        goto yy33;
      }
    }
  yy105:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy107;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy107;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy107;
        goto yy33;
      }
    }
  yy106:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy108;
      if (yych <= '/')
        goto yy33;
      goto yy109;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy109;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy109;
        goto yy33;
      }
    }
  yy107:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy109;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy109;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy109;
        goto yy33;
      }
    }
  yy108:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy110;
      if (yych <= '/')
        goto yy33;
      goto yy111;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy111;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy111;
        goto yy33;
      }
    }
  yy109:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy111;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy111;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy111;
        goto yy33;
      }
    }
  yy110:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy112;
      if (yych <= '/')
        goto yy33;
      goto yy113;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy113;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy113;
        goto yy33;
      }
    }
  yy111:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy113;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy113;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy113;
        goto yy33;
      }
    }
  yy112:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy114;
      if (yych <= '/')
        goto yy33;
      goto yy115;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy115;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy115;
        goto yy33;
      }
    }
  yy113:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy115;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy115;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy115;
        goto yy33;
      }
    }
  yy114:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy116;
      if (yych <= '/')
        goto yy33;
      goto yy117;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy117;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy117;
        goto yy33;
      }
    }
  yy115:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy117;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy117;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy117;
        goto yy33;
      }
    }
  yy116:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy118;
      if (yych <= '/')
        goto yy33;
      goto yy119;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy119;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy119;
        goto yy33;
      }
    }
  yy117:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy119;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy119;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy119;
        goto yy33;
      }
    }
  yy118:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy120;
      if (yych <= '/')
        goto yy33;
      goto yy121;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy121;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy121;
        goto yy33;
      }
    }
  yy119:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy121;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy121;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy121;
        goto yy33;
      }
    }
  yy120:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy122;
      if (yych <= '/')
        goto yy33;
      goto yy123;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy123;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy123;
        goto yy33;
      }
    }
  yy121:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy123;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy123;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy123;
        goto yy33;
      }
    }
  yy122:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy124;
      if (yych <= '/')
        goto yy33;
      goto yy125;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy125;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy125;
        goto yy33;
      }
    }
  yy123:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy125;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy125;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy125;
        goto yy33;
      }
    }
  yy124:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy126;
      if (yych <= '/')
        goto yy33;
      goto yy127;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy127;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy127;
        goto yy33;
      }
    }
  yy125:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy127;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy127;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy127;
        goto yy33;
      }
    }
  yy126:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy128;
      if (yych <= '/')
        goto yy33;
      goto yy129;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy129;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy129;
        goto yy33;
      }
    }
  yy127:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy129;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy129;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy129;
        goto yy33;
      }
    }
  yy128:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy130;
      if (yych <= '/')
        goto yy33;
      goto yy131;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy131;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy131;
        goto yy33;
      }
    }
  yy129:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy131;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy131;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy131;
        goto yy33;
      }
    }
  yy130:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy132;
      if (yych <= '/')
        goto yy33;
      goto yy133;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy133;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy133;
        goto yy33;
      }
    }
  yy131:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy133;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy133;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy133;
        goto yy33;
      }
    }
  yy132:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy134;
      if (yych <= '/')
        goto yy33;
      goto yy135;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy135;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy135;
        goto yy33;
      }
    }
  yy133:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy135;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy135;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy135;
        goto yy33;
      }
    }
  yy134:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy136;
      if (yych <= '/')
        goto yy33;
      goto yy137;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy137;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy137;
        goto yy33;
      }
    }
  yy135:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy137;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy137;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy137;
        goto yy33;
      }
    }
  yy136:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy138;
      if (yych <= '/')
        goto yy33;
      goto yy139;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy139;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy139;
        goto yy33;
      }
    }
  yy137:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy139;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy139;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy139;
        goto yy33;
      }
    }
  yy138:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy140;
      if (yych <= '/')
        goto yy33;
      goto yy141;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy141;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy141;
        goto yy33;
      }
    }
  yy139:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy141;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy141;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy141;
        goto yy33;
      }
    }
  yy140:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy142;
      if (yych <= '/')
        goto yy33;
      goto yy143;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy143;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy143;
        goto yy33;
      }
    }
  yy141:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy143;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy143;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy143;
        goto yy33;
      }
    }
  yy142:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy144;
      if (yych <= '/')
        goto yy33;
      goto yy145;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy145;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy145;
        goto yy33;
      }
    }
  yy143:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy145;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy145;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy145;
        goto yy33;
      }
    }
  yy144:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy146;
      if (yych <= '/')
        goto yy33;
      goto yy147;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy147;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy147;
        goto yy33;
      }
    }
  yy145:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy147;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy147;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy147;
        goto yy33;
      }
    }
  yy146:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy148;
      if (yych <= '/')
        goto yy33;
      goto yy149;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy149;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy149;
        goto yy33;
      }
    }
  yy147:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy149;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy149;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy149;
        goto yy33;
      }
    }
  yy148:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy150;
      if (yych <= '/')
        goto yy33;
      goto yy151;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy151;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy151;
        goto yy33;
      }
    }
  yy149:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy151;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy151;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy151;
        goto yy33;
      }
    }
  yy150:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy152;
      if (yych <= '/')
        goto yy33;
      goto yy153;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy153;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy153;
        goto yy33;
      }
    }
  yy151:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy153;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy153;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy153;
        goto yy33;
      }
    }
  yy152:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy154;
      if (yych <= '/')
        goto yy33;
      goto yy155;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy155;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy155;
        goto yy33;
      }
    }
  yy153:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy155;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy155;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy155;
        goto yy33;
      }
    }
  yy154:
    yych = *++p;
    if (yych <= '9') {
      if (yych == '-')
        goto yy156;
      if (yych <= '/')
        goto yy33;
      goto yy157;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy33;
        goto yy157;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy157;
        goto yy33;
      }
    }
  yy155:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= ',')
          goto yy33;
        if (yych >= '.')
          goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych <= '9')
          goto yy157;
        goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
        goto yy157;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych <= 'z')
          goto yy157;
        goto yy33;
      }
    }
  yy156:
    yych = *++p;
    if (yych <= '@') {
      if (yych <= '/')
        goto yy33;
      if (yych <= '9')
        goto yy158;
      goto yy33;
    } else {
      if (yych <= 'Z')
        goto yy158;
      if (yych <= '`')
        goto yy33;
      if (yych <= 'z')
        goto yy158;
      goto yy33;
    }
  yy157:
    yych = *++p;
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych <= '-')
          goto yy33;
        goto yy34;
      } else {
        if (yych <= '/')
          goto yy33;
        if (yych >= ':')
          goto yy33;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy37;
        if (yych <= '@')
          goto yy33;
      } else {
        if (yych <= '`')
          goto yy33;
        if (yych >= '{')
          goto yy33;
      }
    }
  yy158:
    yych = *++p;
    if (yych == '.')
      goto yy34;
    if (yych == '>')
      goto yy37;
    goto yy33;
  }
}

// Try to match an HTML tag after first <, returning num of chars matched.
bufsize_t _scan_html_tag(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0,   224, 224, 224, 224, 224, 224, 224, 224, 200, 200, 200, 200, 200,
        224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224,
        224, 224, 224, 224, 200, 224, 128, 224, 224, 224, 224, 64,  224, 224,
        224, 224, 224, 244, 240, 224, 244, 244, 244, 244, 244, 244, 244, 244,
        244, 244, 240, 224, 192, 192, 192, 224, 224, 244, 244, 244, 244, 244,
        244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244,
        244, 244, 244, 244, 244, 244, 244, 224, 224, 224, 224, 240, 192, 244,
        244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244,
        244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 244, 224, 224, 224,
        224, 224, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych <= '@') {
      if (yych == '/')
        goto yy162;
    } else {
      if (yych <= 'Z')
        goto yy163;
      if (yych <= '`')
        goto yy160;
      if (yych <= 'z')
        goto yy163;
    }
  yy160:
    ++p;
  yy161 : { return 0; }
  yy162:
    yych = *(marker = ++p);
    if (yych <= '@')
      goto yy161;
    if (yych <= 'Z')
      goto yy164;
    if (yych <= '`')
      goto yy161;
    if (yych <= 'z')
      goto yy164;
    goto yy161;
  yy163:
    yych = *(marker = ++p);
    if (yych <= '.') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy161;
        if (yych <= '\r')
          goto yy168;
        goto yy161;
      } else {
        if (yych <= ' ')
          goto yy168;
        if (yych == '-')
          goto yy168;
        goto yy161;
      }
    } else {
      if (yych <= '@') {
        if (yych <= '9')
          goto yy168;
        if (yych == '>')
          goto yy168;
        goto yy161;
      } else {
        if (yych <= 'Z')
          goto yy168;
        if (yych <= '`')
          goto yy161;
        if (yych <= 'z')
          goto yy168;
        goto yy161;
      }
    }
  yy164:
    yych = *++p;
    if (yybm[0 + yych] & 4) {
      goto yy164;
    }
    if (yych <= 0x1F) {
      if (yych <= 0x08)
        goto yy165;
      if (yych <= '\r')
        goto yy171;
    } else {
      if (yych <= ' ')
        goto yy171;
      if (yych == '>')
        goto yy170;
    }
  yy165:
    p = marker;
    goto yy161;
  yy166:
    yych = *++p;
    if (yybm[0 + yych] & 8) {
      goto yy166;
    }
    if (yych <= '>') {
      if (yych <= '9') {
        if (yych == '/')
          goto yy169;
        goto yy165;
      } else {
        if (yych <= ':')
          goto yy172;
        if (yych <= '=')
          goto yy165;
        goto yy170;
      }
    } else {
      if (yych <= '^') {
        if (yych <= '@')
          goto yy165;
        if (yych <= 'Z')
          goto yy172;
        goto yy165;
      } else {
        if (yych == '`')
          goto yy165;
        if (yych <= 'z')
          goto yy172;
        goto yy165;
      }
    }
  yy167:
    yych = *++p;
  yy168:
    if (yybm[0 + yych] & 8) {
      goto yy166;
    }
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych == '-')
          goto yy167;
        goto yy165;
      } else {
        if (yych <= '/')
          goto yy169;
        if (yych <= '9')
          goto yy167;
        goto yy165;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy170;
        if (yych <= '@')
          goto yy165;
        goto yy167;
      } else {
        if (yych <= '`')
          goto yy165;
        if (yych <= 'z')
          goto yy167;
        goto yy165;
      }
    }
  yy169:
    yych = *++p;
    if (yych != '>')
      goto yy165;
  yy170:
    ++p;
    { return (bufsize_t)(p - start); }
  yy171:
    yych = *++p;
    if (yych <= 0x1F) {
      if (yych <= 0x08)
        goto yy165;
      if (yych <= '\r')
        goto yy171;
      goto yy165;
    } else {
      if (yych <= ' ')
        goto yy171;
      if (yych == '>')
        goto yy170;
      goto yy165;
    }
  yy172:
    yych = *++p;
    if (yybm[0 + yych] & 16) {
      goto yy172;
    }
    if (yych <= ',') {
      if (yych <= '\r') {
        if (yych <= 0x08)
          goto yy165;
      } else {
        if (yych != ' ')
          goto yy165;
      }
    } else {
      if (yych <= '<') {
        if (yych <= '/')
          goto yy169;
        goto yy165;
      } else {
        if (yych <= '=')
          goto yy174;
        if (yych <= '>')
          goto yy170;
        goto yy165;
      }
    }
  yy173:
    yych = *++p;
    if (yych <= '<') {
      if (yych <= ' ') {
        if (yych <= 0x08)
          goto yy165;
        if (yych <= '\r')
          goto yy173;
        if (yych <= 0x1F)
          goto yy165;
        goto yy173;
      } else {
        if (yych <= '/') {
          if (yych <= '.')
            goto yy165;
          goto yy169;
        } else {
          if (yych == ':')
            goto yy172;
          goto yy165;
        }
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '=')
          goto yy174;
        if (yych <= '>')
          goto yy170;
        if (yych <= '@')
          goto yy165;
        goto yy172;
      } else {
        if (yych <= '_') {
          if (yych <= '^')
            goto yy165;
          goto yy172;
        } else {
          if (yych <= '`')
            goto yy165;
          if (yych <= 'z')
            goto yy172;
          goto yy165;
        }
      }
    }
  yy174:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy175;
    }
    if (yych <= 0xE0) {
      if (yych <= '"') {
        if (yych <= 0x00)
          goto yy165;
        if (yych <= ' ')
          goto yy174;
        goto yy176;
      } else {
        if (yych <= '\'')
          goto yy177;
        if (yych <= 0xC1)
          goto yy165;
        if (yych <= 0xDF)
          goto yy178;
        goto yy179;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy181;
        goto yy180;
      } else {
        if (yych <= 0xF0)
          goto yy182;
        if (yych <= 0xF3)
          goto yy183;
        if (yych <= 0xF4)
          goto yy184;
        goto yy165;
      }
    }
  yy175:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy175;
    }
    if (yych <= 0xE0) {
      if (yych <= '=') {
        if (yych <= 0x00)
          goto yy165;
        if (yych <= ' ')
          goto yy166;
        goto yy165;
      } else {
        if (yych <= '>')
          goto yy170;
        if (yych <= 0xC1)
          goto yy165;
        if (yych <= 0xDF)
          goto yy178;
        goto yy179;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy181;
        goto yy180;
      } else {
        if (yych <= 0xF0)
          goto yy182;
        if (yych <= 0xF3)
          goto yy183;
        if (yych <= 0xF4)
          goto yy184;
        goto yy165;
      }
    }
  yy176:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy176;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy165;
        if (yych <= '"')
          goto yy185;
        goto yy165;
      } else {
        if (yych <= 0xDF)
          goto yy186;
        if (yych <= 0xE0)
          goto yy187;
        goto yy188;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy189;
        if (yych <= 0xEF)
          goto yy188;
        goto yy190;
      } else {
        if (yych <= 0xF3)
          goto yy191;
        if (yych <= 0xF4)
          goto yy192;
        goto yy165;
      }
    }
  yy177:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy177;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy165;
        if (yych <= '\'')
          goto yy185;
        goto yy165;
      } else {
        if (yych <= 0xDF)
          goto yy193;
        if (yych <= 0xE0)
          goto yy194;
        goto yy195;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy196;
        if (yych <= 0xEF)
          goto yy195;
        goto yy197;
      } else {
        if (yych <= 0xF3)
          goto yy198;
        if (yych <= 0xF4)
          goto yy199;
        goto yy165;
      }
    }
  yy178:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy175;
    goto yy165;
  yy179:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy178;
    goto yy165;
  yy180:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy178;
    goto yy165;
  yy181:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0x9F)
      goto yy178;
    goto yy165;
  yy182:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy180;
    goto yy165;
  yy183:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy180;
    goto yy165;
  yy184:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0x8F)
      goto yy180;
    goto yy165;
  yy185:
    yych = *++p;
    if (yybm[0 + yych] & 8) {
      goto yy166;
    }
    if (yych == '/')
      goto yy169;
    if (yych == '>')
      goto yy170;
    goto yy165;
  yy186:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy176;
    goto yy165;
  yy187:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy186;
    goto yy165;
  yy188:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy186;
    goto yy165;
  yy189:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0x9F)
      goto yy186;
    goto yy165;
  yy190:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy188;
    goto yy165;
  yy191:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy188;
    goto yy165;
  yy192:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0x8F)
      goto yy188;
    goto yy165;
  yy193:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy177;
    goto yy165;
  yy194:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy193;
    goto yy165;
  yy195:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy193;
    goto yy165;
  yy196:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0x9F)
      goto yy193;
    goto yy165;
  yy197:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy195;
    goto yy165;
  yy198:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0xBF)
      goto yy195;
    goto yy165;
  yy199:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy165;
    if (yych <= 0x8F)
      goto yy195;
    goto yy165;
  }
}

// Try to (liberally) match an HTML tag after first <, returning num of chars
// matched.
bufsize_t _scan_liberal_html_tag(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,  64, 64, 64, 64, 64, 64, 64, 64,  64, 0,  64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 128, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,
    };
    yych = *p;
    if (yych <= 0xE0) {
      if (yych <= '\n') {
        if (yych <= 0x00)
          goto yy201;
        if (yych <= '\t')
          goto yy203;
      } else {
        if (yych <= 0x7F)
          goto yy203;
        if (yych <= 0xC1)
          goto yy201;
        if (yych <= 0xDF)
          goto yy204;
        goto yy205;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy207;
        goto yy206;
      } else {
        if (yych <= 0xF0)
          goto yy208;
        if (yych <= 0xF3)
          goto yy209;
        if (yych <= 0xF4)
          goto yy210;
      }
    }
  yy201:
    ++p;
  yy202 : { return 0; }
  yy203:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy202;
      if (yych <= '\t')
        goto yy212;
      goto yy202;
    } else {
      if (yych <= 0x7F)
        goto yy212;
      if (yych <= 0xC1)
        goto yy202;
      if (yych <= 0xF4)
        goto yy212;
      goto yy202;
    }
  yy204:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy202;
    if (yych <= 0xBF)
      goto yy211;
    goto yy202;
  yy205:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x9F)
      goto yy202;
    if (yych <= 0xBF)
      goto yy216;
    goto yy202;
  yy206:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy202;
    if (yych <= 0xBF)
      goto yy216;
    goto yy202;
  yy207:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy202;
    if (yych <= 0x9F)
      goto yy216;
    goto yy202;
  yy208:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x8F)
      goto yy202;
    if (yych <= 0xBF)
      goto yy218;
    goto yy202;
  yy209:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy202;
    if (yych <= 0xBF)
      goto yy218;
    goto yy202;
  yy210:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy202;
    if (yych <= 0x8F)
      goto yy218;
    goto yy202;
  yy211:
    yych = *++p;
  yy212:
    if (yybm[0 + yych] & 64) {
      goto yy211;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy213;
        if (yych <= '>')
          goto yy214;
      } else {
        if (yych <= 0xDF)
          goto yy216;
        if (yych <= 0xE0)
          goto yy217;
        goto yy218;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy219;
        if (yych <= 0xEF)
          goto yy218;
        goto yy220;
      } else {
        if (yych <= 0xF3)
          goto yy221;
        if (yych <= 0xF4)
          goto yy222;
      }
    }
  yy213:
    p = marker;
    if (yyaccept == 0) {
      goto yy202;
    } else {
      goto yy215;
    }
  yy214:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy211;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy215;
        if (yych <= '>')
          goto yy214;
      } else {
        if (yych <= 0xDF)
          goto yy216;
        if (yych <= 0xE0)
          goto yy217;
        goto yy218;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy219;
        if (yych <= 0xEF)
          goto yy218;
        goto yy220;
      } else {
        if (yych <= 0xF3)
          goto yy221;
        if (yych <= 0xF4)
          goto yy222;
      }
    }
  yy215 : { return (bufsize_t)(p - start); }
  yy216:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy213;
    if (yych <= 0xBF)
      goto yy211;
    goto yy213;
  yy217:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy213;
    if (yych <= 0xBF)
      goto yy216;
    goto yy213;
  yy218:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy213;
    if (yych <= 0xBF)
      goto yy216;
    goto yy213;
  yy219:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy213;
    if (yych <= 0x9F)
      goto yy216;
    goto yy213;
  yy220:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy213;
    if (yych <= 0xBF)
      goto yy218;
    goto yy213;
  yy221:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy213;
    if (yych <= 0xBF)
      goto yy218;
    goto yy213;
  yy222:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy213;
    if (yych <= 0x8F)
      goto yy218;
    goto yy213;
  }
}

bufsize_t _scan_html_comment(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0,   128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 0,   128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych == '-')
      goto yy225;
    ++p;
  yy224 : { return 0; }
  yy225:
    yych = *(marker = ++p);
    if (yych != '-')
      goto yy224;
  yy226:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy226;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy227;
        if (yych <= '-')
          goto yy228;
      } else {
        if (yych <= 0xDF)
          goto yy229;
        if (yych <= 0xE0)
          goto yy230;
        goto yy231;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy232;
        if (yych <= 0xEF)
          goto yy231;
        goto yy233;
      } else {
        if (yych <= 0xF3)
          goto yy234;
        if (yych <= 0xF4)
          goto yy235;
      }
    }
  yy227:
    p = marker;
    goto yy224;
  yy228:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy226;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy227;
        if (yych <= '-')
          goto yy236;
        goto yy227;
      } else {
        if (yych <= 0xDF)
          goto yy229;
        if (yych <= 0xE0)
          goto yy230;
        goto yy231;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy232;
        if (yych <= 0xEF)
          goto yy231;
        goto yy233;
      } else {
        if (yych <= 0xF3)
          goto yy234;
        if (yych <= 0xF4)
          goto yy235;
        goto yy227;
      }
    }
  yy229:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy227;
    if (yych <= 0xBF)
      goto yy226;
    goto yy227;
  yy230:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy227;
    if (yych <= 0xBF)
      goto yy229;
    goto yy227;
  yy231:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy227;
    if (yych <= 0xBF)
      goto yy229;
    goto yy227;
  yy232:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy227;
    if (yych <= 0x9F)
      goto yy229;
    goto yy227;
  yy233:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy227;
    if (yych <= 0xBF)
      goto yy231;
    goto yy227;
  yy234:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy227;
    if (yych <= 0xBF)
      goto yy231;
    goto yy227;
  yy235:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy227;
    if (yych <= 0x8F)
      goto yy231;
    goto yy227;
  yy236:
    yych = *++p;
    if (yych <= 0xE0) {
      if (yych <= '>') {
        if (yych <= 0x00)
          goto yy227;
        if (yych <= '=')
          goto yy226;
      } else {
        if (yych <= 0x7F)
          goto yy226;
        if (yych <= 0xC1)
          goto yy227;
        if (yych <= 0xDF)
          goto yy229;
        goto yy230;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy232;
        goto yy231;
      } else {
        if (yych <= 0xF0)
          goto yy233;
        if (yych <= 0xF3)
          goto yy234;
        if (yych <= 0xF4)
          goto yy235;
        goto yy227;
      }
    }
    ++p;
    { return (bufsize_t)(p - start); }
  }
}

bufsize_t _scan_html_pi(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,   128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 0,   128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yybm[0 + yych] & 128) {
      goto yy240;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy238;
        if (yych <= '?')
          goto yy243;
      } else {
        if (yych <= 0xDF)
          goto yy244;
        if (yych <= 0xE0)
          goto yy245;
        goto yy246;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy247;
        if (yych <= 0xEF)
          goto yy246;
        goto yy248;
      } else {
        if (yych <= 0xF3)
          goto yy249;
        if (yych <= 0xF4)
          goto yy250;
      }
    }
  yy238:
    ++p;
  yy239 : { return 0; }
  yy240:
    yyaccept = 0;
    yych = *(marker = ++p);
  yy241:
    if (yybm[0 + yych] & 128) {
      goto yy240;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy242;
        if (yych <= '?')
          goto yy251;
      } else {
        if (yych <= 0xDF)
          goto yy253;
        if (yych <= 0xE0)
          goto yy254;
        goto yy255;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy256;
        if (yych <= 0xEF)
          goto yy255;
        goto yy257;
      } else {
        if (yych <= 0xF3)
          goto yy258;
        if (yych <= 0xF4)
          goto yy259;
      }
    }
  yy242 : { return (bufsize_t)(p - start); }
  yy243:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych <= '?') {
      if (yych <= 0x00)
        goto yy239;
      if (yych <= '=')
        goto yy241;
      if (yych <= '>')
        goto yy239;
      goto yy240;
    } else {
      if (yych <= 0x7F)
        goto yy241;
      if (yych <= 0xC1)
        goto yy239;
      if (yych <= 0xF4)
        goto yy241;
      goto yy239;
    }
  yy244:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy239;
    if (yych <= 0xBF)
      goto yy240;
    goto yy239;
  yy245:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych <= 0x9F)
      goto yy239;
    if (yych <= 0xBF)
      goto yy253;
    goto yy239;
  yy246:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy239;
    if (yych <= 0xBF)
      goto yy253;
    goto yy239;
  yy247:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy239;
    if (yych <= 0x9F)
      goto yy253;
    goto yy239;
  yy248:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych <= 0x8F)
      goto yy239;
    if (yych <= 0xBF)
      goto yy255;
    goto yy239;
  yy249:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy239;
    if (yych <= 0xBF)
      goto yy255;
    goto yy239;
  yy250:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy239;
    if (yych <= 0x8F)
      goto yy255;
    goto yy239;
  yy251:
    yych = *++p;
    if (yych <= 0xE0) {
      if (yych <= '>') {
        if (yych <= 0x00)
          goto yy252;
        if (yych <= '=')
          goto yy240;
      } else {
        if (yych <= 0x7F)
          goto yy240;
        if (yych <= 0xC1)
          goto yy252;
        if (yych <= 0xDF)
          goto yy253;
        goto yy254;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy256;
        goto yy255;
      } else {
        if (yych <= 0xF0)
          goto yy257;
        if (yych <= 0xF3)
          goto yy258;
        if (yych <= 0xF4)
          goto yy259;
      }
    }
  yy252:
    p = marker;
    if (yyaccept == 0) {
      goto yy242;
    } else {
      goto yy239;
    }
  yy253:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy252;
    if (yych <= 0xBF)
      goto yy240;
    goto yy252;
  yy254:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy252;
    if (yych <= 0xBF)
      goto yy253;
    goto yy252;
  yy255:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy252;
    if (yych <= 0xBF)
      goto yy253;
    goto yy252;
  yy256:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy252;
    if (yych <= 0x9F)
      goto yy253;
    goto yy252;
  yy257:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy252;
    if (yych <= 0xBF)
      goto yy255;
    goto yy252;
  yy258:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy252;
    if (yych <= 0xBF)
      goto yy255;
    goto yy252;
  yy259:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy252;
    if (yych <= 0x8F)
      goto yy255;
    goto yy252;
  }
}

bufsize_t _scan_html_declaration(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,   64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,
        64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,
        64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,
        64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,
        64,  64,  64,  64,  64,  64,  0,   64,  64,  192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 64,  64,  64,  64,  64,  64,  64,
        64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,
        64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,  64,
        64,  64,  0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych <= '@')
      goto yy261;
    if (yych <= 'Z')
      goto yy263;
  yy261:
    ++p;
  yy262 : { return 0; }
  yy263:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy266;
    }
    if (yych <= 0x08)
      goto yy262;
    if (yych <= '\r')
      goto yy264;
    if (yych != ' ')
      goto yy262;
  yy264:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy264;
    }
    if (yych <= 0xED) {
      if (yych <= 0xDF) {
        if (yych >= 0xC2)
          goto yy268;
      } else {
        if (yych <= 0xE0)
          goto yy269;
        if (yych <= 0xEC)
          goto yy270;
        goto yy271;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xEF)
          goto yy270;
        goto yy272;
      } else {
        if (yych <= 0xF3)
          goto yy273;
        if (yych <= 0xF4)
          goto yy274;
      }
    }
  yy265 : { return (bufsize_t)(p - start); }
  yy266:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy266;
    }
    if (yych <= 0x08)
      goto yy267;
    if (yych <= '\r')
      goto yy264;
    if (yych == ' ')
      goto yy264;
  yy267:
    p = marker;
    if (yyaccept == 0) {
      goto yy262;
    } else {
      goto yy265;
    }
  yy268:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy267;
    if (yych <= 0xBF)
      goto yy264;
    goto yy267;
  yy269:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy267;
    if (yych <= 0xBF)
      goto yy268;
    goto yy267;
  yy270:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy267;
    if (yych <= 0xBF)
      goto yy268;
    goto yy267;
  yy271:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy267;
    if (yych <= 0x9F)
      goto yy268;
    goto yy267;
  yy272:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy267;
    if (yych <= 0xBF)
      goto yy270;
    goto yy267;
  yy273:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy267;
    if (yych <= 0xBF)
      goto yy270;
    goto yy267;
  yy274:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy267;
    if (yych <= 0x8F)
      goto yy270;
    goto yy267;
  }
}

bufsize_t _scan_html_cdata(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,   128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 0,   128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych == 'C')
      goto yy277;
    if (yych == 'c')
      goto yy277;
    ++p;
  yy276 : { return 0; }
  yy277:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych == 'D')
      goto yy278;
    if (yych != 'd')
      goto yy276;
  yy278:
    yych = *++p;
    if (yych == 'A')
      goto yy280;
    if (yych == 'a')
      goto yy280;
  yy279:
    p = marker;
    if (yyaccept == 0) {
      goto yy276;
    } else {
      goto yy284;
    }
  yy280:
    yych = *++p;
    if (yych == 'T')
      goto yy281;
    if (yych != 't')
      goto yy279;
  yy281:
    yych = *++p;
    if (yych == 'A')
      goto yy282;
    if (yych != 'a')
      goto yy279;
  yy282:
    yych = *++p;
    if (yych != '[')
      goto yy279;
  yy283:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy283;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy284;
        if (yych <= ']')
          goto yy285;
      } else {
        if (yych <= 0xDF)
          goto yy286;
        if (yych <= 0xE0)
          goto yy287;
        goto yy288;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy289;
        if (yych <= 0xEF)
          goto yy288;
        goto yy290;
      } else {
        if (yych <= 0xF3)
          goto yy291;
        if (yych <= 0xF4)
          goto yy292;
      }
    }
  yy284 : { return (bufsize_t)(p - start); }
  yy285:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy283;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy279;
        if (yych <= ']')
          goto yy293;
        goto yy279;
      } else {
        if (yych <= 0xDF)
          goto yy286;
        if (yych <= 0xE0)
          goto yy287;
        goto yy288;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy289;
        if (yych <= 0xEF)
          goto yy288;
        goto yy290;
      } else {
        if (yych <= 0xF3)
          goto yy291;
        if (yych <= 0xF4)
          goto yy292;
        goto yy279;
      }
    }
  yy286:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy279;
    if (yych <= 0xBF)
      goto yy283;
    goto yy279;
  yy287:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy279;
    if (yych <= 0xBF)
      goto yy286;
    goto yy279;
  yy288:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy279;
    if (yych <= 0xBF)
      goto yy286;
    goto yy279;
  yy289:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy279;
    if (yych <= 0x9F)
      goto yy286;
    goto yy279;
  yy290:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy279;
    if (yych <= 0xBF)
      goto yy288;
    goto yy279;
  yy291:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy279;
    if (yych <= 0xBF)
      goto yy288;
    goto yy279;
  yy292:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy279;
    if (yych <= 0x8F)
      goto yy288;
    goto yy279;
  yy293:
    yych = *++p;
    if (yych <= 0xE0) {
      if (yych <= '>') {
        if (yych <= 0x00)
          goto yy279;
        if (yych <= '=')
          goto yy283;
        goto yy279;
      } else {
        if (yych <= 0x7F)
          goto yy283;
        if (yych <= 0xC1)
          goto yy279;
        if (yych <= 0xDF)
          goto yy286;
        goto yy287;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy289;
        goto yy288;
      } else {
        if (yych <= 0xF0)
          goto yy290;
        if (yych <= 0xF3)
          goto yy291;
        if (yych <= 0xF4)
          goto yy292;
        goto yy279;
      }
    }
  }
}

// Try to match an HTML block tag start line, returning
// an integer code for the type of block (1-6, matching the spec).
// #7 is handled by a separate function, below.
bufsize_t _scan_html_block_start(const unsigned char *p) {
  const unsigned char *marker = NULL;

  {
    unsigned char yych;
    yych = *p;
    if (yych == '<')
      goto yy296;
    ++p;
  yy295 : { return 0; }
  yy296:
    yych = *(marker = ++p);
    switch (yych) {
    case '!':
      goto yy297;
    case '/':
      goto yy299;
    case '?':
      goto yy300;
    case 'A':
    case 'a':
      goto yy301;
    case 'B':
    case 'b':
      goto yy302;
    case 'C':
    case 'c':
      goto yy303;
    case 'D':
    case 'd':
      goto yy304;
    case 'F':
    case 'f':
      goto yy305;
    case 'H':
    case 'h':
      goto yy306;
    case 'I':
    case 'i':
      goto yy307;
    case 'L':
    case 'l':
      goto yy308;
    case 'M':
    case 'm':
      goto yy309;
    case 'N':
    case 'n':
      goto yy310;
    case 'O':
    case 'o':
      goto yy311;
    case 'P':
    case 'p':
      goto yy312;
    case 'S':
    case 's':
      goto yy313;
    case 'T':
    case 't':
      goto yy314;
    case 'U':
    case 'u':
      goto yy315;
    default:
      goto yy295;
    }
  yy297:
    yych = *++p;
    if (yych <= '@') {
      if (yych == '-')
        goto yy316;
    } else {
      if (yych <= 'Z')
        goto yy317;
      if (yych <= '[')
        goto yy318;
    }
  yy298:
    p = marker;
    goto yy295;
  yy299:
    yych = *++p;
    switch (yych) {
    case 'A':
    case 'a':
      goto yy301;
    case 'B':
    case 'b':
      goto yy302;
    case 'C':
    case 'c':
      goto yy303;
    case 'D':
    case 'd':
      goto yy304;
    case 'F':
    case 'f':
      goto yy305;
    case 'H':
    case 'h':
      goto yy306;
    case 'I':
    case 'i':
      goto yy307;
    case 'L':
    case 'l':
      goto yy308;
    case 'M':
    case 'm':
      goto yy309;
    case 'N':
    case 'n':
      goto yy310;
    case 'O':
    case 'o':
      goto yy311;
    case 'P':
    case 'p':
      goto yy319;
    case 'S':
    case 's':
      goto yy320;
    case 'T':
    case 't':
      goto yy321;
    case 'U':
    case 'u':
      goto yy315;
    default:
      goto yy298;
    }
  yy300:
    ++p;
    { return 3; }
  yy301:
    yych = *++p;
    if (yych <= 'S') {
      if (yych <= 'D') {
        if (yych <= 'C')
          goto yy298;
        goto yy322;
      } else {
        if (yych <= 'Q')
          goto yy298;
        if (yych <= 'R')
          goto yy323;
        goto yy324;
      }
    } else {
      if (yych <= 'q') {
        if (yych == 'd')
          goto yy322;
        goto yy298;
      } else {
        if (yych <= 'r')
          goto yy323;
        if (yych <= 's')
          goto yy324;
        goto yy298;
      }
    }
  yy302:
    yych = *++p;
    if (yych <= 'O') {
      if (yych <= 'K') {
        if (yych == 'A')
          goto yy325;
        goto yy298;
      } else {
        if (yych <= 'L')
          goto yy326;
        if (yych <= 'N')
          goto yy298;
        goto yy327;
      }
    } else {
      if (yych <= 'k') {
        if (yych == 'a')
          goto yy325;
        goto yy298;
      } else {
        if (yych <= 'l')
          goto yy326;
        if (yych == 'o')
          goto yy327;
        goto yy298;
      }
    }
  yy303:
    yych = *++p;
    if (yych <= 'O') {
      if (yych <= 'D') {
        if (yych == 'A')
          goto yy328;
        goto yy298;
      } else {
        if (yych <= 'E')
          goto yy329;
        if (yych <= 'N')
          goto yy298;
        goto yy330;
      }
    } else {
      if (yych <= 'd') {
        if (yych == 'a')
          goto yy328;
        goto yy298;
      } else {
        if (yych <= 'e')
          goto yy329;
        if (yych == 'o')
          goto yy330;
        goto yy298;
      }
    }
  yy304:
    yych = *++p;
    switch (yych) {
    case 'D':
    case 'L':
    case 'T':
    case 'd':
    case 'l':
    case 't':
      goto yy331;
    case 'E':
    case 'e':
      goto yy332;
    case 'I':
    case 'i':
      goto yy333;
    default:
      goto yy298;
    }
  yy305:
    yych = *++p;
    if (yych <= 'R') {
      if (yych <= 'N') {
        if (yych == 'I')
          goto yy334;
        goto yy298;
      } else {
        if (yych <= 'O')
          goto yy335;
        if (yych <= 'Q')
          goto yy298;
        goto yy336;
      }
    } else {
      if (yych <= 'n') {
        if (yych == 'i')
          goto yy334;
        goto yy298;
      } else {
        if (yych <= 'o')
          goto yy335;
        if (yych == 'r')
          goto yy336;
        goto yy298;
      }
    }
  yy306:
    yych = *++p;
    if (yych <= 'S') {
      if (yych <= 'D') {
        if (yych <= '0')
          goto yy298;
        if (yych <= '6')
          goto yy331;
        goto yy298;
      } else {
        if (yych <= 'E')
          goto yy337;
        if (yych == 'R')
          goto yy331;
        goto yy298;
      }
    } else {
      if (yych <= 'q') {
        if (yych <= 'T')
          goto yy338;
        if (yych == 'e')
          goto yy337;
        goto yy298;
      } else {
        if (yych <= 'r')
          goto yy331;
        if (yych == 't')
          goto yy338;
        goto yy298;
      }
    }
  yy307:
    yych = *++p;
    if (yych == 'F')
      goto yy339;
    if (yych == 'f')
      goto yy339;
    goto yy298;
  yy308:
    yych = *++p;
    if (yych <= 'I') {
      if (yych == 'E')
        goto yy340;
      if (yych <= 'H')
        goto yy298;
      goto yy341;
    } else {
      if (yych <= 'e') {
        if (yych <= 'd')
          goto yy298;
        goto yy340;
      } else {
        if (yych == 'i')
          goto yy341;
        goto yy298;
      }
    }
  yy309:
    yych = *++p;
    if (yych <= 'E') {
      if (yych == 'A')
        goto yy342;
      if (yych <= 'D')
        goto yy298;
      goto yy343;
    } else {
      if (yych <= 'a') {
        if (yych <= '`')
          goto yy298;
        goto yy342;
      } else {
        if (yych == 'e')
          goto yy343;
        goto yy298;
      }
    }
  yy310:
    yych = *++p;
    if (yych <= 'O') {
      if (yych == 'A')
        goto yy344;
      if (yych <= 'N')
        goto yy298;
      goto yy345;
    } else {
      if (yych <= 'a') {
        if (yych <= '`')
          goto yy298;
        goto yy344;
      } else {
        if (yych == 'o')
          goto yy345;
        goto yy298;
      }
    }
  yy311:
    yych = *++p;
    if (yych <= 'P') {
      if (yych == 'L')
        goto yy331;
      if (yych <= 'O')
        goto yy298;
      goto yy346;
    } else {
      if (yych <= 'l') {
        if (yych <= 'k')
          goto yy298;
        goto yy331;
      } else {
        if (yych == 'p')
          goto yy346;
        goto yy298;
      }
    }
  yy312:
    yych = *++p;
    if (yych <= '>') {
      if (yych <= ' ') {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        if (yych <= 0x1F)
          goto yy298;
        goto yy347;
      } else {
        if (yych == '/')
          goto yy348;
        if (yych <= '=')
          goto yy298;
        goto yy347;
      }
    } else {
      if (yych <= 'R') {
        if (yych == 'A')
          goto yy349;
        if (yych <= 'Q')
          goto yy298;
        goto yy350;
      } else {
        if (yych <= 'a') {
          if (yych <= '`')
            goto yy298;
          goto yy349;
        } else {
          if (yych == 'r')
            goto yy350;
          goto yy298;
        }
      }
    }
  yy313:
    yych = *++p;
    switch (yych) {
    case 'C':
    case 'c':
      goto yy351;
    case 'E':
    case 'e':
      goto yy352;
    case 'O':
    case 'o':
      goto yy353;
    case 'T':
    case 't':
      goto yy354;
    case 'U':
    case 'u':
      goto yy355;
    default:
      goto yy298;
    }
  yy314:
    yych = *++p;
    switch (yych) {
    case 'A':
    case 'a':
      goto yy356;
    case 'B':
    case 'b':
      goto yy357;
    case 'D':
    case 'd':
      goto yy331;
    case 'E':
    case 'e':
      goto yy358;
    case 'F':
    case 'f':
      goto yy359;
    case 'H':
    case 'h':
      goto yy360;
    case 'I':
    case 'i':
      goto yy361;
    case 'R':
    case 'r':
      goto yy362;
    default:
      goto yy298;
    }
  yy315:
    yych = *++p;
    if (yych == 'L')
      goto yy331;
    if (yych == 'l')
      goto yy331;
    goto yy298;
  yy316:
    yych = *++p;
    if (yych == '-')
      goto yy363;
    goto yy298;
  yy317:
    ++p;
    { return 4; }
  yy318:
    yych = *++p;
    if (yych == 'C')
      goto yy364;
    if (yych == 'c')
      goto yy364;
    goto yy298;
  yy319:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= '@') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'A')
          goto yy349;
        if (yych == 'a')
          goto yy349;
        goto yy298;
      }
    }
  yy320:
    yych = *++p;
    if (yych <= 'U') {
      if (yych <= 'N') {
        if (yych == 'E')
          goto yy352;
        goto yy298;
      } else {
        if (yych <= 'O')
          goto yy353;
        if (yych <= 'T')
          goto yy298;
        goto yy355;
      }
    } else {
      if (yych <= 'n') {
        if (yych == 'e')
          goto yy352;
        goto yy298;
      } else {
        if (yych <= 'o')
          goto yy353;
        if (yych == 'u')
          goto yy355;
        goto yy298;
      }
    }
  yy321:
    yych = *++p;
    switch (yych) {
    case 'A':
    case 'a':
      goto yy356;
    case 'B':
    case 'b':
      goto yy357;
    case 'D':
    case 'd':
      goto yy331;
    case 'F':
    case 'f':
      goto yy359;
    case 'H':
    case 'h':
      goto yy360;
    case 'I':
    case 'i':
      goto yy361;
    case 'R':
    case 'r':
      goto yy362;
    default:
      goto yy298;
    }
  yy322:
    yych = *++p;
    if (yych == 'D')
      goto yy365;
    if (yych == 'd')
      goto yy365;
    goto yy298;
  yy323:
    yych = *++p;
    if (yych == 'T')
      goto yy366;
    if (yych == 't')
      goto yy366;
    goto yy298;
  yy324:
    yych = *++p;
    if (yych == 'I')
      goto yy367;
    if (yych == 'i')
      goto yy367;
    goto yy298;
  yy325:
    yych = *++p;
    if (yych == 'S')
      goto yy368;
    if (yych == 's')
      goto yy368;
    goto yy298;
  yy326:
    yych = *++p;
    if (yych == 'O')
      goto yy369;
    if (yych == 'o')
      goto yy369;
    goto yy298;
  yy327:
    yych = *++p;
    if (yych == 'D')
      goto yy370;
    if (yych == 'd')
      goto yy370;
    goto yy298;
  yy328:
    yych = *++p;
    if (yych == 'P')
      goto yy371;
    if (yych == 'p')
      goto yy371;
    goto yy298;
  yy329:
    yych = *++p;
    if (yych == 'N')
      goto yy372;
    if (yych == 'n')
      goto yy372;
    goto yy298;
  yy330:
    yych = *++p;
    if (yych == 'L')
      goto yy373;
    if (yych == 'l')
      goto yy373;
    goto yy298;
  yy331:
    yych = *++p;
    if (yych <= ' ') {
      if (yych <= 0x08)
        goto yy298;
      if (yych <= '\r')
        goto yy347;
      if (yych <= 0x1F)
        goto yy298;
      goto yy347;
    } else {
      if (yych <= '/') {
        if (yych <= '.')
          goto yy298;
        goto yy348;
      } else {
        if (yych == '>')
          goto yy347;
        goto yy298;
      }
    }
  yy332:
    yych = *++p;
    if (yych == 'T')
      goto yy374;
    if (yych == 't')
      goto yy374;
    goto yy298;
  yy333:
    yych = *++p;
    if (yych <= 'V') {
      if (yych <= 'Q') {
        if (yych == 'A')
          goto yy375;
        goto yy298;
      } else {
        if (yych <= 'R')
          goto yy331;
        if (yych <= 'U')
          goto yy298;
        goto yy331;
      }
    } else {
      if (yych <= 'q') {
        if (yych == 'a')
          goto yy375;
        goto yy298;
      } else {
        if (yych <= 'r')
          goto yy331;
        if (yych == 'v')
          goto yy331;
        goto yy298;
      }
    }
  yy334:
    yych = *++p;
    if (yych <= 'G') {
      if (yych == 'E')
        goto yy376;
      if (yych <= 'F')
        goto yy298;
      goto yy377;
    } else {
      if (yych <= 'e') {
        if (yych <= 'd')
          goto yy298;
        goto yy376;
      } else {
        if (yych == 'g')
          goto yy377;
        goto yy298;
      }
    }
  yy335:
    yych = *++p;
    if (yych <= 'R') {
      if (yych == 'O')
        goto yy372;
      if (yych <= 'Q')
        goto yy298;
      goto yy378;
    } else {
      if (yych <= 'o') {
        if (yych <= 'n')
          goto yy298;
        goto yy372;
      } else {
        if (yych == 'r')
          goto yy378;
        goto yy298;
      }
    }
  yy336:
    yych = *++p;
    if (yych == 'A')
      goto yy379;
    if (yych == 'a')
      goto yy379;
    goto yy298;
  yy337:
    yych = *++p;
    if (yych == 'A')
      goto yy380;
    if (yych == 'a')
      goto yy380;
    goto yy298;
  yy338:
    yych = *++p;
    if (yych == 'M')
      goto yy315;
    if (yych == 'm')
      goto yy315;
    goto yy298;
  yy339:
    yych = *++p;
    if (yych == 'R')
      goto yy381;
    if (yych == 'r')
      goto yy381;
    goto yy298;
  yy340:
    yych = *++p;
    if (yych == 'G')
      goto yy382;
    if (yych == 'g')
      goto yy382;
    goto yy298;
  yy341:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= 'M') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'N')
          goto yy383;
        if (yych == 'n')
          goto yy383;
        goto yy298;
      }
    }
  yy342:
    yych = *++p;
    if (yych == 'I')
      goto yy384;
    if (yych == 'i')
      goto yy384;
    goto yy298;
  yy343:
    yych = *++p;
    if (yych == 'N')
      goto yy385;
    if (yych == 'n')
      goto yy385;
    goto yy298;
  yy344:
    yych = *++p;
    if (yych == 'V')
      goto yy331;
    if (yych == 'v')
      goto yy331;
    goto yy298;
  yy345:
    yych = *++p;
    if (yych == 'F')
      goto yy386;
    if (yych == 'f')
      goto yy386;
    goto yy298;
  yy346:
    yych = *++p;
    if (yych == 'T')
      goto yy387;
    if (yych == 't')
      goto yy387;
    goto yy298;
  yy347:
    ++p;
    { return 6; }
  yy348:
    yych = *++p;
    if (yych == '>')
      goto yy347;
    goto yy298;
  yy349:
    yych = *++p;
    if (yych == 'R')
      goto yy388;
    if (yych == 'r')
      goto yy388;
    goto yy298;
  yy350:
    yych = *++p;
    if (yych == 'E')
      goto yy389;
    if (yych == 'e')
      goto yy389;
    goto yy298;
  yy351:
    yych = *++p;
    if (yych == 'R')
      goto yy390;
    if (yych == 'r')
      goto yy390;
    goto yy298;
  yy352:
    yych = *++p;
    if (yych == 'C')
      goto yy371;
    if (yych == 'c')
      goto yy371;
    goto yy298;
  yy353:
    yych = *++p;
    if (yych == 'U')
      goto yy391;
    if (yych == 'u')
      goto yy391;
    goto yy298;
  yy354:
    yych = *++p;
    if (yych == 'Y')
      goto yy392;
    if (yych == 'y')
      goto yy392;
    goto yy298;
  yy355:
    yych = *++p;
    if (yych == 'M')
      goto yy393;
    if (yych == 'm')
      goto yy393;
    goto yy298;
  yy356:
    yych = *++p;
    if (yych == 'B')
      goto yy394;
    if (yych == 'b')
      goto yy394;
    goto yy298;
  yy357:
    yych = *++p;
    if (yych == 'O')
      goto yy327;
    if (yych == 'o')
      goto yy327;
    goto yy298;
  yy358:
    yych = *++p;
    if (yych == 'X')
      goto yy395;
    if (yych == 'x')
      goto yy395;
    goto yy298;
  yy359:
    yych = *++p;
    if (yych == 'O')
      goto yy396;
    if (yych == 'o')
      goto yy396;
    goto yy298;
  yy360:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= 'D') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'E')
          goto yy397;
        if (yych == 'e')
          goto yy397;
        goto yy298;
      }
    }
  yy361:
    yych = *++p;
    if (yych == 'T')
      goto yy394;
    if (yych == 't')
      goto yy394;
    goto yy298;
  yy362:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= '@') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'A')
          goto yy398;
        if (yych == 'a')
          goto yy398;
        goto yy298;
      }
    }
  yy363:
    ++p;
    { return 2; }
  yy364:
    yych = *++p;
    if (yych == 'D')
      goto yy399;
    if (yych == 'd')
      goto yy399;
    goto yy298;
  yy365:
    yych = *++p;
    if (yych == 'R')
      goto yy400;
    if (yych == 'r')
      goto yy400;
    goto yy298;
  yy366:
    yych = *++p;
    if (yych == 'I')
      goto yy401;
    if (yych == 'i')
      goto yy401;
    goto yy298;
  yy367:
    yych = *++p;
    if (yych == 'D')
      goto yy402;
    if (yych == 'd')
      goto yy402;
    goto yy298;
  yy368:
    yych = *++p;
    if (yych == 'E')
      goto yy403;
    if (yych == 'e')
      goto yy403;
    goto yy298;
  yy369:
    yych = *++p;
    if (yych == 'C')
      goto yy404;
    if (yych == 'c')
      goto yy404;
    goto yy298;
  yy370:
    yych = *++p;
    if (yych == 'Y')
      goto yy331;
    if (yych == 'y')
      goto yy331;
    goto yy298;
  yy371:
    yych = *++p;
    if (yych == 'T')
      goto yy405;
    if (yych == 't')
      goto yy405;
    goto yy298;
  yy372:
    yych = *++p;
    if (yych == 'T')
      goto yy406;
    if (yych == 't')
      goto yy406;
    goto yy298;
  yy373:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= 'F') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'G')
          goto yy407;
        if (yych == 'g')
          goto yy407;
        goto yy298;
      }
    }
  yy374:
    yych = *++p;
    if (yych == 'A')
      goto yy408;
    if (yych == 'a')
      goto yy408;
    goto yy298;
  yy375:
    yych = *++p;
    if (yych == 'L')
      goto yy409;
    if (yych == 'l')
      goto yy409;
    goto yy298;
  yy376:
    yych = *++p;
    if (yych == 'L')
      goto yy410;
    if (yych == 'l')
      goto yy410;
    goto yy298;
  yy377:
    yych = *++p;
    if (yych <= 'U') {
      if (yych == 'C')
        goto yy411;
      if (yych <= 'T')
        goto yy298;
      goto yy412;
    } else {
      if (yych <= 'c') {
        if (yych <= 'b')
          goto yy298;
        goto yy411;
      } else {
        if (yych == 'u')
          goto yy412;
        goto yy298;
      }
    }
  yy378:
    yych = *++p;
    if (yych == 'M')
      goto yy331;
    if (yych == 'm')
      goto yy331;
    goto yy298;
  yy379:
    yych = *++p;
    if (yych == 'M')
      goto yy413;
    if (yych == 'm')
      goto yy413;
    goto yy298;
  yy380:
    yych = *++p;
    if (yych == 'D')
      goto yy414;
    if (yych == 'd')
      goto yy414;
    goto yy298;
  yy381:
    yych = *++p;
    if (yych == 'A')
      goto yy415;
    if (yych == 'a')
      goto yy415;
    goto yy298;
  yy382:
    yych = *++p;
    if (yych == 'E')
      goto yy416;
    if (yych == 'e')
      goto yy416;
    goto yy298;
  yy383:
    yych = *++p;
    if (yych == 'K')
      goto yy331;
    if (yych == 'k')
      goto yy331;
    goto yy298;
  yy384:
    yych = *++p;
    if (yych == 'N')
      goto yy331;
    if (yych == 'n')
      goto yy331;
    goto yy298;
  yy385:
    yych = *++p;
    if (yych == 'U')
      goto yy417;
    if (yych == 'u')
      goto yy417;
    goto yy298;
  yy386:
    yych = *++p;
    if (yych == 'R')
      goto yy418;
    if (yych == 'r')
      goto yy418;
    goto yy298;
  yy387:
    yych = *++p;
    if (yych <= 'I') {
      if (yych == 'G')
        goto yy407;
      if (yych <= 'H')
        goto yy298;
      goto yy419;
    } else {
      if (yych <= 'g') {
        if (yych <= 'f')
          goto yy298;
        goto yy407;
      } else {
        if (yych == 'i')
          goto yy419;
        goto yy298;
      }
    }
  yy388:
    yych = *++p;
    if (yych == 'A')
      goto yy378;
    if (yych == 'a')
      goto yy378;
    goto yy298;
  yy389:
    yych = *++p;
    if (yych <= 0x1F) {
      if (yych <= 0x08)
        goto yy298;
      if (yych <= '\r')
        goto yy420;
      goto yy298;
    } else {
      if (yych <= ' ')
        goto yy420;
      if (yych == '>')
        goto yy420;
      goto yy298;
    }
  yy390:
    yych = *++p;
    if (yych == 'I')
      goto yy421;
    if (yych == 'i')
      goto yy421;
    goto yy298;
  yy391:
    yych = *++p;
    if (yych == 'R')
      goto yy422;
    if (yych == 'r')
      goto yy422;
    goto yy298;
  yy392:
    yych = *++p;
    if (yych == 'L')
      goto yy350;
    if (yych == 'l')
      goto yy350;
    goto yy298;
  yy393:
    yych = *++p;
    if (yych == 'M')
      goto yy423;
    if (yych == 'm')
      goto yy423;
    goto yy298;
  yy394:
    yych = *++p;
    if (yych == 'L')
      goto yy402;
    if (yych == 'l')
      goto yy402;
    goto yy298;
  yy395:
    yych = *++p;
    if (yych == 'T')
      goto yy424;
    if (yych == 't')
      goto yy424;
    goto yy298;
  yy396:
    yych = *++p;
    if (yych == 'O')
      goto yy425;
    if (yych == 'o')
      goto yy425;
    goto yy298;
  yy397:
    yych = *++p;
    if (yych == 'A')
      goto yy426;
    if (yych == 'a')
      goto yy426;
    goto yy298;
  yy398:
    yych = *++p;
    if (yych == 'C')
      goto yy383;
    if (yych == 'c')
      goto yy383;
    goto yy298;
  yy399:
    yych = *++p;
    if (yych == 'A')
      goto yy427;
    if (yych == 'a')
      goto yy427;
    goto yy298;
  yy400:
    yych = *++p;
    if (yych == 'E')
      goto yy428;
    if (yych == 'e')
      goto yy428;
    goto yy298;
  yy401:
    yych = *++p;
    if (yych == 'C')
      goto yy394;
    if (yych == 'c')
      goto yy394;
    goto yy298;
  yy402:
    yych = *++p;
    if (yych == 'E')
      goto yy331;
    if (yych == 'e')
      goto yy331;
    goto yy298;
  yy403:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= 'E') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'F')
          goto yy429;
        if (yych == 'f')
          goto yy429;
        goto yy298;
      }
    }
  yy404:
    yych = *++p;
    if (yych == 'K')
      goto yy430;
    if (yych == 'k')
      goto yy430;
    goto yy298;
  yy405:
    yych = *++p;
    if (yych == 'I')
      goto yy419;
    if (yych == 'i')
      goto yy419;
    goto yy298;
  yy406:
    yych = *++p;
    if (yych == 'E')
      goto yy431;
    if (yych == 'e')
      goto yy431;
    goto yy298;
  yy407:
    yych = *++p;
    if (yych == 'R')
      goto yy432;
    if (yych == 'r')
      goto yy432;
    goto yy298;
  yy408:
    yych = *++p;
    if (yych == 'I')
      goto yy433;
    if (yych == 'i')
      goto yy433;
    goto yy298;
  yy409:
    yych = *++p;
    if (yych == 'O')
      goto yy434;
    if (yych == 'o')
      goto yy434;
    goto yy298;
  yy410:
    yych = *++p;
    if (yych == 'D')
      goto yy435;
    if (yych == 'd')
      goto yy435;
    goto yy298;
  yy411:
    yych = *++p;
    if (yych == 'A')
      goto yy328;
    if (yych == 'a')
      goto yy328;
    goto yy298;
  yy412:
    yych = *++p;
    if (yych == 'R')
      goto yy402;
    if (yych == 'r')
      goto yy402;
    goto yy298;
  yy413:
    yych = *++p;
    if (yych == 'E')
      goto yy436;
    if (yych == 'e')
      goto yy436;
    goto yy298;
  yy414:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= 'D') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'E')
          goto yy431;
        if (yych == 'e')
          goto yy431;
        goto yy298;
      }
    }
  yy415:
    yych = *++p;
    if (yych == 'M')
      goto yy402;
    if (yych == 'm')
      goto yy402;
    goto yy298;
  yy416:
    yych = *++p;
    if (yych == 'N')
      goto yy426;
    if (yych == 'n')
      goto yy426;
    goto yy298;
  yy417:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= 'H') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'I')
          goto yy437;
        if (yych == 'i')
          goto yy437;
        goto yy298;
      }
    }
  yy418:
    yych = *++p;
    if (yych == 'A')
      goto yy438;
    if (yych == 'a')
      goto yy438;
    goto yy298;
  yy419:
    yych = *++p;
    if (yych == 'O')
      goto yy384;
    if (yych == 'o')
      goto yy384;
    goto yy298;
  yy420:
    ++p;
    { return 1; }
  yy421:
    yych = *++p;
    if (yych == 'P')
      goto yy439;
    if (yych == 'p')
      goto yy439;
    goto yy298;
  yy422:
    yych = *++p;
    if (yych == 'C')
      goto yy402;
    if (yych == 'c')
      goto yy402;
    goto yy298;
  yy423:
    yych = *++p;
    if (yych == 'A')
      goto yy440;
    if (yych == 'a')
      goto yy440;
    goto yy298;
  yy424:
    yych = *++p;
    if (yych == 'A')
      goto yy441;
    if (yych == 'a')
      goto yy441;
    goto yy298;
  yy425:
    yych = *++p;
    if (yych == 'T')
      goto yy331;
    if (yych == 't')
      goto yy331;
    goto yy298;
  yy426:
    yych = *++p;
    if (yych == 'D')
      goto yy331;
    if (yych == 'd')
      goto yy331;
    goto yy298;
  yy427:
    yych = *++p;
    if (yych == 'T')
      goto yy442;
    if (yych == 't')
      goto yy442;
    goto yy298;
  yy428:
    yych = *++p;
    if (yych == 'S')
      goto yy443;
    if (yych == 's')
      goto yy443;
    goto yy298;
  yy429:
    yych = *++p;
    if (yych == 'O')
      goto yy444;
    if (yych == 'o')
      goto yy444;
    goto yy298;
  yy430:
    yych = *++p;
    if (yych == 'Q')
      goto yy445;
    if (yych == 'q')
      goto yy445;
    goto yy298;
  yy431:
    yych = *++p;
    if (yych == 'R')
      goto yy331;
    if (yych == 'r')
      goto yy331;
    goto yy298;
  yy432:
    yych = *++p;
    if (yych == 'O')
      goto yy446;
    if (yych == 'o')
      goto yy446;
    goto yy298;
  yy433:
    yych = *++p;
    if (yych == 'L')
      goto yy443;
    if (yych == 'l')
      goto yy443;
    goto yy298;
  yy434:
    yych = *++p;
    if (yych == 'G')
      goto yy331;
    if (yych == 'g')
      goto yy331;
    goto yy298;
  yy435:
    yych = *++p;
    if (yych == 'S')
      goto yy447;
    if (yych == 's')
      goto yy447;
    goto yy298;
  yy436:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy298;
        if (yych <= '\r')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= ' ')
          goto yy347;
        if (yych <= '.')
          goto yy298;
        goto yy348;
      }
    } else {
      if (yych <= 'R') {
        if (yych == '>')
          goto yy347;
        goto yy298;
      } else {
        if (yych <= 'S')
          goto yy447;
        if (yych == 's')
          goto yy447;
        goto yy298;
      }
    }
  yy437:
    yych = *++p;
    if (yych == 'T')
      goto yy448;
    if (yych == 't')
      goto yy448;
    goto yy298;
  yy438:
    yych = *++p;
    if (yych == 'M')
      goto yy449;
    if (yych == 'm')
      goto yy449;
    goto yy298;
  yy439:
    yych = *++p;
    if (yych == 'T')
      goto yy389;
    if (yych == 't')
      goto yy389;
    goto yy298;
  yy440:
    yych = *++p;
    if (yych == 'R')
      goto yy370;
    if (yych == 'r')
      goto yy370;
    goto yy298;
  yy441:
    yych = *++p;
    if (yych == 'R')
      goto yy450;
    if (yych == 'r')
      goto yy450;
    goto yy298;
  yy442:
    yych = *++p;
    if (yych == 'A')
      goto yy451;
    if (yych == 'a')
      goto yy451;
    goto yy298;
  yy443:
    yych = *++p;
    if (yych == 'S')
      goto yy331;
    if (yych == 's')
      goto yy331;
    goto yy298;
  yy444:
    yych = *++p;
    if (yych == 'N')
      goto yy425;
    if (yych == 'n')
      goto yy425;
    goto yy298;
  yy445:
    yych = *++p;
    if (yych == 'U')
      goto yy452;
    if (yych == 'u')
      goto yy452;
    goto yy298;
  yy446:
    yych = *++p;
    if (yych == 'U')
      goto yy453;
    if (yych == 'u')
      goto yy453;
    goto yy298;
  yy447:
    yych = *++p;
    if (yych == 'E')
      goto yy425;
    if (yych == 'e')
      goto yy425;
    goto yy298;
  yy448:
    yych = *++p;
    if (yych == 'E')
      goto yy378;
    if (yych == 'e')
      goto yy378;
    goto yy298;
  yy449:
    yych = *++p;
    if (yych == 'E')
      goto yy443;
    if (yych == 'e')
      goto yy443;
    goto yy298;
  yy450:
    yych = *++p;
    if (yych == 'E')
      goto yy454;
    if (yych == 'e')
      goto yy454;
    goto yy298;
  yy451:
    yych = *++p;
    if (yych == '[')
      goto yy455;
    goto yy298;
  yy452:
    yych = *++p;
    if (yych == 'O')
      goto yy456;
    if (yych == 'o')
      goto yy456;
    goto yy298;
  yy453:
    yych = *++p;
    if (yych == 'P')
      goto yy331;
    if (yych == 'p')
      goto yy331;
    goto yy298;
  yy454:
    yych = *++p;
    if (yych == 'A')
      goto yy389;
    if (yych == 'a')
      goto yy389;
    goto yy298;
  yy455:
    ++p;
    { return 5; }
  yy456:
    yych = *++p;
    if (yych == 'T')
      goto yy402;
    if (yych == 't')
      goto yy402;
    goto yy298;
  }
}

// Try to match an HTML block tag start line of type 7, returning
// 7 if successful, 0 if not.
bufsize_t _scan_html_block_start_7(const unsigned char *p) {
  const unsigned char *marker = NULL;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,   224, 224, 224, 224, 224, 224, 224, 224, 198, 210, 194, 198, 194,
        224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224, 224,
        224, 224, 224, 224, 198, 224, 128, 224, 224, 224, 224, 64,  224, 224,
        224, 224, 224, 233, 232, 224, 233, 233, 233, 233, 233, 233, 233, 233,
        233, 233, 232, 224, 192, 192, 192, 224, 224, 233, 233, 233, 233, 233,
        233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233,
        233, 233, 233, 233, 233, 233, 233, 224, 224, 224, 224, 232, 192, 233,
        233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233,
        233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 233, 224, 224, 224,
        224, 224, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych == '<')
      goto yy459;
    ++p;
  yy458 : { return 0; }
  yy459:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '@') {
      if (yych != '/')
        goto yy458;
    } else {
      if (yych <= 'Z')
        goto yy461;
      if (yych <= '`')
        goto yy458;
      if (yych <= 'z')
        goto yy461;
      goto yy458;
    }
    yych = *++p;
    if (yych <= '@')
      goto yy460;
    if (yych <= 'Z')
      goto yy462;
    if (yych <= '`')
      goto yy460;
    if (yych <= 'z')
      goto yy462;
  yy460:
    p = marker;
    if (yyaccept == 0) {
      goto yy458;
    } else {
      goto yy469;
    }
  yy461:
    yych = *++p;
    if (yybm[0 + yych] & 2) {
      goto yy463;
    }
    if (yych <= '=') {
      if (yych <= '.') {
        if (yych == '-')
          goto yy461;
        goto yy460;
      } else {
        if (yych <= '/')
          goto yy464;
        if (yych <= '9')
          goto yy461;
        goto yy460;
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '>')
          goto yy465;
        if (yych <= '@')
          goto yy460;
        goto yy461;
      } else {
        if (yych <= '`')
          goto yy460;
        if (yych <= 'z')
          goto yy461;
        goto yy460;
      }
    }
  yy462:
    yych = *++p;
    if (yych <= '/') {
      if (yych <= 0x1F) {
        if (yych <= 0x08)
          goto yy460;
        if (yych <= '\r')
          goto yy466;
        goto yy460;
      } else {
        if (yych <= ' ')
          goto yy466;
        if (yych == '-')
          goto yy462;
        goto yy460;
      }
    } else {
      if (yych <= '@') {
        if (yych <= '9')
          goto yy462;
        if (yych == '>')
          goto yy465;
        goto yy460;
      } else {
        if (yych <= 'Z')
          goto yy462;
        if (yych <= '`')
          goto yy460;
        if (yych <= 'z')
          goto yy462;
        goto yy460;
      }
    }
  yy463:
    yych = *++p;
    if (yybm[0 + yych] & 2) {
      goto yy463;
    }
    if (yych <= '>') {
      if (yych <= '9') {
        if (yych != '/')
          goto yy460;
      } else {
        if (yych <= ':')
          goto yy467;
        if (yych <= '=')
          goto yy460;
        goto yy465;
      }
    } else {
      if (yych <= '^') {
        if (yych <= '@')
          goto yy460;
        if (yych <= 'Z')
          goto yy467;
        goto yy460;
      } else {
        if (yych == '`')
          goto yy460;
        if (yych <= 'z')
          goto yy467;
        goto yy460;
      }
    }
  yy464:
    yych = *++p;
    if (yych != '>')
      goto yy460;
  yy465:
    yych = *++p;
    if (yybm[0 + yych] & 4) {
      goto yy465;
    }
    if (yych <= 0x08)
      goto yy460;
    if (yych <= '\n')
      goto yy468;
    if (yych <= '\v')
      goto yy460;
    if (yych <= '\r')
      goto yy470;
    goto yy460;
  yy466:
    yych = *++p;
    if (yych <= 0x1F) {
      if (yych <= 0x08)
        goto yy460;
      if (yych <= '\r')
        goto yy466;
      goto yy460;
    } else {
      if (yych <= ' ')
        goto yy466;
      if (yych == '>')
        goto yy465;
      goto yy460;
    }
  yy467:
    yych = *++p;
    if (yybm[0 + yych] & 8) {
      goto yy467;
    }
    if (yych <= ',') {
      if (yych <= '\r') {
        if (yych <= 0x08)
          goto yy460;
        goto yy471;
      } else {
        if (yych == ' ')
          goto yy471;
        goto yy460;
      }
    } else {
      if (yych <= '<') {
        if (yych <= '/')
          goto yy464;
        goto yy460;
      } else {
        if (yych <= '=')
          goto yy472;
        if (yych <= '>')
          goto yy465;
        goto yy460;
      }
    }
  yy468:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 4) {
      goto yy465;
    }
    if (yych <= 0x08)
      goto yy469;
    if (yych <= '\n')
      goto yy468;
    if (yych <= '\v')
      goto yy469;
    if (yych <= '\r')
      goto yy470;
  yy469 : { return 7; }
  yy470:
    ++p;
    goto yy469;
  yy471:
    yych = *++p;
    if (yych <= '<') {
      if (yych <= ' ') {
        if (yych <= 0x08)
          goto yy460;
        if (yych <= '\r')
          goto yy471;
        if (yych <= 0x1F)
          goto yy460;
        goto yy471;
      } else {
        if (yych <= '/') {
          if (yych <= '.')
            goto yy460;
          goto yy464;
        } else {
          if (yych == ':')
            goto yy467;
          goto yy460;
        }
      }
    } else {
      if (yych <= 'Z') {
        if (yych <= '=')
          goto yy472;
        if (yych <= '>')
          goto yy465;
        if (yych <= '@')
          goto yy460;
        goto yy467;
      } else {
        if (yych <= '_') {
          if (yych <= '^')
            goto yy460;
          goto yy467;
        } else {
          if (yych <= '`')
            goto yy460;
          if (yych <= 'z')
            goto yy467;
          goto yy460;
        }
      }
    }
  yy472:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy473;
    }
    if (yych <= 0xE0) {
      if (yych <= '"') {
        if (yych <= 0x00)
          goto yy460;
        if (yych <= ' ')
          goto yy472;
        goto yy474;
      } else {
        if (yych <= '\'')
          goto yy475;
        if (yych <= 0xC1)
          goto yy460;
        if (yych <= 0xDF)
          goto yy476;
        goto yy477;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy479;
        goto yy478;
      } else {
        if (yych <= 0xF0)
          goto yy480;
        if (yych <= 0xF3)
          goto yy481;
        if (yych <= 0xF4)
          goto yy482;
        goto yy460;
      }
    }
  yy473:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy473;
    }
    if (yych <= 0xE0) {
      if (yych <= '=') {
        if (yych <= 0x00)
          goto yy460;
        if (yych <= ' ')
          goto yy463;
        goto yy460;
      } else {
        if (yych <= '>')
          goto yy465;
        if (yych <= 0xC1)
          goto yy460;
        if (yych <= 0xDF)
          goto yy476;
        goto yy477;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy479;
        goto yy478;
      } else {
        if (yych <= 0xF0)
          goto yy480;
        if (yych <= 0xF3)
          goto yy481;
        if (yych <= 0xF4)
          goto yy482;
        goto yy460;
      }
    }
  yy474:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy474;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy460;
        if (yych <= '"')
          goto yy483;
        goto yy460;
      } else {
        if (yych <= 0xDF)
          goto yy484;
        if (yych <= 0xE0)
          goto yy485;
        goto yy486;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy487;
        if (yych <= 0xEF)
          goto yy486;
        goto yy488;
      } else {
        if (yych <= 0xF3)
          goto yy489;
        if (yych <= 0xF4)
          goto yy490;
        goto yy460;
      }
    }
  yy475:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy475;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy460;
        if (yych <= '\'')
          goto yy483;
        goto yy460;
      } else {
        if (yych <= 0xDF)
          goto yy491;
        if (yych <= 0xE0)
          goto yy492;
        goto yy493;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy494;
        if (yych <= 0xEF)
          goto yy493;
        goto yy495;
      } else {
        if (yych <= 0xF3)
          goto yy496;
        if (yych <= 0xF4)
          goto yy497;
        goto yy460;
      }
    }
  yy476:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy473;
    goto yy460;
  yy477:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy476;
    goto yy460;
  yy478:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy476;
    goto yy460;
  yy479:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0x9F)
      goto yy476;
    goto yy460;
  yy480:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy478;
    goto yy460;
  yy481:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy478;
    goto yy460;
  yy482:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0x8F)
      goto yy478;
    goto yy460;
  yy483:
    yych = *++p;
    if (yybm[0 + yych] & 2) {
      goto yy463;
    }
    if (yych == '/')
      goto yy464;
    if (yych == '>')
      goto yy465;
    goto yy460;
  yy484:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy474;
    goto yy460;
  yy485:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy484;
    goto yy460;
  yy486:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy484;
    goto yy460;
  yy487:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0x9F)
      goto yy484;
    goto yy460;
  yy488:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy486;
    goto yy460;
  yy489:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy486;
    goto yy460;
  yy490:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0x8F)
      goto yy486;
    goto yy460;
  yy491:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy475;
    goto yy460;
  yy492:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy491;
    goto yy460;
  yy493:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy491;
    goto yy460;
  yy494:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0x9F)
      goto yy491;
    goto yy460;
  yy495:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy493;
    goto yy460;
  yy496:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0xBF)
      goto yy493;
    goto yy460;
  yy497:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy460;
    if (yych <= 0x8F)
      goto yy493;
    goto yy460;
  }
}

// Try to match an HTML block end line of type 1
bufsize_t _scan_html_block_end_1(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,  64, 64, 64, 64, 64, 64,  64, 64, 64, 0,  64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 128, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,
    };
    yych = *p;
    if (yych <= 0xDF) {
      if (yych <= ';') {
        if (yych <= 0x00)
          goto yy499;
        if (yych != '\n')
          goto yy501;
      } else {
        if (yych <= '<')
          goto yy502;
        if (yych <= 0x7F)
          goto yy501;
        if (yych >= 0xC2)
          goto yy503;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy504;
        if (yych == 0xED)
          goto yy506;
        goto yy505;
      } else {
        if (yych <= 0xF0)
          goto yy507;
        if (yych <= 0xF3)
          goto yy508;
        if (yych <= 0xF4)
          goto yy509;
      }
    }
  yy499:
    ++p;
  yy500 : { return 0; }
  yy501:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy500;
      if (yych <= '\t')
        goto yy511;
      goto yy500;
    } else {
      if (yych <= 0x7F)
        goto yy511;
      if (yych <= 0xC1)
        goto yy500;
      if (yych <= 0xF4)
        goto yy511;
      goto yy500;
    }
  yy502:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '.') {
      if (yych <= 0x00)
        goto yy500;
      if (yych == '\n')
        goto yy500;
      goto yy511;
    } else {
      if (yych <= 0x7F) {
        if (yych <= '/')
          goto yy521;
        goto yy511;
      } else {
        if (yych <= 0xC1)
          goto yy500;
        if (yych <= 0xF4)
          goto yy511;
        goto yy500;
      }
    }
  yy503:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy500;
    if (yych <= 0xBF)
      goto yy510;
    goto yy500;
  yy504:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x9F)
      goto yy500;
    if (yych <= 0xBF)
      goto yy514;
    goto yy500;
  yy505:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy500;
    if (yych <= 0xBF)
      goto yy514;
    goto yy500;
  yy506:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy500;
    if (yych <= 0x9F)
      goto yy514;
    goto yy500;
  yy507:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x8F)
      goto yy500;
    if (yych <= 0xBF)
      goto yy516;
    goto yy500;
  yy508:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy500;
    if (yych <= 0xBF)
      goto yy516;
    goto yy500;
  yy509:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy500;
    if (yych <= 0x8F)
      goto yy516;
    goto yy500;
  yy510:
    yych = *++p;
  yy511:
    if (yybm[0 + yych] & 64) {
      goto yy510;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy512;
        if (yych <= '<')
          goto yy513;
      } else {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        goto yy516;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy517;
        if (yych <= 0xEF)
          goto yy516;
        goto yy518;
      } else {
        if (yych <= 0xF3)
          goto yy519;
        if (yych <= 0xF4)
          goto yy520;
      }
    }
  yy512:
    p = marker;
    if (yyaccept == 0) {
      goto yy500;
    } else {
      goto yy534;
    }
  yy513:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xDF) {
      if (yych <= '.') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= '/')
          goto yy521;
        if (yych <= 0x7F)
          goto yy510;
        if (yych <= 0xC1)
          goto yy512;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy515;
        if (yych == 0xED)
          goto yy517;
        goto yy516;
      } else {
        if (yych <= 0xF0)
          goto yy518;
        if (yych <= 0xF3)
          goto yy519;
        if (yych <= 0xF4)
          goto yy520;
        goto yy512;
      }
    }
  yy514:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy512;
    if (yych <= 0xBF)
      goto yy510;
    goto yy512;
  yy515:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy512;
    if (yych <= 0xBF)
      goto yy514;
    goto yy512;
  yy516:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy512;
    if (yych <= 0xBF)
      goto yy514;
    goto yy512;
  yy517:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy512;
    if (yych <= 0x9F)
      goto yy514;
    goto yy512;
  yy518:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy512;
    if (yych <= 0xBF)
      goto yy516;
    goto yy512;
  yy519:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy512;
    if (yych <= 0xBF)
      goto yy516;
    goto yy512;
  yy520:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy512;
    if (yych <= 0x8F)
      goto yy516;
    goto yy512;
  yy521:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 's') {
      if (yych <= 'R') {
        if (yych <= '\n') {
          if (yych <= 0x00)
            goto yy512;
          if (yych <= '\t')
            goto yy510;
          goto yy512;
        } else {
          if (yych != 'P')
            goto yy510;
        }
      } else {
        if (yych <= 'o') {
          if (yych <= 'S')
            goto yy523;
          if (yych <= 'T')
            goto yy524;
          goto yy510;
        } else {
          if (yych <= 'p')
            goto yy522;
          if (yych <= 'r')
            goto yy510;
          goto yy523;
        }
      }
    } else {
      if (yych <= 0xEC) {
        if (yych <= 0xC1) {
          if (yych <= 't')
            goto yy524;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        } else {
          if (yych <= 0xDF)
            goto yy514;
          if (yych <= 0xE0)
            goto yy515;
          goto yy516;
        }
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xED)
            goto yy517;
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy522:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'Q') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'q') {
          if (yych <= 'R')
            goto yy525;
          goto yy510;
        } else {
          if (yych <= 'r')
            goto yy525;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy523:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 't') {
      if (yych <= 'C') {
        if (yych <= '\t') {
          if (yych <= 0x00)
            goto yy512;
          goto yy510;
        } else {
          if (yych <= '\n')
            goto yy512;
          if (yych <= 'B')
            goto yy510;
          goto yy526;
        }
      } else {
        if (yych <= 'b') {
          if (yych == 'T')
            goto yy527;
          goto yy510;
        } else {
          if (yych <= 'c')
            goto yy526;
          if (yych <= 's')
            goto yy510;
          goto yy527;
        }
      }
    } else {
      if (yych <= 0xEC) {
        if (yych <= 0xC1) {
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        } else {
          if (yych <= 0xDF)
            goto yy514;
          if (yych <= 0xE0)
            goto yy515;
          goto yy516;
        }
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xED)
            goto yy517;
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy524:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'D') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'd') {
          if (yych <= 'E')
            goto yy528;
          goto yy510;
        } else {
          if (yych <= 'e')
            goto yy528;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy525:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'D') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'd') {
          if (yych <= 'E')
            goto yy529;
          goto yy510;
        } else {
          if (yych <= 'e')
            goto yy529;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy526:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'Q') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'q') {
          if (yych <= 'R')
            goto yy530;
          goto yy510;
        } else {
          if (yych <= 'r')
            goto yy530;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy527:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'X') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'x') {
          if (yych <= 'Y')
            goto yy531;
          goto yy510;
        } else {
          if (yych <= 'y')
            goto yy531;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy528:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'W') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'w') {
          if (yych <= 'X')
            goto yy532;
          goto yy510;
        } else {
          if (yych <= 'x')
            goto yy532;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy529:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xDF) {
      if (yych <= '=') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= '>')
          goto yy533;
        if (yych <= 0x7F)
          goto yy510;
        if (yych <= 0xC1)
          goto yy512;
        goto yy514;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy515;
        if (yych == 0xED)
          goto yy517;
        goto yy516;
      } else {
        if (yych <= 0xF0)
          goto yy518;
        if (yych <= 0xF3)
          goto yy519;
        if (yych <= 0xF4)
          goto yy520;
        goto yy512;
      }
    }
  yy530:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'H') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'h') {
          if (yych <= 'I')
            goto yy535;
          goto yy510;
        } else {
          if (yych <= 'i')
            goto yy535;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy531:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'K') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'k') {
          if (yych <= 'L')
            goto yy525;
          goto yy510;
        } else {
          if (yych <= 'l')
            goto yy525;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy532:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'S') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 's') {
          if (yych <= 'T')
            goto yy536;
          goto yy510;
        } else {
          if (yych <= 't')
            goto yy536;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy533:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy510;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy534;
        if (yych <= '<')
          goto yy513;
      } else {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        goto yy516;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy517;
        if (yych <= 0xEF)
          goto yy516;
        goto yy518;
      } else {
        if (yych <= 0xF3)
          goto yy519;
        if (yych <= 0xF4)
          goto yy520;
      }
    }
  yy534 : { return (bufsize_t)(p - start); }
  yy535:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'O') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'o') {
          if (yych <= 'P')
            goto yy537;
          goto yy510;
        } else {
          if (yych <= 'p')
            goto yy537;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy536:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= '@') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= '`') {
          if (yych <= 'A')
            goto yy538;
          goto yy510;
        } else {
          if (yych <= 'a')
            goto yy538;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy537:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'S') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 's') {
          if (yych <= 'T')
            goto yy529;
          goto yy510;
        } else {
          if (yych <= 't')
            goto yy529;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy538:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'Q') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'q') {
          if (yych >= 'S')
            goto yy510;
        } else {
          if (yych <= 'r')
            goto yy539;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy539:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= 'D') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= 'd') {
          if (yych >= 'F')
            goto yy510;
        } else {
          if (yych <= 'e')
            goto yy540;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  yy540:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy513;
    }
    if (yych <= 0xC1) {
      if (yych <= '@') {
        if (yych <= 0x00)
          goto yy512;
        if (yych == '\n')
          goto yy512;
        goto yy510;
      } else {
        if (yych <= '`') {
          if (yych <= 'A')
            goto yy529;
          goto yy510;
        } else {
          if (yych <= 'a')
            goto yy529;
          if (yych <= 0x7F)
            goto yy510;
          goto yy512;
        }
      }
    } else {
      if (yych <= 0xED) {
        if (yych <= 0xDF)
          goto yy514;
        if (yych <= 0xE0)
          goto yy515;
        if (yych <= 0xEC)
          goto yy516;
        goto yy517;
      } else {
        if (yych <= 0xF0) {
          if (yych <= 0xEF)
            goto yy516;
          goto yy518;
        } else {
          if (yych <= 0xF3)
            goto yy519;
          if (yych <= 0xF4)
            goto yy520;
          goto yy512;
        }
      }
    }
  }
}

// Try to match an HTML block end line of type 2
bufsize_t _scan_html_block_end_2(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,  64, 64, 64, 64, 64, 64, 64, 64, 64,  0,  64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 128, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,
    };
    yych = *p;
    if (yych <= 0xDF) {
      if (yych <= ',') {
        if (yych <= 0x00)
          goto yy542;
        if (yych != '\n')
          goto yy544;
      } else {
        if (yych <= '-')
          goto yy545;
        if (yych <= 0x7F)
          goto yy544;
        if (yych >= 0xC2)
          goto yy546;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy547;
        if (yych == 0xED)
          goto yy549;
        goto yy548;
      } else {
        if (yych <= 0xF0)
          goto yy550;
        if (yych <= 0xF3)
          goto yy551;
        if (yych <= 0xF4)
          goto yy552;
      }
    }
  yy542:
    ++p;
  yy543 : { return 0; }
  yy544:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy543;
      if (yych <= '\t')
        goto yy554;
      goto yy543;
    } else {
      if (yych <= 0x7F)
        goto yy554;
      if (yych <= 0xC1)
        goto yy543;
      if (yych <= 0xF4)
        goto yy554;
      goto yy543;
    }
  yy545:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy564;
    }
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy543;
      if (yych <= '\t')
        goto yy554;
      goto yy543;
    } else {
      if (yych <= 0x7F)
        goto yy554;
      if (yych <= 0xC1)
        goto yy543;
      if (yych <= 0xF4)
        goto yy554;
      goto yy543;
    }
  yy546:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy543;
    if (yych <= 0xBF)
      goto yy553;
    goto yy543;
  yy547:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x9F)
      goto yy543;
    if (yych <= 0xBF)
      goto yy557;
    goto yy543;
  yy548:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy543;
    if (yych <= 0xBF)
      goto yy557;
    goto yy543;
  yy549:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy543;
    if (yych <= 0x9F)
      goto yy557;
    goto yy543;
  yy550:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x8F)
      goto yy543;
    if (yych <= 0xBF)
      goto yy559;
    goto yy543;
  yy551:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy543;
    if (yych <= 0xBF)
      goto yy559;
    goto yy543;
  yy552:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy543;
    if (yych <= 0x8F)
      goto yy559;
    goto yy543;
  yy553:
    yych = *++p;
  yy554:
    if (yybm[0 + yych] & 64) {
      goto yy553;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy555;
        if (yych <= '-')
          goto yy556;
      } else {
        if (yych <= 0xDF)
          goto yy557;
        if (yych <= 0xE0)
          goto yy558;
        goto yy559;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy560;
        if (yych <= 0xEF)
          goto yy559;
        goto yy561;
      } else {
        if (yych <= 0xF3)
          goto yy562;
        if (yych <= 0xF4)
          goto yy563;
      }
    }
  yy555:
    p = marker;
    if (yyaccept == 0) {
      goto yy543;
    } else {
      goto yy566;
    }
  yy556:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy553;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy555;
        if (yych <= '-')
          goto yy564;
        goto yy555;
      } else {
        if (yych <= 0xDF)
          goto yy557;
        if (yych <= 0xE0)
          goto yy558;
        goto yy559;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy560;
        if (yych <= 0xEF)
          goto yy559;
        goto yy561;
      } else {
        if (yych <= 0xF3)
          goto yy562;
        if (yych <= 0xF4)
          goto yy563;
        goto yy555;
      }
    }
  yy557:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy555;
    if (yych <= 0xBF)
      goto yy553;
    goto yy555;
  yy558:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy555;
    if (yych <= 0xBF)
      goto yy557;
    goto yy555;
  yy559:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy555;
    if (yych <= 0xBF)
      goto yy557;
    goto yy555;
  yy560:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy555;
    if (yych <= 0x9F)
      goto yy557;
    goto yy555;
  yy561:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy555;
    if (yych <= 0xBF)
      goto yy559;
    goto yy555;
  yy562:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy555;
    if (yych <= 0xBF)
      goto yy559;
    goto yy555;
  yy563:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy555;
    if (yych <= 0x8F)
      goto yy559;
    goto yy555;
  yy564:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy564;
    }
    if (yych <= 0xDF) {
      if (yych <= '=') {
        if (yych <= 0x00)
          goto yy555;
        if (yych == '\n')
          goto yy555;
        goto yy553;
      } else {
        if (yych <= '>')
          goto yy565;
        if (yych <= 0x7F)
          goto yy553;
        if (yych <= 0xC1)
          goto yy555;
        goto yy557;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy558;
        if (yych == 0xED)
          goto yy560;
        goto yy559;
      } else {
        if (yych <= 0xF0)
          goto yy561;
        if (yych <= 0xF3)
          goto yy562;
        if (yych <= 0xF4)
          goto yy563;
        goto yy555;
      }
    }
  yy565:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy553;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy566;
        if (yych <= '-')
          goto yy556;
      } else {
        if (yych <= 0xDF)
          goto yy557;
        if (yych <= 0xE0)
          goto yy558;
        goto yy559;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy560;
        if (yych <= 0xEF)
          goto yy559;
        goto yy561;
      } else {
        if (yych <= 0xF3)
          goto yy562;
        if (yych <= 0xF4)
          goto yy563;
      }
    }
  yy566 : { return (bufsize_t)(p - start); }
  }
}

// Try to match an HTML block end line of type 3
bufsize_t _scan_html_block_end_3(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,  64, 64, 64, 64, 64, 64, 64, 64, 64,  0,  64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 128, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,
    };
    yych = *p;
    if (yych <= 0xDF) {
      if (yych <= '>') {
        if (yych <= 0x00)
          goto yy568;
        if (yych != '\n')
          goto yy570;
      } else {
        if (yych <= '?')
          goto yy571;
        if (yych <= 0x7F)
          goto yy570;
        if (yych >= 0xC2)
          goto yy572;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy573;
        if (yych == 0xED)
          goto yy575;
        goto yy574;
      } else {
        if (yych <= 0xF0)
          goto yy576;
        if (yych <= 0xF3)
          goto yy577;
        if (yych <= 0xF4)
          goto yy578;
      }
    }
  yy568:
    ++p;
  yy569 : { return 0; }
  yy570:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy569;
      if (yych <= '\t')
        goto yy580;
      goto yy569;
    } else {
      if (yych <= 0x7F)
        goto yy580;
      if (yych <= 0xC1)
        goto yy569;
      if (yych <= 0xF4)
        goto yy580;
      goto yy569;
    }
  yy571:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '=') {
      if (yych <= 0x00)
        goto yy569;
      if (yych == '\n')
        goto yy569;
      goto yy580;
    } else {
      if (yych <= 0x7F) {
        if (yych <= '>')
          goto yy590;
        goto yy580;
      } else {
        if (yych <= 0xC1)
          goto yy569;
        if (yych <= 0xF4)
          goto yy580;
        goto yy569;
      }
    }
  yy572:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy569;
    if (yych <= 0xBF)
      goto yy579;
    goto yy569;
  yy573:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x9F)
      goto yy569;
    if (yych <= 0xBF)
      goto yy583;
    goto yy569;
  yy574:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy569;
    if (yych <= 0xBF)
      goto yy583;
    goto yy569;
  yy575:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy569;
    if (yych <= 0x9F)
      goto yy583;
    goto yy569;
  yy576:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x8F)
      goto yy569;
    if (yych <= 0xBF)
      goto yy585;
    goto yy569;
  yy577:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy569;
    if (yych <= 0xBF)
      goto yy585;
    goto yy569;
  yy578:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy569;
    if (yych <= 0x8F)
      goto yy585;
    goto yy569;
  yy579:
    yych = *++p;
  yy580:
    if (yybm[0 + yych] & 64) {
      goto yy579;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy581;
        if (yych <= '?')
          goto yy582;
      } else {
        if (yych <= 0xDF)
          goto yy583;
        if (yych <= 0xE0)
          goto yy584;
        goto yy585;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy586;
        if (yych <= 0xEF)
          goto yy585;
        goto yy587;
      } else {
        if (yych <= 0xF3)
          goto yy588;
        if (yych <= 0xF4)
          goto yy589;
      }
    }
  yy581:
    p = marker;
    if (yyaccept == 0) {
      goto yy569;
    } else {
      goto yy591;
    }
  yy582:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy582;
    }
    if (yych <= 0xDF) {
      if (yych <= '=') {
        if (yych <= 0x00)
          goto yy581;
        if (yych == '\n')
          goto yy581;
        goto yy579;
      } else {
        if (yych <= '>')
          goto yy590;
        if (yych <= 0x7F)
          goto yy579;
        if (yych <= 0xC1)
          goto yy581;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy584;
        if (yych == 0xED)
          goto yy586;
        goto yy585;
      } else {
        if (yych <= 0xF0)
          goto yy587;
        if (yych <= 0xF3)
          goto yy588;
        if (yych <= 0xF4)
          goto yy589;
        goto yy581;
      }
    }
  yy583:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy581;
    if (yych <= 0xBF)
      goto yy579;
    goto yy581;
  yy584:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy581;
    if (yych <= 0xBF)
      goto yy583;
    goto yy581;
  yy585:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy581;
    if (yych <= 0xBF)
      goto yy583;
    goto yy581;
  yy586:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy581;
    if (yych <= 0x9F)
      goto yy583;
    goto yy581;
  yy587:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy581;
    if (yych <= 0xBF)
      goto yy585;
    goto yy581;
  yy588:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy581;
    if (yych <= 0xBF)
      goto yy585;
    goto yy581;
  yy589:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy581;
    if (yych <= 0x8F)
      goto yy585;
    goto yy581;
  yy590:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy579;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy591;
        if (yych <= '?')
          goto yy582;
      } else {
        if (yych <= 0xDF)
          goto yy583;
        if (yych <= 0xE0)
          goto yy584;
        goto yy585;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy586;
        if (yych <= 0xEF)
          goto yy585;
        goto yy587;
      } else {
        if (yych <= 0xF3)
          goto yy588;
        if (yych <= 0xF4)
          goto yy589;
      }
    }
  yy591 : { return (bufsize_t)(p - start); }
  }
}

// Try to match an HTML block end line of type 4
bufsize_t _scan_html_block_end_4(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,   128, 128, 128, 128, 128, 128, 128, 128, 128, 0,   128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 64,  128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128,
        128, 128, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yybm[0 + yych] & 64) {
      goto yy596;
    }
    if (yych <= 0xE0) {
      if (yych <= '\n') {
        if (yych <= 0x00)
          goto yy593;
        if (yych <= '\t')
          goto yy595;
      } else {
        if (yych <= 0x7F)
          goto yy595;
        if (yych <= 0xC1)
          goto yy593;
        if (yych <= 0xDF)
          goto yy598;
        goto yy599;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy601;
        goto yy600;
      } else {
        if (yych <= 0xF0)
          goto yy602;
        if (yych <= 0xF3)
          goto yy603;
        if (yych <= 0xF4)
          goto yy604;
      }
    }
  yy593:
    ++p;
  yy594 : { return 0; }
  yy595:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy594;
      if (yych <= '\t')
        goto yy606;
      goto yy594;
    } else {
      if (yych <= 0x7F)
        goto yy606;
      if (yych <= 0xC1)
        goto yy594;
      if (yych <= 0xF4)
        goto yy606;
      goto yy594;
    }
  yy596:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy605;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy597;
        if (yych <= '>')
          goto yy596;
      } else {
        if (yych <= 0xDF)
          goto yy608;
        if (yych <= 0xE0)
          goto yy609;
        goto yy610;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy611;
        if (yych <= 0xEF)
          goto yy610;
        goto yy612;
      } else {
        if (yych <= 0xF3)
          goto yy613;
        if (yych <= 0xF4)
          goto yy614;
      }
    }
  yy597 : { return (bufsize_t)(p - start); }
  yy598:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy594;
    if (yych <= 0xBF)
      goto yy605;
    goto yy594;
  yy599:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x9F)
      goto yy594;
    if (yych <= 0xBF)
      goto yy608;
    goto yy594;
  yy600:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy594;
    if (yych <= 0xBF)
      goto yy608;
    goto yy594;
  yy601:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy594;
    if (yych <= 0x9F)
      goto yy608;
    goto yy594;
  yy602:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x8F)
      goto yy594;
    if (yych <= 0xBF)
      goto yy610;
    goto yy594;
  yy603:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy594;
    if (yych <= 0xBF)
      goto yy610;
    goto yy594;
  yy604:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy594;
    if (yych <= 0x8F)
      goto yy610;
    goto yy594;
  yy605:
    yych = *++p;
  yy606:
    if (yybm[0 + yych] & 128) {
      goto yy605;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy607;
        if (yych <= '>')
          goto yy596;
      } else {
        if (yych <= 0xDF)
          goto yy608;
        if (yych <= 0xE0)
          goto yy609;
        goto yy610;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy611;
        if (yych <= 0xEF)
          goto yy610;
        goto yy612;
      } else {
        if (yych <= 0xF3)
          goto yy613;
        if (yych <= 0xF4)
          goto yy614;
      }
    }
  yy607:
    p = marker;
    if (yyaccept == 0) {
      goto yy594;
    } else {
      goto yy597;
    }
  yy608:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy607;
    if (yych <= 0xBF)
      goto yy605;
    goto yy607;
  yy609:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy607;
    if (yych <= 0xBF)
      goto yy608;
    goto yy607;
  yy610:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy607;
    if (yych <= 0xBF)
      goto yy608;
    goto yy607;
  yy611:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy607;
    if (yych <= 0x9F)
      goto yy608;
    goto yy607;
  yy612:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy607;
    if (yych <= 0xBF)
      goto yy610;
    goto yy607;
  yy613:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy607;
    if (yych <= 0xBF)
      goto yy610;
    goto yy607;
  yy614:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy607;
    if (yych <= 0x8F)
      goto yy610;
    goto yy607;
  }
}

// Try to match an HTML block end line of type 5
bufsize_t _scan_html_block_end_5(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,  64, 64, 64,  64, 64, 64, 64, 64, 64, 0,  64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 128, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 64, 64,  64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64,
        64, 64, 0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,   0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
        0,  0,  0,  0,
    };
    yych = *p;
    if (yych <= 0xDF) {
      if (yych <= '\\') {
        if (yych <= 0x00)
          goto yy616;
        if (yych != '\n')
          goto yy618;
      } else {
        if (yych <= ']')
          goto yy619;
        if (yych <= 0x7F)
          goto yy618;
        if (yych >= 0xC2)
          goto yy620;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy621;
        if (yych == 0xED)
          goto yy623;
        goto yy622;
      } else {
        if (yych <= 0xF0)
          goto yy624;
        if (yych <= 0xF3)
          goto yy625;
        if (yych <= 0xF4)
          goto yy626;
      }
    }
  yy616:
    ++p;
  yy617 : { return 0; }
  yy618:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy617;
      if (yych <= '\t')
        goto yy628;
      goto yy617;
    } else {
      if (yych <= 0x7F)
        goto yy628;
      if (yych <= 0xC1)
        goto yy617;
      if (yych <= 0xF4)
        goto yy628;
      goto yy617;
    }
  yy619:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy638;
    }
    if (yych <= '\n') {
      if (yych <= 0x00)
        goto yy617;
      if (yych <= '\t')
        goto yy628;
      goto yy617;
    } else {
      if (yych <= 0x7F)
        goto yy628;
      if (yych <= 0xC1)
        goto yy617;
      if (yych <= 0xF4)
        goto yy628;
      goto yy617;
    }
  yy620:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy617;
    if (yych <= 0xBF)
      goto yy627;
    goto yy617;
  yy621:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x9F)
      goto yy617;
    if (yych <= 0xBF)
      goto yy631;
    goto yy617;
  yy622:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy617;
    if (yych <= 0xBF)
      goto yy631;
    goto yy617;
  yy623:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy617;
    if (yych <= 0x9F)
      goto yy631;
    goto yy617;
  yy624:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x8F)
      goto yy617;
    if (yych <= 0xBF)
      goto yy633;
    goto yy617;
  yy625:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy617;
    if (yych <= 0xBF)
      goto yy633;
    goto yy617;
  yy626:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x7F)
      goto yy617;
    if (yych <= 0x8F)
      goto yy633;
    goto yy617;
  yy627:
    yych = *++p;
  yy628:
    if (yybm[0 + yych] & 64) {
      goto yy627;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy629;
        if (yych <= ']')
          goto yy630;
      } else {
        if (yych <= 0xDF)
          goto yy631;
        if (yych <= 0xE0)
          goto yy632;
        goto yy633;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy634;
        if (yych <= 0xEF)
          goto yy633;
        goto yy635;
      } else {
        if (yych <= 0xF3)
          goto yy636;
        if (yych <= 0xF4)
          goto yy637;
      }
    }
  yy629:
    p = marker;
    if (yyaccept == 0) {
      goto yy617;
    } else {
      goto yy640;
    }
  yy630:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy627;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy629;
        if (yych <= ']')
          goto yy638;
        goto yy629;
      } else {
        if (yych <= 0xDF)
          goto yy631;
        if (yych <= 0xE0)
          goto yy632;
        goto yy633;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy634;
        if (yych <= 0xEF)
          goto yy633;
        goto yy635;
      } else {
        if (yych <= 0xF3)
          goto yy636;
        if (yych <= 0xF4)
          goto yy637;
        goto yy629;
      }
    }
  yy631:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy629;
    if (yych <= 0xBF)
      goto yy627;
    goto yy629;
  yy632:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy629;
    if (yych <= 0xBF)
      goto yy631;
    goto yy629;
  yy633:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy629;
    if (yych <= 0xBF)
      goto yy631;
    goto yy629;
  yy634:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy629;
    if (yych <= 0x9F)
      goto yy631;
    goto yy629;
  yy635:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy629;
    if (yych <= 0xBF)
      goto yy633;
    goto yy629;
  yy636:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy629;
    if (yych <= 0xBF)
      goto yy633;
    goto yy629;
  yy637:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy629;
    if (yych <= 0x8F)
      goto yy633;
    goto yy629;
  yy638:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy638;
    }
    if (yych <= 0xDF) {
      if (yych <= '=') {
        if (yych <= 0x00)
          goto yy629;
        if (yych == '\n')
          goto yy629;
        goto yy627;
      } else {
        if (yych <= '>')
          goto yy639;
        if (yych <= 0x7F)
          goto yy627;
        if (yych <= 0xC1)
          goto yy629;
        goto yy631;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy632;
        if (yych == 0xED)
          goto yy634;
        goto yy633;
      } else {
        if (yych <= 0xF0)
          goto yy635;
        if (yych <= 0xF3)
          goto yy636;
        if (yych <= 0xF4)
          goto yy637;
        goto yy629;
      }
    }
  yy639:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy627;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= '\n')
          goto yy640;
        if (yych <= ']')
          goto yy630;
      } else {
        if (yych <= 0xDF)
          goto yy631;
        if (yych <= 0xE0)
          goto yy632;
        goto yy633;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy634;
        if (yych <= 0xEF)
          goto yy633;
        goto yy635;
      } else {
        if (yych <= 0xF3)
          goto yy636;
        if (yych <= 0xF4)
          goto yy637;
      }
    }
  yy640 : { return (bufsize_t)(p - start); }
  }
}

// Try to match a link title (in single quotes, in double quotes, or
// in parentheses), returning number of chars matched.  Allow one
// level of internal nesting (quotes within quotes).
bufsize_t _scan_link_title(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    static const unsigned char yybm[] = {
        0,   208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208,
        208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208,
        208, 208, 208, 208, 208, 208, 192, 208, 208, 208, 208, 144, 80,  80,
        208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208,
        208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208,
        208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208,
        208, 208, 208, 208, 208, 208, 208, 208, 32,  208, 208, 208, 208, 208,
        208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208,
        208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208, 208,
        208, 208, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych <= '&') {
      if (yych == '"')
        goto yy643;
    } else {
      if (yych <= '\'')
        goto yy644;
      if (yych <= '(')
        goto yy645;
    }
    ++p;
  yy642 : { return 0; }
  yy643:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x00)
      goto yy642;
    if (yych <= 0x7F)
      goto yy647;
    if (yych <= 0xC1)
      goto yy642;
    if (yych <= 0xF4)
      goto yy647;
    goto yy642;
  yy644:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= 0x00)
      goto yy642;
    if (yych <= 0x7F)
      goto yy660;
    if (yych <= 0xC1)
      goto yy642;
    if (yych <= 0xF4)
      goto yy660;
    goto yy642;
  yy645:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych <= '(') {
      if (yych <= 0x00)
        goto yy642;
      if (yych <= '\'')
        goto yy672;
      goto yy642;
    } else {
      if (yych <= 0x7F)
        goto yy672;
      if (yych <= 0xC1)
        goto yy642;
      if (yych <= 0xF4)
        goto yy672;
      goto yy642;
    }
  yy646:
    yych = *++p;
  yy647:
    if (yybm[0 + yych] & 16) {
      goto yy646;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= 0x00)
          goto yy648;
        if (yych <= '"')
          goto yy649;
        goto yy651;
      } else {
        if (yych <= 0xC1)
          goto yy648;
        if (yych <= 0xDF)
          goto yy652;
        goto yy653;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy655;
        goto yy654;
      } else {
        if (yych <= 0xF0)
          goto yy656;
        if (yych <= 0xF3)
          goto yy657;
        if (yych <= 0xF4)
          goto yy658;
      }
    }
  yy648:
    p = marker;
    if (yyaccept <= 1) {
      if (yyaccept == 0) {
        goto yy642;
      } else {
        goto yy650;
      }
    } else {
      if (yyaccept == 2) {
        goto yy662;
      } else {
        goto yy674;
      }
    }
  yy649:
    ++p;
  yy650 : { return (bufsize_t)(p - start); }
  yy651:
    yych = *++p;
    if (yybm[0 + yych] & 16) {
      goto yy646;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= 0x00)
          goto yy648;
        if (yych <= '"')
          goto yy683;
        goto yy651;
      } else {
        if (yych <= 0xC1)
          goto yy648;
        if (yych >= 0xE0)
          goto yy653;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy655;
        goto yy654;
      } else {
        if (yych <= 0xF0)
          goto yy656;
        if (yych <= 0xF3)
          goto yy657;
        if (yych <= 0xF4)
          goto yy658;
        goto yy648;
      }
    }
  yy652:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy646;
    goto yy648;
  yy653:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy652;
    goto yy648;
  yy654:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy652;
    goto yy648;
  yy655:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0x9F)
      goto yy652;
    goto yy648;
  yy656:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy654;
    goto yy648;
  yy657:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy654;
    goto yy648;
  yy658:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0x8F)
      goto yy654;
    goto yy648;
  yy659:
    yych = *++p;
  yy660:
    if (yybm[0 + yych] & 64) {
      goto yy659;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= 0x00)
          goto yy648;
        if (yych >= '(')
          goto yy663;
      } else {
        if (yych <= 0xC1)
          goto yy648;
        if (yych <= 0xDF)
          goto yy664;
        goto yy665;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy667;
        goto yy666;
      } else {
        if (yych <= 0xF0)
          goto yy668;
        if (yych <= 0xF3)
          goto yy669;
        if (yych <= 0xF4)
          goto yy670;
        goto yy648;
      }
    }
  yy661:
    ++p;
  yy662 : { return (bufsize_t)(p - start); }
  yy663:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy659;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= 0x00)
          goto yy648;
        if (yych <= '\'')
          goto yy684;
        goto yy663;
      } else {
        if (yych <= 0xC1)
          goto yy648;
        if (yych >= 0xE0)
          goto yy665;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy667;
        goto yy666;
      } else {
        if (yych <= 0xF0)
          goto yy668;
        if (yych <= 0xF3)
          goto yy669;
        if (yych <= 0xF4)
          goto yy670;
        goto yy648;
      }
    }
  yy664:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy659;
    goto yy648;
  yy665:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy664;
    goto yy648;
  yy666:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy664;
    goto yy648;
  yy667:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0x9F)
      goto yy664;
    goto yy648;
  yy668:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy666;
    goto yy648;
  yy669:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy666;
    goto yy648;
  yy670:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0x8F)
      goto yy666;
    goto yy648;
  yy671:
    yych = *++p;
  yy672:
    if (yybm[0 + yych] & 128) {
      goto yy671;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= '(')
          goto yy648;
        if (yych >= '*')
          goto yy675;
      } else {
        if (yych <= 0xC1)
          goto yy648;
        if (yych <= 0xDF)
          goto yy676;
        goto yy677;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy679;
        goto yy678;
      } else {
        if (yych <= 0xF0)
          goto yy680;
        if (yych <= 0xF3)
          goto yy681;
        if (yych <= 0xF4)
          goto yy682;
        goto yy648;
      }
    }
  yy673:
    ++p;
  yy674 : { return (bufsize_t)(p - start); }
  yy675:
    yych = *++p;
    if (yych <= 0xDF) {
      if (yych <= '[') {
        if (yych <= 0x00)
          goto yy648;
        if (yych == ')')
          goto yy685;
        goto yy671;
      } else {
        if (yych <= '\\')
          goto yy675;
        if (yych <= 0x7F)
          goto yy671;
        if (yych <= 0xC1)
          goto yy648;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0)
          goto yy677;
        if (yych == 0xED)
          goto yy679;
        goto yy678;
      } else {
        if (yych <= 0xF0)
          goto yy680;
        if (yych <= 0xF3)
          goto yy681;
        if (yych <= 0xF4)
          goto yy682;
        goto yy648;
      }
    }
  yy676:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy671;
    goto yy648;
  yy677:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy676;
    goto yy648;
  yy678:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy676;
    goto yy648;
  yy679:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0x9F)
      goto yy676;
    goto yy648;
  yy680:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy678;
    goto yy648;
  yy681:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0xBF)
      goto yy678;
    goto yy648;
  yy682:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy648;
    if (yych <= 0x8F)
      goto yy678;
    goto yy648;
  yy683:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 16) {
      goto yy646;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= 0x00)
          goto yy650;
        if (yych <= '"')
          goto yy649;
        goto yy651;
      } else {
        if (yych <= 0xC1)
          goto yy650;
        if (yych <= 0xDF)
          goto yy652;
        goto yy653;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy655;
        goto yy654;
      } else {
        if (yych <= 0xF0)
          goto yy656;
        if (yych <= 0xF3)
          goto yy657;
        if (yych <= 0xF4)
          goto yy658;
        goto yy650;
      }
    }
  yy684:
    yyaccept = 2;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy659;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= 0x00)
          goto yy662;
        if (yych <= '\'')
          goto yy661;
        goto yy663;
      } else {
        if (yych <= 0xC1)
          goto yy662;
        if (yych <= 0xDF)
          goto yy664;
        goto yy665;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy667;
        goto yy666;
      } else {
        if (yych <= 0xF0)
          goto yy668;
        if (yych <= 0xF3)
          goto yy669;
        if (yych <= 0xF4)
          goto yy670;
        goto yy662;
      }
    }
  yy685:
    yyaccept = 3;
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy671;
    }
    if (yych <= 0xE0) {
      if (yych <= '\\') {
        if (yych <= '(')
          goto yy674;
        if (yych <= ')')
          goto yy673;
        goto yy675;
      } else {
        if (yych <= 0xC1)
          goto yy674;
        if (yych <= 0xDF)
          goto yy676;
        goto yy677;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych == 0xED)
          goto yy679;
        goto yy678;
      } else {
        if (yych <= 0xF0)
          goto yy680;
        if (yych <= 0xF3)
          goto yy681;
        if (yych <= 0xF4)
          goto yy682;
        goto yy674;
      }
    }
  }
}

// Match space characters, including newlines.
bufsize_t _scan_spacechars(const unsigned char *p) {
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 128, 128, 128, 128, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   128, 0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0,   0,   0,   0, 0,
    };
    yych = *p;
    if (yybm[0 + yych] & 128) {
      goto yy687;
    }
    ++p;
    { return 0; }
  yy687:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy687;
    }
    { return (bufsize_t)(p - start); }
  }
}

// Match ATX heading start.
bufsize_t _scan_atx_heading_start(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0,   0,   0, 0, 0,
    };
    yych = *p;
    if (yych == '#')
      goto yy690;
    ++p;
  yy689 : { return 0; }
  yy690:
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy691;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy689;
      if (yych <= '\n')
        goto yy693;
      goto yy689;
    } else {
      if (yych <= '\r')
        goto yy693;
      if (yych == '#')
        goto yy694;
      goto yy689;
    }
  yy691:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy691;
    }
  yy692 : { return (bufsize_t)(p - start); }
  yy693:
    ++p;
    goto yy692;
  yy694:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy691;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy695;
      if (yych <= '\n')
        goto yy693;
    } else {
      if (yych <= '\r')
        goto yy693;
      if (yych == '#')
        goto yy696;
    }
  yy695:
    p = marker;
    goto yy689;
  yy696:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy691;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy695;
      if (yych <= '\n')
        goto yy693;
      goto yy695;
    } else {
      if (yych <= '\r')
        goto yy693;
      if (yych != '#')
        goto yy695;
    }
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy691;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy695;
      if (yych <= '\n')
        goto yy693;
      goto yy695;
    } else {
      if (yych <= '\r')
        goto yy693;
      if (yych != '#')
        goto yy695;
    }
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy691;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy695;
      if (yych <= '\n')
        goto yy693;
      goto yy695;
    } else {
      if (yych <= '\r')
        goto yy693;
      if (yych != '#')
        goto yy695;
    }
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy691;
    }
    if (yych <= 0x08)
      goto yy695;
    if (yych <= '\n')
      goto yy693;
    if (yych == '\r')
      goto yy693;
    goto yy695;
  }
}

// Match setext heading line.  Return 1 for level-1 heading,
// 2 for level-2, 0 for no match.
bufsize_t _scan_setext_heading_line(const unsigned char *p) {
  const unsigned char *marker = NULL;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0, 0,  0, 0, 0, 0, 0, 0, 0, 32, 0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  32, 0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 64, 0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 128, 0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0, 0, 0, 0, 0,   0, 0, 0, 0,
        0, 0,  0, 0, 0, 0, 0, 0, 0, 0,  0,  0, 0, 0,
    };
    yych = *p;
    if (yych == '-')
      goto yy699;
    if (yych == '=')
      goto yy700;
    ++p;
  yy698 : { return 0; }
  yy699:
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 64) {
      goto yy705;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy698;
      if (yych <= '\n')
        goto yy702;
      goto yy698;
    } else {
      if (yych <= '\r')
        goto yy702;
      if (yych == ' ')
        goto yy702;
      goto yy698;
    }
  yy700:
    yych = *(marker = ++p);
    if (yybm[0 + yych] & 128) {
      goto yy709;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy698;
      if (yych <= '\n')
        goto yy707;
      goto yy698;
    } else {
      if (yych <= '\r')
        goto yy707;
      if (yych == ' ')
        goto yy707;
      goto yy698;
    }
  yy701:
    yych = *++p;
  yy702:
    if (yybm[0 + yych] & 32) {
      goto yy701;
    }
    if (yych <= 0x08)
      goto yy703;
    if (yych <= '\n')
      goto yy704;
    if (yych == '\r')
      goto yy704;
  yy703:
    p = marker;
    goto yy698;
  yy704:
    ++p;
    { return 2; }
  yy705:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy701;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy703;
      if (yych <= '\n')
        goto yy704;
      goto yy703;
    } else {
      if (yych <= '\r')
        goto yy704;
      if (yych == '-')
        goto yy705;
      goto yy703;
    }
  yy706:
    yych = *++p;
  yy707:
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy703;
      if (yych <= '\t')
        goto yy706;
      if (yych >= '\v')
        goto yy703;
    } else {
      if (yych <= '\r')
        goto yy708;
      if (yych == ' ')
        goto yy706;
      goto yy703;
    }
  yy708:
    ++p;
    { return 1; }
  yy709:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy709;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy703;
      if (yych <= '\t')
        goto yy706;
      if (yych <= '\n')
        goto yy708;
      goto yy703;
    } else {
      if (yych <= '\r')
        goto yy708;
      if (yych == ' ')
        goto yy706;
      goto yy703;
    }
  }
}

// Scan an opening code fence.
bufsize_t _scan_open_code_fence(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0,   192, 192, 192, 192, 192, 192, 192, 192, 192, 0,   192, 192, 0,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 144, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192, 192,
        224, 192, 0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
        0,   0,   0,   0,
    };
    yych = *p;
    if (yych == '`')
      goto yy712;
    if (yych == '~')
      goto yy713;
    ++p;
  yy711 : { return 0; }
  yy712:
    yych = *(marker = ++p);
    if (yych == '`')
      goto yy714;
    goto yy711;
  yy713:
    yych = *(marker = ++p);
    if (yych == '~')
      goto yy716;
    goto yy711;
  yy714:
    yych = *++p;
    if (yybm[0 + yych] & 16) {
      goto yy717;
    }
  yy715:
    p = marker;
    goto yy711;
  yy716:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy718;
    }
    goto yy715;
  yy717:
    yych = *++p;
    if (yybm[0 + yych] & 16) {
      goto yy717;
    }
    if (yych <= 0xDF) {
      if (yych <= '\f') {
        if (yych <= 0x00)
          goto yy715;
        if (yych == '\n') {
          marker = p;
          goto yy720;
        }
        marker = p;
        goto yy719;
      } else {
        if (yych <= '\r') {
          marker = p;
          goto yy720;
        }
        if (yych <= 0x7F) {
          marker = p;
          goto yy719;
        }
        if (yych <= 0xC1)
          goto yy715;
        marker = p;
        goto yy721;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0) {
          marker = p;
          goto yy722;
        }
        if (yych == 0xED) {
          marker = p;
          goto yy724;
        }
        marker = p;
        goto yy723;
      } else {
        if (yych <= 0xF0) {
          marker = p;
          goto yy725;
        }
        if (yych <= 0xF3) {
          marker = p;
          goto yy726;
        }
        if (yych <= 0xF4) {
          marker = p;
          goto yy727;
        }
        goto yy715;
      }
    }
  yy718:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy718;
    }
    if (yych <= 0xDF) {
      if (yych <= '\f') {
        if (yych <= 0x00)
          goto yy715;
        if (yych == '\n') {
          marker = p;
          goto yy729;
        }
        marker = p;
        goto yy728;
      } else {
        if (yych <= '\r') {
          marker = p;
          goto yy729;
        }
        if (yych <= 0x7F) {
          marker = p;
          goto yy728;
        }
        if (yych <= 0xC1)
          goto yy715;
        marker = p;
        goto yy730;
      }
    } else {
      if (yych <= 0xEF) {
        if (yych <= 0xE0) {
          marker = p;
          goto yy731;
        }
        if (yych == 0xED) {
          marker = p;
          goto yy733;
        }
        marker = p;
        goto yy732;
      } else {
        if (yych <= 0xF0) {
          marker = p;
          goto yy734;
        }
        if (yych <= 0xF3) {
          marker = p;
          goto yy735;
        }
        if (yych <= 0xF4) {
          marker = p;
          goto yy736;
        }
        goto yy715;
      }
    }
  yy719:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy719;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy715;
        if (yych >= 0x0E)
          goto yy715;
      } else {
        if (yych <= 0xDF)
          goto yy721;
        if (yych <= 0xE0)
          goto yy722;
        goto yy723;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy724;
        if (yych <= 0xEF)
          goto yy723;
        goto yy725;
      } else {
        if (yych <= 0xF3)
          goto yy726;
        if (yych <= 0xF4)
          goto yy727;
        goto yy715;
      }
    }
  yy720:
    ++p;
    p = marker;
    { return (bufsize_t)(p - start); }
  yy721:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy719;
    goto yy715;
  yy722:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy721;
    goto yy715;
  yy723:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy721;
    goto yy715;
  yy724:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0x9F)
      goto yy721;
    goto yy715;
  yy725:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy723;
    goto yy715;
  yy726:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy723;
    goto yy715;
  yy727:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0x8F)
      goto yy723;
    goto yy715;
  yy728:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy728;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= 0x00)
          goto yy715;
        if (yych >= 0x0E)
          goto yy715;
      } else {
        if (yych <= 0xDF)
          goto yy730;
        if (yych <= 0xE0)
          goto yy731;
        goto yy732;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy733;
        if (yych <= 0xEF)
          goto yy732;
        goto yy734;
      } else {
        if (yych <= 0xF3)
          goto yy735;
        if (yych <= 0xF4)
          goto yy736;
        goto yy715;
      }
    }
  yy729:
    ++p;
    p = marker;
    { return (bufsize_t)(p - start); }
  yy730:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy728;
    goto yy715;
  yy731:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy730;
    goto yy715;
  yy732:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy730;
    goto yy715;
  yy733:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0x9F)
      goto yy730;
    goto yy715;
  yy734:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy732;
    goto yy715;
  yy735:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0xBF)
      goto yy732;
    goto yy715;
  yy736:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy715;
    if (yych <= 0x8F)
      goto yy732;
    goto yy715;
  }
}

// Scan a closing code fence with length at least len.
bufsize_t _scan_close_code_fence(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0, 0, 0, 0, 0, 0, 0, 0, 0,  128, 0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   128, 0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 32, 0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 64, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0,  0,   0,   0, 0, 0,
    };
    yych = *p;
    if (yych == '`')
      goto yy739;
    if (yych == '~')
      goto yy740;
    ++p;
  yy738 : { return 0; }
  yy739:
    yych = *(marker = ++p);
    if (yych == '`')
      goto yy741;
    goto yy738;
  yy740:
    yych = *(marker = ++p);
    if (yych == '~')
      goto yy743;
    goto yy738;
  yy741:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy744;
    }
  yy742:
    p = marker;
    goto yy738;
  yy743:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy745;
    }
    goto yy742;
  yy744:
    yych = *++p;
    if (yybm[0 + yych] & 32) {
      goto yy744;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy742;
      if (yych <= '\t') {
        marker = p;
        goto yy746;
      }
      if (yych <= '\n') {
        marker = p;
        goto yy747;
      }
      goto yy742;
    } else {
      if (yych <= '\r') {
        marker = p;
        goto yy747;
      }
      if (yych == ' ') {
        marker = p;
        goto yy746;
      }
      goto yy742;
    }
  yy745:
    yych = *++p;
    if (yybm[0 + yych] & 64) {
      goto yy745;
    }
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy742;
      if (yych <= '\t') {
        marker = p;
        goto yy748;
      }
      if (yych <= '\n') {
        marker = p;
        goto yy749;
      }
      goto yy742;
    } else {
      if (yych <= '\r') {
        marker = p;
        goto yy749;
      }
      if (yych == ' ') {
        marker = p;
        goto yy748;
      }
      goto yy742;
    }
  yy746:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy746;
    }
    if (yych <= 0x08)
      goto yy742;
    if (yych <= '\n')
      goto yy747;
    if (yych != '\r')
      goto yy742;
  yy747:
    ++p;
    p = marker;
    { return (bufsize_t)(p - start); }
  yy748:
    yych = *++p;
    if (yych <= '\f') {
      if (yych <= 0x08)
        goto yy742;
      if (yych <= '\t')
        goto yy748;
      if (yych >= '\v')
        goto yy742;
    } else {
      if (yych <= '\r')
        goto yy749;
      if (yych == ' ')
        goto yy748;
      goto yy742;
    }
  yy749:
    ++p;
    p = marker;
    { return (bufsize_t)(p - start); }
  }
}

// Scans an entity.
// Returns number of chars matched.
bufsize_t _scan_entity(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    yych = *p;
    if (yych == '&')
      goto yy752;
    ++p;
  yy751 : { return 0; }
  yy752:
    yych = *(marker = ++p);
    if (yych <= '@') {
      if (yych != '#')
        goto yy751;
    } else {
      if (yych <= 'Z')
        goto yy754;
      if (yych <= '`')
        goto yy751;
      if (yych <= 'z')
        goto yy754;
      goto yy751;
    }
    yych = *++p;
    if (yych <= 'W') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy755;
    } else {
      if (yych <= 'X')
        goto yy756;
      if (yych == 'x')
        goto yy756;
    }
  yy753:
    p = marker;
    goto yy751;
  yy754:
    yych = *++p;
    if (yych <= '@') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy757;
      goto yy753;
    } else {
      if (yych <= 'Z')
        goto yy757;
      if (yych <= '`')
        goto yy753;
      if (yych <= 'z')
        goto yy757;
      goto yy753;
    }
  yy755:
    yych = *++p;
    if (yych <= '/')
      goto yy753;
    if (yych <= '9')
      goto yy758;
    if (yych == ';')
      goto yy759;
    goto yy753;
  yy756:
    yych = *++p;
    if (yych <= '@') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy760;
      goto yy753;
    } else {
      if (yych <= 'F')
        goto yy760;
      if (yych <= '`')
        goto yy753;
      if (yych <= 'f')
        goto yy760;
      goto yy753;
    }
  yy757:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy761;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
        goto yy761;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'z')
          goto yy761;
        goto yy753;
      }
    }
  yy758:
    yych = *++p;
    if (yych <= '/')
      goto yy753;
    if (yych <= '9')
      goto yy762;
    if (yych != ';')
      goto yy753;
  yy759:
    ++p;
    { return (bufsize_t)(p - start); }
  yy760:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy763;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'F') {
        if (yych <= '@')
          goto yy753;
        goto yy763;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'f')
          goto yy763;
        goto yy753;
      }
    }
  yy761:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy764;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
        goto yy764;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'z')
          goto yy764;
        goto yy753;
      }
    }
  yy762:
    yych = *++p;
    if (yych <= '/')
      goto yy753;
    if (yych <= '9')
      goto yy765;
    if (yych == ';')
      goto yy759;
    goto yy753;
  yy763:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy766;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'F') {
        if (yych <= '@')
          goto yy753;
        goto yy766;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'f')
          goto yy766;
        goto yy753;
      }
    }
  yy764:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy767;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
        goto yy767;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'z')
          goto yy767;
        goto yy753;
      }
    }
  yy765:
    yych = *++p;
    if (yych <= '/')
      goto yy753;
    if (yych <= '9')
      goto yy768;
    if (yych == ';')
      goto yy759;
    goto yy753;
  yy766:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy769;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'F') {
        if (yych <= '@')
          goto yy753;
        goto yy769;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'f')
          goto yy769;
        goto yy753;
      }
    }
  yy767:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy770;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
        goto yy770;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'z')
          goto yy770;
        goto yy753;
      }
    }
  yy768:
    yych = *++p;
    if (yych <= '/')
      goto yy753;
    if (yych <= '9')
      goto yy771;
    if (yych == ';')
      goto yy759;
    goto yy753;
  yy769:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy772;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'F') {
        if (yych <= '@')
          goto yy753;
        goto yy772;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'f')
          goto yy772;
        goto yy753;
      }
    }
  yy770:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy773;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
        goto yy773;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'z')
          goto yy773;
        goto yy753;
      }
    }
  yy771:
    yych = *++p;
    if (yych <= '/')
      goto yy753;
    if (yych <= '9')
      goto yy774;
    if (yych == ';')
      goto yy759;
    goto yy753;
  yy772:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy774;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'F') {
        if (yych <= '@')
          goto yy753;
        goto yy774;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'f')
          goto yy774;
        goto yy753;
      }
    }
  yy773:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy775;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
        goto yy775;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'z')
          goto yy775;
        goto yy753;
      }
    }
  yy774:
    yych = *++p;
    if (yych == ';')
      goto yy759;
    goto yy753;
  yy775:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy776;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy776:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy777;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy777:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy778;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy778:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy779;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy779:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy780;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy780:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy781;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy781:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy782;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy782:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy783;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy783:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy784;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy784:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy785;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy785:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy786;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy786:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy787;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy787:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy788;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy788:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy789;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy789:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy790;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy790:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy791;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy791:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy792;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy792:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy793;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy793:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy794;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy794:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy795;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy795:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy796;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy796:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy797;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy797:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy798;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych >= '{')
          goto yy753;
      }
    }
  yy798:
    yych = *++p;
    if (yych <= ';') {
      if (yych <= '/')
        goto yy753;
      if (yych <= '9')
        goto yy774;
      if (yych <= ':')
        goto yy753;
      goto yy759;
    } else {
      if (yych <= 'Z') {
        if (yych <= '@')
          goto yy753;
        goto yy774;
      } else {
        if (yych <= '`')
          goto yy753;
        if (yych <= 'z')
          goto yy774;
        goto yy753;
      }
    }
  }
}

// Returns positive value if a URL begins in a way that is potentially
// dangerous, with javascript:, vbscript:, file:, or data:, otherwise 0.
bufsize_t _scan_dangerous_url(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    unsigned int yyaccept = 0;
    yych = *p;
    if (yych <= 'V') {
      if (yych <= 'F') {
        if (yych == 'D')
          goto yy801;
        if (yych >= 'F')
          goto yy802;
      } else {
        if (yych == 'J')
          goto yy803;
        if (yych >= 'V')
          goto yy804;
      }
    } else {
      if (yych <= 'f') {
        if (yych == 'd')
          goto yy801;
        if (yych >= 'f')
          goto yy802;
      } else {
        if (yych <= 'j') {
          if (yych >= 'j')
            goto yy803;
        } else {
          if (yych == 'v')
            goto yy804;
        }
      }
    }
    ++p;
  yy800 : { return 0; }
  yy801:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych == 'A')
      goto yy805;
    if (yych == 'a')
      goto yy805;
    goto yy800;
  yy802:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych == 'I')
      goto yy807;
    if (yych == 'i')
      goto yy807;
    goto yy800;
  yy803:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych == 'A')
      goto yy808;
    if (yych == 'a')
      goto yy808;
    goto yy800;
  yy804:
    yyaccept = 0;
    yych = *(marker = ++p);
    if (yych == 'B')
      goto yy809;
    if (yych == 'b')
      goto yy809;
    goto yy800;
  yy805:
    yych = *++p;
    if (yych == 'T')
      goto yy810;
    if (yych == 't')
      goto yy810;
  yy806:
    p = marker;
    if (yyaccept == 0) {
      goto yy800;
    } else {
      goto yy818;
    }
  yy807:
    yych = *++p;
    if (yych == 'L')
      goto yy811;
    if (yych == 'l')
      goto yy811;
    goto yy806;
  yy808:
    yych = *++p;
    if (yych == 'V')
      goto yy812;
    if (yych == 'v')
      goto yy812;
    goto yy806;
  yy809:
    yych = *++p;
    if (yych == 'S')
      goto yy813;
    if (yych == 's')
      goto yy813;
    goto yy806;
  yy810:
    yych = *++p;
    if (yych == 'A')
      goto yy814;
    if (yych == 'a')
      goto yy814;
    goto yy806;
  yy811:
    yych = *++p;
    if (yych == 'E')
      goto yy815;
    if (yych == 'e')
      goto yy815;
    goto yy806;
  yy812:
    yych = *++p;
    if (yych == 'A')
      goto yy809;
    if (yych == 'a')
      goto yy809;
    goto yy806;
  yy813:
    yych = *++p;
    if (yych == 'C')
      goto yy816;
    if (yych == 'c')
      goto yy816;
    goto yy806;
  yy814:
    yych = *++p;
    if (yych == ':')
      goto yy817;
    goto yy806;
  yy815:
    yych = *++p;
    if (yych == ':')
      goto yy819;
    goto yy806;
  yy816:
    yych = *++p;
    if (yych == 'R')
      goto yy820;
    if (yych == 'r')
      goto yy820;
    goto yy806;
  yy817:
    yyaccept = 1;
    yych = *(marker = ++p);
    if (yych == 'I')
      goto yy821;
    if (yych == 'i')
      goto yy821;
  yy818 : { return (bufsize_t)(p - start); }
  yy819:
    ++p;
    goto yy818;
  yy820:
    yych = *++p;
    if (yych == 'I')
      goto yy822;
    if (yych == 'i')
      goto yy822;
    goto yy806;
  yy821:
    yych = *++p;
    if (yych == 'M')
      goto yy823;
    if (yych == 'm')
      goto yy823;
    goto yy806;
  yy822:
    yych = *++p;
    if (yych == 'P')
      goto yy824;
    if (yych == 'p')
      goto yy824;
    goto yy806;
  yy823:
    yych = *++p;
    if (yych == 'A')
      goto yy825;
    if (yych == 'a')
      goto yy825;
    goto yy806;
  yy824:
    yych = *++p;
    if (yych == 'T')
      goto yy815;
    if (yych == 't')
      goto yy815;
    goto yy806;
  yy825:
    yych = *++p;
    if (yych == 'G')
      goto yy826;
    if (yych != 'g')
      goto yy806;
  yy826:
    yych = *++p;
    if (yych == 'E')
      goto yy827;
    if (yych != 'e')
      goto yy806;
  yy827:
    yych = *++p;
    if (yych != '/')
      goto yy806;
    yych = *++p;
    if (yych <= 'W') {
      if (yych <= 'J') {
        if (yych == 'G')
          goto yy828;
        if (yych <= 'I')
          goto yy806;
        goto yy829;
      } else {
        if (yych == 'P')
          goto yy830;
        if (yych <= 'V')
          goto yy806;
        goto yy831;
      }
    } else {
      if (yych <= 'j') {
        if (yych == 'g')
          goto yy828;
        if (yych <= 'i')
          goto yy806;
        goto yy829;
      } else {
        if (yych <= 'p') {
          if (yych <= 'o')
            goto yy806;
          goto yy830;
        } else {
          if (yych == 'w')
            goto yy831;
          goto yy806;
        }
      }
    }
  yy828:
    yych = *++p;
    if (yych == 'I')
      goto yy832;
    if (yych == 'i')
      goto yy832;
    goto yy806;
  yy829:
    yych = *++p;
    if (yych == 'P')
      goto yy833;
    if (yych == 'p')
      goto yy833;
    goto yy806;
  yy830:
    yych = *++p;
    if (yych == 'N')
      goto yy834;
    if (yych == 'n')
      goto yy834;
    goto yy806;
  yy831:
    yych = *++p;
    if (yych == 'E')
      goto yy835;
    if (yych == 'e')
      goto yy835;
    goto yy806;
  yy832:
    yych = *++p;
    if (yych == 'F')
      goto yy836;
    if (yych == 'f')
      goto yy836;
    goto yy806;
  yy833:
    yych = *++p;
    if (yych == 'E')
      goto yy834;
    if (yych != 'e')
      goto yy806;
  yy834:
    yych = *++p;
    if (yych == 'G')
      goto yy836;
    if (yych == 'g')
      goto yy836;
    goto yy806;
  yy835:
    yych = *++p;
    if (yych == 'B')
      goto yy837;
    if (yych == 'b')
      goto yy837;
    goto yy806;
  yy836:
    ++p;
    { return 0; }
  yy837:
    yych = *++p;
    if (yych == 'P')
      goto yy836;
    if (yych == 'p')
      goto yy836;
    goto yy806;
  }
}

// Scans a footnote definition opening.
bufsize_t _scan_footnote_definition(const unsigned char *p) {
  const unsigned char *marker = NULL;
  const unsigned char *start = p;

  {
    unsigned char yych;
    static const unsigned char yybm[] = {
        0,   64, 64, 64, 64, 64, 64, 64, 64, 128, 0,  64, 64, 0,  64, 64,
        64,  64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64,
        128, 64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64,
        64,  64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64,
        64,  64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64,
        64,  64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 0,  64, 64,
        64,  64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64,
        64,  64, 64, 64, 64, 64, 64, 64, 64, 64,  64, 64, 64, 64, 64, 64,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
        0,   0,  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0,  0,  0,
    };
    yych = *p;
    if (yych == '[')
      goto yy840;
    ++p;
  yy839 : { return 0; }
  yy840:
    yych = *(marker = ++p);
    if (yych != '^')
      goto yy839;
    yych = *++p;
    if (yych != ']')
      goto yy843;
  yy841:
    p = marker;
    goto yy839;
  yy842:
    yych = *++p;
  yy843:
    if (yybm[0 + yych] & 64) {
      goto yy842;
    }
    if (yych <= 0xEC) {
      if (yych <= 0xC1) {
        if (yych <= ' ')
          goto yy841;
        if (yych <= ']')
          goto yy851;
        goto yy841;
      } else {
        if (yych <= 0xDF)
          goto yy844;
        if (yych <= 0xE0)
          goto yy845;
        goto yy846;
      }
    } else {
      if (yych <= 0xF0) {
        if (yych <= 0xED)
          goto yy847;
        if (yych <= 0xEF)
          goto yy846;
        goto yy848;
      } else {
        if (yych <= 0xF3)
          goto yy849;
        if (yych <= 0xF4)
          goto yy850;
        goto yy841;
      }
    }
  yy844:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy841;
    if (yych <= 0xBF)
      goto yy842;
    goto yy841;
  yy845:
    yych = *++p;
    if (yych <= 0x9F)
      goto yy841;
    if (yych <= 0xBF)
      goto yy844;
    goto yy841;
  yy846:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy841;
    if (yych <= 0xBF)
      goto yy844;
    goto yy841;
  yy847:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy841;
    if (yych <= 0x9F)
      goto yy844;
    goto yy841;
  yy848:
    yych = *++p;
    if (yych <= 0x8F)
      goto yy841;
    if (yych <= 0xBF)
      goto yy846;
    goto yy841;
  yy849:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy841;
    if (yych <= 0xBF)
      goto yy846;
    goto yy841;
  yy850:
    yych = *++p;
    if (yych <= 0x7F)
      goto yy841;
    if (yych <= 0x8F)
      goto yy846;
    goto yy841;
  yy851:
    yych = *++p;
    if (yych != ':')
      goto yy841;
  yy852:
    yych = *++p;
    if (yybm[0 + yych] & 128) {
      goto yy852;
    }
    { return (bufsize_t)(p - start); }
  }
}
